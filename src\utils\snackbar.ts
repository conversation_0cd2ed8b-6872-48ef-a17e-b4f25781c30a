import { useSnackbar, VariantType, Snackbar<PERSON>ey, ProviderContext } from "notistack";
import { createRef } from "react";

// Tạo một ref có thể mutate
const snackbarRef = { current: null as ProviderContext | null };

export const SnackbarUtilsConfigurator: React.FC = () => {
  const snackbar = useSnackbar();
  snackbarRef.current = snackbar;
  return null;
};

export const SnackbarUtils = {
  success(msg: string) {
    this.toast(msg, "success");
  },
  warning(msg: string) {
    this.toast(msg, "warning");
  },
  info(msg: string) {
    this.toast(msg, "info");
  },
  error(msg: string) {
    this.toast(msg, "error");
  },
  toast(msg: string, variant: VariantType = "default") {
    if (snackbarRef.current) {
      snackbarRef.current.enqueueSnackbar(msg, { variant });
    }
  },
  close(key?: SnackbarKey) {
    if (snackbarRef.current) {
      snackbarRef.current.closeSnackbar(key);
    }
  },
};
