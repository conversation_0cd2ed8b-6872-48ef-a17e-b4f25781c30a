import React, { useState } from 'react';
import { Box, Chip, Divider, Menu, MenuItem, Typography } from '@mui/material';
import MoreHorizOutlinedIcon from '@mui/icons-material/MoreHorizOutlined';

import TitleDialog from '@/src/components/dialog/TitleDialog';
import { paymentMethodType } from './PaymentMethodForm';

export default function ListPaymentMethod({
  paymentMethods,
  handleClickEdit,
  handleClickIsActive,
  handleClickDelete
}: {
  paymentMethods: paymentMethodType[];
  handleClickEdit: (paymentMethod: paymentMethodType) => void;
  handleClickIsActive?: (paymentMethod: paymentMethodType) => void;
  handleClickDelete?: (paymentMethod: paymentMethodType) => void;
}) {
  const [menuState, setMenuState] = useState<{ anchorEl: HTMLElement | null; paymentMethod: paymentMethodType | null }>(
    {
      anchorEl: null,
      paymentMethod: null
    }
  );

  const [confirmDialog, setConfirmDialog] = useState<{ open: boolean; paymentMethod: paymentMethodType | null }>({
    open: false,
    paymentMethod: null
  });

  const handleClickMenu = (event, paymentMethod: paymentMethodType) => {
    setMenuState({
      anchorEl: event.currentTarget,
      paymentMethod
    });
  };

  const handleCloseMenu = () => {
    setMenuState({ anchorEl: null, paymentMethod: null });
  };

  const handleOpenConfirmDialog = (paymentMethod: paymentMethodType) => {
    setConfirmDialog({ open: true, paymentMethod });
  };

  const handleCloseConfirmDialog = () => {
    setConfirmDialog({ open: false, paymentMethod: null });
  };

  const handleConfirmDelete = () => {
    if (confirmDialog.paymentMethod) {
      handleClickDelete?.(confirmDialog.paymentMethod);
    }
    handleCloseConfirmDialog();
  };

  const open = Boolean(menuState.anchorEl);

  return (
    <Box>
      {paymentMethods.map((paymentMethod, index) => {
        return (
          <Box key={index} marginBottom={2}>
            <Box display="flex" justifyContent={'space-between'} marginBottom={2}>
              <Box display={'flex'} gap={1} alignItems={'center'}>
                <Typography>{paymentMethod.paymentMethodName}</Typography>

                {paymentMethod.isActive ? (
                  <Chip label="Hoạt động" color="primary" size="small" sx={{ opacity: 0.7 }} />
                ) : (
                  <Chip label="Không hoạt động" color="error" size="small" sx={{ opacity: 0.7 }} />
                )}
              </Box>

              <MoreHorizOutlinedIcon
                sx={{ color: 'gray', cursor: 'pointer' }}
                onClick={(e) => handleClickMenu(e, paymentMethod)}
              />
            </Box>

            {/* Only render Menu when a specific item's menu is open */}
            {menuState.paymentMethod === paymentMethod && (
              <Menu
                id="basic-menu"
                anchorEl={menuState.anchorEl}
                open={open && menuState.paymentMethod === paymentMethod}
                onClose={handleCloseMenu}
                MenuListProps={{
                  'aria-labelledby': 'basic-button'
                }}
              >
                <MenuItem
                  onClick={() => {
                    handleCloseMenu();
                    if (menuState.paymentMethod) {
                      handleClickEdit(menuState.paymentMethod);
                    }
                  }}
                >
                  Sửa
                </MenuItem>
                <MenuItem
                  onClick={(e) => {
                    if (menuState.paymentMethod) {
                      handleClickIsActive?.(menuState.paymentMethod);
                    }
                  }}
                >
                  {paymentMethod.isActive ? 'Không hoạt động' : 'Hoạt động'}
                </MenuItem>

                <MenuItem
                  onClick={() => {
                    handleCloseMenu();
                    handleOpenConfirmDialog(paymentMethod);
                  }}
                >
                  <Typography color="error">Xóa</Typography>
                </MenuItem>
              </Menu>
            )}
            <TitleDialog
              title={`Xóa ${paymentMethod.paymentMethodName}`}
              open={confirmDialog.open}
              handleClose={handleCloseConfirmDialog}
              handleSubmit={handleConfirmDelete}
              submitBtnTitle="Xác nhận"
            >
              <Box>
                <Typography>Bạn có chắc muốn xóa phương thức thanh toán này?</Typography>
              </Box>
            </TitleDialog>
            <Divider />
          </Box>
        );
      })}
    </Box>
  );
}
