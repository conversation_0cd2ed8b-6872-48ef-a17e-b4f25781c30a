export interface GetProductRequest {
  partnerId: string;
  shopId: string;
  itemsType: 'Product' | 'Service';
  categoryId: string | null;
  subCategoryId: string | null;
  search: string;
  skip: number;
  limit: number;
}

export type GetProductRequestBody = Omit<GetProductRequest, 'skip' | 'limit'>;

export interface Product {
  productId: string;
  productName: string;
  productDesc: string;
  productImage: string;
  categoryId: string;
  price: number;
  publish: 'Publish' | 'UnPublish';
  active: 'Actived' | 'InActived';
  created: string;
  updated: string;
}

export interface ProductResponse {
  data: Product[];
  total: number;
} 