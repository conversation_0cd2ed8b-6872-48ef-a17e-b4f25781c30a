import React from "react";
import { useFormContext } from "react-hook-form";
import { VoucherPromotionFormData } from "../PromotionForm";
import { Box, List, ListItem, ListItemText, Typography } from "@mui/material";
import { formatMoney } from "@/src/utils/format-money";
import dayjs from "dayjs";
export const typeLimitText = (type: string): string => {
  switch (type) {
    case "All":
      return "Tất cả sản phẩm";
    case "Category":
      return "Chỉ định danh mục sản phẩm";
    case "Product":
      return "Chỉ định sản phẩm";
    case "NotRequired":
      return "Không yêu cầu";
    case "MinMoneyRequired":
      return "Yêu cầu tối thiểu số tiền";
    default:
      return "Không xác định"; // Dự phòng trường hợp không nằm trong danh sách
  }
};

export const conditionTypeText = (type: string): string => {
  switch (type) {
    case "All":
      return "Tất cả sản phẩm";
    case "Group":
      return "Chỉ định nhóm khách hàng";
    case "Customer":
      return "Chỉ định khách hàng";

    default:
      return "Không xác định"; // Dự phòng trường hợp không nằm trong danh sách
  }
};

export const releaseTypeText = (type: string): string => {
  switch (type) {
    case "Free":
      return "Phát hành miễn phí";
    case "ExchangePoints":
      return "Đổi điểm lấy voucher";

    default:
      return "Không xác định"; // Dự phòng trường hợp không nằm trong danh sách
  }
};

export const useVoucherStatus = (voucher) => {
  if (voucher.status === "Actived") return "Hoạt động";
  if (voucher.status === "InActived") return "Vô hiệu hoá";
  if (voucher.status === "Expired") return "Hết hạn";
};

export const isVoucherExprired = (voucher) => {
  const currentDate = new Date(); // Lấy thời gian hiện tại
  const endDateTime = new Date(voucher.endDate); // Chuyển endDate thành đối tượng Date

  if (voucher.isLongTerm) {
    return false;
  }
  if (endDateTime < currentDate) {
    return true;
  }
  return false;
};

export const useTimeVoucherEffect = (voucher) => {
  const startDate = voucher.isLongTerm
    ? dayjs(voucher.created).format("DD/MM/YYYY HH:mm")
    : dayjs(voucher.startDate).format("DD/MM/YYYY HH:mm");

  // Nếu voucher là dài hạn
  if (voucher.isLongTerm) {
    return `Có hiệu lực từ ${startDate} (Dài hạn)`;
  }

  const endDate = dayjs(voucher.endDate).format("DD/MM/YYYY HH:mm");
  return `Có hiệu lực từ ${startDate} đến ${endDate}`;
};

export const effectiveDateVoucher = (voucher) => {
  const startDate = voucher.isLongTerm
    ? dayjs(voucher.created).format("DD/MM/YYYY")
    : dayjs(voucher.startDate).format("DD/MM/YYYY");

  // Nếu voucher là dài hạn
  if (voucher.isLongTerm) {
    return `${dayjs(voucher.createdDate).format("DD/MM/YYYY")} (Dài hạn)`;
  }

  const endDate = dayjs(voucher.endDate).format("DD/MM/YYYY");
  return `${startDate} đến ${endDate}`;
};

export default function PromotionSummaryBox() {
  const {
    watch,

    formState: { errors },
  } = useFormContext<VoucherPromotionFormData>(); // Use context to get control
  const releaseType = watch("releaseType");
  const limitType = watch("limitType");
  const conditionType = watch("conditionType");
  const discountType = watch("discountType");
  const minOrder = watch("minOrder");
  const maxDiscount = watch("maxDiscount");
  const maxUsagePerUser = watch("maxUsagePerUser");
  return (
    <Box>
      <Typography variant="h6">Tóm tắt voucher</Typography>
      <Typography fontWeight="bold" marginTop={1}>
        Loại voucher
      </Typography>
      <List sx={{ p: 0, marginLeft: 1 }}>
        <ListItem disablePadding>
          <ListItemText
            primary={`• ${releaseType == "Free" ? "Phát hành miễn phí" : "Đổi điểm lấy voucher"}`}
          />
        </ListItem>
      </List>

      <Typography fontWeight="bold" marginTop={2}>
        Chi tiết áp dụng
      </Typography>
      <List sx={{ p: 0, marginLeft: 1 }}>
        {/* <ListItem disablePadding>
          <ListItemText primary={`• ${typeLimitText(limitType)}`} />
        </ListItem> */}
        <ListItem disablePadding>
          <ListItemText primary={`• ${conditionTypeText(conditionType)}`} />
        </ListItem>

        <ListItem disablePadding>
          <ListItemText primary={`• Đơn hàng tối thiểu ${formatMoney(minOrder)}đ`} />
        </ListItem>
        {discountType === "Percent" && (
          <ListItem disablePadding>
            <ListItemText primary={`• Giảm tối đa ${formatMoney(maxDiscount)}đ`} />
          </ListItem>
        )}
        <ListItem disablePadding>
          <ListItemText
            primary={`• ${
              maxUsagePerUser === 0
                ? "Không giới hạn số lần dùng voucher mỗi khách hàng"
                : `Mỗi khách hàng được sử dụng voucher ${maxUsagePerUser} lần`
            }`}
          />
        </ListItem>
      </List>
    </Box>
  );
}
