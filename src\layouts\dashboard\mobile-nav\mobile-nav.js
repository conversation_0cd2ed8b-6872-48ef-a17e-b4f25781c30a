import { useMemo, useState, useEffect, useRef } from "react";
import PropTypes from "prop-types";
import File04Icon from "@untitled-ui/icons-react/build/esm/File04";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Drawer from "@mui/material/Drawer";
import Stack from "@mui/material/Stack";
import SvgIcon from "@mui/material/SvgIcon";
import Typography from "@mui/material/Typography";
import { useTheme } from "@mui/material/styles";
import Collapse from "@mui/material/Collapse";
import { Logo } from "src/components/logo";
import { RouterLink } from "src/components/router-link";
import { Scrollbar } from "src/components/scrollbar";
import { usePathname } from "src/hooks/use-pathname";
import { paths } from "src/paths";
import { formatDisplayPhoneNumber } from "@/src/utils/format";
import { TenantSwitch } from "../tenant-switch";
import { MobileNavSection } from "./mobile-nav-section";
import { formatDateDisplay } from "@/src/utils/date-utils";
import { logger } from "@/src/utils/logger";
import { useFunction } from "@/src/api/hooks/function/use-function";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { useAppSelector } from "@/src/redux/hooks";
import Link from "next/link";
import TruncatedText from "@/src/components/truncated-text/truncated-text";

const MOBILE_NAV_WIDTH = 280;

const useCssVars = (color) => {
  const theme = useTheme();

  return useMemo(() => {
    switch (color) {
      // Blend-in and discrete have no difference on mobile because
      // there's a backdrop and differences are not visible
      case "blend-in":
      case "discrete":
        if (theme.palette.mode === "dark") {
          return {
            "--nav-bg": theme.palette.background.default,
            "--nav-color": theme.palette.neutral[100],
            "--nav-logo-border": theme.palette.neutral[700],
            "--nav-section-title-color": theme.palette.neutral[400],
            "--nav-item-color": theme.palette.neutral[400],
            "--nav-item-hover-bg": "rgba(255, 255, 255, 0.04)",
            "--nav-item-active-bg": "rgba(255, 255, 255, 0.04)",
            "--nav-item-active-color": theme.palette.text.primary,
            "--nav-item-disabled-color": theme.palette.neutral[600],
            "--nav-item-icon-color": theme.palette.neutral[500],
            "--nav-item-icon-active-color": theme.palette.primary.main,
            "--nav-item-icon-disabled-color": theme.palette.neutral[700],
            "--nav-item-chevron-color": theme.palette.neutral[700],
            "--nav-scrollbar-color": theme.palette.neutral[400],
          };
        } else {
          return {
            "--nav-bg": theme.palette.background.default,
            "--nav-color": theme.palette.text.primary,
            "--nav-logo-border": theme.palette.neutral[100],
            "--nav-section-title-color": theme.palette.neutral[400],
            "--nav-item-color": theme.palette.text.secondary,
            "--nav-item-hover-bg": theme.palette.action.hover,
            "--nav-item-active-bg": theme.palette.action.selected,
            "--nav-item-active-color": theme.palette.text.primary,
            "--nav-item-disabled-color": theme.palette.neutral[400],
            "--nav-item-icon-color": theme.palette.neutral[400],
            "--nav-item-icon-active-color": theme.palette.primary.main,
            "--nav-item-icon-disabled-color": theme.palette.neutral[400],
            "--nav-item-chevron-color": theme.palette.neutral[400],
            "--nav-scrollbar-color": theme.palette.neutral[900],
          };
        }

      case "evident":
        if (theme.palette.mode === "dark") {
          return {
            "--nav-bg": theme.palette.neutral[800],
            "--nav-color": theme.palette.common.white,
            "--nav-logo-border": theme.palette.neutral[700],
            "--nav-section-title-color": theme.palette.neutral[400],
            "--nav-item-color": theme.palette.neutral[400],
            "--nav-item-hover-bg": "rgba(255, 255, 255, 0.04)",
            "--nav-item-active-bg": "rgba(255, 255, 255, 0.04)",
            "--nav-item-active-color": theme.palette.common.white,
            "--nav-item-disabled-color": theme.palette.neutral[500],
            "--nav-item-icon-color": theme.palette.neutral[400],
            "--nav-item-icon-active-color": theme.palette.primary.main,
            "--nav-item-icon-disabled-color": theme.palette.neutral[500],
            "--nav-item-chevron-color": theme.palette.neutral[600],
            "--nav-scrollbar-color": theme.palette.neutral[400],
          };
        } else {
          return {
            "--nav-bg": theme.palette.neutral[800],
            "--nav-color": theme.palette.common.white,
            "--nav-logo-border": theme.palette.neutral[700],
            "--nav-section-title-color": theme.palette.neutral[400],
            "--nav-item-color": theme.palette.neutral[400],
            "--nav-item-hover-bg": "rgba(255, 255, 255, 0.04)",
            "--nav-item-active-bg": "rgba(255, 255, 255, 0.04)",
            "--nav-item-active-color": theme.palette.common.white,
            "--nav-item-disabled-color": theme.palette.neutral[500],
            "--nav-item-icon-color": theme.palette.neutral[400],
            "--nav-item-icon-active-color": theme.palette.primary.main,
            "--nav-item-icon-disabled-color": theme.palette.neutral[500],
            "--nav-item-chevron-color": theme.palette.neutral[600],
            "--nav-scrollbar-color": theme.palette.neutral[400],
          };
        }

      default:
        return {};
    }
  }, [theme, color]);
};

export const MobileNav = (props) => {
  const { getActivePackageFunctions } = useFunction();
  const { color = "evident", open, onClose, sections = [] } = props;
  const pathname = usePathname();
  const cssVars = useCssVars(color);
  const { profile } = useAppSelector((state) => state.profile);
  const [activePackage, setActivePackage] = useState([]);

  const hasFetched = useRef(false);
  const { isAgency } = useAllPermissions();

  const [isDetailsVisible, setIsDetailsVisible] = useState(false);
  const limit = 20;
  const searchQuery = null;

  const fetchActivePackageFunctions = async () => {
    if (hasFetched.current) return;
    hasFetched.current = true;
    try {
      const res = await getActivePackageFunctions();
      if (res && res.status === 200) {
        setActivePackage(res.data.data);
      }
    } catch (error) {
      logger.error("Error fetching active package functions:", error);
    }
  };
  useEffect(() => {
    fetchActivePackageFunctions();
  }, []);

  const handleToggleDetails = () => {
    setIsDetailsVisible((prev) => !prev);
  };

  if (!profile) {
    return null;
  }

  return (
    <Drawer
      anchor="left"
      onClose={onClose}
      open={open}
      PaperProps={{
        sx: {
          ...cssVars,
          // backgroundColor: 'var(--nav-bg)',
          backgroundColor: "#ffffff",
          color: "var(--nav-color)",
          width: MOBILE_NAV_WIDTH,
        },
      }}
      variant="temporary"
    >
      <Scrollbar
        sx={{
          height: "100%",
          "& .simplebar-content": {
            height: "100%",
          },
          "& .simplebar-scrollbar:before": {
            background: "var(--nav-scrollbar-color)",
          },
        }}
      >
        <Stack sx={{ height: "100%" }}>
          <Stack alignItems="center" direction="row" spacing={2} sx={{ p: 3 }}>
            <TenantSwitch sx={{ flexGrow: 1 }} isCollapsed={false} />
          </Stack>
          <Stack
            component="nav"
            spacing={2}
            sx={{
              flexGrow: 1,
              px: 2,
            }}
          >
            {sections.map((section, index) => (
              <MobileNavSection
                items={section.items}
                key={index}
                pathname={pathname}
                subheader={section.subheader}
              />
            ))}
          </Stack>
          <Box
            sx={{
              background: "#0045FF",
              borderRadius: "10px 10px 0 0",
              padding: "25px 10px 15px 10px",
              width: "90%",
              margin: "0 auto",
            }}
          >
            <Stack sx={{ display: "flex", flexDirection: "column", gap: 2, position: "relative" }}>
              <Stack
                flexDirection={"row"}
                alignItems={"center"}
                justifyContent={"space-between"}
                gap={2}
              >
                <Stack flexDirection={"row"} gap={"5px"} alignItems={"center"}>
                  <Box>
                    <img src="/logo/logo-evotech.png" />
                  </Box>
                  <Box>
                    <Stack>
                      <Typography sx={{ fontSize: "14px", fontWeight: 400, color: "#fff" }}>
                        EvotechCDP
                      </Typography>
                      <Typography sx={{ fontSize: "10px", fontWeight: 400, color: "#fff" }}>
                        Phiên bản: v1
                      </Typography>
                    </Stack>
                  </Box>
                </Stack>
                {activePackage && activePackage.packageName ? (
                  <TruncatedText
                    typographyProps={{
                      fontSize: "10px",
                      color: "#fff",
                    }}
                    text={activePackage.packageName}
                  >
                    {/* Temporarily hidden package upgrade link */}
                    {/* {isAgency && (
                        <Typography
                          sx={{
                            color: "#FFFF00",
                            fontStyle: "italic",
                            fontSize: "10px",
                            fontWeight: "400",
                            position: "absolute",
                            top: -18,
                            right: 0,
                          }}
                        >
                          Nâng cấp
                        </Typography>
                      )} */}
                  </TruncatedText>
                ) : (
                  <Typography
                    style={{
                      fontSize: "10px",
                      fontWeight: 400,
                      color: "#fff",
                      position: "relative",
                    }}
                  >
                    Chưa có gói
                    {/* Temporarily hidden package purchase link */}
                    {/* {isAgency && (
                        <Typography
                          sx={{
                            color: "#FFFF00",
                            fontStyle: "italic",
                            fontSize: "12px",
                            fontWeight: "400",
                            position: "absolute",
                            top: -18,
                            right: 0,
                          }}
                        >
                          Mua gói
                        </Typography>
                      )} */}
                  </Typography>
                )}
              </Stack>
              <Collapse in={isDetailsVisible}>
                <Box sx={{ display: "flex", flexDirection: "column", gap: "12px" }}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography sx={{ color: "#FFFFFF", fontSize: "12px", fontWeight: "400" }}>
                      Tài khoản
                    </Typography>
                    <Typography sx={{ color: "#FFFFFF", fontSize: "12px", fontWeight: "700" }}>
                      {formatDisplayPhoneNumber(profile.phoneNumber)}
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography sx={{ color: "#FFFFFF", fontSize: "12px", fontWeight: "400" }}>
                      Ngày kích hoạt
                    </Typography>
                    <Typography sx={{ color: "#FFFFFF", fontSize: "12px", fontWeight: "700" }}>
                      {formatDateDisplay(activePackage.startDate)}
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography sx={{ color: "#FFFFFF", fontSize: "12px", fontWeight: "400" }}>
                      Ngày hết hạn
                    </Typography>
                    <Typography sx={{ color: "#FFFFFF", fontSize: "12px", fontWeight: "700" }}>
                      {formatDateDisplay(activePackage.endDate)}
                    </Typography>
                  </Box>
                </Box>
              </Collapse>
              <img
                width={"10px"}
                height={"10px"}
                style={{
                  position: "absolute",
                  left: "46%",
                  top: "-18px",
                  cursor: "pointer",
                  transform: isDetailsVisible ? "rotate(180deg)" : "rotate(0deg)",
                  transition: "transform 0.3s ease",
                }}
                src="/logo/icon-double-up.png"
                onClick={handleToggleDetails}
              />
            </Stack>
          </Box>
          <Box sx={{ p: 0 }}></Box>
        </Stack>
      </Scrollbar>
    </Drawer>
  );
};

MobileNav.propTypes = {
  color: PropTypes.oneOf(["blend-in", "discrete", "evident"]),
  onClose: PropTypes.func,
  open: PropTypes.bool,
  sections: PropTypes.array,
};
