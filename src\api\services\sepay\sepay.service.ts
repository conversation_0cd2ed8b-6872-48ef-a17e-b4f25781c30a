import { apiClient } from '@/src/api/config/api-client';
import { API_PATHS } from '@/src/api/constants/api-paths';

export const sepayService = {
  /**
   * Tra cứu tên chủ tài khoản Sepay
   * @param {string} bankShortCode - Mã ngân hàng
   * @param {string} accountNumber - Số tài khoản
   * @param {string} clientMessageId - UUID cho header Client-Message-Id
   * @returns {Promise<any>}
   */
  lookupAccountHolderName: async (bankShortCode: string, accountNumber: string, clientMessageId: string) => {
    return apiClient.post(
      API_PATHS.SEPAY.GET_ACCOUNT_HOLDER_NAME.replace('{bankShortCode}', bankShortCode),
      { accountNumber: accountNumber },
      {
        headers: {
          'Client-Message-Id': clientMessageId,
          'Content-Type': 'application/json',
        },
      }
    );
  },

  /**
   * Tạo tài khoản ngân hàng Sepay
   * @param {string} bankShortCode - Mã ngân hàng
   * @param {string} clientMessageId - UUID cho header Client-Message-Id
   * @param {object} data - Thông tin tài khoản (company_id, account_holder_name, ...)
   * @returns {Promise<any>}
   */
  createBankAccount: async (bankShortCode: string, clientMessageId: string, data: object) => {
    return apiClient.post(
      API_PATHS.SEPAY.CREATE_BANK_ACCOUNT_ASYNC.replace('{bankShortCode}', bankShortCode),
      data,
      {
        headers: {
          'Client-Message-Id': clientMessageId,
          'Content-Type': 'application/json',
        },
      }
    );
  },

  /**
   * Xác thực OTP liên kết API Sepay
   * @param {string} bankShortCode - Mã ngân hàng
   * @param {string} requestId - Request-Id header
   * @param {string} clientMessageId - Client-Message-Id header
   * @param {object} data - { otp }
   * @returns {Promise<any>}
   */
  confirmApiConnection: async (bankShortCode: string, requestId: string, clientMessageId: string, data: object) => {
    return apiClient.post(
      API_PATHS.SEPAY.CONFIRM_BANK_ACCOUNT_ASYNC.replace('{bankShortCode}', bankShortCode),
      data,
      {
        headers: {
          'Request-Id': requestId,
          'Client-Message-Id': clientMessageId,
          'Content-Type': 'application/json',
        },
      }
    );
  },

  /**
   * Gửi yêu cầu xoá tài khoản ngân hàng Sepay (gửi OTP)
   * @param {string} bankShortCode - Mã ngân hàng
   * @param {string} bankAccountId - bankAccountId (param)
   * @param {string} clientMessageId - Client-Message-Id header
   * @returns {Promise<any>}
   */
  requestDeleteBankAccount: async (bankShortCode: string, bankAccountId: string, clientMessageId: string) => {
    return apiClient.post(
      `${API_PATHS.SEPAY.REQUEST_DELETE_BANK_ACCOUNT.replace('{bankShortCode}', bankShortCode)}/${bankAccountId}`,
      {},
      {
        headers: {
          'Client-Message-Id': clientMessageId,
          'Content-Type': 'application/json',
        },
      }
    );
  },

  /**
   * Xác nhận xoá tài khoản ngân hàng Sepay (OTP)
   * @param {string} bankShortCode - Mã ngân hàng
   * @param {string} clientMessageId - Client-Message-Id header
   * @param {string} requestId - Request-Id header
   * @param {object} data - { otp }
   * @returns {Promise<any>}
   */
  confirmDeleteBankAccount: async (bankShortCode: string, clientMessageId: string, requestId: string, data: { otp: string }) => {
    return apiClient.post(
      API_PATHS.SEPAY.CONFIRM_DELETE_BANK_ACCOUNT.replace('{bankShortCode}', bankShortCode),
      data,
      {
        headers: {
          'Client-Message-Id': clientMessageId,
          'Request-Id': requestId,
          'Content-Type': 'application/json',
        },
      }
    );
  },

  /**
   * Lấy thông tin tài khoản ngân hàng Sepay đã liên kết
   * @param {string} bankShortCode - Mã ngân hàng
   * @param {string} clientMessageId - Client-Message-Id header
   * @returns {Promise<any>}
   */
  getBankAccount: async (bankShortCode: string, clientMessageId: string) => {
    return apiClient.get(
      API_PATHS.SEPAY.GET_BANK_ACCOUNT.replace('{bankShortCode}', bankShortCode),
      {
        headers: {
          'Client-Message-Id': clientMessageId,
        },
      }
    );
  },

  /**
   * Xoá vĩnh viễn tài khoản ngân hàng Sepay (không cần OTP, chuẩn REST: /forceDelete/:bankAccountId)
   * @param {string} bankShortCode - Mã ngân hàng
   * @param {string} bankAccountId - bankAccountId (param)
   * @param {string} clientMessageId - Client-Message-Id header
   * @param {string} requestId - Request-Id header
   * @returns {Promise<any>}
   */
  forceDeleteBankAccount: async (bankShortCode: string, bankAccountId: string, clientMessageId: string, requestId: string) => {
    return apiClient.post(
      `${API_PATHS.SEPAY.FORCE_DELETE_BANK_ACCOUNT.replace('{bankShortCode}', bankShortCode)}/${bankAccountId}`,
      {},
      {
        headers: {
          'Request-Id': requestId,
          'Client-Message-Id': clientMessageId,
          'Content-Type': 'application/json',
        },
      }
    );
  },

  /**
   * Gửi lại OTP liên kết API Sepay (chuẩn REST: /requestApiConnection/:bankAccountId, header Request-Id)
   * @param {string} bankShortCode - Mã ngân hàng
   * @param {string} bankAccountId - bankAccountId (param)
   * @param {string} clientMessageId - Client-Message-Id header
   * @param {string} requestId - Request-Id header
   * @returns {Promise<any>}
   */
  resendOtp: async (bankShortCode: string, bankAccountId: string, clientMessageId: string, requestId: string) => {
    return apiClient.post(
      `${API_PATHS.SEPAY.RESENT_OTP.replace('{bankShortCode}', bankShortCode)}/${bankAccountId}`,
      {},
      {
        headers: {
          'Request-Id': requestId,
          'Client-Message-Id': clientMessageId,
          'Content-Type': 'application/json',
        },
      }
    );
  },
};