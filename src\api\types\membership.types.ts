export enum ExchangeHistoryType {
  ALL = "Tất cả",
  Shop = "Đặt hàng",
  Introduce = "Giớ<PERSON> thiệu",
  Adjust = "Điều chỉnh",
  Register = "Đ<PERSON>ng ký",
  Discount = "Giảm giá",
  Redeem = "<PERSON>uy đổi",
  PointRefund = "<PERSON>àn điểm",
}

export interface GetExchangeHistoryParams {
  skip: number;
  limit: number;
  shopId: string;
  search?: string;
  type: ExchangeHistoryType;
  fromDate: Date;
  toDate: Date;
}

export enum TypeRewardCondition {
  PaymentSuccess = 0, // Cộng điểm khi thanh toán thành công
  DeliverySuccess = 1, // Cộng điểm khi giao hàng thành công
}

export enum TypePointCalculation {
  TotalOrderValue = 0,
  TotalExcludingShipping = 1,
}

// Cập nhật lại interface OrderJson
interface OrderJson {
  status: boolean;
  isPointAdd: TypeRewardCondition;
  isScore: TypePointCalculation;
}

interface RegisterJson {
  rule: number;
  status: boolean;
}

interface IsPurchase {
  isRequire: boolean;
  minSpent: number;
}

interface ShareJson {
  rule: number;
  maxTurns: number;
  status: boolean;
  isPurchase: IsPurchase;
}

export interface EarnPointConfig {
  orderJson: OrderJson;
  registerJson: RegisterJson;
  shareJson: ShareJson;
}

interface IsScore {
  isValue: boolean;
  value: number;
  optionDate: number;
}

interface DiscountJson {
  maxPercent: number;
  isScore: IsScore;
  status: boolean;
  rule: number;
}

interface VoucherJson {
  status: boolean;
}

export interface ExchangePointsConfig {
  discountJson: DiscountJson;
  voucherJson: VoucherJson;
}

// Interface cho response của API
export interface MembershipConfig {
  id: string;
  configMembershipId: string | null;
  shopId: string;
  status: boolean;
  created: string;
  updated: string;
  earnPoint: string;
  exchangePoints: string;
  validUntil: string;
}

// Default
export enum TypeValidUntil {
  Unlimited = "Unlimited",
  Days180 = "Days180",
  Year1 = "Year1",
  Year2 = "Year2",
}

export const DEFAULT_EARN_POINT_CONFIG: EarnPointConfig = {
  orderJson: {
    status: true,
    isPointAdd: TypeRewardCondition.PaymentSuccess,
    isScore: TypePointCalculation.TotalOrderValue,
  },
  registerJson: {
    rule: 0,
    status: true,
  },
  shareJson: {
    rule: 0,
    maxTurns: 0,
    status: true,
    isPurchase: {
      isRequire: false,
      minSpent: 0,
    },
  },
};

export const DEFAULT_EXCHANGE_POINTS_CONFIG: ExchangePointsConfig = {
  discountJson: {
    maxPercent: 100,
    isScore: {
      isValue: true,
      value: 0,
      optionDate: 0,
    },
    status: true,
    rule: 1000,
  },
  voucherJson: {
    status: true,
  },
};

export const TYPE_VALID_UNTIL_DISPLAY: Record<TypeValidUntil, string> = {
  [TypeValidUntil.Unlimited]: "Không giới hạn",
  [TypeValidUntil.Days180]: "180 ngày",
  [TypeValidUntil.Year1]: "1 năm",
  [TypeValidUntil.Year2]: "2 năm",
};

// Helper function để lấy tên hiển thị
export const getTypeValidUntilDisplay = (type: TypeValidUntil): string => {
  return TYPE_VALID_UNTIL_DISPLAY[type] || "Không giới hạn";
};

export enum OptionDate {
  Day = 1,
  Week = 2,
  Month = 3,
}

export const TYPE_OPTION_DATE_DISPLAY: Record<OptionDate, string> = {
  [OptionDate.Day]: "Ngày",
  [OptionDate.Week]: "Tuần",
  [OptionDate.Month]: "Tháng",
};

export const mapNumberToTypeValidUntil = (value: number): OptionDate => {
  return value as OptionDate;
};

export const mapTypeValidUntilToNumber = (type: OptionDate): number => {
  return type as number;
};

export const mapPercentValue = (value: number): string => `${value}%`;
export const mapPercentToNumber = (value: string): number => Number(value.replace("%", ""));

export const formatPrice = (price) => {
  if (!price) return "0";
  return new Intl.NumberFormat("en-US").format(price);
};
