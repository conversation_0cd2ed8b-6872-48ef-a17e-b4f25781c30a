import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  Stack,
  Grid,
  TablePagination,
  Avatar,
  Tooltip,
  IconButton,
} from "@mui/material";
import { formatPrice } from "src/api/types/membership.types";
import AddRank from "./AddRank";
import SetupPoint from "./SetUpPoint";
import { useMembershipLevel } from "@/src/api/hooks/membership-level/use-membership-level";
import { useStoreId } from "@/src/hooks/use-store-id";
import _ from "lodash";
import { useSnackbar } from "notistack";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { usePathname } from "next/navigation";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import TruncatedText from "@/src/components/truncated-text/truncated-text";
const OverviewTab = ({ tabIndex, setTabIndex }) => {
  const pathname = usePathname();
  const [selected, setSelected] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [showAddRank, setShowAddRank] = useState(false);
  const [showSetupPoint, setShowSetupPoint] = useState(false);
  const [listMembershipLevel, setListMembershipLevel] = useState([]);
  const { getMembershipLevel, deleteMembershipLevel, summaryMembershipLevel } =
    useMembershipLevel();
  const [totalCount, setTotalCount] = useState(0);
  const StoreId = useStoreId();
  const [editData, setEditData] = useState(null);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [deletingId, setDeletingId] = useState(null);
  const { enqueueSnackbar } = useSnackbar();
  const [summaryData, setSummaryData] = useState({
    totalPoints: 0,
    totalUsers: 0,
  });
  // State lưu lỗi ảnh theo từng levelId
  const [imageErrors, setImageErrors] = useState({});
  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  const handleImageError = (levelId) => {
    setImageErrors((prev) => ({ ...prev, [levelId]: true }));
  };
  const fetchMembershipLevelList = async (currentPage, pageSize, shopId) => {
    const skip = currentPage * pageSize;
    const limit = pageSize;
    const response = await getMembershipLevel(skip, limit, shopId);

    if (response?.data) {
      setTotalCount(response.data.total || 0);

      const listFetchMembershipLevel = response.data.data;
      setListMembershipLevel(listFetchMembershipLevel);
    }
  };

  const fetchSummaryData = async (shopId) => {
    try {
      const sumData = { shopId: shopId };
      const response = await summaryMembershipLevel(sumData);
      if (response?.data) {
        setSummaryData({
          totalPoints: response.data.totalPoints || 0,
          totalUsers: response.data.userCount || 0,
        });
      }
    } catch (error) {
      console.error("Error fetching summary:", error);
    }
  };

  const handleDeleteClick = (levelId) => {
    setDeletingId(levelId);
    setOpenDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setDeletingId(null);
  };

  const handleConfirmDelete = async () => {
    try {
      const deleteData = { levelId: deletingId };
      await deleteMembershipLevel(deleteData);
      enqueueSnackbar("Xóa hạng thành viên thành công", { variant: "success" });
      handleRefreshData();
    } catch (error) {
      console.error("Error deleting membership level:", error);
    } finally {
      handleCloseDeleteDialog();
    }
  };

  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      setSelected(listMembershipLevel.map((row) => row.levelId));
    } else {
      setSelected([]);
    }
  };

  const handleCheckboxClick = (id) => {
    setSelected((prevSelected) =>
      prevSelected.includes(id) ? prevSelected.filter((item) => item !== id) : [...prevSelected, id]
    );
  };

  useEffect(() => {
    if (!StoreId) return;
    debouncedFetchMembershipLevelList(page, rowsPerPage, StoreId);
    fetchSummaryData(StoreId);
    return () => {
      debouncedFetchMembershipLevelList.cancel(); // Cancel debounce if component unmounts or searchText changes
    };
  }, [StoreId, page, rowsPerPage]);

  const debouncedFetchMembershipLevelList = useCallback(
    _.debounce((currentPage, pageSize, shopId) => {
      fetchMembershipLevelList(currentPage, pageSize, shopId);
    }, 400), // Delay 1s
    []
  );

  const isAllSelected = selected.length === listMembershipLevel.length;

  const handleChangePage = (_event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleEdit = (row) => {
    setEditData(row);
    setShowSetupPoint(true);
  };

  const handleRefreshData = useCallback(() => {
    if (!StoreId) return;
    debouncedFetchMembershipLevelList(page, rowsPerPage, StoreId);
  }, [StoreId, page, rowsPerPage]);

  if (showAddRank) {
    return <AddRank goBack={() => setShowAddRank(false)} onSuccess={handleRefreshData} />;
  }
  if (showSetupPoint) {
    return (
      <SetupPoint
        goBack={() => {
          setShowSetupPoint(false);
          setEditData(null);
        }}
        editData={editData}
        onSuccess={handleRefreshData}
      />
    );
  }

  return (
    <Box sx={{ marginBottom: "100px" }}>
      <Paper sx={{ p: 3, mb: 3, boxShadow: 0, border: "1px solid #D5D5D5" }}>
        <Typography sx={{ color: "#000", fontSize: "20px", fontWeight: "700", mb: 2 }}>
          Tổng quan dữ liệu
        </Typography>

        <Grid container spacing={3} alignItems="stretch">
          <Grid item xs={6} sm={6}>
            <Box
              sx={{
                height: "100%",
                display: "flex",
                flexDirection: "column",
                justifyContent: "space-between",
              }}
            >
              <Typography sx={{ fontSize: "16px", fontWeight: "400", color: "#000" }}>
                Tổng số điểm của người dùng
              </Typography>
              <Typography
                sx={{
                  fontSize: "22px",
                  fontWeight: "600",
                  mt: 1,
                  cursor: "pointer",
                  color: "primary.main",
                  "&:hover": {
                    color: "primary.dark",
                    textDecoration: "underline",
                  },
                }}
                onClick={() => setTabIndex(2)}
              >
                {summaryData.totalPoints.toLocaleString()}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6} sm={6}>
            <Box
              sx={{
                height: "100%",
                display: "flex",
                flexDirection: "column",
                justifyContent: "space-between",
              }}
            >
              <Typography sx={{ fontSize: "16px", fontWeight: "400", color: "#000" }}>
                Tổng số người dùng có tích điểm
              </Typography>
              <Typography
                sx={{
                  fontSize: "22px",
                  fontWeight: "600",
                  mt: 1,
                  cursor: "pointer",
                  color: "primary.main",
                  "&:hover": {
                    color: "primary.dark",
                    textDecoration: "underline",
                  },
                }}
                onClick={() => setTabIndex(1)}
              >
                {summaryData.totalUsers.toLocaleString()}
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      <Stack direction={"row"} gap={2} alignItems="center">
        <Typography sx={{ fontSize: "20px", fontWeight: "bold", mb: 2 }}>Xếp hạng</Typography>
        <Tooltip
          title={!isGranted(pathname, PERMISSION_TYPE_ENUM.Add) ? "Bạn không có quyền thêm" : ""}
        >
          <span>
            <Button
              variant="contained"
              sx={{
                mb: 2,
                color: "#fff",
                padding: "8px 14px",
                backgroundColor: "#2654FE",
                borderRadius: "5px",
                "&:hover": { backgroundColor: "#003ea5" },
              }}
              disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Add)}
              onClick={() => setShowAddRank(true)}
            >
              Thêm mới
            </Button>
          </span>
        </Tooltip>
      </Stack>

      <TableContainer sx={{ overflowX: "auto" }} component={Paper}>
        <Table sx={{ width: "max-content", minWidth: "100%" }}>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox color="primary" checked={isAllSelected} onChange={handleSelectAllClick} />
              </TableCell>
              <TableCell sx={{ width: 5 }}>STT</TableCell>
              <TableCell sx={{ width: 100 }}>Logo hạng</TableCell>
              <TableCell sx={{ width: 500 }}>Tên hạng</TableCell>
              <TableCell sx={{ width: 300 }}>Tổng chi tiêu tối thiểu (VNĐ)</TableCell>
              <TableCell sx={{ width: 300 }}>Tỉ lệ tích điểm (%)</TableCell>
              <TableCell
                align="center"
                sx={{
                  width: 150,
                  position: "sticky",
                  right: 0,
                  background: "#fff",
                  zIndex: 2,
                }}
              >
                Quản lý
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {listMembershipLevel.map((row, index) => {
              // Kiểm tra nếu `row.image` là null hoặc `row.image.link` không tồn tại
              const isImageValid = row.image?.link && !imageErrors[row.levelId];
              return (
                <TableRow key={row.levelId}>
                  <TableCell padding="checkbox">
                    <Checkbox
                      color="primary"
                      checked={selected.includes(row.levelId)}
                      onChange={() => handleCheckboxClick(row.levelId)}
                    />
                  </TableCell>
                  <TableCell>{page * rowsPerPage + index + 1}</TableCell>
                  <TableCell>
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                      <Avatar
                        src={isImageValid ? row.image.link : undefined} // Nếu ảnh hợp lệ, dùng link; nếu không, bỏ src
                        alt={row.levelName}
                        onError={() => handleImageError(row.levelId)} // Nếu ảnh lỗi, cập nhật state
                        sx={{
                          width: 40,
                          height: 40,
                          border: "1px solid #eee",
                          backgroundColor: "#f5f5f5",
                          "& img": {
                            objectFit: "cover",
                            p: "1px",
                          },
                        }}
                      >
                        {!isImageValid && row.levelName?.charAt(0)}
                        {/* Hiển thị chữ cái đầu nếu ảnh lỗi hoặc không có ảnh */}
                      </Avatar>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <TruncatedText text={row.levelName} typographyProps={{ width: 500 }} />
                  </TableCell>
                  <TableCell>{formatPrice(row.spendingThreshold)}</TableCell>
                  <TableCell>{row.pointRate}%</TableCell>
                  <TableCell
                    align="center"
                    sx={{
                      position: "sticky",
                      right: 0,
                      background: "#fff",
                      zIndex: 2,
                    }}
                  >
                    {isGranted(pathname, PERMISSION_TYPE_ENUM.Edit) ? (
                      <IconButton
                        onClick={() => handleEdit(row)}
                        size="small"
                        sx={{ color: "primary.main" }}
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                    ) : (
                      <Tooltip title="Bạn không có quyền sửa">
                        <span>
                          <IconButton size="small" sx={{ color: "primary.main" }} disabled={true}>
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </span>
                      </Tooltip>
                    )}
                    {isGranted(pathname, PERMISSION_TYPE_ENUM.Delete) ? (
                      <IconButton
                        onClick={() => handleDeleteClick(row.levelId)}
                        size="small"
                        sx={{ color: "error.main" }}
                        disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    ) : (
                      <Tooltip title="Bạn không có quyền xoá">
                        <span>
                          <IconButton
                            size="small"
                            sx={{ color: "error.main" }}
                            disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </span>
                      </Tooltip>
                    )}
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>

        {/* Phân trang */}
        <TablePagination
          rowsPerPageOptions={rowPerPageOptionsDefault}
          component="div"
          count={totalCount}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Số dòng mỗi trang"
        />
      </TableContainer>

      <Dialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        aria-labelledby="alert-dialog-title"
      >
        <DialogTitle id="alert-dialog-title">Xác nhận xóa</DialogTitle>
        <DialogContent>
          <Typography>Bạn có chắc chắn muốn xóa hạng thành viên này không?</Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleCloseDeleteDialog}
            sx={{
              color: "#797A7C",
              border: "1px solid #797A7C",
            }}
          >
            Hủy bỏ
          </Button>
          <Button onClick={handleConfirmDelete} variant="contained" color="error" autoFocus>
            Xác nhận
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default OverviewTab;
