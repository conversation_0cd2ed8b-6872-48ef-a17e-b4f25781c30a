import {
    Paper,
    Button,
    Box,
    Typography,
} from "@mui/material"
import { Inventory2 } from "@mui/icons-material"
import { Grid } from "@mui/system"

interface Order {
    id: string
    table: string
    customerName: string
    phone: string
    status: string
    notificationCount: number
}

const orders: Order[] = [
    {
        id: "ABC123",
        table: "Bàn 1",
        customerName: "Sơn dz 123",
        phone: "0349587999",
        status: "Đang làm",
        notificationCount: 1,
    },
    {
        id: "ABC123",
        table: "Bàn 1",
        customerName: "Le Van Son",
        phone: "0349587999",
        status: "Đang làm",
        notificationCount: 1,
    },
    {
        id: "ABC123",
        table: "Bàn 1",
        customerName: "Le Van Luyen",
        phone: "0349587999",
        status: "Đang làm",
        notificationCount: 1,
    },
    {
        id: "ABC123",
        table: "Bàn 1",
        customerName: "<PERSON><PERSON><PERSON>",
        phone: "0349587999",
        status: "Đang làm",
        notificationCount: 2,
    },
]

const renderOrderIcon = (order: Order) => (
    <Box
        sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            width: 80,
            height: "100%",
            borderRadius: 1,
            bgcolor: order.notificationCount === 1 ? "#FF9800" : "#FFA726",
            position: 'absolute',
            left: -30,
            top: 0,
            bottom: 0,
        }}
    >
        <Inventory2 sx={{ color: "#fff", fontSize: 24 }} />
    </Box>
)

export default function OrderList() {
    return (
        <Box >
            {orders.map((order, index) => (
                <Paper key={index} sx={{ p: 2, mb: 2, position: 'relative', border: '1px solid #D3D3D3', borderRadius: 1, boxShadow: 'none' }}>
                    {renderOrderIcon(order)}
                    <Grid container alignItems="center" spacing={2} sx={{ pl: 6 }}>
                        <Grid size={{ xs: 2 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Typography fontWeight="bold">
                                    {order.id}
                                </Typography>
                                <Typography variant="body1" sx={{ ml: 5 }}>
                                    {order.table}
                                </Typography>
                            </Box>
                        </Grid>

                        <Grid size={{ xs: 3 }} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                            <Typography fontWeight="bold">{order.customerName}</Typography>
                            <Typography textAlign="center">
                                {order.phone}
                            </Typography>
                        </Grid>

                        <Grid size={{ xs: 3 }}>
                            <Typography variant="body1" color="text.secondary">
                                {order.status}
                            </Typography>
                        </Grid>

                        <Grid size={{ xs: 2 }}>
                            <Button
                                variant="contained"
                                sx={{
                                    width: 160,
                                    height: 50,
                                    bgcolor: order.notificationCount === 1 ? "#DBEF28" : "#38CB76",
                                    color: "#454545",
                                    fontWeight: 'bold',
                                    whiteSpace: 'nowrap',
                                    boxShadow: 'none',
                                }}
                            >
                                Thông báo lần {order.notificationCount}
                            </Button>
                        </Grid>

                        <Grid size={{ xs: 2 }}>
                            <Button
                                variant="contained"
                                sx={{
                                    width: 160,
                                    height: 50,
                                    bgcolor: "#D1D1D1",
                                    color: "#2E2E2E",
                                    fontWeight: 'bold',
                                    boxShadow: 'none',
                                }}
                            >
                                Hoàn thành
                            </Button>
                        </Grid>
                    </Grid>
                </Paper>
            ))}
        </Box>
    )
}