import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { authService } from "../../api/services/auth/auth.service";
import { ErrorHandlerService } from "../../api/services/error-handler.service";
import { ChangePasswordRequest } from "../../api/types/auth.types";

interface ProfileState {
  profile: any | null;
  loading: boolean;
  error: string | null;
}

const initialState: ProfileState = {
  profile: null,
  loading: false,
  error: null,
};

export const getProfile = createAsyncThunk("profile/getProfile", async (_, { rejectWithValue }) => {
  try {
    const response = await authService.getProfile();
    return response.data.profile;
  } catch (err: any) {
    const errorResponse = ErrorHandlerService.handle(err);
    return rejectWithValue(errorResponse.detail);
  }
});

export const updateProfile = createAsyncThunk(
  "profile/updateProfile",
  async (data: any, { rejectWithValue }) => {
    try {
      const response = await authService.updateProfile(data);
      return response.data.profile;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err);
      return rejectWithValue(errorResponse.detail);
    }
  }
);

export const changePassword = createAsyncThunk(
  "profile/changePassword",
  async (data: ChangePasswordRequest, { rejectWithValue }) => {
    try {
      const response = await authService.changePassword(data);
      return response.data;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err);
      return rejectWithValue(errorResponse.detail);
    }
  }
);

export const registerDevice = createAsyncThunk(
  "profile/registerDevice",
  async (token: string, { rejectWithValue }) => {
    try {
      const response = await authService.registerDevice(token);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err);
      return rejectWithValue(errorResponse.detail);
    }
  }
);

export const revokeDevice = createAsyncThunk(
  "profile/revokeDevice",
  async (token: string, { rejectWithValue }) => {
    try {
      const response = await authService.revokeDevice(token);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err);
      return rejectWithValue(errorResponse.detail);
    }
  }
);

const profileSlice = createSlice({
  name: "profile",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Get Profile
      .addCase(getProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProfile.fulfilled, (state, action: PayloadAction<any>) => {
        state.loading = false;
        state.profile = action.payload;
      })
      .addCase(getProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Update Profile
      .addCase(updateProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateProfile.fulfilled, (state, action: PayloadAction<any>) => {
        state.loading = false;
        state.profile = action.payload;
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      .addCase(changePassword.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(changePassword.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(changePassword.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      .addCase(registerDevice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(registerDevice.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(registerDevice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(revokeDevice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(revokeDevice.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(revokeDevice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export default profileSlice.reducer;
