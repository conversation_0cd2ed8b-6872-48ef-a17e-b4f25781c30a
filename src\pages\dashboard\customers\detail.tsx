import React, { useEffect, useState } from "react";
import DashboardLayout from "@/src/layouts/dashboard";
import { useRouter } from "next/router";
import PageTitleWithBackBtn from "@/src/layouts/dashboard/page-title-with-back-btn/page-title";
import { Box } from "@mui/system";
import EditCustomerForm from "@/src/components/customers/EditCustomerForm";
import { useUser } from "@/src/api/hooks/user/use-user";
import { getProvinces } from "@/src/slices/addressSlice";
import { useDispatch } from "react-redux";
import { fetchTags } from "@/src/slices/listTagSlice";
import { AppDispatch } from "@/src/store";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import { tokens } from "@/src/locales/tokens";
import { useTranslation } from "react-i18next";
import { paths } from "@/src/paths";
import { Padding } from "@/src/styles/CommonStyle";

export default function CustomerDetail() {
  const { t } = useTranslation();
  const { detailUser, loading, error, updateUser, deleteUsers } = useUser();
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();
  // Changed: Get id from query parameter instead of dynamic route
  const { id } = router.query;
  const [user, setUser] = useState(null);
  const snackbar = useSnackbar();
  
  const fetchUser = async () => {
    try {
      const response = await detailUser(id);
      if (response && response.data) {
        const { data } = response;
        setUser(data);
      } else {
        await router.push(paths.customers.list);
      }
    } catch (error) {
      console.log("🚀 ~ fetchUser ~ error:", error);
    }
  };

  useEffect(() => {
    if (!id) return; // Đảm bảo `id` đã sẵn sàng
    fetchUser();
    dispatch(getProvinces());
    dispatch(fetchTags());
  }, [id]);

  const handleUpdateUser = async (userData) => {
    try {
      const response = await updateUser(userData);
      if (response && response.data) {
        snackbar.success(t(tokens.customers.edit.success));
        await fetchUser(); // Refresh user data
      }
    } catch (error) {
      snackbar.error(t(tokens.customers.edit.error));
    }
  };

  const handleDeleteUser = async (userId) => {
    try {
      const response = await deleteUsers([userId]);
      if (response) {
        snackbar.success(t(tokens.customers.delete.success));
        router.push(paths.customers.list);
      }
    } catch (error) {
      snackbar.error(t(tokens.customers.delete.error));
    }
  };

  return (
    <DashboardLayout>
      <Box sx={Padding}>
        <PageTitleWithBackBtn
          title={t(tokens.customers.detail.title)}
          backPath={paths.customers.list}
        />
        {user && (
          <EditCustomerForm
            user={user}
            loading={loading}
            onUpdateUser={handleUpdateUser}
            onDeleteUser={handleDeleteUser}
          />
        )}
      </Box>
    </DashboardLayout>
  );
}
