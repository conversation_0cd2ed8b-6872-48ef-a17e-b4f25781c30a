import { Box, Card, Checkbox, Typography, TextField, Chip, Button } from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { useState } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import { useRouter } from 'next/router';
import { paths } from '@/src/paths';
import Grid from '@mui/system/Grid';

const notificationTitles = [
  {
    key: 'welcome',
    title: 'Chào mừng khách hàng mới',
    message: 'Chào mừng [Tên khách hàng] đến với [Tên cửa hàng]! 🎉 Đặc quyền dành riêng cho bạn: [Phần trăm giảm giá] cho [Tên sản phẩm/danh mục]. Mua ngay tại: [<PERSON> sản phẩm]. Cảm ơn bạn đã chọn chúng tôi! ❤️',
    params: ['[Thời gian áp dụng]:', '[Mã ưu đãi]', '[hotline]', '[điểm thưởng]', '[Quà tặng kèm]']
  },
  {
    key: 'referralPoints',
    title: 'Thông báo cộng điểm giới thiệu',
    message: '🎉 Chúc mừng [Tên khách hàng]! Bạn vừa được cộng [Số điểm thưởng] điểm vào tài khoản nhờ giới thiệu thành công [Tên khách hàng được giới thiệu]. Điểm thưởng có thể sử dụng để [Lợi ích từ điểm thưởng]. Xem chi tiết tại: [Link tài khoản]',
    params: ['[Thời gian hiệu lực]', '[Tên chương trình]', '[hotline]']
  },
  {
    key: 'upgradePoints',
    title: 'Thông báo nâng hạng tích điểm',
    message: '🎉 Xin chúc mừng [Tên khách hàng]! Tài khoản của bạn đã được nâng lên hạng [Hạng mới] nhờ tích lũy [Số điểm] điểm. Tận hưởng ngay các ưu đãi độc quyền dành cho thành viên [Hạng mới]. Xem chi tiết quyền lợi tại: [Link quyền lợi]',
    params: ['[Thời gian hiệu lực]', '[Hotline]']
  },
  {
    key: 'reminderPoints',
    title: 'Thông báo nhắc nhở sử dụng điểm',
    message: '🎁 Đừng bỏ lỡ, [Tên khách hàng]! Bạn còn [Số điểm] điểm thưởng trong tài khoản, sẽ hết hạn vào [Ngày hết hạn]. Đổi ngay điểm để nhận ưu đãi hoặc quà tặng hấp dẫn tại: [Link đổi điểm]',
    params: ['[Tên chương trình tích điểm]', '[Ưu đã]']
  },
  {
    key: 'periodicThanks',
    title: 'Cảm ơn khách hàng định kỳ',
    message: '💖 [Tên khách hàng] thân mến, cảm ơn bạn đã đồng hành cùng [Tên cửa hàng] trong suốt [Thời gian]. Sự ủng hộ của bạn là động lực để chúng tôi không ngừng cải thiện và mang đến trải nghiệm tốt hơn. Nhận ngay món quà tri ân đặc biệt tại: [Link quà tặng]',
    params: ['[Thời hạn nhận quà]']
  },
  {
    key: 'productReview',
    title: 'Đánh giá sản phẩm',
    message: '🛍️ [Tên khách hàng] ơi, bạn thấy [Tên sản phẩm] như thế nào? Chia sẻ cảm nhận của bạn để giúp chúng tôi phục vụ tốt hơn! Đánh giá ngay tại: [Link đánh giá]. Cảm ơn bạn đã đồng hành cùng [Tên cửa hàng]! 💬',
    params: ['[Ưu đãi]', '[hotline]']
  },
  {
    key: 'orderReview',
    title: 'Đánh giá đơn hàng',
    message: '🛒 [Tên khách hàng] thân mến, bạn hài lòng với đơn hàng [Mã đơn hàng] đã đặt tại [Tên cửa hàng] chứ? Hãy dành chút thời gian để đánh giá và giúp chúng tôi phục vụ bạn tốt hơn! Đánh giá ngay tại: [Link đánh giá]. Cảm ơn bạn đã tin tưởng và ủng hộ! 🌟',
    params: ['[Ngày giao hàng]', '[Ưu đã]']
  },
  {
    key: 'productGuide',
    title: 'Hướng dẫn sử dụng sản phẩm',
    message: '📖 [Tên khách hàng] thân mến, cảm ơn bạn đã chọn [Tên sản phẩm]! Để sử dụng sản phẩm hiệu quả nhất, hãy tham khảo hướng dẫn chi tiết tại: [Link hướng dẫn]. Nếu cần hỗ trợ thêm, đừng ngần ngại liên hệ với chúng tôi qua [Số hotline]. Chúc bạn trải nghiệm tuyệt vời! 🌟',
    params: ['[Số đơn hàng]', '[Số đơn hàng]', '[Số đơn hàng]']
  },
  {
    key: 'maintenanceReminder',
    title: 'Nhắc lịch bảo hành/bảo dưỡng',
    message: '🔧 [Tên khách hàng] thân mến, sản phẩm [Tên sản phẩm] của bạn đến lịch [bảo hành/bảo dưỡng] vào ngày [Ngày bảo hành/bảo dưỡng]. Hãy đặt lịch ngay để đảm bảo sản phẩm luôn hoạt động tốt nhất. Đặt lịch nhanh tại: [Link đặt lịch] hoặc liên hệ [Số hotline]. Chúng tôi luôn sẵn sàng hỗ trợ bạn! 😊',
    params: ['[hotline]', '[Ưu đã]', '[Danh sách cửa hàng]']
  },
  {
    key: 'repurchaseReminder',
    title: 'Nhắc nhở tái mua hàng',
    message: '🛒 [Tên khách hàng] ơi, đã đến lúc bổ sung [Tên sản phẩm] để không bỏ lỡ trải nghiệm tuyệt vời! Đặt lại ngay hôm nay và nhận ưu đãi [Ưu đãi/giảm giá] tại: [Link đặt hàng]. Chúng tôi luôn sẵn sàng phục vụ bạn! 🌟',
    params: ['[ưu đãi]']
  },
  {
    key: 'birthday',
    title: 'Chúc mừng sinh nhật',
    message: 'Chúc mừng sinh nhật [Tên khách hàng]! 🎉 Nhân dịp đặc biệt này, [Tên cửa hàng] xin gửi tặng bạn ưu đãi độc quyền: Giảm [Ưu đãi]% cho sản phẩm [Tên sản phẩm]. 🎂 Mua ngay tại: [Link sản phẩm] 🎁',
    params: ['Mã giảm giá', 'Thời hạn ưu đãi']
  },
  {
    key: 'newZaloNotification',
    title: 'Thêm thông báo Zalo mới',
    message: '',
    params: []
  }
];

const cardStyles = {
  padding: 2,
  bgcolor: '#f5f5f5',
  borderRadius: 1,
  boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
  cursor: 'pointer',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  height: '100%',
  '&:hover': {
    boxShadow: '0px 6px 16px rgba(0, 0, 0, 0.2)'
  },
  minHeight: '150px'
};

const checkboxStyles = {
  color: '#e0e0e0',
  '&.Mui-checked': {
    color: '#4caf50'
  },
  '& .MuiSvgIcon-root': {
    fontSize: 20
  },
  padding: 0,
  marginLeft: 1
};

const NotificationCard = ({ title, message, params }) => {
  const router = useRouter();

  const handleClick = () => {
    router.push({
      pathname: paths.settings.management,
      query: { title, message, params: JSON.stringify(params) }
    });
  };

  return (
    <Card sx={cardStyles} onClick={handleClick}>
      <Typography variant="subtitle1" sx={{ marginBottom: 2, fontWeight: 500 }}>
        {title}
      </Typography>
      {['Thông báo hệ thống', 'Zalo'].map((label, i) => (
        <Box
          key={i}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: i === 0 ? 1 : 0,
            width: '100%'
          }}
        >
          <Typography variant="body2">{label}</Typography>
          <Checkbox checked checkedIcon={<CheckCircleIcon />} sx={checkboxStyles} />
        </Box>
      ))}
    </Card>
  );
};

const AddNewLinkCard = () => {
  const router = useRouter();

  const handleClick = () => {
    router.push(paths.settings.managementAdd);
  };

  return (
    <Card sx={cardStyles} onClick={handleClick}>
      <Typography variant="subtitle1" sx={{ mt: 6, fontWeight: 500, textAlign: 'center', color: 'primary.main' }}>
        Thêm thông báo Zalo mới
      </Typography>
    </Card>
  );
};

export default function NotificationCustomerCare() {
  return (
    <Box sx={{ padding: 3 }}>
      <Grid container spacing={2}>
        {notificationTitles.map((notification, index) => (
          <Grid size={{ xs: 12, md: 4 }} key={index}>
            <NotificationCard {...notification} />
          </Grid>
        ))}
        <Grid size={{ xs: 12, md: 4 }}>
          <AddNewLinkCard />
        </Grid>
      </Grid>
    </Box>
  );
}