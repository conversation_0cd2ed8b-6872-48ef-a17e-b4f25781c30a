import React from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  MenuItem,
  Select,
  InputLabel,
  FormControl,
  Typography,
  Divider
} from '@mui/material';
import Grid from '@mui/system/Grid';

const storeNameInput = (
  <Box sx={{ boxShadow: 1, padding: 2, borderRadius: 1 }}>
    <InputLabel htmlFor="store-name">Tên của hàng</InputLabel>
    <TextField id="store-name" placeholder="Syncshop" variant="outlined" fullWidth size="small" />
  </Box>
);

const emailInput = (
  <Box sx={{ boxShadow: 1, padding: 2, borderRadius: 1 }}>
    <InputLabel htmlFor="email">Email liên hệ</InputLabel>
    <TextField id="email" type="email" variant="outlined" fullWidth size="small" />
  </Box>
);

const businessTypeInput = (
  <Box sx={{ boxShadow: 1, padding: 2, borderRadius: 1 }}>
    <InputLabel htmlFor="business-type">Ngành nghề kinh doanh</InputLabel>
    <FormControl variant="outlined" fullWidth size="small">
      <Select id="business-type" defaultValue="">
        <MenuItem value="restaurant">Restaurant</MenuItem>
        <MenuItem value="retail">Retail</MenuItem>
        <MenuItem value="service">Service</MenuItem>
      </Select>
    </FormControl>
  </Box>
);

const storeIdInput = (
  <Box sx={{ boxShadow: 1, padding: 2, borderRadius: 1 }}>
    <InputLabel htmlFor="store-id">ID của hàng</InputLabel>
    <TextField id="store-id" variant="outlined" placeholder="123123123" fullWidth size="small" />
  </Box>
);

const businessNameInput = (
  <Box sx={{ boxShadow: 1, padding: 2, borderRadius: 1 }}>
    <InputLabel htmlFor="business-name">Tên doanh nghiệp</InputLabel>
    <TextField id="business-name" variant="outlined" fullWidth size="small" />
  </Box>
);

const phoneInput = (
  <Box sx={{ boxShadow: 1, padding: 2, borderRadius: 1 }}>
    <InputLabel htmlFor="phone">Số điện thoại</InputLabel>
    <TextField id="phone" type="tel" variant="outlined" fullWidth size="small" />
  </Box>
);

const taxCodeInput = (
  <Box sx={{ boxShadow: 1, padding: 2, borderRadius: 1 }}>
    <InputLabel htmlFor="tax-code">Mã số thuế</InputLabel>
    <TextField id="tax-code" variant="outlined" fullWidth size="small" />
  </Box>
);

const addressInput = (
  <Box sx={{ boxShadow: 1, padding: 2, borderRadius: 1 }}>
    <InputLabel htmlFor="address">Địa chỉ</InputLabel>
    <TextField id="address" variant="outlined" fullWidth size="small" />
  </Box>
);

const wardInput = (
  <Box sx={{ boxShadow: 1, padding: 2, borderRadius: 1 }}>
    <InputLabel htmlFor="ward">Phường/Xã</InputLabel>
    <FormControl variant="outlined" fullWidth size="small">
      <Select id="ward" defaultValue="">
        <MenuItem value="ward1">Phường 1</MenuItem>
        <MenuItem value="ward2">Phường 2</MenuItem>
      </Select>
    </FormControl>
  </Box>
);

const districtInput = (
  <Box sx={{ boxShadow: 1, padding: 2, borderRadius: 1 }}>
    <InputLabel htmlFor="district">Quận/Huyện</InputLabel>
    <FormControl variant="outlined" fullWidth size="small">
      <Select id="district" defaultValue="">
        <MenuItem value="district1">Quận 1</MenuItem>
        <MenuItem value="district2">Quận 2</MenuItem>
      </Select>
    </FormControl>
  </Box>
);

const cityInput = (
  <Box sx={{ boxShadow: 1, padding: 2, borderRadius: 1 }}>
    <InputLabel htmlFor="city">Tỉnh/Thành phố</InputLabel>
    <FormControl variant="outlined" fullWidth size="small">
      <Select id="city" defaultValue="">
        <MenuItem value="hcm">TP. Hồ Chí Minh</MenuItem>
        <MenuItem value="hn">Hà Nội</MenuItem>
        <MenuItem value="dn">Đà Nẵng</MenuItem>
      </Select>
    </FormControl>
  </Box>
);

export default function SettingOverview() {
  return (
    <Box
      sx={{
        padding: 4,
        display: 'flex',
        flexDirection: 'column',
        gap: 4
      }}
    >
      <Grid container spacing={2} sx={{ flex: 1 }}>
        <Grid size={{ xs: 12, md: 4 }}>
          <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', mt: 4 }}>
            <Typography variant="h6">Chi tiết của hàng</Typography>
          </Box>
        </Grid>
        <Grid size={{ xs: 12, md: 8 }}>
          <Card sx={{ height: '100%', boxShadow: 3 }}>
            <CardContent sx={{ display: 'grid', gap: 2, paddingTop: 1 }}>
              {storeNameInput}
              {emailInput}
              {businessTypeInput}
              {storeIdInput}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Divider sx={{ my: 2, borderColor: 'rgba(104, 92, 92, 0.1)', borderWidth: 1 }} />

      <Grid container spacing={2} sx={{ flex: 1 }}>
        <Grid size={{ xs: 12, md: 4 }}>
          <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', mt: 4 }}>
            <Typography variant="h6">Thông tin doanh nghiệp</Typography>
          </Box>
        </Grid>
        <Grid size={{ xs: 12, md: 8 }}>
          <Card sx={{ height: '100%', boxShadow: 3 }}>
            <CardContent sx={{ display: 'grid', gap: 2, paddingTop: 1 }}>
              {businessNameInput}
              {phoneInput}
              {taxCodeInput}
              {addressInput}
              <Grid container spacing={2}>
                <Grid size={{ xs: 6 }}>{wardInput}</Grid>
                <Grid size={{ xs: 6 }}>{districtInput}</Grid>
                <Grid size={{ xs: 12 }}>{cityInput}</Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
