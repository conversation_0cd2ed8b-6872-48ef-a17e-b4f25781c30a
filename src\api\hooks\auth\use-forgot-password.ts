import { useState } from "react";
import { authService } from "../../services/auth/auth.service";
import { ErrorHandlerService } from "../../services/error-handler.service";
import type { ResetPasswordRequest, VerifyOtpRequest } from "../../types/auth.types";

export const useForgotPassword = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleResponse = (response: any) => {
    if (response?.data?.isCheck) {
      return response;
    } else {
      const message = response?.data?.message || "Unknown error";
      setError(message);
    }
  };

  const checkPhoneNumber = async (
    phoneNumber: string,
    options = { showSnackbar: false, logError: true }
  ) => {
    try {
      setLoading(true);
      setError(null);
      const response = await authService.checkPhoneNumber(phoneNumber, options);
      return handleResponse(response);
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: false,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const sendOtp = async (
    phoneNumber: string,
    options = { showSnackbar: false, logError: true }
  ) => {
    try {
      setLoading(true);
      setError(null);
      const response = await authService.sendOtp(phoneNumber, options);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: false,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const verifyOtp = async (
    data: VerifyOtpRequest,
    options = { showSnackbar: false, logError: true }
  ) => {
    try {
      setLoading(true);
      setError(null);
      const response = await authService.verifyOtp(data, options);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: false,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  return { checkPhoneNumber, sendOtp, verifyOtp, loading, error };
};
