import React, { memo, useState, useCallback } from "react";
import { Box, Card, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { FormikProps } from "formik";
import { tokens } from "@/src/locales/tokens";
import { ProductFormValues } from "../../../../../../types/product/form";
import PriceTextField from "@/src/components/price-text-field";

interface PriceProps {
  formik: FormikProps<ProductFormValues>;
}

/**
 * Price Component
 * Handles price inputs for products/services with optimized performance
 */
const Price: React.FC<PriceProps> = memo(({ formik }) => {
  const { t } = useTranslation();

  // Local state for optimizing input performance
  const [localPriceCapital, setLocalPriceCapital] = useState(formik.values.priceCapital);
  const [localPriceReal, setLocalPriceReal] = useState(formik.values.priceReal);
  const [localPrice, setLocalPrice] = useState(formik.values.price);

  const handlePriceCapitalChange = useCallback(
    (value: number) => {
      setLocalPriceCapital(value);
      formik.setFieldValue("priceCapital", value, true);
      formik.setFieldTouched("priceCapital", true, true);
    },
    [formik]
  );

  const handlePriceRealChange = useCallback(
    (value: number) => {
      setLocalPriceReal(value);
      formik.setFieldValue("priceReal", value).then(() => {
        formik.validateField("price");
        formik.validateField("priceReal");
      });
      formik.setFieldTouched("priceReal", true, true);
    },
    [formik]
  );

  const handlePriceChange = useCallback(
    (value: number) => {
      setLocalPrice(value);
      formik.setFieldValue("price", value).then(() => {
        formik.validateField("price");
        formik.validateField("priceReal");
      });
      formik.setFieldTouched("price", true, true);
    },
    [formik]
  );

  const handlePriceCapitalBlur = useCallback(() => {
    formik.setFieldTouched("priceCapital", true, true);
  }, [formik]);

  const handlePriceRealBlur = useCallback(() => {
    formik.setFieldTouched("priceReal", true, true);
  }, [formik]);

  const handlePriceBlur = useCallback(() => {
    formik.setFieldTouched("price", true, true);
  }, [formik]);

  React.useEffect(() => {
    setLocalPriceCapital(formik.values.priceCapital);
  }, [formik.values.priceCapital]);

  React.useEffect(() => {
    setLocalPriceReal(formik.values.priceReal);
  }, [formik.values.priceReal]);

  React.useEffect(() => {
    setLocalPrice(formik.values.price);
  }, [formik.values.price]);

  return (
    <Card sx={{ p: 2.5 }}>
      <Typography variant="subtitle1" sx={{ mb: 2.5, fontWeight: 600 }}>
        {t(tokens.contentManagement.product.variant.table.pricing)}
      </Typography>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
        {/* Price Capital */}

        <Typography variant="body2" sx={{ display: "flex", fontSize: "16px", fontWeight: 600 }}>
          Giá vốn <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
        </Typography>
        <PriceTextField
          fullWidth
          size="small"
          name="priceCapital"
          value={localPriceCapital}
          onChange={handlePriceCapitalChange}
          error={formik.touched.priceCapital && Boolean(formik.errors.priceCapital)}
          helperText={
            formik.touched.priceCapital && formik.errors.priceCapital
              ? String(formik.errors.priceCapital)
              : "" // Thay đổi text mặc định thành chuỗi rỗng
          }
          sx={{
            "& .MuiOutlinedInput-root": {
              backgroundColor: "background.paper",
              height: "45px",
            },
          }}
        />

        {/* Price Real */}
        <Typography
          variant="body2"
          sx={{ display: "flex", fontSize: "16px", fontWeight: 600, mt: 2 }}
        >
          Giá niêm yết <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
        </Typography>
        <PriceTextField
          fullWidth
          size="small"
          name="priceReal"
          value={localPriceReal}
          onChange={handlePriceRealChange}
          error={formik.touched.priceReal && Boolean(formik.errors.priceReal)}
          helperText={
            formik.touched.priceReal && formik.errors.priceReal
              ? String(formik.errors.priceReal)
              : ""
            // "Giá niêm yết phải lớn hơn 0 và lớn hơn hoặc bằng giá bán"
          }
          sx={{
            "& .MuiOutlinedInput-root": {
              backgroundColor: "background.paper",
              height: "45px",
            },
          }}
        />

        {/* Price */}
        <Typography
          variant="body2"
          sx={{ display: "flex", fontSize: "16px", fontWeight: 600, mt: 2 }}
        >
          Giá bán <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
        </Typography>
        <PriceTextField
          fullWidth
          size="small"
          name="price"
          value={localPrice}
          onChange={handlePriceChange}
          error={formik.touched.price && Boolean(formik.errors.price)}
          helperText={
            formik.touched.price && formik.errors.price ? String(formik.errors.price) : ""
            // "Giá bán phải lớn hơn 0 và lớn hơn hoặc bằng giá vốn"
          }
          sx={{
            "& .MuiOutlinedInput-root": {
              backgroundColor: "background.paper",
              height: "45px",
            },
          }}
        />
      </Box>
    </Card>
  );
});

// Display name for debugging purposes
Price.displayName = "Price";

export default Price;
