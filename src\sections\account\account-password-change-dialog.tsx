import { FC, useState } from "react";
import { useFormik } from "formik";
import { useTranslation } from "react-i18next";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import { tokens } from "@/src/locales/tokens";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import { useAppDispatch } from "@/src/redux/hooks";

import {
  passwordChangeSchema,
  passwordChangeInitialValues,
  PasswordFormValues,
} from "./validations/password-validations";
import { performPasswordChange } from "./utils/password-change-handlers";
import { PasswordTextField } from "./password-text-field";

interface AccountPasswordChangeDialogProps {
  open: boolean;
  onClose: () => void;
}

export const AccountPasswordChangeDialog: FC<AccountPasswordChangeDialogProps> = ({
  open,
  onClose,
}) => {
  const { t } = useTranslation();
  const snackbar = useSnackbar();
  const dispatch = useAppDispatch();

  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const formik = useFormik<PasswordFormValues>({
    initialValues: passwordChangeInitialValues,
    validationSchema: passwordChangeSchema,
    onSubmit: (values, helpers) => {
      performPasswordChange(
        values,
        helpers,
        dispatch,
        onClose,
        snackbar.success,
        snackbar.error,
        t,
        t(tokens.settings.passwordUpdateSuccess)
      );
    },
  });

  const handleClose = () => {
    formik.resetForm();
    onClose();
  };

  const toggleOldPassword = () => setShowOldPassword(!showOldPassword);
  const toggleNewPassword = () => setShowNewPassword(!showNewPassword);
  const toggleConfirmPassword = () => setShowConfirmPassword(!showConfirmPassword);

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <form noValidate onSubmit={formik.handleSubmit}>
        <DialogTitle>{t(tokens.settings.changePassword)}</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ pt: 1 }}>
            <PasswordTextField
              label="Mật khẩu cũ"
              placeholder="Nhập mật khẩu cũ"
              name="oldPassword"
              onBlur={formik.handleBlur}
              onChange={formik.handleChange}
              value={formik.values.oldPassword}
              error={!!(formik.touched.oldPassword && formik.errors.oldPassword)}
              helperText={formik.touched.oldPassword && formik.errors.oldPassword}
              showPassword={showOldPassword}
              onTogglePassword={toggleOldPassword}
            />

            <PasswordTextField
              label="Mật khẩu mới"
              placeholder="Nhập mật khẩu mới"
              name="newPassword"
              onBlur={formik.handleBlur}
              onChange={formik.handleChange}
              value={formik.values.newPassword}
              error={!!(formik.touched.newPassword && formik.errors.newPassword)}
              helperText={formik.touched.newPassword && formik.errors.newPassword}
              showPassword={showNewPassword}
              onTogglePassword={toggleNewPassword}
            />

            <PasswordTextField
              label="Xác nhận mật khẩu mới"
              placeholder="Nhập lại mật khẩu mới"
              name="confirmPassword"
              onBlur={formik.handleBlur}
              onChange={formik.handleChange}
              value={formik.values.confirmPassword}
              error={!!(formik.touched.confirmPassword && formik.errors.confirmPassword)}
              helperText={formik.touched.confirmPassword && formik.errors.confirmPassword}
              showPassword={showConfirmPassword}
              onTogglePassword={toggleConfirmPassword}
            />

            <Typography variant="caption" sx={{ color: "text.secondary", pt: 1 }}>
              Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt.
            </Typography>
          </Stack>
        </DialogContent>
        <DialogActions sx={{ pb: 3, px: 3 }}>
          <Button onClick={handleClose} color="inherit">
            Hủy
          </Button>
          <Button type="submit" variant="contained" disabled={formik.isSubmitting}>
            Đổi mật khẩu
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};
