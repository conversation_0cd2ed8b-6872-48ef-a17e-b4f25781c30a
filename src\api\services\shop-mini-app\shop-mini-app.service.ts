import { apiClient } from '@/src/api/config/api-client';
import { API_PATHS } from '@/src/api/constants/api-paths';
import type { ApiError, ErrorConfig } from '@/src/api/types/error.types';
import type { ExtendedRequestConfig } from '@/src/api/types/api.types';
import { GetProductRequest, GetProductRequestBody } from '@/src/api/types/product.types';

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export const shopMiniAppService = {
  createShopMiniApp: async <T = any>(model: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.SHOP_MINI_APP_PARTNER.CREATE_SHOP_MINI_APP,
        model,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getShopMiniApp: async <T = any>(shopId: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.SHOP_MINI_APP_PARTNER.GET_SHOP_MINI_APP}?shopId=${shopId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
