import React, { useState } from 'react';
import {
  Box,
  Button,
  Paper,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import { DateRange } from '@mui/icons-material';
import { Sidebar } from './index';
import SearchIcon from '@mui/icons-material/Search';
import DashboardLayout from '../../../../layouts/dashboard';
import { Container } from '@mui/system';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DateRangePicker } from '@mui/x-date-pickers-pro/DateRangePicker';

const Dashboard = () => {
  const [openDatePicker, setOpenDatePicker] = useState(false);
  const [dateRange, setDateRange] = useState('Full-time');
  const [activeTab, setActiveTab] = useState('cho-duyet');
  const [selectedDateRange, setSelectedDateRange] = useState([null, null]);

  const [openPopup, setOpenPopup] = useState(false);
  const [selectedRow, setSelectedRow] = useState(null);

  const handleClosePopup = () => {
    setOpenPopup(false);
    setSelectedRow(null);
  };

  const handleOpenDatePicker = () => {
    setOpenDatePicker(true);
  };

  const handleDateRangeChange = (event) => {
    const value = event.target.value;
    setDateRange(value);
    if (value === 'DateRange') {
      setOpenDatePicker(true);
    }
  };

  const handleCloseDatePicker = () => {
    setOpenDatePicker(false);
    if (!selectedDateRange[0] && !selectedDateRange[1]) {
      setDateRange('Full-time');
    }
  };

  const row_pay = [
    {
      id: 'ABC123',
      name: 'Ngô Văn Tuấn',
      total_customers: '100',
      customers_bought: '10',
      order_successful: '10',
      revenue: '10.000.000đ',
      average_value: '100.000đ',
      total_commission: '10.000.000đ',
      income_tax: '1.000.000đ',
      actually_received: '9.000.000đ',
      receiver: 'Ngô Văn Tuấn',
      account_number: '**********',
      bank_name: 'Ngân hàng quân đội MB bank',
      status: 1,
      time: '8:00 10/10/2024',
    },
  ];

  const rows = [
    {
      id: 'ABC123',
      name: 'Ngô Văn Tuấn',
      total_customers: '100',
      customers_bought: '10',
      order_successful: '10',
      revenue: '10.000.000đ',
      average_value: '100.000đ',
      total_commission: '10.000.000đ',
      income_tax: '1.000.000đ',
      actually_received: '9.000.000đ',
      receiver: 'Ngô Văn Tuấn',
      account_number: '**********',
      bank_name: 'Ngân hàng quân đội MB bank',
      status: 1,
    },
    {
      id: 'ABC121',
      name: 'Ngô Văn A',
      total_customers: '100',
      customers_bought: '10',
      order_successful: '10',
      revenue: '10.000.000đ',
      average_value: '100.000đ',
      total_commission: '10.000.000đ',
      income_tax: '1.000.000đ',
      actually_received: '9.000.000đ',
      receiver: 'Ngô Văn Tuấn',
      account_number: '**********',
      bank_name: 'Ngân hàng quân đội MB bank',
      status: 0,
    },
  ];

  return (
    <DashboardLayout>
      <Container
        maxWidth={false}
        sx={{
          display: 'flex',
          gap: { xs: '10px', md: '15px' },
          p: { xs: 1, md: 0 },
          m: 0,
          flexDirection: { xs: 'column', lg: 'row' },
        }}
      >
        <Sidebar />
        <Box sx={{ flexGrow: 1, padding: { xs: '15px' }, width: { xs: '100%', lg: '70%' } }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
            <Button
              sx={{
                mx: 1,
                borderBottom: activeTab === 'cho-duyet' ? '2px solid #2654FE' : 'none',
                borderRadius: '0 !important',
                textTransform: 'none',
                fontSize: '16px',
                fontWeight: '700',
                fontFamily: 'Inter',
                color: '#000',
              }}
              color={activeTab === 'cho-duyet' ? 'primary' : 'inherit'}
              onClick={() => setActiveTab('cho-duyet')}
            >
              Chờ duyệt
            </Button>
            <Button
              sx={{
                mx: 1,
                borderBottom: activeTab === 'paid' ? '2px solid #2654FE' : 'none',
                borderRadius: '0 !important',
                textTransform: 'none',
                fontSize: '16px',
                fontWeight: '700',
                fontFamily: 'Inter',
                color: '#000',
              }}
              color={activeTab === 'paid' ? 'primary' : 'inherit'}
              onClick={() => setActiveTab('paid')}
            >
              Đã thanh toán
            </Button>
          </Box>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              mb: 2,
              gap: 2,
              '@media (max-width: 600px)': { flexDirection: 'column' },
            }}
          >
            <TextField
              variant="outlined"
              placeholder=""
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon sx={{ color: '#000000', fontSize: '32px' }} />
                  </InputAdornment>
                ),
              }}
              sx={{ flexGrow: 1, '@media (max-width: 600px)': { width: '100%' } }}
            />
            <FormControl sx={{ minWidth: 200, '@media (max-width: 600px)': { width: '100%' } }}>
              <Select
                value={dateRange}
                onChange={handleDateRangeChange}
                displayEmpty
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <DateRange sx={{ fontSize: 20 }} />
                    <Typography>
                      {selected === 'Full-time' ? 'Toàn thời gian' : selected}
                    </Typography>
                    {selected === 'Full-time' && (
                      <Typography variant="caption" color="text.secondary"></Typography>
                    )}
                  </Box>
                )}
                inputProps={{ 'aria-label': 'Select date range' }}
              >
                <MenuItem value="Full-time">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography>Toàn thời gian</Typography>
                    <Typography variant="caption" color="text.secondary"></Typography>
                  </Box>
                </MenuItem>
                <MenuItem value="Today">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography>Hôm nay</Typography>
                  </Box>
                </MenuItem>
                <MenuItem value="Yesterday">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography>Hôm qua</Typography>
                  </Box>
                </MenuItem>
                <MenuItem value="7 days">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography>7 ngày qua</Typography>
                  </Box>
                </MenuItem>
                <MenuItem value="30 days">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography>30 ngày qua</Typography>
                  </Box>
                </MenuItem>
                <MenuItem value="DateRange" onClick={handleOpenDatePicker}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography>Date Range</Typography>
                  </Box>
                </MenuItem>
              </Select>
            </FormControl>

            <Dialog open={openDatePicker} onClose={handleCloseDatePicker}>
              <DialogTitle>Chọn khoảng thời gian</DialogTitle>
              <DialogContent>
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DateRangePicker
                    onChange={(newValue) => {
                      setSelectedDateRange(newValue);
                      setDateRange('DateRange');
                    }}
                  />
                </LocalizationProvider>
              </DialogContent>
              <DialogActions>
                <Button onClick={handleCloseDatePicker}>Hủy</Button>
                <Button
                  onClick={() => setOpenDatePicker(false)}
                  disabled={!selectedDateRange[0] || !selectedDateRange[1]}
                >
                  Xác nhận
                </Button>
              </DialogActions>
            </Dialog>
          </Box>

          <Box>
            <TableContainer component={Paper}>
              {activeTab === 'cho-duyet' && (
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ minWidth: '100px' }}>Mã đối tác</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Tên đối tác</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Tổng khách hàng</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Khách hàng đã mua</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Đơn hàng thành công</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Doanh thu</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Giá trị đơn trung bình</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Tổng hoa hồng</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Thuế thu nhập</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Thực nhận</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Người nhận</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Số tài khoản</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Ngân hàng</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Thanh toán</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {rows.map((row) => (
                      <TableRow key={row.id}>
                        <TableCell>{row.id}</TableCell>
                        <TableCell>{row.name}</TableCell>
                        <TableCell>{row.total_customers}</TableCell>
                        <TableCell>{row.customers_bought}</TableCell>
                        <TableCell>{row.order_successful}</TableCell>
                        <TableCell>{row.revenue}</TableCell>
                        <TableCell>{row.average_value}</TableCell>
                        <TableCell>{row.total_commission}</TableCell>
                        <TableCell>{row.income_tax}</TableCell>
                        <TableCell>{row.actually_received}</TableCell>
                        <TableCell>{row.receiver}</TableCell>
                        <TableCell>{row.account_number}</TableCell>
                        <TableCell>{row.bank_name}</TableCell>
                        <TableCell sx={{ color: row.status === 1 ? '#2654FE' : 'inherit' }}>
                          {row.status === 1 ? 'THANH TOÁN' : 'CHƯA THANH TOÁN'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}

              {activeTab === 'paid' && (
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ minWidth: '100px' }}>Mã đối tác</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Tên đối tác</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Tổng khách hàng</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Khách hàng đã mua</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Đơn hàng thành công</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Doanh thu</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Giá trị đơn trung bình</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Tổng hoa hồng</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Thuế thu nhập</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Thực nhận</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Người nhận</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Số tài khoản</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Ngân hàng</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Thanh toán</TableCell>
                      <TableCell sx={{ minWidth: '100px' }}>Thời gian chi</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {row_pay.map((row) => (
                      <TableRow key={row.id}>
                        <TableCell>{row.id}</TableCell>
                        <TableCell>{row.name}</TableCell>
                        <TableCell>{row.total_customers}</TableCell>
                        <TableCell>{row.customers_bought}</TableCell>
                        <TableCell>{row.order_successful}</TableCell>
                        <TableCell>{row.revenue}</TableCell>
                        <TableCell>{row.average_value}</TableCell>
                        <TableCell>{row.total_commission}</TableCell>
                        <TableCell>{row.income_tax}</TableCell>
                        <TableCell>{row.actually_received}</TableCell>
                        <TableCell>{row.receiver}</TableCell>
                        <TableCell>{row.account_number}</TableCell>
                        <TableCell>{row.bank_name}</TableCell>
                        <TableCell sx={{ color: row.status === 1 ? '#2654FE' : 'inherit' }}>
                          {row.status === 1 ? 'Đã chi' : 'Chưa chi'}
                        </TableCell>
                        <TableCell sx={{ color: '#2654FE' }}>{row.time}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}

              <Dialog open={openPopup} onClose={handleClosePopup}>
                <DialogTitle>Chỉnh sửa thông tin</DialogTitle>
                <DialogContent>
                  <Typography>Mã đối tác: {selectedRow?.id}</Typography>
                  <Typography>Tên đối tác: {selectedRow?.name}</Typography>
                </DialogContent>
                <DialogActions>
                  <Button onClick={handleClosePopup}>Đóng</Button>
                </DialogActions>
              </Dialog>
            </TableContainer>
          </Box>
        </Box>
      </Container>
    </DashboardLayout>
  );
};

export default Dashboard;
