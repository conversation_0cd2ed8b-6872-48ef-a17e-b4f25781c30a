import React, { useState } from "react";
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  Snackbar,
  Alert,
  InputAdornment,
  FormHelperText,
} from "@mui/material";
import KeyIcon from "@mui/icons-material/VpnKey";
import AppRegistrationIcon from "@mui/icons-material/AppRegistration";
import IconButton from "@mui/material/IconButton";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";

export interface IntegrationField {
  name: string;
  label: string;
  type?: string;
  placeholder?: string;
  required?: boolean;
}

interface IntegrationFormProps {
  title: string;
  fields: IntegrationField[];
  onSubmit: (values: Record<string, string>) => Promise<void>;
  loading?: boolean;
  successMessage?: string;
  values?: Record<string, string> | null;
}

const IntegrationForm: React.FC<IntegrationFormProps> = ({
  title,
  fields,
  onSubmit,
  loading = false,
  successMessage = "<PERSON><PERSON><PERSON> thông tin thành công!",
  values: initialValues,
}) => {
  const [values, setValues] = useState<Record<string, string>>(
    () => initialValues ?? Object.fromEntries(fields.map((f) => [f.name, ""]))
  );
  const [submitting, setSubmitting] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [showPassword, setShowPassword] = useState<Record<string, boolean>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  React.useEffect(() => {
    if (initialValues) {
      setValues(initialValues);
    }
  }, [initialValues]);

  // Validation functions
  const validateAppId = (value: string): string => {
    if (!value.trim()) {
      return "App ID là bắt buộc";
    }
    if (!/^\d+$/.test(value.trim())) {
      return "App ID không hợp lệ";
    }
    if (value.trim().length < 3 || value.trim().length > 10) {
      return "App ID không hợp lệ";
    }
    return "";
  };

  const validateSecretKey = (value: string): string => {
    if (!value.trim()) {
      return "Secret Key là bắt buộc";
    }
    if (value.trim().length !== 128) {
      return "Secret Key không hợp lệ";
    }
    if (!/^[a-zA-Z0-9]+$/.test(value.trim())) {
      return "Secret Key không hợp lệ";
    }
    return "";
  };

  const validateField = (name: string, value: string): string => {
    if (name === "appId") {
      return validateAppId(value);
    }
    if (name === "secretKey") {
      return validateSecretKey(value);
    }
    // For other fields, just check if required
    const field = fields.find((f) => f.name === name);
    if (field?.required && !value.trim()) {
      return `${field.label} là bắt buộc`;
    }
    return "";
  };

  const handleChange = (name: string, value: string) => {
    setValues((prev) => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }

    // Validate on change for immediate feedback
    const error = validateField(name, value);
    if (error) {
      setErrors((prev) => ({ ...prev, [name]: error }));
    }
  };

  const handleTogglePassword = (name: string) => {
    setShowPassword((prev) => ({ ...prev, [name]: !prev[name] }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all fields before submission
    const newErrors: Record<string, string> = {};
    fields.forEach((field) => {
      const error = validateField(field.name, values[field.name] || "");
      if (error) {
        newErrors[field.name] = error;
      }
    });

    setErrors(newErrors);

    // If there are errors, don't submit
    if (Object.keys(newErrors).length > 0) {
      return;
    }

    setSubmitting(true);
    try {
      await onSubmit(values);
      setOpenSnackbar(true);
    } catch (error) {
      console.error("Submit error:", error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit}
      sx={{
        display: "flex",
        flexDirection: "column",
        gap: 3,
        alignItems: "center",
        width: "100%",
        px: { xs: 0, sm: 1 },
      }}
    >
      {title && (
        <Typography variant="h5" fontWeight={700} mb={2} align="center" color="#1976d2">
          {title}
        </Typography>
      )}
      <Box
        p={2}
        sx={{ backgroundColor: "white" }}
        gap={2}
        display={"flex"}
        width={"100%"}
        flexDirection={"column"}
        borderRadius={1}
      >
        {fields.map((field) => (
          <Box key={field.name}>
            <TextField
              label={field.label}
              value={values[field.name]}
              onChange={(e) => handleChange(field.name, e.target.value)}
              required={field.required}
              placeholder={field.placeholder}
              type={field.type === "password" && !showPassword[field.name] ? "password" : "text"}
              fullWidth
              margin="none"
              error={!!errors[field.name]}
              InputLabelProps={{
                shrink: true,
                sx: {
                  fontWeight: 600,
                  color: errors[field.name] ? "#d32f2f" : "#757575",
                  fontSize: 14,
                  mb: 0.5,
                },
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start" sx={{ height: "100%" }}>
                    {field.name.toLowerCase().includes("key") ? (
                      <KeyIcon
                        sx={{ color: "#90caf9", fontSize: 22, mr: 0.5, alignSelf: "center" }}
                      />
                    ) : (
                      <AppRegistrationIcon
                        sx={{ color: "#90caf9", fontSize: 22, mr: 0.5, alignSelf: "center" }}
                      />
                    )}
                  </InputAdornment>
                ),
                endAdornment:
                  field.type === "password" ? (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label={showPassword[field.name] ? "Ẩn mật khẩu" : "Hiện mật khẩu"}
                        onClick={() => handleTogglePassword(field.name)}
                        edge="end"
                        size="small"
                      >
                        {showPassword[field.name] ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ) : undefined,
                sx: {
                  borderRadius: 3,
                  bgcolor: "#fff",
                  boxShadow: "0 1px 6px 0 rgba(25, 118, 210, 0.07)",
                  fontSize: 16,
                  pl: 1.5,
                  transition: "box-shadow 0.2s, border-color 0.2s",
                  "&.Mui-focused": {
                    boxShadow: "0 2px 8px 0 rgba(25, 118, 210, 0.15)",
                    borderColor: "#1976d2",
                  },
                  "& input": {
                    border: "none !important",
                    outline: "none !important",
                    boxShadow: "none !important",
                  },
                  "& input:-webkit-autofill": {
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "#000000",
                    transition: "background-color 5000s ease-in-out 0s",
                    boxShadow: "inset 0 0 20px 20px #23232329",
                  },
                  "& input:-webkit-autofill:hover": {
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "#000000",
                    transition: "background-color 5000s ease-in-out 0s",
                    boxShadow: "inset 0 0 20px 20px #23232329",
                  },
                  "& input:-webkit-autofill:focus": {
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "#000000",
                    transition: "background-color 5000s ease-in-out 0s",
                    boxShadow: "inset 0 0 20px 20px #23232329",
                  },
                  "& input:-webkit-autofill:active": {
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "#000000",
                    transition: "background-color 5000s ease-in-out 0s",
                    boxShadow: "inset 0 0 20px 20px #23232329",
                  },
                },
              }}
            />
            {errors[field.name] && (
              <FormHelperText error sx={{ mt: 0.5, ml: 0 }}>
                {errors[field.name]}
              </FormHelperText>
            )}
          </Box>
        ))}
      </Box>
      <Button
        type="submit"
        variant="contained"
        color="primary"
        size="large"
        sx={{
          borderRadius: 1,
          fontWeight: 700,
          fontSize: 17,
          px: 6,
          alignSelf: "center",
          bgcolor: "#1976d2",
          boxShadow: "0 2px 8px 0 rgba(25, 118, 210, 0.10)",
          "&:hover": { bgcolor: "#115293" },
        }}
        disabled={loading || submitting}
      >
        {loading || submitting ? "Đang kết nối..." : "Kết nối"}
      </Button>
      <Snackbar
        open={openSnackbar}
        autoHideDuration={2000}
        onClose={() => setOpenSnackbar(false)}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <Alert severity="success" sx={{ width: "100%" }}>
          {successMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default IntegrationForm;
