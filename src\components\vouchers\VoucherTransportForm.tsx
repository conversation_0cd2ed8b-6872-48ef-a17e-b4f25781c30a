import React, { useEffect, useState } from "react";
import Grid from "@mui/material/Grid2";
import {
  Box,
  Button,
  InputAdornment,
  Typography,
  Stack,
  Alert,
  useTheme,
  alpha,
  TextField,
} from "@mui/material";
import { Controller, FormProvider, useForm, useFormContext } from "react-hook-form";
import {
  CODE_TYPE,
  RELEASE_TYPE,
  DISCOUNT_TYPE,
  LIMIT_TYPE,
  CONDITION_TYPE,
  VOUCHER_TYPE,
  VOUCHER_STATUS,
  SHIPPING_DISCOUNT_TYPE,
  VOUCHER_TAB,
  TYPE_DISTRIBUTION,
} from "@/src/api/types/voucher.type";
import PromotionVoucherBox4 from "./PromotionVoucherBox4";
import CustomSwitch from "../custom-switch";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import { formatDateTimeForApi } from "@/src/utils/date-utils";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useVoucher } from "@/src/api/hooks/voucher/use-voucher";
import { useRouter } from "next/router";
import useSnackbar from "@/src/hooks/use-snackbar";
import { paths } from "@/src/paths";
import TitleDialog from "../dialog/TitleDialog";
import VoucherTransportBox2 from "./transports/VoucherTransportBox2";
import VoucherTransportSummaryBox from "./transports/VoucherTransportSummaryBox";
import { CommonMediaUpload } from "@/src/components/common-media-upload";
import { FILE_SIZE_2MB } from "@/src/constants/constant";
import { FileType } from "@/src/constants/file-types";
import { CreateFileGroupRequest, RefType } from "@/src/api/types/media.types";
import {
  ExistingMediaFile,
  scrollToTop,
} from "@/src/pages/dashboard/product/product-management/create/create";
import dayjs from "dayjs";
import { ImageProcessor } from "@/src/utils/image-processor";
import { useMedia } from "@/src/api/hooks/media/use-media";
import { FormHeader, FormSection, FormRadioGroup, FormActions } from "./shared";
import InfoIcon from "@mui/icons-material/Info";

export type VoucherTransportFormData = {
  voucherId?: string;
  voucherCode?: string;
  codeType: string;
  releaseType: string;
  quantity: number;
  isMinMoneyRequired: boolean;
  minOrder: number;
  maxDiscount?: number;
  percentDiscount?: number;
  moneyDiscount: number;
  discountType: string;
  limitType: string;
  categoryIds?: string[];
  productIds?: string[];
  conditionType: string;
  userGroupId?: string;
  userIds?: string[];
  maxUsagePerUser: number;
  isLongTerm: boolean;
  startDate: Date;
  endDate?: Date | null;
  voucherName?: string;
  voucherNameOrigin?: string;
  exchangePoints?: number;
  voucherType: string;
  status: string;
  shippingDiscountType: string;
  voucherCodePrefix: string;
  image?: string;
  originalEndDate?: Date;
  distributionType?: string;
};

const now = new Date();
now.setHours(0, 0, 0, 0);
export const initFormVoucherPromotionData: VoucherTransportFormData = {
  voucherId: "",
  releaseType: RELEASE_TYPE.FREE,
  codeType: CODE_TYPE.COMMON,
  quantity: 1,
  isMinMoneyRequired: false,
  minOrder: 0,
  maxDiscount: 0,
  percentDiscount: 0,
  moneyDiscount: 0,
  discountType: DISCOUNT_TYPE.MONEY,
  limitType: LIMIT_TYPE.ALL,
  categoryIds: [],
  productIds: [],
  conditionType: CONDITION_TYPE.ALL,
  userGroupId: "",
  userIds: [],
  maxUsagePerUser: 1,
  startDate: now,
  endDate: null,
  isLongTerm: false,
  voucherCode: "",
  voucherName: "",
  voucherNameOrigin: "",
  exchangePoints: 0,
  voucherType: VOUCHER_TYPE.TRANSPORT,
  status: VOUCHER_STATUS.ACTIVED,
  shippingDiscountType: SHIPPING_DISCOUNT_TYPE.FREE,
  voucherCodePrefix: "",
  originalEndDate: null,
  distributionType: TYPE_DISTRIBUTION.ON_RECEIVE,
};

const validationSchema = Yup.object().shape({
  voucherId: Yup.string().nullable().notRequired(),
  voucherCode: Yup.string()
    .matches(/^[A-Za-z0-9_-]+$/, "Mã voucher không được chứa dấu, khoảng cách hoặc ký tự đặc biệt")
    .when("codeType", {
      is: CODE_TYPE.COMMON,
      then: () => Yup.string().required("Mã voucher là bắt buộc"),
      otherwise: () => Yup.string().nullable().notRequired(),
    }),
  voucherCodePrefix: Yup.string()
    .matches(
      /^[A-Za-z0-9_-]+$/,
      "Tiền tố mã voucher không được chứa dấu, khoảng cách hoặc ký tự đặc biệt"
    )
    .when("codeType", {
      is: CODE_TYPE.UNIQUE,
      then: () => Yup.string().required("Tiền tố mã voucher là bắt buộc"),
      otherwise: () => Yup.string().nullable().notRequired(),
    }),
  codeType: Yup.string()
    .oneOf([CODE_TYPE.COMMON, CODE_TYPE.UNIQUE], 'Loại mã phải là "Common" hoặc "Unique"')
    .required("Loại mã là bắt buộc"),
  voucherType: Yup.string()
    .oneOf([VOUCHER_TYPE.TRANSPORT], 'Loại voucher phải là "Transport"')
    .required("Loại voucher là bắt buộc"),
  releaseType: Yup.string()
    .oneOf(
      [RELEASE_TYPE.FREE, RELEASE_TYPE.POINT],
      'Loại phát hành phải là "Free" hoặc "ExchangePoints"'
    )
    .required("Loại phát hành là bắt buộc"),
  quantity: Yup.number()
    .transform((value, originalValue) => (originalValue === "" ? undefined : value))
    .typeError("Số lượng phải là một số")
    .required("Số lượng không được để trống")
    .positive("Số lượng phải lớn hơn 0")
    .integer("Số lượng phải là số nguyên")
    .min(1, "Số lượng phải ít nhất là 1")
    .max(2147483647, "Số lượng đã vượt mức tối đa"),
  isMinMoneyRequired: Yup.boolean(),
  minOrder: Yup.number()
    .transform((value, originalValue) => (originalValue === "" ? undefined : value))
    .typeError("Giá trị đơn hàng tối thiểu phải là một số")
    .min(0, "Đơn hàng tối thiểu phải lớn hơn hoặc bằng 0")
    .max(9223372036854775807, "Đơn hàng tối thiểu đã vượt mức tối đa"),
  moneyDiscount: Yup.number().when("shippingDiscountType", {
    is: SHIPPING_DISCOUNT_TYPE.FIXED,
    then: () =>
      Yup.number()
        .transform((value, originalValue) => (originalValue === "" ? undefined : value))
        .typeError("Số tiền giảm giá phải là một số")
        .required("Số tiền giảm giá là bắt buộc")
        .min(1, "Số tiền giảm giá phải lớn hơn 0")
        .max(9223372036854775807, "Số tiền giảm đã vượt mức tối đa"),
    otherwise: () => Yup.number().notRequired(),
  }),
  discountType: Yup.string()
    .oneOf(
      [DISCOUNT_TYPE.PERCENT, DISCOUNT_TYPE.MONEY, "None"],
      'Loại giảm giá phải là "Percent", "Money" hoặc "None"'
    )
    .required("Loại giảm giá là bắt buộc"),
  limitType: Yup.string()
    .oneOf([LIMIT_TYPE.ALL, LIMIT_TYPE.CATEGORY, LIMIT_TYPE.PRODUCT], "Loại giới hạn phải hợp lệ")
    .required("Loại giới hạn là bắt buộc"),
  categoryIds: Yup.array()
    .of(Yup.string())
    .when("limitType", {
      is: LIMIT_TYPE.CATEGORY,
      then: () => Yup.array().of(Yup.string()).min(1, "Danh mục sản phẩm không được để trống"),
      otherwise: () => Yup.array().nullable(),
    }),
  productIds: Yup.array()
    .of(Yup.string())
    .when("limitType", {
      is: LIMIT_TYPE.PRODUCT,
      then: () => Yup.array().of(Yup.string()).min(1, "Sản phẩm không được để trống"),
      otherwise: () => Yup.array().nullable(),
    }),
  conditionType: Yup.string()
    .oneOf(
      [CONDITION_TYPE.ALL, CONDITION_TYPE.GROUP, CONDITION_TYPE.CUSTOMER],
      'Loại điều kiện phải là "All", "Group" hoặc "Customer"'
    )
    .required("Loại điều kiện là bắt buộc"),
  userGroupId: Yup.string()
    .nullable()
    .notRequired()
    .when("conditionType", {
      is: CONDITION_TYPE.GROUP,
      then: () => Yup.string().required("Nhóm khách hàng không được để trống"),
      otherwise: () => Yup.string().nullable(),
    }),
  userIds: Yup.array()
    .of(Yup.string())
    .when("conditionType", {
      is: CONDITION_TYPE.CUSTOMER,
      then: () => Yup.array().of(Yup.string()).min(1, "Khách hàng không được để trống"),
      otherwise: () => Yup.array().nullable(),
    }),
  maxUsagePerUser: Yup.number()
    .transform((value, originalValue) => (originalValue === "" ? undefined : value))
    .typeError("Số lần sử dụng phải là một số")
    .required("Số lần sử dụng là bắt buộc")
    .positive("Số lần sử dụng phải lớn hơn 0")
    .integer("Số lần sử dụng phải là số nguyên")
    .min(1, "Số lần sử dụng phải ít nhất là 1")
    .max(2147483647, "Số lần sử dụng đã vượt mức tối đa"),
  isLongTerm: Yup.boolean(),
  startDate: Yup.date().when(["isLongTerm", "voucherId"], (isLongTerm, voucherId) => {
    if (!isLongTerm && !!voucherId) {
      return Yup.date().required("Ngày bắt đầu là bắt buộc");
    }
    if (!isLongTerm) {
      return Yup.date()
        .min(new Date(new Date().setHours(0, 0, 0, 0)), "Ngày bắt đầu phải từ hôm nay trở đi")
        .required("Ngày bắt đầu là bắt buộc");
    }
    return Yup.date().nullable();
  }),
  endDate: Yup.date().when(["isLongTerm", "originalEndDate"], {
    is: (isLongTerm, originalEndDate) => !isLongTerm,
    then: function () {
      return Yup.date()
        .min(Yup.ref("startDate"), "Ngày kết thúc phải sau ngày bắt đầu")
        .test(
          "min-5-minutes-if-changed",
          "Thời gian kết thúc phải sau thời điểm hiện tại ít nhất 5 phút",
          function (value) {
            const { originalEndDate } = this.parent;
            if (!value) return false;
            // Nếu không đổi so với DB thì pass
            if (
              originalEndDate &&
              new Date(originalEndDate).getTime() === new Date(value).getTime()
            ) {
              return true;
            }
            // Nếu đổi thì phải lớn hơn hiện tại + 5 phút
            const minTime = new Date();
            minTime.setMinutes(minTime.getMinutes() + 5);
            return new Date(value) >= minTime;
          }
        )
        .required("Ngày kết thúc là bắt buộc");
    },
    otherwise: () => Yup.date().nullable(),
  }),
  status: Yup.string()
    .oneOf(
      [VOUCHER_STATUS.ACTIVED, VOUCHER_STATUS.INACTIVED],
      'Trạng thái phải là "Actived" hoặc "InActived"'
    )
    .required("Trạng thái là bắt buộc"),
  voucherName: Yup.string()
    .max(255, "Tên voucher không được vượt quá 255 ký tự")
    .transform((value) => (typeof value === "string" ? value.trim() : value))
    .required("Tên voucher là bắt buộc")
    .test("not-empty", "Tên voucher không được để trống", (value) => !!value && value.length > 0),
  voucherNameOrigin: Yup.string().nullable().notRequired(),
  exchangePoints: Yup.number().when("releaseType", {
    is: RELEASE_TYPE.POINT,
    then: () =>
      Yup.number()
        .transform((value, originalValue) => (originalValue === "" ? undefined : value))
        .positive("Số điểm phải lớn hơn 0")
        .integer("Số điểm phải là số nguyên")
        .min(1, "Số điểm phải ít nhất là 1")
        .max(10000, "Số điểm tối đa là 10.000 điểm")
        .required("Số điểm là bắt buộc"),
    otherwise: () =>
      Yup.number()
        .transform((value, originalValue) => (originalValue === "" ? undefined : value))
        .notRequired(),
  }),
  shippingDiscountType: Yup.string()
    .oneOf(
      [SHIPPING_DISCOUNT_TYPE.FREE, SHIPPING_DISCOUNT_TYPE.FIXED],
      'Loại giảm giá phải là "Free" hoặc "Fixed"'
    )
    .required("Loại giảm giá là bắt buộc"),
  image: Yup.mixed().nullable().notRequired(),
  originalEndDate: Yup.date().nullable(),
  distributionType: Yup.string()
    .oneOf(
      [TYPE_DISTRIBUTION.IMMEDIATE, TYPE_DISTRIBUTION.ON_RECEIVE],
      'Loại phân phối phải là "Immediate" hoặc "OnReceive"'
    )
    .when("codeType", {
      is: CODE_TYPE.UNIQUE,
      then: () => Yup.string().required("Loại phân phối là bắt buộc"),
      otherwise: () => Yup.string().nullable().notRequired(),
    }),
});

export default function VoucherTransportForm({ voucher }) {
  const methods = useForm({
    defaultValues: {
      ...initFormVoucherPromotionData,
      voucherType: VOUCHER_TYPE.TRANSPORT,
    },
    resolver: yupResolver(validationSchema),
  });

  const {
    formState: { errors },
    control,
    handleSubmit,
    watch,
    getValues,
    setValue,
    reset,
  } = methods;

  const releaseType = watch("releaseType");
  const storeId = useStoreId();
  const { createVoucher, updateVoucher, loading } = useVoucher();
  const router = useRouter();
  const snackbar = useSnackbar();
  const [isDoneInitDetail, setIsDoneInitDetail] = useState(false);
  const { uploadFile } = useMedia();
  const [localLoading, setLocalLoading] = useState(false);

  useEffect(() => {
    if (voucher && voucher.voucherDetails && !isDoneInitDetail) {
      reset({
        voucherId: voucher.voucherId,
        releaseType: voucher.releaseType,
        codeType: voucher.codeType,
        quantity: voucher.quantity,
        isMinMoneyRequired: voucher.isMinMoneyRequired,
        minOrder: voucher.minOrder,
        moneyDiscount: voucher.moneyDiscount,
        discountType: voucher.discountType,
        limitType: voucher.limitType,
        categoryIds: voucher.categoryIds,
        productIds: voucher.productIds,
        conditionType: voucher.conditionType,
        userGroupId: voucher.userGroupId,
        userIds: voucher.userIds,
        maxUsagePerUser: voucher.maxUsagePerUser,
        startDate: voucher.startDate ? dayjs(voucher.startDate).toDate() : null,
        status: voucher.status,
        endDate: voucher.endDate ? dayjs(voucher.endDate).toDate() : null,
        isLongTerm: voucher.isLongTerm,
        voucherCode: voucher.voucherDetails[0]?.voucherCode,
        voucherName: voucher.voucherName,
        voucherNameOrigin: voucher.voucherNameOrigin,
        exchangePoints: voucher.exchangePoints || 0,
        voucherType: VOUCHER_TYPE.TRANSPORT,
        shippingDiscountType: voucher.shippingDiscountType,
        voucherCodePrefix: voucher.voucherCodePrefix,
        image: voucher.image,
        originalEndDate: voucher.endDate ? dayjs(voucher.endDate).toDate() : null,
        distributionType: voucher.distributionType,
      });
      setIsDoneInitDetail(true);
    }
  }, [voucher]);

  const onSubmit = async (data: VoucherTransportFormData) => {
    if (localLoading) return; // Chặn double submit
    setLocalLoading(true);
    try {
      if (data.codeType === CODE_TYPE.COMMON) {
        if (!/^[A-Za-z0-9]+$/.test(data.voucherCode || "")) {
          snackbar.error("Mã voucher không được chứa dấu, khoảng cách hoặc ký tự đặc biệt");
          return;
        }
        if (!data.voucherCode) {
          snackbar.error("Mã voucher là bắt buộc");
          return;
        }
        if ((data.voucherCode || "").length > 30) {
          snackbar.error("Mã voucher không được vượt quá 30 ký tự");
          return;
        }
      }
      if (data.codeType === CODE_TYPE.UNIQUE) {
        if (!/^[A-Za-z0-9]+$/.test(data.voucherCodePrefix || "")) {
          snackbar.error("Tiền tố mã voucher không được chứa dấu, khoảng cách hoặc ký tự đặc biệt");
          return;
        }
        if (!data.voucherCodePrefix) {
          snackbar.error("Tiền tố mã voucher là bắt buộc");
          return;
        }
        if ((data.voucherCodePrefix || "").length > 10) {
          snackbar.error("Tiền tố mã voucher không được vượt quá 10 ký tự");
          return;
        }
      }
      if (localFiles.length === 0 && existingFiles.length === 0) {
        snackbar.error("Bạn cần chọn ít nhất 1 ảnh!");
        scrollToTop();
        return;
      }
      let uploadedFiles = [];
      if (localFiles.length > 0) {
        for (const file of localFiles) {
          const processedFile = await ImageProcessor.processImage(file);
          const data: CreateFileGroupRequest = {
            FileUpload: processedFile,
            ShopId: storeId,
            GroupFileId: defaultGroupId,
            RefType: RefType.Voucher,
            RefId: "",
          };
          const response = await uploadFile(data);
          uploadedFiles.push(response.data);
        }
      }
      const newImages = uploadedFiles.map((file) => ({
        type: file.type === "IMAGE" ? FileType.IMAGE : FileType.VIDEO,
        link: file.url || file.link,
        mediaFileId: file.mediaFileId,
      }));
      const formData = {
        ...data,
        shopId: storeId,
        image: newImages.length > 0 ? newImages[0] : existingFiles[0],
        voucherType: VOUCHER_TYPE.TRANSPORT,
        startDate: formatDateTimeForApi(data.startDate),
        endDate: formatDateTimeForApi(data.endDate),
        minOrder: data.minOrder,
      };

      if (data.limitType === LIMIT_TYPE.ALL) {
        formData.productIds = [];
        formData.categoryIds = [];
      } else if (data.limitType === LIMIT_TYPE.CATEGORY) {
        formData.productIds = [];
      } else if (data.limitType === LIMIT_TYPE.PRODUCT) {
        formData.categoryIds = [];
      }

      if (data.conditionType === CONDITION_TYPE.ALL) {
        formData.userIds = [];
        formData.userGroupId = "";
      } else if (data.conditionType === CONDITION_TYPE.CUSTOMER) {
        formData.userGroupId = "";
      } else if (data.conditionType === CONDITION_TYPE.GROUP) {
        formData.userIds = [];
      }

      if (data.discountType === DISCOUNT_TYPE.MONEY) {
        formData.percentDiscount = 0;
      }

      if (!voucher) {
        const response = await createVoucher(formData);
        if (response?.data?.result) {
          snackbar.success("Tạo voucher thành công");
          if (formData.codeType == CODE_TYPE.COMMON) {
            router.push(paths.marketing.vouchers.list + "/?tab=" + VOUCHER_TAB.VOUCHER);
          } else {
            router.push(paths.marketing.vouchers.list + "/?tab=" + VOUCHER_TAB.UNIQUE_VOUCHER);
          }
        }
        return;
      }

      const response = await updateVoucher(formData);
      if (response?.data?.result) {
        snackbar.success("Cập nhật voucher thành công");
        if (formData.codeType == CODE_TYPE.COMMON) {
          router.push(paths.marketing.vouchers.list + "/?tab=" + VOUCHER_TAB.VOUCHER);
        } else {
          router.push(paths.marketing.vouchers.list + "/?tab=" + VOUCHER_TAB.UNIQUE_VOUCHER);
        }
      }
    } finally {
      setLocalLoading(false);
    }
  };

  const handleCancel = () => {
    router.push(paths.marketing.vouchers.list);
  };

  // Automatically set exchangePoints = 0 when releaseType is "Free"
  useEffect(() => {
    if (releaseType === "Free") {
      setValue("exchangePoints", 0); // Set exchangePoints to 0
    }
  }, [releaseType, setValue]);

  // Add effect to handle codeType changes
  useEffect(() => {
    const codeType = watch("codeType");
    if (codeType === CODE_TYPE.UNIQUE) {
      setValue("releaseType", RELEASE_TYPE.FREE);
    }
  }, [watch("codeType"), setValue]);

  // Add effect to handle distributionType changes
  useEffect(() => {
    const distributionType = getValues("distributionType");
    if (distributionType === TYPE_DISTRIBUTION.IMMEDIATE) {
      setValue("conditionType", CONDITION_TYPE.ALL);
      setValue("userGroupId", "");
      setValue("userIds", []);
    }
  }, [watch("distributionType"), setValue, getValues]);

  const handleGenerateCode = () => {
    const newCode = generateRandomCode();
    setValue("voucherCode", newCode);
  };

  const generateRandomCode = () => {
    const timestamp = Date.now().toString(36).toUpperCase();
    const randomStr = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `VOUCHER${timestamp}${randomStr}`;
  };

  const [existingFiles, setExistingFiles] = useState<ExistingMediaFile[]>([]);
  const [localFiles, setLocalFiles] = useState<File[]>([]);
  const [errorMsg, setErrorMsg] = useState<string>("");
  const [defaultGroupId, setDefaultGroupId] = useState<string>("");
  const { getGroups } = require("@/src/api/hooks/media/use-media").useMedia();

  useEffect(() => {
    const fetchDefaultGroup = async () => {
      try {
        const data = {
          ShopId: storeId,
          Skip: 0,
          Limit: 10,
        };
        const response = await getGroups(data);
        if (response?.data?.data?.length > 0) {
          setDefaultGroupId(response.data.data[0].groupFileId);
        }
      } catch (error) {
        console.error("Error fetching groups:", error);
      }
    };
    if (storeId) fetchDefaultGroup();
  }, [storeId]);

  useEffect(() => {
    if (voucher && voucher.image) {
      const imageObj =
        typeof voucher.image === "string"
          ? { link: voucher.image, url: voucher.image, type: FileType.IMAGE }
          : voucher.image;
      setExistingFiles([imageObj]);
      setValue("image", imageObj);
    } else {
      setExistingFiles([]);
      setValue("image", null);
    }
  }, [voucher]);

  const handleFilesChange = async (filesOrObjects: any[]) => {
    setLocalFiles(filesOrObjects);
  };

  const handleRemove = () => {
    setExistingFiles([]);
    setLocalFiles([]);
    setValue("image", null);
  };

  const theme = useTheme();

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Box
          sx={{ minHeight: "100vh", backgroundColor: alpha(theme.palette.background.default, 0.5) }}
        >
          {/* Form Header */}
          <FormHeader
            title="Voucher giảm giá vận chuyển"
            subtitle="Tạo voucher giảm giá phí vận chuyển cho khách hàng"
            onBack={handleCancel}
            status={voucher ? { label: voucher.status, color: "primary" } : undefined}
          />

          {/* Main Content */}
          <Box sx={{ paddingInline: 3, pb: 10 }}>
            <Grid container spacing={3}>
              {/* Left Column - Main Form */}
              <Grid size={{ xs: 12, lg: 8 }}>
                <Stack spacing={3}>
                  {/* Basic Information Section */}
                  <FormSection
                    title="Thông tin cơ bản"
                    subtitle="Thiết lập thông tin chính cho voucher vận chuyển"
                    icon={<InfoIcon />}
                    isLoading={!isDoneInitDetail && !!voucher}
                  >
                    <Stack spacing={3}>
                      <Box>
                        <Typography
                          sx={{
                            fontSize: "0.875rem",
                            fontWeight: 500,
                            color: theme.palette.text.primary,
                            mb: 1,
                            display: "flex",
                            alignItems: "center",
                          }}
                        >
                          Tên voucher
                          <Typography
                            component="span"
                            sx={{ color: theme.palette.error.main, ml: 0.5 }}
                          >
                            *
                          </Typography>
                        </Typography>
                        <Controller
                          name="voucherName"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              placeholder="Nhập tên voucher"
                              error={!!errors.voucherName}
                              helperText={errors.voucherName?.message}
                              fullWidth
                              variant="outlined"
                              sx={{
                                "& .MuiOutlinedInput-root": {
                                  borderRadius: 2,
                                  backgroundColor: theme.palette.background.paper,
                                  "&:hover .MuiOutlinedInput-notchedOutline": {
                                    borderColor: alpha(theme.palette.primary.main, 0.4),
                                  },
                                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                                    borderWidth: 1,
                                  },
                                  "& .MuiOutlinedInput-notchedOutline": {
                                    borderColor: alpha(theme.palette.divider, 0.3),
                                    transition: "border-color 0.2s ease-in-out",
                                  },
                                },
                                "& .MuiFormHelperText-root": {
                                  fontSize: "0.75rem",
                                  mt: 0.75,
                                },
                              }}
                            />
                          )}
                        />
                      </Box>

                      <Box>
                        <Typography
                          sx={{
                            fontSize: "0.875rem",
                            fontWeight: 500,
                            color: theme.palette.text.primary,
                            mb: 2,
                          }}
                        >
                          Ảnh voucher
                          <Typography
                            component="span"
                            sx={{ color: theme.palette.error.main, ml: 0.5 }}
                          >
                            *
                          </Typography>
                        </Typography>
                        <CommonMediaUpload
                          caption="Thêm ảnh voucher"
                          maxFiles={1}
                          accept={{ "image/*": [".png", ".jpg", ".jpeg"] }}
                          maxSize={FILE_SIZE_2MB}
                          existingFiles={existingFiles}
                          setExistingFiles={setExistingFiles}
                          localFiles={localFiles}
                          setLocalFiles={setLocalFiles}
                          onFilesChange={handleFilesChange}
                          onRemove={handleRemove}
                          defaultGroupId={defaultGroupId}
                          isShowPreviewImage={true}
                          errorMsg={errorMsg}
                          setErrorMsg={setErrorMsg}
                        />
                      </Box>
                    </Stack>
                  </FormSection>

                  {/* Voucher Code Configuration */}
                  {!voucher && (
                    <FormSection
                      title="Cấu hình mã voucher"
                      subtitle="Chọn loại mã voucher và thiết lập mã"
                    >
                      <Stack spacing={3}>
                        <FormRadioGroup
                          name="codeType"
                          control={control}
                          label="Loại mã voucher"
                          required
                          variant="card"
                          options={[
                            {
                              value: CODE_TYPE.COMMON,
                              label: "Voucher dùng chung",
                              description:
                                "Một mã voucher có thể được sử dụng bởi nhiều khách hàng",
                            },
                            {
                              value: CODE_TYPE.UNIQUE,
                              label: "Voucher một mã",
                              description: "Mỗi khách hàng sẽ có một mã voucher riêng biệt",
                            },
                          ]}
                          error={errors.codeType}
                        />

                        {watch("codeType") === CODE_TYPE.COMMON && (
                          <Box>
                            <Typography
                              sx={{
                                fontSize: "0.875rem",
                                fontWeight: 500,
                                color: theme.palette.text.primary,
                                mb: 1,
                                display: "flex",
                                alignItems: "center",
                              }}
                            >
                              Mã voucher
                              <Typography
                                component="span"
                                sx={{ color: theme.palette.error.main, ml: 0.5 }}
                              >
                                *
                              </Typography>
                            </Typography>
                            <Controller
                              name="voucherCode"
                              control={control}
                              render={({ field }) => (
                                <TextField
                                  {...field}
                                  placeholder="Nhập mã voucher"
                                  error={!!errors.voucherCode}
                                  helperText={errors.voucherCode?.message}
                                  fullWidth
                                  variant="outlined"
                                  InputProps={{
                                    endAdornment: (
                                      <InputAdornment position="end">
                                        <Button
                                          onClick={handleGenerateCode}
                                          size="small"
                                          variant="outlined"
                                          sx={{
                                            minWidth: "auto",
                                            px: 2,
                                            height: "32px",
                                            fontSize: "0.75rem",
                                            borderRadius: 1.5,
                                          }}
                                        >
                                          Tạo mã
                                        </Button>
                                      </InputAdornment>
                                    ),
                                  }}
                                  sx={{
                                    "& .MuiOutlinedInput-root": {
                                      borderRadius: 2,
                                      backgroundColor: theme.palette.background.paper,
                                      "&:hover .MuiOutlinedInput-notchedOutline": {
                                        borderColor: alpha(theme.palette.primary.main, 0.4),
                                      },
                                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                                        borderWidth: 1,
                                      },
                                      "& .MuiOutlinedInput-notchedOutline": {
                                        borderColor: alpha(theme.palette.divider, 0.3),
                                        transition: "border-color 0.2s ease-in-out",
                                      },
                                    },
                                    "& .MuiFormHelperText-root": {
                                      fontSize: "0.75rem",
                                      mt: 0.75,
                                    },
                                  }}
                                />
                              )}
                            />
                          </Box>
                        )}

                        {watch("codeType") === CODE_TYPE.UNIQUE && (
                          <Box>
                            <Typography
                              sx={{
                                fontSize: "0.875rem",
                                fontWeight: 500,
                                color: theme.palette.text.primary,
                                mb: 1,
                                display: "flex",
                                alignItems: "center",
                              }}
                            >
                              Tiền tố mã voucher
                              <Typography
                                component="span"
                                sx={{ color: theme.palette.error.main, ml: 0.5 }}
                              >
                                *
                              </Typography>
                            </Typography>
                            <Controller
                              name="voucherCodePrefix"
                              control={control}
                              render={({ field }) => (
                                <TextField
                                  {...field}
                                  placeholder="Nhập tiền tố mã voucher"
                                  error={!!errors.voucherCodePrefix}
                                  helperText={errors.voucherCodePrefix?.message}
                                  fullWidth
                                  variant="outlined"
                                  sx={{
                                    "& .MuiOutlinedInput-root": {
                                      borderRadius: 2,
                                      backgroundColor: theme.palette.background.paper,
                                      "&:hover .MuiOutlinedInput-notchedOutline": {
                                        borderColor: alpha(theme.palette.primary.main, 0.4),
                                      },
                                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                                        borderWidth: 1,
                                      },
                                      "& .MuiOutlinedInput-notchedOutline": {
                                        borderColor: alpha(theme.palette.divider, 0.3),
                                        transition: "border-color 0.2s ease-in-out",
                                      },
                                    },
                                    "& .MuiFormHelperText-root": {
                                      fontSize: "0.75rem",
                                      mt: 0.75,
                                    },
                                  }}
                                />
                              )}
                            />
                          </Box>
                        )}
                      </Stack>
                    </FormSection>
                  )}

                  {/* Distribution Type for Unique Vouchers */}
                  {watch("codeType") === CODE_TYPE.UNIQUE && (
                    <FormSection
                      title="Hình thức phát hành"
                      subtitle="Chọn cách thức phát hành mã voucher"
                    >
                      {!voucher ? (
                        <FormRadioGroup
                          name="distributionType"
                          control={control}
                          label="Loại phát hành"
                          required
                          variant="card"
                          options={[
                            {
                              value: TYPE_DISTRIBUTION.IMMEDIATE,
                              label: "Phát hành ngay lập tức",
                              description: "Tạo tất cả mã voucher ngay khi lưu",
                            },
                            {
                              value: TYPE_DISTRIBUTION.ON_RECEIVE,
                              label: "Khi người dùng nhận",
                              description: "Tạo mã voucher khi khách hàng nhận voucher",
                            },
                          ]}
                          error={errors.distributionType}
                        />
                      ) : (
                        <Alert severity="info" sx={{ borderRadius: 2 }}>
                          <Typography variant="body2">
                            <strong>Hình thức phát hành:</strong>{" "}
                            {watch("distributionType") === TYPE_DISTRIBUTION.IMMEDIATE
                              ? "Phát hành ngay lập tức"
                              : "Khi người dùng nhận"}
                          </Typography>
                        </Alert>
                      )}
                    </FormSection>
                  )}

                  {/* Release Type for Common Vouchers */}
                  {watch("codeType") === CODE_TYPE.COMMON && (
                    <FormSection
                      title="Loại voucher giảm giá"
                      subtitle="Chọn cách thức khách hàng nhận voucher"
                    >
                      <FormRadioGroup
                        name="releaseType"
                        control={control}
                        label="Loại phát hành"
                        required
                        variant="card"
                        options={[
                          {
                            value: RELEASE_TYPE.FREE,
                            label: "Phát hành miễn phí",
                            description: "Khách hàng có thể nhận voucher miễn phí",
                          },
                          {
                            value: RELEASE_TYPE.POINT,
                            label: "Đổi điểm lấy voucher",
                            description: "Khách hàng cần sử dụng điểm để đổi voucher",
                          },
                        ]}
                        error={errors.releaseType}
                      />
                    </FormSection>
                  )}

                  {/* Exchange Points Section */}
                  {releaseType === RELEASE_TYPE.POINT && watch("codeType") === CODE_TYPE.COMMON && (
                    <FormSection
                      title="Điểm quy đổi"
                      subtitle="Thiết lập số điểm cần thiết để đổi voucher"
                    >
                      <Box>
                        <Typography
                          sx={{
                            fontSize: "0.875rem",
                            fontWeight: 500,
                            color: theme.palette.text.primary,
                            mb: 1,
                            display: "flex",
                            alignItems: "center",
                          }}
                        >
                          Số điểm quy đổi
                          <Typography
                            component="span"
                            sx={{ color: theme.palette.error.main, ml: 0.5 }}
                          >
                            *
                          </Typography>
                        </Typography>
                        <Controller
                          name="exchangePoints"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              placeholder="Nhập số điểm quy đổi"
                              type="number"
                              error={!!errors.exchangePoints}
                              helperText={
                                errors.exchangePoints?.message ||
                                "1 điểm của khách hàng tương đương 1.000đ"
                              }
                              fullWidth
                              variant="outlined"
                              sx={{
                                "& .MuiOutlinedInput-root": {
                                  borderRadius: 2,
                                  backgroundColor: theme.palette.background.paper,
                                  "&:hover .MuiOutlinedInput-notchedOutline": {
                                    borderColor: alpha(theme.palette.primary.main, 0.4),
                                  },
                                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                                    borderWidth: 1,
                                  },
                                  "& .MuiOutlinedInput-notchedOutline": {
                                    borderColor: alpha(theme.palette.divider, 0.3),
                                    transition: "border-color 0.2s ease-in-out",
                                  },
                                },
                                "& .MuiFormHelperText-root": {
                                  fontSize: "0.75rem",
                                  mt: 0.75,
                                },
                              }}
                            />
                          )}
                        />
                      </Box>
                    </FormSection>
                  )}

                  {/* Quantity Section */}
                  <FormSection
                    title="Số lượng phát hành"
                    subtitle="Thiết lập số lượng voucher có thể phát hành"
                  >
                    <Box>
                      <Typography
                        sx={{
                          fontSize: "0.875rem",
                          fontWeight: 500,
                          color: theme.palette.text.primary,
                          mb: 1,
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        Số lượng voucher
                        <Typography
                          component="span"
                          sx={{ color: theme.palette.error.main, ml: 0.5 }}
                        >
                          *
                        </Typography>
                      </Typography>
                      <Controller
                        name="quantity"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            placeholder="Nhập số lượng voucher"
                            type="number"
                            error={!!errors.quantity}
                            helperText={errors.quantity?.message}
                            fullWidth
                            variant="outlined"
                            sx={{
                              "& .MuiOutlinedInput-root": {
                                borderRadius: 2,
                                backgroundColor: theme.palette.background.paper,
                                "&:hover .MuiOutlinedInput-notchedOutline": {
                                  borderColor: alpha(theme.palette.primary.main, 0.4),
                                },
                                "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                                  borderWidth: 1,
                                },
                                "& .MuiOutlinedInput-notchedOutline": {
                                  borderColor: alpha(theme.palette.divider, 0.3),
                                  transition: "border-color 0.2s ease-in-out",
                                },
                              },
                              "& .MuiFormHelperText-root": {
                                fontSize: "0.75rem",
                                mt: 0.75,
                              },
                            }}
                          />
                        )}
                      />
                    </Box>
                  </FormSection>

                  {/* Shipping Discount Configuration Section */}
                  <FormSection
                    title="Cấu hình giảm giá vận chuyển"
                    subtitle="Thiết lập loại và mức độ giảm giá vận chuyển"
                  >
                    <ShippingDiscountConfigurationBox />
                  </FormSection>

                  {/* Shipping Conditions Section */}
                  <FormSection
                    title="Điều kiện vận chuyển"
                    subtitle="Thiết lập các điều kiện áp dụng voucher vận chuyển"
                  >
                    <VoucherTransportBox2 voucher={voucher} />
                  </FormSection>
                </Stack>
              </Grid>

              {/* Right Column - Summary */}
              <Grid size={{ xs: 12, lg: 4 }}>
                <Stack spacing={3}>
                  <FormSection title="Tóm tắt voucher" subtitle="Xem trước thông tin voucher">
                    <VoucherTransportSummaryBox />
                  </FormSection>

                  <FormSection
                    title="Hiệu lực voucher"
                    subtitle="Bật/tắt trạng thái hoạt động của voucher"
                  >
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                      }}
                    >
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {watch("status") === VOUCHER_STATUS.ACTIVED
                          ? "Voucher có hiệu lực"
                          : "Voucher không có hiệu lực"}
                      </Typography>
                      <Controller
                        name="status"
                        control={control}
                        render={({ field: { onChange, value } }) => (
                          <CustomSwitch
                            checked={value === VOUCHER_STATUS.ACTIVED}
                            onChange={(e) =>
                              onChange(
                                e.target.checked ? VOUCHER_STATUS.ACTIVED : VOUCHER_STATUS.INACTIVED
                              )
                            }
                          />
                        )}
                      />
                    </Box>
                  </FormSection>
                  <FormSection
                    title="Điều kiện sử dụng"
                    subtitle="Thiết lập các điều kiện và giới hạn sử dụng voucher"
                  >
                    <PromotionVoucherBox4 />
                  </FormSection>
                </Stack>
              </Grid>
            </Grid>

            {/* Form Actions */}
            <FormActions
              onCancel={handleCancel}
              onSave={handleSubmit(onSubmit)}
              isLoading={loading || localLoading}
              variant="fixed"
              additionalActions={voucher?.voucherId && <DeleteBox voucherId={voucher.voucherId} />}
            />
          </Box>
        </Box>
      </form>
    </FormProvider>
  );
}

const ShippingDiscountConfigurationBox = () => {
  const {
    control,
    watch,
    formState: { errors },
  } = useFormContext<VoucherTransportFormData>();
  const theme = useTheme();
  return (
    <Stack spacing={3}>
      <FormRadioGroup
        name="shippingDiscountType"
        control={control}
        label="Loại giảm giá vận chuyển"
        required
        variant="card"
        options={[
          {
            value: SHIPPING_DISCOUNT_TYPE.FREE,
            label: "Miễn phí vận chuyển",
            description: "Khách hàng được miễn phí hoàn toàn phí vận chuyển",
          },
          {
            value: SHIPPING_DISCOUNT_TYPE.FIXED,
            label: "Giảm theo số tiền",
            description: "Giảm một số tiền cố định từ phí vận chuyển",
          },
        ]}
        error={errors.shippingDiscountType}
      />

      {watch("shippingDiscountType") === SHIPPING_DISCOUNT_TYPE.FIXED && (
        <Box>
          <Typography
            sx={{
              fontSize: "0.875rem",
              fontWeight: 500,
              color: theme.palette.text.primary,
              mb: 1,
              display: "flex",
              alignItems: "center",
            }}
          >
            Số tiền giảm
            <Typography component="span" sx={{ color: theme.palette.error.main, ml: 0.5 }}>
              *
            </Typography>
          </Typography>
          <Controller
            name="moneyDiscount"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                placeholder="Nhập số tiền giảm"
                type="number"
                error={!!errors.moneyDiscount}
                helperText={errors.moneyDiscount?.message}
                fullWidth
                variant="outlined"
                InputProps={{
                  startAdornment: <InputAdornment position="start">₫</InputAdornment>,
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: 2,
                    backgroundColor: theme.palette.background.paper,
                    "&:hover .MuiOutlinedInput-notchedOutline": {
                      borderColor: alpha(theme.palette.primary.main, 0.4),
                    },
                    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                      borderWidth: 1,
                    },
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderColor: alpha(theme.palette.divider, 0.3),
                      transition: "border-color 0.2s ease-in-out",
                    },
                  },
                  "& .MuiFormHelperText-root": {
                    fontSize: "0.75rem",
                    mt: 0.75,
                  },
                }}
              />
            )}
          />
        </Box>
      )}
    </Stack>
  );
};

const DeleteBox = ({ voucherId }) => {
  const [openDialog, setOpenDialog] = useState(false);
  const { deleteVoucher } = useVoucher();
  const router = useRouter();
  const snackbar = useSnackbar();
  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const clickOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleConfirmDelete = async () => {
    const response = await deleteVoucher([voucherId]);
    if (response?.data.result) {
      await router.push(paths.marketing.vouchers.list);
      snackbar.success("Xóa voucher thành công");
    }
  };
  return (
    <>
      <Button variant="contained" color="error" onClick={clickOpenDialog}>
        Xóa voucher
      </Button>

      <TitleDialog
        title="Xóa voucher"
        open={openDialog}
        handleClose={handleCloseDialog}
        submitBtnTitle="Xác nhận"
        handleSubmit={handleConfirmDelete}
      >
        <Typography>Bạn có chắc muốn xóa voucher này?</Typography>
      </TitleDialog>
    </>
  );
};
