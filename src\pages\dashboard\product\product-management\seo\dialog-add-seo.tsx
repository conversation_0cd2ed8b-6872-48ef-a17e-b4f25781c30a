import React, { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  IconButton,
  TextField,
  Typography,
  Stack,
} from "@mui/material";
import { Close as CloseIcon } from "@mui/icons-material";
import { useTranslation } from "react-i18next";
import { tokens } from "@/src/locales/tokens";
import { SeoTagType } from "../../../../../types/product/form";

interface DialogAddSeoProps {
  open: boolean;
  onClose: () => void;
  onSave: (seoData: SeoTagType) => void;
  initialData?: SeoTagType;
}

const DialogAddSeo: React.FC<DialogAddSeoProps> = ({ open, onClose, onSave, initialData }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<SeoTagType>({
    pageTitle: "",
    pageDesc: "",
    tags: "",
    url: "",
  });

  useEffect(() => {
    if (initialData) {
      setFormData(initialData);
    }
  }, [initialData]);

  const handleChange =
    (field: keyof SeoTagType) => (event: React.ChangeEvent<HTMLInputElement>) => {
      setFormData((prev) => ({
        ...prev,
        [field]: event.target.value,
      }));
    };

  const handleSave = () => {
    onSave(formData);
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Typography variant="h6">Tối ưu hóa công cụ tìm kiếm (SEO)</Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Stack spacing={2.5} sx={{ mt: 2 }}>
          <TextField
            fullWidth
            label="Tiêu đề trang"
            value={formData.pageTitle}
            onChange={handleChange("pageTitle")}
            inputProps={{ maxLength: 70 }}
            helperText={`${formData.pageTitle.length}/70 ký tự`}
          />

          <TextField
            fullWidth
            label="Mô tả trang"
            value={formData.pageDesc}
            onChange={handleChange("pageDesc")}
            multiline
            rows={4}
            inputProps={{ maxLength: 320 }}
            helperText={`${formData.pageDesc.length}/320 ký tự`}
          />

          <TextField
            fullWidth
            label="Thẻ"
            value={formData.tags}
            onChange={handleChange("tags")}
            helperText="Nhập tối đa 3 thẻ, phân cách bằng dấu phẩy"
          />

          <TextField
            fullWidth
            label="Đường dẫn"
            value={formData.url}
            onChange={handleChange("url")}
            placeholder="https://syncshopki.myallvalue.com/products/"
          />
        </Stack>
      </DialogContent>

      <DialogActions sx={{ p: 2.5 }}>
        <Button onClick={onClose} variant="outlined">
          {t(tokens.common.cancel)}
        </Button>
        <Button onClick={handleSave} variant="contained">
          {t(tokens.common.save)}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DialogAddSeo;
