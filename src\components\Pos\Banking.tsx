import { Container, Box, Typography, Button } from "@mui/material";
import { Grid } from "@mui/system";
import React, { useState, useEffect } from "react";
import { usePayment } from "@/src/api/hooks/payment/use-payment";
import { useStoreId } from "@/src/hooks/use-store-id";

interface PaymentConfirmationProps {
  onClose: () => void;
  bankName: string;
  totalAmount: number; // thêm prop totalAmount
  paymentSelected: any;
  cart: any;
}

const title = (
  <Typography
    variant="body1"
    sx={{ textAlign: "center", mb: 2, fontWeight: "bold", mt: -2, fontSize: "1.3rem" }}
  >
    Xác nhận thanh toán
  </Typography>
);

function PaymentBanking({
  bankName,
  totalAmount,
  paymentSelected,
  cart,
}: PaymentConfirmationProps) {
  const amount = (
    <Typography variant="h4" sx={{ textAlign: "center", mb: 4, fontWeight: "bold" }}>
      {totalAmount.toLocaleString()}đ
    </Typography>
  );

  return (
    <Container
      maxWidth={false}
      disableGutters
      sx={{ height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
    >
      <Box
        sx={{
          width: "90%",
          height: 400, // Set height to 400px
          backgroundColor: "white",
          borderRadius: 1,
          overflow: "hidden",
          p: 3,
        }}
      >
        {title}
        <Typography variant="h6" sx={{ textAlign: "center", mb: 4 }}>
          {paymentSelected ? paymentSelected.name : bankName}
        </Typography>
        {paymentSelected &&
          ((paymentSelected.bankAccountNumber &&
            paymentSelected.customerBankName &&
            paymentSelected.bankShortCode) ||
            paymentSelected.paymentPhoto) && (
            <Box sx={{ display: "flex", justifyContent: "center", mb: 4 }}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  maxWidth: 250,
                  maxHeight: 200,
                }}
              >
                <img
                  src={
                    paymentSelected.bankAccountNumber &&
                    paymentSelected.customerBankName &&
                    paymentSelected.bankShortCode
                      ? `https://apiqr.web2m.com/api/generate/${paymentSelected.bankShortCode}/${
                          paymentSelected.bankAccountNumber
                        }/${encodeURIComponent(
                          paymentSelected.customerBankName
                        )}?amount=${totalAmount}&memo=${encodeURIComponent(
                          `Thanh toan ${cart?.cartNo}`
                        )}&is_mask=0&bg=0`
                      : paymentSelected.paymentPhoto
                  }
                  alt={paymentSelected.name}
                  style={{ maxWidth: "100%", maxHeight: "100%", objectFit: "contain" }}
                />
              </Box>
            </Box>
          )}
        {amount}
      </Box>
    </Container>
  );
}

export default PaymentBanking;
