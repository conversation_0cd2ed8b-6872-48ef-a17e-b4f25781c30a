import { useState } from "react";
import { ErrorHandlerService } from "../../services/error-handler.service";
import { syncService } from "../../services/sync-service/sync-service.service";

export const useSyncService = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const configSyncService = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await syncService.configSyncService(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw err;
    } finally {
      setLoading(false);
    }
  };
  const getSyncServiceConfig = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await syncService.getSyncServiceConfig(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw err;
    } finally {
      setLoading(false);
    }
  };
  const updateAccessCode = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await syncService.updateAccessCode(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteSyncServiceConfig = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await syncService.deleteSyncServiceConfig(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    configSyncService,
    getSyncServiceConfig,
    updateAccessCode,
    deleteSyncServiceConfig,
    loading,
    error,
  };
};
