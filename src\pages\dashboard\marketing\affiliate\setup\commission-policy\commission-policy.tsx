import React, { useState } from 'react';
import {
  Box,
  Button,
  Paper,
  Typography,
  Tabs,
  Tab,
  Switch,
  RadioGroup,
  FormControlLabel,
  Radio,
  TextField,
  InputAdornment,
  Tooltip,
  MenuItem,
  Select,
  FormControl,
  Checkbox,
  IconButton,
} from '@mui/material';
import { Sidebar } from './../../index';
import InfoIcon from '@mui/icons-material/Info';
import DashboardLayout from '../../../../../../layouts/dashboard';
import { Container } from '@mui/system';
import { styled } from '@mui/material/styles';

const CustomSwitch = styled(Switch)(() => ({
  '& .MuiSwitch-switchBase.Mui-checked': {
    color: '#2654FE',
  },
  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
    backgroundColor: '#2654FE',
  },
  '& .MuiSwitch-track': {
    backgroundColor: '#ddd',
    borderRadius: 20,
  },
}));

const Dashboard = () => {
  const [tabValue, setTabValue] = useState(0);
  const [isLevel2Enabled, setIsLevel2Enabled] = useState(true);
  const [isEnabled, setIsEnabled] = useState(true);
  const [permission, setPermission] = React.useState('allow');
  const [commissionAllowed, setCommissionAllowed] = useState(true);
  const [commissionRate, setCommissionRate] = useState('');
  const [approvalMethod, setApprovalMethod] = useState('auto');
  const [spendingLimit, setSpendingLimit] = useState('');
  const [purchaseLimit, setPurchaseLimit] = useState('');
  const [validityPeriod, setValidityPeriod] = useState('no_limit');
  const [selected, setSelected] = useState(false);
  const [isActive, setIsActive] = useState(true);

  return (
    <DashboardLayout>
      <Container
        maxWidth={false}
        sx={{
          display: 'flex',
          gap: { xs: '10px', md: '15px' },
          p: { xs: 1, md: 0 },
          m: 0,
          flexDirection: { xs: 'column', lg: 'row' },
        }}
      >
        <Sidebar />
        <Box
          sx={{ width: '70%', mx: 'auto', mt: 3, '@media (max-width: 1200px)': { width: '100%' } }}
        >
          <Paper
            sx={{
              borderRadius: 0,
              overflow: 'hidden',
              boxShadow: 'none',
              borderBottom: '1px solid #787878',
              marginBottom: '25px',
            }}
          >
            <Tabs
              value={tabValue}
              onChange={(e, newValue) => setTabValue(newValue)}
              indicatorColor="primary"
              textColor="primary"
            >
              <Tab label="Chính sách hoa hồng" />
              <Tab label="Cài đặt nâng cao" />
            </Tabs>
          </Paper>

          {tabValue === 0 && (
            <Box mt={2} sx={{ boxShadow: 'none' }}>
              <Paper
                sx={{
                  p: 2,
                  mb: 2,
                  boxShadow: 'none',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <Box width={'55%'}>
                  <Typography fontWeight={700}>Chính sách hoa hồng mặc định</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Đặt mức hoa hồng phù hợp với ngân sách tiếp thị của bạn, có thể hỗ trợ hoa hồng
                    cấp 2
                  </Typography>
                </Box>
                <Box
                  display="flex"
                  justifyContent="end"
                  alignItems="center"
                  gap={2}
                  mt={1}
                  width={'43%'}
                  sx={{
                    '@media (max-width: 767px)': {
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'end',
                    },
                  }}
                >
                  <Typography
                    sx={{
                      color: '#787878',
                      fontSize: '14px',
                      fontWeight: '500',
                      '@media (max-width: 767px)': { textAlign: 'right' },
                    }}
                  >
                    Hoa hồng hiện tại: 10%
                  </Typography>
                  <Button
                    href="/dashboard/marketing/affiliate/setup/commission-policy/default-commission-policy"
                    variant="contained"
                    sx={{
                      bgcolor: '#2654FE',
                      color: '#fff',
                      fontWeight: '400',
                      fontSize: '14px',
                      textTransform: 'none',
                      borderRadius: '10px',
                    }}
                  >
                    Thiết lập
                  </Button>
                </Box>
              </Paper>

              <Paper
                sx={{
                  p: 2,
                  mb: 2,
                  boxShadow: 'none',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <Box width={'55%'}>
                  <Typography fontWeight={700}>Hoa hồng theo nhóm đối tác</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Phân các đối tác thành các nhóm khác nhau và cung cấp mức hoa hồng riêng cho
                    từng đối tác
                  </Typography>
                </Box>
                <Box
                  display="flex"
                  justifyContent="end"
                  alignItems="center"
                  gap={2}
                  mt={1}
                  width={'43%'}
                >
                  <Button
                    href="/dashboard/marketing/affiliate/setup/commission-policy/group-commission-policy"
                    variant="contained"
                    sx={{
                      bgcolor: '#2654FE',
                      color: '#fff',
                      fontWeight: '400',
                      fontSize: '14px',
                      textTransform: 'none',
                      borderRadius: '10px',
                    }}
                  >
                    Thiết lập
                  </Button>
                </Box>
              </Paper>

              <Paper
                sx={{
                  p: 2,
                  boxShadow: 'none',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <Box width={'55%'}>
                  <Typography fontWeight={700}>Hoa hồng cấp 2</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Sau khi kích hoạt, các đối tác có thể phát triển thêm các đối tác cấp 2 và hưởng
                    hoa hồng cấp 2 theo chính sách thiết lập
                  </Typography>
                </Box>

                <Box
                  display="flex"
                  justifyContent="end"
                  alignItems="center"
                  gap={2}
                  mt={1}
                  width={'43%'}
                >
                  <CustomSwitch checked={isEnabled} onChange={() => setIsEnabled(!isEnabled)} />
                </Box>
              </Paper>
            </Box>
          )}

          {tabValue === 1 && (
            <Box sx={{ padding: 3 }}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'start',
                  '@media (max-width: 600px)': { flexDirection: 'column' },
                }}
              >
                <Box sx={{ width: '35%', '@media (max-width: 600px)': { width: '100%' } }}>
                  <Typography variant="h6" fontWeight="bold">
                    Cài đặt quy tắc nâng cao
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    Thiết lập các quy tắc đặc biệt cho đối tác kinh doanh của bạn
                  </Typography>
                </Box>

                <Box
                  sx={{
                    width: '65%',
                    paddingLeft: '20px',
                    '@media (max-width: 600px)': { width: '100%', paddingLeft: '0px' },
                  }}
                >
                  <FormControl component="fieldset">
                    <Typography variant="subtitle1" fontWeight="bold">
                      Hoa hồng tự giới thiệu
                    </Typography>
                    <RadioGroup
                      value={commissionAllowed ? 'allowed' : 'not_allowed'}
                      onChange={(e) => setCommissionAllowed(e.target.value === 'allowed')}
                    >
                      <Box>
                        <FormControlLabel value="allowed" control={<Radio />} label="Cho phép" />
                        {commissionAllowed && (
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                              paddingLeft: '30px',
                            }}
                          >
                            <Box>
                              <TextField
                                value={commissionRate}
                                onChange={(e) => setCommissionRate(e.target.value)}
                                size="small"
                                placeholder=""
                                InputProps={{
                                  endAdornment: <InputAdornment position="end">%</InputAdornment>,
                                }}
                                sx={{
                                  mt: 1,
                                  '& fieldset': { borderRadius: '8px' },
                                  width: '150px',
                                  '@media (max-width: 600px)': { width: '100%' },
                                }}
                              />
                              <Tooltip
                                title="Khi cho phép hoa hồng tự giới thiệu, mỗi khi đối tác mua hàng họ sẽ được hoa hồng ngay trên đơn hàng họ mua với tỷ lệ hoa hồng được bạn cài đặt tại đây."
                                arrow
                              >
                                <IconButton
                                  size="small"
                                  sx={{
                                    color: '#D9D9D9',
                                  }}
                                >
                                  <InfoIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                              <Typography
                                sx={{ color: '#787878', fontSize: '10px', fontWeight: '400' }}
                              >
                                Nhập tỷ lệ hoa hồng tự giới thiệu cho đối tác của bạn
                              </Typography>
                            </Box>
                          </Box>
                        )}
                      </Box>

                      <FormControlLabel
                        value="not_allowed"
                        control={<Radio />}
                        label="Không cho phép"
                      />
                    </RadioGroup>
                  </FormControl>

                  <Typography variant="subtitle1" fontWeight="bold" sx={{ mt: 3 }}>
                    Thời gian hiệu lực cho các đối tác
                  </Typography>
                  <Typography variant="body2">
                    Đối tác được nhận hoa hồng cho các đơn hàng do khách hàng mua trong
                    <Select
                      value={validityPeriod}
                      onChange={(e) => setValidityPeriod(e.target.value)}
                      size="small"
                      sx={{ ml: 1, minWidth: 150, overflow: 'visible', marginRight: '5px' }}
                      MenuProps={{
                        disablePortal: true,
                        anchorOrigin: {
                          vertical: 'bottom',
                          horizontal: 'right',
                        },
                        transformOrigin: {
                          vertical: 'top',
                          horizontal: 'right',
                        },
                      }}
                    >
                      <MenuItem value="no_limit">Không giới hạn</MenuItem>
                      <MenuItem value="30_days">1</MenuItem>
                      <MenuItem value="60_days">2</MenuItem>
                      <MenuItem value="60_days">5</MenuItem>
                      <MenuItem value="60_days">10</MenuItem>
                    </Select>
                    sau khi khách hàng truy cập vào liên kết và trở thành cấp dưới của đối tác
                  </Typography>

                  <FormControl component="fieldset" sx={{ mt: 3 }}>
                    <Typography variant="subtitle1" fontWeight="bold">
                      Phê duyệt đối tác
                    </Typography>
                    <RadioGroup
                      value={approvalMethod}
                      onChange={(e) => setApprovalMethod(e.target.value)}
                    >
                      <FormControlLabel
                        sx={{
                          '.& span': { fontSize: '16px', fontWeight: '400', color: '#000000' },
                        }}
                        value="auto"
                        control={<Radio />}
                        label="Phê duyệt tự động"
                      />
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 0,
                          flexWrap: 'wrap',
                          marginLeft: '30px',
                        }}
                      >
                        {approvalMethod === 'auto' && (
                          <Box>
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={selected}
                                  onChange={() => setSelected(!selected)}
                                />
                              }
                              label={
                                <Typography variant="body1">
                                  Tổng số tiền chi tiêu tối thiểu
                                </Typography>
                              }
                              sx={{ marginRight: '10px' }}
                            />

                            <TextField
                              value={spendingLimit}
                              onChange={(e) => setSpendingLimit(e.target.value)}
                              size="small"
                              InputProps={{
                                endAdornment: <InputAdornment position="end">đ</InputAdornment>,
                              }}
                              sx={{
                                width: '100px',
                                '& input': {
                                  textAlign: 'right',
                                  borderRight: '1px solid #D9D9D9',
                                  padding: '5px 10px',
                                },
                              }}
                            />

                            <Typography variant="body1" sx={{ marginLeft: '10px' }}>
                              để đủ điều kiện tham gia đối tác kinh doanh
                            </Typography>
                          </Box>
                        )}
                      </Box>

                      <FormControlLabel
                        value="manual"
                        control={<Radio />}
                        label="Phê duyệt thủ công"
                      />
                      {approvalMethod === 'manual' && (
                        <Typography variant="body2" sx={{ marginLeft: '30px', color: 'gray' }}>
                          Sau khi đối tác đăng ký, admin sẽ xem xét lại các đơn đăng ký và phê duyệt
                          để đối tác có thể tham gia vào hệ thống và hưởng đầy đủ chính sách của bạn
                        </Typography>
                      )}

                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 0,
                          flexWrap: 'wrap',
                          marginLeft: '30px',
                        }}
                      >
                        {approvalMethod === 'manual' && (
                          <Box>
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={selected}
                                  onChange={() => setSelected(!selected)}
                                />
                              }
                              label={
                                <Typography variant="body1">
                                  Đối tác cần mua đơn hàng tối thiểu
                                </Typography>
                              }
                              sx={{ marginRight: '10px' }}
                            />

                            {/* TextField */}
                            <TextField
                              value={purchaseLimit}
                              onChange={(e) => setPurchaseLimit(e.target.value)}
                              size="small"
                              InputProps={{
                                endAdornment: <InputAdornment position="end">đ</InputAdornment>,
                              }}
                              sx={{
                                width: '100px',
                                '& input': {
                                  textAlign: 'right',
                                  borderRight: '1px solid #D9D9D9',
                                  padding: '5px 10px',
                                },
                              }}
                            />

                            <Typography variant="body1" sx={{ marginLeft: '10px' }}>
                              để đủ điều kiện tham gia đối tác kinh doanh
                            </Typography>
                          </Box>
                        )}

                        <Box display="flex" alignItems="center"></Box>
                      </Box>
                    </RadioGroup>
                  </FormControl>
                </Box>
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'start',
                  '@media (max-width: 600px)': { flexDirection: 'column' },
                }}
              >
                <Box sx={{ width: '35%', '@media (max-width: 600px)': { width: '100%' } }}>
                  <Typography variant="h6" fontWeight="500" sx={{ mt: 4 }}>
                    Cài đặt thanh toán
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    Thiết lập phương thức bạn sẽ thanh toán hoa hồng cho đối tác của bạn
                  </Typography>
                </Box>

                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'start',
                    width: '65%',
                    paddingLeft: '20px',
                    flexDirection: 'column',
                    '@media (max-width: 600px)': { width: '100%', paddingLeft: '0px' },
                  }}
                >
                  <Typography variant="h6" fontWeight="500" sx={{ mt: 4 }}>
                    Ngày thanh toán
                  </Typography>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      flexDirection: 'row',
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{ mb: 2, marginTop: '16px', '.& p': { margin: '0 !important' } }}
                    >
                      Ngày thanh toán hàng tháng:
                    </Typography>
                    <TextField
                      size="small"
                      type="number"
                      defaultValue="1"
                      sx={{ ml: 2, width: '60px' }}
                    />
                  </Box>
                </Box>
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'end',
                  alignItems: 'center',
                  marginTop: '20px',
                  paddingBottom: '30px',
                  borderBottom: '1px solid #787878',
                }}
              >
                <Button
                  variant="contained"
                  sx={{
                    bgcolor: '#2654FE',
                    color: '#fff',
                    fontWeight: '400',
                    fontSize: '14px',
                    textTransform: 'none',
                    borderRadius: '10px',
                  }}
                >
                  Lưu
                </Button>
              </Box>
              <Box
                sx={{ marginTop: '30px' }}
                display="flex"
                alignItems="center"
                justifyContent="space-between"
              >
                <Box>
                  <Typography sx={{ fontWeight: '700', fontSize: '16px', color: '#000' }}>
                    Trạng thái chương trình đối tác
                  </Typography>
                  <Typography sx={{ fontWeight: '400', fontSize: '14px', color: '#000' }}>
                    Sau khi ngừng kích hoạt, các đối tác mới sẽ không thể tham gia chương trình đối
                    tác kinh doanh của bạn. Các đối tác cũ vẫn hoạt động theo chính sách của bạn.
                  </Typography>
                </Box>
                <Switch
                  checked={isActive}
                  onChange={() => setIsActive(!isActive)}
                  color="primary"
                />
              </Box>
            </Box>
          )}
        </Box>
      </Container>
    </DashboardLayout>
  );
};

export default Dashboard;
