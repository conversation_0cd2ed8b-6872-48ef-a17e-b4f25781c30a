import React from 'react';
import { Dialog, DialogContent, DialogTitle, Box, Typography, Grid } from '@mui/material';

const QuickReportModal = ({ open, onClose, partner, reportOverviews }) => {
  if (!partner) return null;

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="md">
      <DialogTitle sx={{ fontWeight: 'bold', fontSize: '1.5rem' }}>
        Báo cáo nhanh - {partner.fullname}
      </DialogTitle>
      <DialogContent sx={{ padding: '20px 20px' }}>
        <Box sx={{ p: 2 }}>
          <Grid container spacing={2}>
            {reportOverviews.length > 0 && reportOverviews.map((item, index) => (
              <Grid
                item
                xs={12}
                sm={4}
                key={index}
                sx={{
                  '@media (max-width: 600px)': {
                    width: '100%',
                  },
                }}
              >
                <Box
                  sx={{
                    bgcolor: '#fff',
                    borderRight: (index + 1) % 3 !== 0 ? '1px solid #e0e0e0' : 'none',
                    textAlign: 'center',
                    p: 2,
                    '@media (max-width: 600px)': {
                      borderRight: 'none',
                      width: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                    },
                  }}
                >
                  <Typography sx={{ fontWeight: 'bold', fontSize: '20px', textAlign: 'left' }}>
                    {item.value}
                  </Typography>
                  <Typography sx={{ color: '#757575', mt: 1, textAlign: 'left', fontSize: '15px' }}>
                    {item.label}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default QuickReportModal;
