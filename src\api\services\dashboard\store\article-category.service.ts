import { apiClient } from "@/src/api/config/api-client";
import { API_PATHS } from "@/src/api/constants/api-paths";
import type { ApiError, ErrorConfig } from "@/src/api/types/error.types";
import type { ExtendedRequestConfig } from "@/src/api/types/api.types";
import {
  GetArticleCategoryRequest,
  GetArticleCategoryRequestBody,
} from "@/src/api/types/article-category.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export interface MediaFileDto {
  mediaFileId: string;
  type: string;
  link: string;
}

export interface ArticleCategoryDto {
  articleCategoryId: string;
  parentId: string;
  shopId: string;
  categoryName: string;
  categoryLevel: string;
  image: MediaFileDto;
  categoryDesc: string;
  typePublish: string;
  status: string;
  created?: string;
  updated?: string;
}

export const articleCategoryService = {
  getArticleCategory: async <T = any>(
    params: GetArticleCategoryRequest,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<GetArticleCategoryRequestBody, T>(
        `${API_PATHS.ARTICLE_CATEGORY.GET_ARTICLE_CATEGORY}?skip=${params.skip}&limit=${params.limit}&shopId=${params.shopId}&search=${params.search}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createArticleCategory: async <T = any>(data: ArticleCategoryDto, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.ARTICLE_CATEGORY.CREATE_ARTICLE_CATEGORY,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateArticleCategory: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.ARTICLE_CATEGORY.UPDATE_ARTICLE_CATEGORY,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateImageArticleCategory: async <T = any>(
    articleCategoryId: string,
    imageFile: File,
    errorConfig?: ErrorConfig
  ) => {
    const formData = new FormData();
    formData.append("ArticleCategoryId", articleCategoryId);
    formData.append("FileUpload", imageFile);

    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    };

    try {
      const response = await apiClient.post<FormData, T>(
        API_PATHS.ARTICLE_CATEGORY.UPDATE_IMAGE_ARTICLE_CATEGORY,
        formData,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteArticleCategory: async <T = any>(articleCategoryId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete<T>(
        `${API_PATHS.ARTICLE_CATEGORY.DELETE_ARTICLE_CATEGORY}?articleCategoryId=${articleCategoryId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getArticleCategoryDetail: async <T = any>(
    articleCategoryId: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.ARTICLE_CATEGORY.GET_ARTICLE_CATEGORY_DETAIL}/${articleCategoryId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
