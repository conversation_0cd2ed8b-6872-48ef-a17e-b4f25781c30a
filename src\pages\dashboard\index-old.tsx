import React, { useEffect, useState } from 'react';
import DashboardLayout from '../../layouts/dashboard';
import StoresPage from '../store/stores';
import { useSettings } from '@/src/hooks/use-settings';
import { Seo } from '@/src/components/seo';
import { usePageView } from 'src/hooks/use-page-view';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Divider from '@mui/material/Divider';
import MenuItem from '@mui/material/MenuItem';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import TitleTypography from '@/src/components/title-typography/title-typography';
import { useTranslation } from 'react-i18next';
import { useSnackbar } from '@/src/hooks/use-snackbar';
import { tokens } from '@/src/locales/tokens';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { Tab, Tabs, Tooltip } from '@mui/material';
import { useRouter } from 'next/router';
import PopupAds from './store/content-management/popup-ads';
import ArticleCategories from './store/content-management/article-categories';
import Articles from './store/content-management/articles';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import { BusinessCenterOutlined, DescriptionOutlined, PeopleOutline, ReplayOutlined, StreamOutlined } from '@mui/icons-material';
import { Grid } from '@mui/system';
import { BarChart } from '@mui/x-charts/BarChart';
import { dashboardPartnerService } from '@/src/api/services/dashboard/partner/dashboardpartner.service';
import { useStoreId } from '@/src/hooks/use-store-id';
import * as Icons from "@mui/icons-material";
import { formatCurrency } from '@/src/utils/format-number';
import { formatDateDisplay, formatDateTimeDisplay } from '@/src/utils/date-utils';
import ShopOnline from '@/src/components/dashboard/ShopOnline';
import ShopOffline from '@/src/components/dashboard/ShopOffline';
import { IDashboarPartner } from '@/src/api/types/dashboard.types';
const now = new Date();
const TAB_STORAGE_KEY = 'contentManagementTab';


function DynamicIcon({ name }) {
  const IconComponent = Icons[name]; // Lấy icon theo tên
  return IconComponent ? <IconComponent /> : <span>Icon not found</span>;
}

const Page = () => {
  const settings = useSettings();
  const { t } = useTranslation();
  const router = useRouter();
  const storeId = useStoreId();
  const [dashboardParter, setDashboardPartner] = useState<IDashboarPartner>();

  const [tabIndex, setTabIndex] = useState(() => {
    const { tab } = router.query;
    if (tab) {
      const index = parseInt(tab as string);
      if (!isNaN(index) && index >= 0 && index <= 2) {
        return index;
      }
    }

    const savedTab = localStorage.getItem(TAB_STORAGE_KEY);
    if (savedTab !== null) {
      return parseInt(savedTab, 10);
    }

    return 0;
  });

  useEffect(() => {
    const { tab } = router.query;
    if (tab) {
      const index = parseInt(tab as string, 10);
      if (!isNaN(index) && index >= 0 && index <= 2) {
        setTabIndex(index);
        localStorage.setItem(TAB_STORAGE_KEY, index.toString());
      }
    }
  }, [router.query]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabIndex(newValue);
    localStorage.setItem(TAB_STORAGE_KEY, newValue.toString());
    router.push(
      {
        pathname: router.pathname,
        query: { ...router.query, tab: newValue }
      },
      undefined,
      { shallow: true }
    );
  };

  const fetchDashboardPartner = async () => {
    const res = await dashboardPartnerService.getDashboardPartner(storeId)
    setDashboardPartner(res?.data?.data)
  }
  useEffect(() => {
    fetchDashboardPartner();

    const interval = setInterval(() => {
      fetchDashboardPartner();
    }, 60000);

    return () => clearInterval(interval);
  }, [storeId]);

  return (
    <Box sx={{}}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', paddingRight: 4 }}>
        <TitleTypography>Trang chủ</TitleTypography>
        {/* <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Card sx={{ padding: '6px 14px', backgroundColor: 'blue', color: 'white' }}>
            <Typography>
              Số dư: 10.000.000d
            </Typography>
          </Card>
          <Card sx={{ display: 'flex', margin: '0 25px' }}>
            <Typography sx={{ marginRight: '4px' }}>Trợ lý AI</Typography>
            <SmartToyIcon color='info' />
          </Card>
          <Card>
            <Typography>
              Hỗ trợ
            </Typography>
          </Card>
        </Box> */}
      </Box>
      <Stack sx={{ padding: 4, marginRight: 3.8, borderRadius: '20px', marginTop: 2, border: '1px solid #f6f8fa' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', marginBottom: '20px' }}>
          <TitleTypography >Tổng quan hệ thống</TitleTypography>
          <Box sx={{ fontSize: '14px', display: 'flex', alignItems: 'center' }}>
            <Typography>Cập nhật {formatDateTimeDisplay(dashboardParter?.today)}</Typography>
            <ReplayOutlined style={{ fontSize: '16px', color: 'blue', marginLeft: '5px' }} />
          </Box>
        </Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', width: '70%', justifyContent: 'space-between' }}>
            <Grid container spacing={2} sx={{ display: 'flex', justifyContent: 'space-between', flexWrap: 'nowrap', marginRight: 2.2 }}>
              {dashboardParter?.infoCount?.map((item, index) => (
                <Grid sx={{ width: '25%', height: '100%' }} key={index}>
                  <Card
                    sx={{
                      textAlign: "center",
                      padding: '10px',
                      height: '180px',
                      borderRadius: '20px'
                    }}
                  >
                    <CardContent sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'space-between', height: '100%', borderRadius: '20px' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'nowrap' }}>
                        <Typography sx={{ textAlign: 'left', fontSize: '16px', marginRight: '4px' }}>
                          <DynamicIcon name={item?.iconName} />
                        </Typography>
                        <Typography fontWeight='700' sx={{ textAlign: 'left', fontSize: '14px' }}>
                          {item?.title}
                        </Typography>
                      </Box>
                      <Typography fontWeight="700" fontSize='20px' sx={{ fontSize: '20px', textAlign: 'center' }}>
                        {index === 0 ? formatCurrency(Number(item.valueToday)) + "đ" : item.valueToday}
                      </Typography>
                      <Typography color="textSecondary" fontWeight='400' sx={{ fontSize: '14px', textAlign: 'left' }}>
                        Hôm qua: {index === 0 ? formatCurrency(Number(item.valueYesterday)) + "đ" : item.valueYesterday}

                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
            <Box sx={{ marginTop: 13 }}>
              <BarChart
                series={[
                  {
                    data: dashboardParter?.totalUserByDayChart || [],
                    label: "Khách hàng hoạt động"
                  },
                  {
                    data: dashboardParter?.totalUserOrderByDayChart || [],
                    label: "Khách hàng có mua hàng",
                  },
                ]}
                height={340}
                xAxis={[{ data: dashboardParter?.listDayChart || [], scaleType: 'band' }]}
                yAxis={[
                  {
                    max: Math.max(...(dashboardParter?.totalUserByDayChart || []), ...(dashboardParter?.totalUserOrderByDayChart || [])) * 1.5
                  }
                ]}
                margin={{ top: 10, bottom: 30, left: 40, right: 10 }}
              />
            </Box>
          </Box>
          <Box sx={{ width: '30%', border: '1px solid #f6f8fa', borderRadius: '20px' }}>
            <CardContent sx={{ height: '100%', borderRadius: '20px' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'nowrap', justifyContent: 'space-between', marginBottom: 2 }}>
                <Typography sx={{ textAlign: 'left', fontSize: '16px', fontWeight: 500 }}>
                  Top 10 sản phẩm bán chạy
                </Typography>
                {/* <Typography>Chi tiết</Typography> */}
              </Box>
              {/* map list */}
              <Box>

                {dashboardParter?.top10ItemPopulars?.map(item => (
                  <Box key={item.key} sx={{
                    display: 'flex', justifyContent: 'space-between',
                    alignItems: 'center', marginBottom: 1, borderBottom: '1px solid #4541',
                    paddingBottom: 1
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', width: '80%' }}>
                      <Box sx={{ width: 38, height: 38, minWidth: 38, position: 'relative' }}>
                        <img
                          src={item?.image}
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover',
                            border: '1px solid #4544',
                            borderRadius: '50px',
                            position: 'absolute',
                            top: 0,
                            left: 0
                          }}
                        />
                      </Box>
                      <Tooltip title={item?.name} arrow>
                        <Typography sx={{
                          marginLeft: 1,
                          fontSize: 15,
                          marginRight: 2,
                          lineHeight: 1,
                          fontWeight: 500,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          width: '100%'
                        }}>{item?.name}</Typography>
                      </Tooltip>
                    </Box>
                    <Typography sx={{ fontWeight: 500 }}>{item?.totalQuantity}</Typography>
                  </Box>

                ))}

              </Box>
            </CardContent>

          </Box>
        </Box>

      </Stack>
      <Card sx={{ p: 2, mt: 2 }}>
        <Tabs value={tabIndex} onChange={handleTabChange} sx={{ mb: 2 }}>
          <Tab label={t(tokens.contentManagement.tabs.shopOnline)} />
          <Tab label={t(tokens.contentManagement.tabs.shopOffline)} />
        </Tabs>
        {tabIndex === 0 && <ShopOnline dashboardParter={dashboardParter} />}
        {tabIndex === 1 && <ShopOffline dashboardParter={dashboardParter} />}
      </Card>
    </Box>
  );
};

Page.getLayout = (page) => <DashboardLayout>{page}</DashboardLayout>;

export default Page;