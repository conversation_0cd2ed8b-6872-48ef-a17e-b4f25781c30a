/**
 * Format a number to currency string
 * @param value - Number to format
 * @returns Formatted string with thousand separators
 * @example
 * formatCurrency(1000000) // "1,000,000"
 * formatCurrency(1234.5) // "1,234"
 */
export const formatCurrency = (value: number | undefined): string => {
  if (value === undefined || value === null) return '';

  // Convert to integer and format with thousand separators
  const formattedValue = Math.round(value)
    .toString()
    .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  return formattedValue;
};

/**
 * Parse a currency string to number
 * @param value - Currency string to parse
 * @returns Parsed number value
 * @example
 * parseCurrency("1,000,000") // 1000000
 * parseCurrency("1,234") // 1234
 */
export const parseCurrency = (value: string): number => {
  // Remove all non-digit characters except decimal point
  const cleanValue = value.replace(/[^0-9]/g, '');

  // Convert to number, default to 0 if invalid
  const numberValue = cleanValue ? parseInt(cleanValue, 10) : 0;
  return numberValue;
};
