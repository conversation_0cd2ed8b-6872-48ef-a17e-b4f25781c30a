import React, { useEffect, useState } from "react";
import {
  Box,
  Typography,
  IconButton,
  TextField,
  ListItem,
  ListItemText,
  Checkbox,
  Card,
  Divider,
  Link as MuiLink,
  Link,
  Tooltip,
  Button,
  CircularProgress,
} from "@mui/material";
import LocalShippingIcon from "@mui/icons-material/LocalShipping";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import ToggleOnIcon from "@mui/icons-material/ToggleOn";
import ToggleOffIcon from "@mui/icons-material/ToggleOff";
import Grid from "@mui/system/Grid";
import { paths } from "@/src/paths";
import {
  AhamovePaymentMethod,
  defaultShopSetting,
  IShopSettingCreate,
  useShopSetting,
} from "@/src/api/hooks/shop-setting/use-shop-setting";
import { useStore } from "react-redux";
import { useStoreId } from "@/src/hooks/use-store-id";
import EditShippingProviderDialog, { ShippingProviderFormData } from "./EditShippingProviderDialog";
import useSnackbar from "@/src/hooks/use-snackbar";
import _ from "lodash";
import { useBranch } from "@/src/api/hooks/branch/use-branch";
import { Branch } from "@/src/pages/dashboard/settings/location";
import { useTransportMethod } from "@/src/api/hooks/transport-method/use-transport-method";
import {
  TransportMethod,
  TransportServiceType,
  UpdateTransportSettingRequest,
} from "@/src/api/types/transport-method.types";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import PriceTextField from "../../price-text-field";
import { formatCurrency, parseCurrency } from "@/src/utils/format-number";
import { useShop } from "@/src/api/hooks/shop/use-shop";
import { usePathname } from "next/navigation";
const LeftColumn = ({ children }) => (
  <Grid size={{ xs: 12, md: 4 }}>
    <Box p={1}>{children}</Box>
  </Grid>
);

const RightColumn = ({ children }) => (
  <Grid size={{ xs: 12, md: 8 }}>
    <Box p={1}>{children}</Box>
  </Grid>
);

interface ShippingProviderProps {
  provider: TransportMethod;
  handleToggleProvider: (provider: TransportMethod, serviceType: TransportServiceType) => void;
  handleEdit: (provider: TransportMethod) => void;
  serviceType: TransportServiceType;
  isGranted: boolean;
}

const ShippingProvider: React.FC<ShippingProviderProps> = ({
  provider,
  handleToggleProvider,
  handleEdit,
  serviceType,
  isGranted,
}) => {
  const pathname = usePathname();
  const isEnabled =
    provider.serviceOptions?.some(
      (option) => option.serviceType === serviceType && option.isEnabled
    ) ?? false;
  return (
    <ListItem sx={{ px: 0, py: 0.5 }}>
      <LocalShippingIcon sx={{ mr: 1, color: "#2654FE", fontSize: 30 }} />
      <ListItemText primary={provider.transportName} />
      <Box sx={{ display: "flex", alignItems: "center" }} gap={2}>
        <Tooltip title={!isGranted ? "Bạn không có quyền sửa" : ""}>
          <span>
            <IconButton
              disabled={!isGranted}
              onClick={() => handleToggleProvider(provider, serviceType)}
              size="large"
              color={isEnabled ? "primary" : "default"}
            >
              {isEnabled ? (
                <ToggleOnIcon sx={{ fontSize: 35 }} />
              ) : (
                <ToggleOffIcon sx={{ fontSize: 35 }} />
              )}
            </IconButton>
          </span>
        </Tooltip>
        {isGranted ? (
          <Typography
            sx={{ mr: 1, cursor: "pointer", color: "#2654FE" }}
            onClick={() => handleEdit(provider)}
          >
            Sửa
          </Typography>
        ) : (
          <Tooltip title="Bạn không có quyền sửa">
            <Typography sx={{ mr: 1, cursor: "pointer", opacity: 0.5 }}>Sửa</Typography>
          </Tooltip>
        )}
      </Box>
    </ListItem>
  );
};
const LocalProvider = ({ provider, selectedLocalProvider, handleToggleLocalProvider }) => (
  <ListItem sx={{ px: 0, py: 0.5 }}>
    <LocalShippingIcon sx={{ mr: 1, color: "#2654FE", fontSize: 30 }} />
    <ListItemText primary={provider} />
    <IconButton onClick={() => handleToggleLocalProvider(provider)} size="large">
      {selectedLocalProvider === provider ? (
        <ToggleOnIcon color="primary" sx={{ fontSize: 35 }} />
      ) : (
        <ToggleOffIcon color="action" sx={{ fontSize: 35 }} />
      )}
    </IconButton>
  </ListItem>
);

const StorePickup = ({ storeEnabled, handleToggleStore, branch }) => (
  <ListItem sx={{ px: 0, py: 0.5 }}>
    <LocationOnIcon sx={{ mr: 1, color: "primary.main", ml: 1, fontSize: 30 }} />
    <ListItemText
      primary={branch?.branchName}
      secondary={[branch?.address, branch?.wardName, branch?.districtName, branch?.provinceName]
        .filter(Boolean)
        .join(", ")}
    />
    {/* <IconButton onClick={handleToggleStore} size="large">
      {storeEnabled ? (
        <ToggleOnIcon color="primary" sx={{ fontSize: 35 }} />
      ) : (
        <ToggleOffIcon color="action" sx={{ fontSize: 35 }} />
      )}
    </IconButton> */}
  </ListItem>
);

type AhamoveConfig = {
  paymentMethod: AhamovePaymentMethod | null;
  phoneNumber: string;
  isEnabled: boolean;
};

// Tạo một type tổng hợp cho tất cả các provider
type ProviderConfig = {
  ahamoveBike: AhamoveConfig; // Sử dụng index signature
};

export const defaultProviderConfig: ProviderConfig = {
  ahamoveBike: {
    isEnabled: false,
    paymentMethod: "CASH",
    phoneNumber: "",
  },
};

export default function CombinedShipping() {
  const pathname = usePathname();
  const [storeEnabled, setStoreEnabled] = useState<boolean>(true);

  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [currentProvider, setCurrentProvider] = useState<TransportMethod>(null);
  const [listBranch, setListBranch] = useState<Branch[]>([]);
  const { getBranches } = useBranch();
  const { detailShop, updateShopDelivery, loading } = useShop();

  const [shopTransportPrice, setShopTransportPrice] = useState<number>(0);
  const [transportMethods, setTransportMethods] = useState<TransportMethod[]>([]);

  const expressTransports: TransportMethod[] = transportMethods.filter((transport) =>
    transport.serviceOptions?.some((option) => option.serviceType === "Express")
  );
  const standardTransports: TransportMethod[] = transportMethods.filter((transport) =>
    transport.serviceOptions?.some((option) => option.serviceType === "Standard")
  );

  const { getListTransportMethod, updateTransportMethod, signUpAhamove } = useTransportMethod();
  const storeId = useStoreId();

  const snackbar = useSnackbar();

  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  const fetchBranches = async (shopId) => {
    try {
      const res = await getBranches(0, 99, shopId);
      if (res?.data) {
        setListBranch(res?.data?.data);
      }
    } catch (error) {
      console.error("Error fetching branches:", error);
    }
  };

  const fetchListTransportMethod = async (shopId) => {
    try {
      const res = await getListTransportMethod({ shopId });
      if (res?.data) {
        const { data }: { data: TransportMethod[] } = res.data;
        setTransportMethods(data);
      }
    } catch (error) {
      console.log("🚀 ~ fetchListTransportMethod ~ error:", error);
    }
  };

  const handleToggleSelfDelivery = () => {};

  const handleToggleStore = () => {
    setStoreEnabled((prev) => !prev);
  };

  const handleEdit = (provider: TransportMethod) => {
    setCurrentProvider(provider);
    setEditDialogOpen(true);
  };

  const handleSubmitConfig = async (data: ShippingProviderFormData) => {
    const updateData: UpdateTransportSettingRequest = {
      transportMethodId: currentProvider.transportMethodId,
      transportConfig: currentProvider.transportConfig,
    };

    try {
      if (currentProvider.transportCode == "Ahamove") {
        const ahamoveSignupResult = await signUpAhamove({
          phoneNumber: data.phoneNumber,
          shopId: storeId,
        });
        if (!ahamoveSignupResult?.data) return;
        updateData.transportConfig.ahamove.phoneNumber = data.phoneNumber;
      } else if (currentProvider.transportCode == "JTExpress") {
        updateData.transportConfig.jtExpress = {
          customerCode: data.customerCode,
          password: data.password,
        };
      }

      const response = await updateTransportMethod(updateData);

      if (response.data) {
        await fetchListTransportMethod(storeId);
        snackbar.success("Cập nhật cấu hình thành công");
        handleCloseDialog();
      }
    } catch (error) {
      console.log("🚀 ~ handleSubmitConfig ~ error:", error);
    }
  };

  function isTransportConfigInvalid(transportMethod: TransportMethod): boolean {
    const { transportCode, transportConfig } = transportMethod;

    switch (transportCode) {
      case "Ahamove":
        return !transportConfig.ahamove?.phoneNumber;
      case "JTExpress":
        const jt = transportConfig.jtExpress;
        return !jt?.customerCode || !jt?.password;
      default:
        return false;
    }
  }

  const handleToggleProvider = async (
    transportMethod: TransportMethod,
    serviceType: TransportServiceType
  ) => {
    const currentServiceOption = transportMethod.serviceOptions?.find(
      (opt) => opt.serviceType === serviceType
    );
    const willEnable = !currentServiceOption.isEnabled;

    if (willEnable && isTransportConfigInvalid(transportMethod)) {
      setCurrentProvider(transportMethod);
      setEditDialogOpen(true);
      return;
    }
    const newUpdate = {
      transportMethodId: transportMethod.transportMethodId,
      serviceOptions: [
        {
          serviceType: serviceType,
          isEnabled: !currentServiceOption.isEnabled,
        },
      ],
    };

    // transportSettings[provider]["isEnabled"] = !transportSettings[provider]["isEnabled"];
    const response = await updateTransportMethod(newUpdate);
    if (response.data) {
      await fetchListTransportMethod(storeId);
      snackbar.success("Cập nhật cấu hình thành công");
    }
  };

  const handleCloseDialog = () => {
    setEditDialogOpen(false);
    setCurrentProvider(null);
  };

  const handlePriceChange = (event) => {
    const newValue = parseCurrency(event.target.value);
    setShopTransportPrice(newValue);
  };

  const handleSaveTransport = async () => {
    const res = await updateShopDelivery({ shopId: storeId, transportPrice: shopTransportPrice });
    if (res?.data) {
      await fetchShopDetail();
      snackbar.success("Cập nhật thành công");
    }
  };

  const fetchShopDetail = async () => {
    try {
      const detail = await detailShop(storeId);
      const shopDetail = detail?.data as any;
      if (shopDetail) {
        setShopTransportPrice(shopDetail.transportPrice);
      }
    } catch (error) {
    } finally {
    }
  };

  useEffect(() => {
    if (storeId) {
      fetchBranches(storeId);
      fetchListTransportMethod(storeId);
      fetchShopDetail();
    }
  }, [storeId]);

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Grid container spacing={1} sx={{ mt: 5 }}>
        <LeftColumn>
          <Typography fontWeight="bold" marginBottom={1} variant="h6">
            Giao hàng nhanh
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Địa điểm giao hàng được hiển thị khi người tiêu dùng đặt hàng và phí giao hàng phải trả
          </Typography>
        </LeftColumn>

        <RightColumn>
          <Card sx={{ p: 1 }}>
            <Box sx={{ mb: 2, padding: 1, backgroundColor: "white", mt: -2 }}>
              <Typography variant="h6" gutterBottom>
                Nhà cung cấp dịch vụ vận chuyển
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Đối với nhà cung cấp dịch vụ vận chuyển, bạn cần chọn phương thức đặt hàng trước để
                nâng cao hiệu quả vận chuyển.
              </Typography>
              {expressTransports.map((transport, index) => (
                <ShippingProvider
                  key={index}
                  provider={transport}
                  handleToggleProvider={handleToggleProvider}
                  handleEdit={handleEdit}
                  serviceType="Express"
                  isGranted={isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                />
              ))}

              <EditShippingProviderDialog
                open={editDialogOpen}
                onClose={handleCloseDialog}
                onSubmit={handleSubmitConfig}
                provider={currentProvider}
              />
            </Box>
          </Card>
        </RightColumn>
      </Grid>

      <Divider sx={{ marginBottom: 2, marginTop: 2 }} />

      <Grid container spacing={1}>
        <LeftColumn>
          <Typography fontWeight="bold" marginBottom={1} variant="h6">
            Giao hàng tận nơi
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Quản lý phạm vi khu vực và quy tắc tính phí có thể giao hàng, đồng thời giao hàng trực
            tiếp đến khách hàng địa phương.
          </Typography>
        </LeftColumn>

        <RightColumn>
          <Card sx={{ p: 1, marginBottom: 1 }}>
            <Box sx={{ mb: 2, padding: 1, mt: -2 }}>
              <Typography variant="h6" gutterBottom>
                Lựa chọn nhà cung cấp giao hàng địa phương (Giao nhanh trong ngày)
              </Typography>
              {standardTransports.map((transport, index) => (
                <ShippingProvider
                  key={index}
                  provider={transport}
                  handleToggleProvider={handleToggleProvider}
                  handleEdit={handleEdit}
                  serviceType="Standard"
                  isGranted={isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                />
              ))}
            </Box>
          </Card>
        </RightColumn>
      </Grid>

      <Divider sx={{ marginBottom: 2, marginTop: 2 }} />

      <Grid container spacing={1}>
        <LeftColumn>
          <Typography fontWeight="bold" marginBottom={1} variant="h6">
            Nhận tại cửa hàng
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Sau khi chức năng tự lấy hàng tại nơi được bật, các điểm tự lấy hàng sẽ được hiển thị
            tại đây. Khách hàng có thể chọn điểm tự lấy hàng gần nhất. Sau khi khách hàng đặt hàng,
            bạn cần giao hàng đến điểm tự lấy hàng được chỉ định càng sớm càng tốt.
          </Typography>
        </LeftColumn>

        <RightColumn>
          <Card sx={{ p: 1, marginBottom: 0 }}>
            <Box
              sx={{
                padding: 1,
                display: "flex",
                alignItems: "flex-start",
                gap: 1,
                marginTop: -2,
              }}
            >
              <Checkbox size="large" disabled />
              <Box>
                <Typography variant="body1" fontWeight="bold">
                  Hiển thị ưu tiên
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Khi khách hàng đặt hàng và chọn phương thức giao hàng, "Nhận tại cửa hàng" sẽ được
                  hiển thị đầu tiên.
                </Typography>
              </Box>
            </Box>
            <Box sx={{ mb: 3, padding: 1, mt: 1 }}>
              <Grid container justifyContent="space-between" alignItems="center">
                <Grid>
                  <Typography variant="body1" gutterBottom>
                    Danh sách điểm bán
                  </Typography>
                </Grid>
                <Grid>
                  {isGranted("/dashboard/settings/location/", PERMISSION_TYPE_ENUM.Add) ? (
                    <Link href={paths.settings.location.add}>
                      <MuiLink
                        sx={{
                          color: "#2654FE",
                          textDecoration: "none",
                          "&:hover": {
                            textDecoration: "underline",
                          },
                        }}
                      >
                        Thêm điểm bán
                      </MuiLink>
                    </Link>
                  ) : (
                    <Tooltip
                      title={
                        !isGranted("/dashboard/settings/location/", PERMISSION_TYPE_ENUM.Add)
                          ? "Bạn không có quyền thêm "
                          : ""
                      }
                    >
                      <span>
                        <Link>
                          <MuiLink
                            sx={{
                              color: "gray",
                              textDecoration: "none",
                              "&:hover": {
                                textDecoration: "underline",
                              },
                            }}
                          >
                            Thêm điểm bán
                          </MuiLink>
                        </Link>
                      </span>
                    </Tooltip>
                  )}
                </Grid>
              </Grid>
              {listBranch.map((item, index) => {
                return (
                  <Box key={index}>
                    <StorePickup
                      storeEnabled={storeEnabled}
                      handleToggleStore={handleToggleStore}
                      branch={item}
                    />
                    {index < listBranch.length - 1 && <Divider />}
                  </Box>
                );
              })}
            </Box>
          </Card>
        </RightColumn>
      </Grid>

      <Divider sx={{ marginBottom: 2, mt: 2 }} />

      <Grid container spacing={1}>
        <LeftColumn>
          <Typography fontWeight="bold" marginBottom={1} variant="h6">
            Tự vận chuyển
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Kích hoạt chức năng này nếu cửa hàng tự giao hàng hoặc gói ship thủ công
          </Typography>
        </LeftColumn>

        <RightColumn>
          <Card sx={{ p: 1, marginBottom: 0 }}>
            {/* <Box
              sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", mb: 1 }}
            >
              <Typography variant="h6" gutterBottom sx={{ mb: 1, mt: -2 }}>
                Cửa hàng tự vận chuyển
              </Typography>
              <IconButton onClick={handleToggleSelfDelivery} size="large">
                
                <ToggleOffIcon color="action" sx={{ fontSize: 35 }} />
              </IconButton>
            </Box> */}
            <Box>
              <Typography variant="body1" sx={{ mb: 0.5 }}>
                Phí vận chuyển
              </Typography>
              <Box sx={{ display: "flex", mb: 0.5, flexDirection: "column" }}>
                <Tooltip
                  title={
                    !isGranted(pathname, PERMISSION_TYPE_ENUM.Edit) ? "Bạn không có quyền sửa" : ""
                  }
                >
                  <span>
                    <TextField
                      fullWidth
                      size="small"
                      onChange={handlePriceChange}
                      disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                      value={formatCurrency(shopTransportPrice)}
                      InputProps={{
                        endAdornment: <Typography>đ</Typography>,
                      }}
                    />
                  </span>
                </Tooltip>
                <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
                  <Tooltip
                    title={
                      !isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)
                        ? "Bạn không có quyền sửa"
                        : ""
                    }
                  >
                    <span>
                      <Button
                        variant="contained"
                        disabled={loading || !isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                        onClick={handleSaveTransport}
                        startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
                      >
                        Lưu
                      </Button>
                    </span>
                  </Tooltip>
                </Box>
              </Box>
            </Box>
          </Card>
        </RightColumn>
      </Grid>
    </Box>
  );
}
