import React from 'react';
import { Box, Typography, useMediaQuery, useTheme } from '@mui/material';
import Grid from '@mui/system/Grid';

const exampleTexts = [
  'Người tiêu dùng mua đơn hàng 1.000.000đ',
  'Người tiêu dùng được tích lũy 100 điểm',
  '<PERSON><PERSON> người tiêu dùng mua sắm trong tương lai 100 điểm = 100.000đ',
  'Người tiêu dùng có thể đổi điểm thành voucher theo chương trình của cửa hàng'
];

const ExampleSectionContent = ({ selectedTier }) => (
  <>
    {exampleTexts.map((text, index) => (
      <Grid size={{ xs: 12 }} key={index}>
        <Box sx={{ display: 'flex', alignItems: 'center', marginBottom: 3 }}>
          <img src="/assets/customerlist/Vector.png" alt="Icon" style={{ marginRight: '8px' }} />
          <Typography variant="body2">{text}</Typography>
        </Box>
      </Grid>
    ))}
    {selectedTier === 'multi' && (
      <Grid size={{ xs: 12 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', marginBottom: 3 }}>
          <img src="/assets/customerlist/Vector.png" alt="Icon" style={{ marginRight: '8px' }} />
          <Typography variant="body2">
            Người tiêu dùng chi tiêu lũy kế trong vòng 12 tháng tính theo năm tài chính đủ điều kiện sẽ được nâng hạng
            và tỉ lệ tích lũy đủ điều kiện sẽ được nâng hạng
          </Typography>
        </Box>
      </Grid>
    )}
  </>
);

const HighlightedText = () => {
  const theme = useTheme();
  return (
    <Grid size={{ xs: 12 }}>
      <Typography
        sx={{
          position: 'absolute',
          right: '5%',
          top: '3%',
          color: theme.palette.success.main,
          textDecorationColor: theme.palette.warning.main,
          fontSize: '2rem',
          fontFamily: "'Roboto', sans-serif",
          fontWeight: '700',
          textTransform: 'uppercase',
          background: `linear-gradient(to right, ${theme.palette.success.main}, ${theme.palette.warning.main})`,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          transition: 'all 0.3s ease',
          '&:hover': {
            color: theme.palette.warning.main,
            transform: 'scale(1.2)',
            textShadow: '2px 2px 5px rgba(0, 0, 0, 0.3)'
          }
        }}
      >
        10%
      </Typography>
    </Grid>
  );
};

const ExampleSectionHeader = () => (
  <Grid size={{ xs: 12 }}>
    <Typography variant="subtitle1" sx={{ fontWeight: 'bold', marginBottom: 1, fontSize: '1.5rem' }}>
      Ví dụ minh họa
    </Typography>
    <Typography variant="body2" sx={{ marginBottom: 1, fontSize: '0.7rem' }}>
      Điểm được tính tích lũy dựa trên tổng giá trị đơn hàng đã <br /> trừ giảm giá và phí vận vận chuyển
    </Typography>
  </Grid>
);

const ExampleSection = ({ selectedTier }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Grid
      container
      spacing={2}
      sx={{
        backgroundColor: theme.palette.mode === 'dark' ? '#424242' : '#FFF8E1', // Nền màu vàng cho chế độ sáng và màu tối cho chế độ tối
        padding: 3,
        position: 'relative',
        borderRadius: 2,
        border: `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#FFF8E1'}` // Đổi màu viền theo chế độ
      }}
      flex={1}
    >
      <ExampleSectionHeader />
      <ExampleSectionContent selectedTier={selectedTier} />
      <HighlightedText />
      <Grid size={{ xs: 12 }}>
        <img
          src="/assets/customerlist/noto-v1_money-bag.png"
          alt="Minh họa"
          style={{
            position: 'relative',
            marginLeft: '50%',
            marginBottom: '-30%',
            transform: 'scale(0.8)'
          }}
        />
      </Grid>
    </Grid>
  );
};

export default ExampleSection;
