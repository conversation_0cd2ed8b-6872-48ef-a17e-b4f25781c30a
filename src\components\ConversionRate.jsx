import React, { useState } from 'react';
import { Box, Typography, Select, MenuItem, FormControl, InputLabel } from '@mui/material';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  LineElement,
  PointElement,
  CategoryScale,
  LinearScale,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
const convertData = Array.from({ length: 31 }, (_, index) => ({
  day: index + 1,
  source: Math.floor(Math.random() * 8000) + 2000,
  customer: Math.floor(Math.random() * 6000) + 1000,
}));

ChartJS.register(
  LineElement,
  PointElement,
  CategoryScale,
  LinearScale,
  Title,
  Tooltip,
  Legend
);

const ConversionRate = ({ selectedMonth, setSelectedMonth, handleMonthChange, dataChart }) => {

  if (!dataChart) return null;

  const chartData = {
    labels: dataChart.day,
    datasets: [
      {
        label: 'Người dùng',
        data: dataChart.user,
        borderColor: '#2e96ff',
        backgroundColor: '#2e96ff',
        tension: 0.4,
      },
      {
        label: 'Khách hàng',
        data: dataChart.customer,
        borderColor: '#02b2af',
        backgroundColor: '#02b2af',
        tension: 0.4,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: `Tỉ lệ chuyển đổi khách hàng - Tháng ${selectedMonth}`,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0,
        },
      },
    },
  };
  return (
    <Box
      sx={{
        p: 2,
        boxShadow: '6px 6px 54px 0 #0000000D !important;',
        borderRadius: '15px',
        marginTop: '35px',
        background: '#fff'
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 1,
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontSize: '18px',
            fontWeight: 600,
            color: '#333',
          }}
        >
          Chuyển đổi
        </Typography>
        <FormControl sx={{ minWidth: 100 }}>
          <Select
            sx={{ height: '36px' }}
            value={selectedMonth}
            onChange={handleMonthChange}
            displayEmpty
          >
            <MenuItem value="1">Tháng 1</MenuItem>
            <MenuItem value="2">Tháng 2</MenuItem>
            <MenuItem value="3">Tháng 3</MenuItem>
            <MenuItem value="4">Tháng 4</MenuItem>
            <MenuItem value="5">Tháng 5</MenuItem>
            <MenuItem value="6">Tháng 6</MenuItem>
            <MenuItem value="7">Tháng 7</MenuItem>
            <MenuItem value="8">Tháng 8</MenuItem>
            <MenuItem value="9">Tháng 9</MenuItem>
            <MenuItem value="10">Tháng 10</MenuItem>
            <MenuItem value="11">Tháng 11</MenuItem>
            <MenuItem value="12">Tháng 12</MenuItem>
          </Select>
        </FormControl>
      </Box>

      <Box sx={{ height: 300, width: '100%' }}>
        <Line data={chartData} options={chartOptions} />
      </Box>
      {/* <ResponsiveContainer width="100%" height={250}>
        {' '}
        <AreaChart data={convertData} margin={{ top: 10, right: 10, left: -20, bottom: 0 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb00" />
          <XAxis
            dataKey="day"
            tick={{ fontSize: 12, fill: '#2B303466', fontWeight: '600' }}
            tickLine={false}
            axisLine={{ stroke: '#e5e7eb00' }}
          />
          <YAxis
            tickFormatter={(value) => `${value / 1000}k`}
            domain={[0, 10000]}
            tick={{ fontSize: 12, fill: '#2B303466', fontWeight: '600' }}
            tickLine={false}
            axisLine={{ stroke: '#e5e7eb00' }}
          />
          <Tooltip
            formatter={(value) => `${value}`}
            contentStyle={{
              fontSize: '12px',
              borderRadius: '4px',
              border: 'none',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
            }}
          />
          <Legend
            verticalAlign="bottom"
            height={36}
            iconType="circle"
            formatter={(value) => <span style={{ fontSize: '12px', color: '#666' }}>{value}</span>}
          />
          <Area
            type="monotone"
            dataKey="source"
            name="Người dùng"
            stackId="1"
            stroke="#FF8F6D"
            fill="#FF8F6D"
          // fillOpacity={0.4}
          />
          <Area
            type="monotone"
            dataKey="customer"
            name="Khách hàng"
            stackId="1"
            stroke="#8280FF"
            fill="#8280FF"
          // fillOpacity={0.4}
          />
        </AreaChart>
      </ResponsiveContainer> */}
    </Box>
  );
};

export default ConversionRate;
