import React, { useState, useEffect } from "react";
import {
  Box,
  Button,
  Card,
  TextField,
  Typography,
  IconButton,
  FormControlLabel,
  CircularProgress,
  Grid,
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import DashboardLayout from "../../../../layouts/dashboard";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import { tokens } from "@/src/locales/tokens";
import TitleTypography from "@/src/components/title-typography/title-typography";
import { useStoreId } from "@/src/hooks/use-store-id";
import useSnackbar from "@/src/hooks/use-snackbar";
import { paths } from "@/src/paths";
import { useFormik } from "formik";
import * as Yup from "yup";
import { StorageService } from "nextjs-api-lib";
import CustomSwitch from "@/src/components/custom-switch";
import { logger } from "@/src/utils/logger";
import { useArticleCategory } from "@/src/api/hooks/dashboard/store/use-article-category";
import { Padding } from "@/src/styles/CommonStyle";
import { FileDropzone } from "@/src/components/file-dropzone";
import { useMedia } from "@/src/api/hooks/media/use-media";
import { FILE_SIZE_2MB, MAX_FILE_IMAGE } from "@/src/constants/constant";
import { CreateFileGroupRequest, GetGroupFileRequest, RefType } from "@/src/api/types/media.types";
import { CommonMediaUpload } from "@/src/components/common-media-upload";
import { ExistingMediaFile, scrollToTop } from "../../product/product-management/create/create";
import { ImageProcessor } from "@/src/utils/image-processor";
import { FileType } from "@/src/constants/file-types";
import { ArticleCategoryDto } from "@/src/api/services/dashboard/store/article-category.service";

interface FormValues {
  categoryName: string;
  typePublish: string;
  image: File | string;
  submit: string | null;
}

const CreateArticleCategory: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const router = useRouter();
  const snackbar = useSnackbar();
  const storeId = useStoreId();
  const [isEdit, setIsEdit] = useState(false);
  const [isPublished, setIsPublished] = useState(false);

  const [existingFiles, setExistingFiles] = useState<ExistingMediaFile[]>([]);
  const [localFiles, setLocalFiles] = useState<File[]>([]);
  const { createArticleCategory, updateArticleCategory, getArticleCategoryDetail } =
    useArticleCategory();
  const [partnerId, setPartnerId] = useState("");
  const [errorMsg, setErrorMsg] = useState<string>("");

  const validationSchema = Yup.object({
    categoryName: Yup.string()
      .required(t(tokens.contentManagement.articleCategory.create.form.nameRequired))
      .max(255, t(tokens.contentManagement.articleCategory.create.form.nameMaxLength)),
    // image: Yup.object().required("Hình ảnh danh mục là bắt buộc"),
  });

  const formik = useFormik<FormValues>({
    initialValues: {
      categoryName: "",
      typePublish: "UnPublish",
      submit: null,
      image: null,
    },
    validationSchema,
    onSubmit: handleSubmit,
  });

  useEffect(() => {
    const storedPartnerId = StorageService.get("partnerId") || "";
    if (storedPartnerId) {
      setPartnerId(storedPartnerId as string);
    }
  }, []);

  const fetchDetailCategoryArticle = async () => {
    if (router?.query?.id) {
      const id =
        typeof router.query.id === "string"
          ? router.query.id
          : Array.isArray(router.query.id)
          ? router.query.id[0]
          : "";
      if (id) {
        const res = await getArticleCategoryDetail(id);
        if (res?.status === 200) {
          setIsEdit(true);
          formik.setValues({
            categoryName: res?.data?.data?.categoryName || "",
            typePublish: res?.data?.data?.typePublish || "UnPublish",
            image: res?.data?.data?.image || "",
            submit: null,
          });
          setIsPublished(res?.data?.data?.typePublish === "Publish");
          setExistingFiles([res?.data?.data?.image]);
        }
      }
    }
  };

  useEffect(() => {
    fetchDetailCategoryArticle();
  }, [router?.query?.id]);

  async function handleSubmit(values: FormValues) {
    try {
      if (localFiles.length + existingFiles.length === 0) {
        setErrorMsg("Hãy chọn ít nhất 1 ảnh");
        scrollToTop();
        return;
      }
      let uploadedFiles = [];
      if (localFiles.length > 0) {
        for (const file of localFiles) {
          const processedFile = await ImageProcessor.processImage(file);
          const data: CreateFileGroupRequest = {
            FileUpload: processedFile,
            ShopId: storeId,
            GroupFileId: defaultGroupId,
            RefType: RefType.ArticleCategory,
            RefId: "",
          };
          const response = await uploadFile(data);
          uploadedFiles.push(response.data);
        }
      }
      const newImages = uploadedFiles.map((file) => ({
        type: file.type === "IMAGE" ? FileType.IMAGE : FileType.VIDEO,
        link: file.url || file.link,
        mediaFileId: file.mediaFileId,
      }));

      const payload: ArticleCategoryDto = {
        articleCategoryId: isEdit ? (router.query.id as string) : "",
        parentId: "",
        shopId: storeId,
        categoryName: values.categoryName,
        typePublish: isPublished ? "Publish" : "UnPublish",
        categoryLevel: "",
        image:
          newImages.length > 0
            ? newImages[0]
            : existingFiles[0]
            ? {
                type: FileType.IMAGE,
                link:
                  (existingFiles[0] as any).link ||
                  (existingFiles[0] as any).url ||
                  existingFiles[0],
                mediaFileId:
                  (existingFiles[0] as any).mediaFileId || (existingFiles[0] as any).id || "",
              }
            : undefined,
        categoryDesc: "",
        status: isPublished ? "Actived" : "InActived",
      };

      if (isEdit) {
        await updateArticleCategory(payload);
        snackbar.success(t(tokens.contentManagement.articleCategory.edit.success));
      } else {
        await createArticleCategory(payload);
        snackbar.success(t(tokens.contentManagement.articleCategory.create.success));
      }

      router.push(paths.dashboard.store.contentManagement);
    } catch (error) {
      logger.error(error);
    }
  }

  const handleCancel = () => {
    router.back();
  };

  const handlePublishChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsPublished(event.target.checked);
    formik.setFieldValue("typePublish", event.target.checked ? "Publish" : "UnPublish");
  };
  const { uploadFile, getGroups } = useMedia();
  const [defaultGroupId, setDefaultGroupId] = useState<string>("");

  const [newImageFile, setNewImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);

  useEffect(() => {
    const fetchDefaultGroup = async () => {
      try {
        const data: GetGroupFileRequest = {
          ShopId: storeId,
          Skip: 0,
          Limit: 10,
        };
        const response = await getGroups(data);
        if (response.data?.data?.length > 0) {
          setDefaultGroupId(response.data.data[0].groupFileId);
        }
      } catch (error) {}
    };
    fetchDefaultGroup();
  }, [storeId]);
  const isValidImageFile = (file: File): boolean => {
    const filePath = file.name || "";

    const fileExtension = filePath.split(".").pop()?.toLowerCase() || "";

    const allowedExtensions = ["png", "jpg", "jpeg", "jfif"];
    return allowedExtensions.includes(fileExtension);
  };
  const handleImageDrop = (acceptedFiles: File[]) => {
    if (acceptedFiles[0]) {
      const file = acceptedFiles[0];
      if (isValidImageFile(file)) {
        setNewImageFile(file);
        formik.setFieldValue("image", file);
        const previewUrl = URL.createObjectURL(file);
        setImagePreview(previewUrl);
        setPreviewUrls([previewUrl]);
      } else {
        snackbar.error(
          "Định dạng file không hợp lệ. Hãy chọn file định dạng PNG, JPG, JPEG hoặc JFIF."
        );
        setImagePreview(null);
        setPreviewUrls([]);
      }
    }
  };
  const handleImageRemove = () => {
    setNewImageFile(null);
    formik.setFieldValue("image", null);
    setImagePreview(null);
    setPreviewUrls([]);
  };

  const handleFileUpload = async (file: File) => {
    if (file) {
      if (file.size > FILE_SIZE_2MB) {
        snackbar.error(
          `Kích thước ảnh không được vượt quá ${(FILE_SIZE_2MB / 1024 / 1024).toFixed(1)}MB.`
        );
        return;
      }
      const allowedTypes = ["image/png", "image/jpeg", "image/jpg", "image/gif"];
      if (!allowedTypes.includes(file.type)) {
        alert("Định dạng ảnh không hợp lệ. Chỉ chấp nhận PNG, JPG, JPEG, GIF.");
        return;
      }

      try {
        const data: CreateFileGroupRequest = {
          FileUpload: file,
          GroupFileId: defaultGroupId,
          ShopId: storeId,
          RefType: RefType.ArticleCategory,
        };
        const response = await uploadFile(data);
        if (response?.data?.link) {
          return response?.data?.link;
        }
      } catch (error) {
        alert("Có lỗi xảy ra khi tải lên ảnh. Vui lòng thử lại.");
      }
    }
  };

  const handleFilesChange = (files: File[]) => {
    setExistingFiles([]);
    setLocalFiles([]);
    if (files.length > MAX_FILE_IMAGE) {
      snackbar.error(`Bạn chỉ có thể tải lên tối đa ${MAX_FILE_IMAGE} tệp hình ảnh.`);
      return;
    }
    if (files.length > 0 && !isValidImageFile(files[0])) {
      snackbar.error(
        "Định dạng file không hợp lệ. Hãy chọn file định dạng PNG, JPG, JPEG hoặc JFIF."
      );
      return;
    }
    formik.setFieldValue("image", files[0] || null);
    // setShowPopup(true);
    // Cập nhật localFiles và imagePreview

    if (files.length > 0) {
      setLocalFiles(files);
      setImagePreview(URL.createObjectURL(files[0]));
    } else {
      setImagePreview(null);
      setErrorMsg("Hãy chọn hình ảnh");
    }
  };

  const handleRemove = (index: number, isExisting: boolean) => {
    if (isExisting) {
      const updated = [...existingFiles];
      updated.splice(index, 1);
      setExistingFiles(updated);
    } else {
      const localIdx = index - existingFiles.length;
      setLocalFiles((prev) => prev.filter((_, i) => i !== localIdx));
    }
  };

  return (
    <DashboardLayout>
      <Box sx={{ p: Padding }}>
        <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
          <IconButton onClick={handleCancel} sx={{ mr: 2 }}>
            <ArrowBackIcon />
          </IconButton>
          <TitleTypography>
            {"Quản lý nội dung/ Popup/" +
              t(
                isEdit
                  ? tokens.contentManagement.articleCategory.create.editTitle
                  : tokens.contentManagement.articleCategory.create.title
              )}
          </TitleTypography>
        </Box>

        <Card sx={{ p: 3 }}>
          <Box
            sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", mb: 4 }}
          >
            <Typography variant="h6">
              {t(tokens.contentManagement.articleCategory.create.form.contentTitle)}
            </Typography>
            <FormControlLabel
              control={<CustomSwitch checked={isPublished} onChange={handlePublishChange} />}
              label={t(tokens.contentManagement.articleCategory.create.form.isActive)}
              labelPlacement="end"
              sx={{
                mr: 0,
                "& .MuiFormControlLabel-label": {
                  ml: 2,
                },
              }}
            />
          </Box>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" sx={{ mb: 2, display: "flex" }}>
                {t(tokens.contentManagement.articleCategory.create.form.categoryName)}{" "}
                <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
              </Typography>
              <TextField
                fullWidth
                name="categoryName"
                value={formik.values.categoryName}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={!!(formik.touched.categoryName && formik.errors.categoryName)}
                helperText={formik.touched.categoryName && formik.errors.categoryName}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    height: "45px",
                  },
                  mb: 4,
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" sx={{ mb: 2, display: "flex" }}>
                {t(tokens.contentManagement.category.create.form.imageTitle)}{" "}
                <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
              </Typography>
              <CommonMediaUpload
                caption="Thêm ảnh"
                maxFiles={1}
                accept={{
                  "image/*": [".png", ".jpg", ".jpeg"],
                }}
                maxSize={FILE_SIZE_2MB} // 2MB
                existingFiles={existingFiles}
                localFiles={localFiles}
                setLocalFiles={setLocalFiles}
                onFilesChange={handleFilesChange}
                onRemove={handleRemove}
                defaultGroupId={defaultGroupId}
                setExistingFiles={setExistingFiles}
                isShowPreviewImage={true}
                errorMsg={errorMsg}
                setErrorMsg={setErrorMsg}
              />
              {/* <FileDropzone
                accept={{ "image/*": [".png", ".jpg", ".jpeg"] }}
                caption={t(tokens.contentManagement.category.create.form.imageCaption)}
                maxFiles={1}
                maxSize={FILE_SIZE_2MB}
                onDrop={handleImageDrop}
                onRemove={handleImageRemove}
                showError={!isEdit && !!(formik.touched.image && formik.errors.image)}
                existingFiles={previewUrls}
                previewUrlsImg={
                  isEdit
                    ? Array.isArray(previewUrls) && previewUrls.length > 0
                      ? previewUrls
                      : [formik.values.image]
                    : previewUrls
                }
                setPreviewUrlsImg={setPreviewUrls}
              /> */}
              {/* {formik.touched.image && formik.errors.image && (
                <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                  {formik.errors.image as string}
                </Typography>
              )} */}
            </Grid>
          </Grid>

          <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end", mt: 4 }}>
            <Button onClick={handleCancel} variant="outlined" sx={{ minWidth: 120 }}>
              {t(tokens.contentManagement.articleCategory.create.form.cancelButton)}
            </Button>
            <Button
              variant="contained"
              onClick={() => formik.handleSubmit()}
              disabled={formik.isSubmitting}
              sx={{ minWidth: 120 }}
            >
              {formik.isSubmitting ? (
                <CircularProgress size={24} />
              ) : (
                t(tokens.contentManagement.articleCategory.create.form.saveButton)
              )}
            </Button>
          </Box>
        </Card>
      </Box>
    </DashboardLayout>
  );
};

export default CreateArticleCategory;
