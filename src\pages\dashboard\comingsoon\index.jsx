// Overview.jsx
import React from 'react';
import { Box, Typography } from '@mui/material';

const Overview = () => {
  return (
    <Box
      sx={{
        backgroundImage: "url('/logo/logo-comingsoon/Group 48395.png')",
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'contain',
        minHeight: '600px',
        padding: '20px',
      }}
    >
      <Typography
        sx={{
          color: '#2654FE',
          fontSize: '120px',
          fontWeight: '500',
          marginBottom: '25px',
          '@media(max-width: 880px)': {
            fontSize: '65px',
          },
          '@media(max-width: 480px)': {
            fontSize: '45px',
          },
        }}
      >
        Coming Soon
      </Typography>
      <Typography
        color="#A5A8AB"
        fontSize={'25px'}
        fontWeight={'400'}
        maxWidth={'500px'}
        sx={{
          '@media(max-width: 480px)': {
            fontSize: '23px',
          },
        }}
      >
        🚀 Tính năng đang được chúng tôi phát triển để mang đến trải nghiệm tuyệt vời hơn. Hãy kiên
        nhẫn chờ đợi, điều bất ngờ sắp đến! 🔥
      </Typography>
    </Box>
  );
};

export default Overview;
