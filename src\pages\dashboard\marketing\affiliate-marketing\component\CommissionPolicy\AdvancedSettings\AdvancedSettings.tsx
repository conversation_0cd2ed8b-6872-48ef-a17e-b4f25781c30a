import React, { useContext, useEffect, useState } from "react";
import {
  Box,
  Typography,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  TextField,
  InputAdornment,
  Tooltip,
  IconButton,
  Select,
  MenuItem,
  Checkbox,
  Button,
  Switch,
  styled,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import InfoIcon from "@mui/icons-material/Info";
import { useAffiliation } from "@/src/api/hooks/affiliation/use-affiliation";
import { AffiliatePolicyContext } from "../CommissionPolicy";
import { useStoreId } from "@/src/hooks/use-store-id";
import useSnackbar from "@/src/hooks/use-snackbar";
import { AdvancedCommissionsConfig } from "@/src/api/types/affiliation.type";
import TitleDialog from "@/src/components/dialog/TitleDialog";

const CustomSwitch = styled(Switch)(() => ({
  "& .MuiSwitch-switchBase.Mui-checked": {
    color: "#2654FE",
  },
  "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track": {
    backgroundColor: "#2654FE",
  },
  "& .MuiSwitch-track": {
    backgroundColor: "#ddd",
    borderRadius: 20,
  },
}));

const AdvancedSettings = () => {
  const [selected, setSelected] = useState(false);
  const { commissionsConfigData, setCommissionsConfigData } = useContext(AffiliatePolicyContext);
  const storeId = useStoreId();
  const snackbar = useSnackbar();
  const [localAdvancedCommissionsConfig, setLocalAdvancedCommissionsConfig] =
    useState<AdvancedCommissionsConfig>(commissionsConfigData.advancedCommissionsConfig);
  const [openConfirm, setOpenConfirm] = useState(false);
  const [checked, setChecked] = useState(false);

  const { getCommissionsConfig, updateCommissionsConfig, updateCommissionActiveStatus } =
    useAffiliation();

  // Kiểm tra xem tất cả hoa hồng có bằng 0 không
  const areAllCommissionsZero = () => {
    const { basicCommissionsConfig, advancedCommissionsConfig } = commissionsConfigData;

    // Kiểm tra hoa hồng cơ bản
    const levelOneZero = basicCommissionsConfig.levelOneCommissionPercentage === 0;
    const levelTwoZero = basicCommissionsConfig.levelTwoCommissionPercentage === 0;

    return basicCommissionsConfig.isActiveLevelTwo ? levelOneZero || levelTwoZero : levelOneZero;
  };

  const handleChangeActiveStatus = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const isActiveStatus = event.target.checked;

    // Nếu đang cố gắng bật và tất cả hoa hồng bằng 0, không cho phép
    if (isActiveStatus && areAllCommissionsZero()) {
      snackbar.error(
        "Không thể kích hoạt chương trình khi tất cả hoa hồng các bậc đều bằng 0. Vui lòng thiết lập hoa hồng trước khi kích hoạt."
      );
      return;
    }

    const response = await updateCommissionActiveStatus({
      shopId: storeId,
      isActive: isActiveStatus,
    });
    if (response?.data) {
      snackbar.success("Cập nhật thông tin thành công");
      fetchCommissionsConfig(storeId);
    }
  };

  const fetchCommissionsConfig = async (shopId: string) => {
    const response = await getCommissionsConfig({ shopId });
    if (response?.data) {
      const { commissionsConfigId, shopId, ...filteredData } = response.data.data; // Loại bỏ field
      setCommissionsConfigData(filteredData);
    }
  };

  const handleCommissionChange = (value: string) => {
    // Chỉ cho phép số nguyên, không có số thập phân
    const integerValue = value.replace(/\D/g, ""); // Loại bỏ tất cả ký tự không phải số

    // Chuyển giá trị thành số nguyên và giới hạn trong khoảng từ 0 đến 100
    const numberValue = Math.max(0, Math.min(100, Number(integerValue)));

    // Cập nhật giá trị
    setLocalAdvancedCommissionsConfig({
      ...localAdvancedCommissionsConfig,
      selfReferralCommissionPercentage: numberValue.toString(),
    });
  };

  const changePartnerCommExpiry = (value: string | number) => {
    setLocalAdvancedCommissionsConfig({
      ...localAdvancedCommissionsConfig,
      partnerCommExpiry: parseFloat(value.toString()),
    });
  };

  const handleSubmitAdvanceConfig = async () => {
    let newData = {
      ...commissionsConfigData,
      advancedCommissionsConfig: localAdvancedCommissionsConfig,
    };
    if (!selected) {
      newData.advancedCommissionsConfig.minSpendToApproved = null;
    }

    if (!newData.advancedCommissionsConfig.isSelfReferralCommission) {
      newData.advancedCommissionsConfig.selfReferralCommissionPercentage = "0";
    }

    const response = await updateCommissionsConfig({ shopId: storeId }, newData);
    if (response?.data) {
      snackbar.success("Cập nhật thông tin thành công");
      fetchCommissionsConfig(storeId);
    }
  };

  const handleConfirm = () => {
    setLocalAdvancedCommissionsConfig((prev) => ({
      ...prev,
      isSelfReferralCommission: true,
    }));
    setOpenConfirm(false);
    setChecked(false);
  };

  const handleCancel = () => {
    setOpenConfirm(false);
    setChecked(false);
  };

  useEffect(() => {
    if (storeId) {
      fetchCommissionsConfig(storeId);
    }
  }, [storeId]);

  useEffect(() => {
    setLocalAdvancedCommissionsConfig(commissionsConfigData.advancedCommissionsConfig);
    if (
      commissionsConfigData.advancedCommissionsConfig &&
      commissionsConfigData.advancedCommissionsConfig.minSpendToApproved !== null
    ) {
      setSelected(true);
    }
  }, [commissionsConfigData]);

  const formatNumber = (num: string | number | null) => {
    if (num === null || num === undefined) return ""; // Trả về chuỗi rỗng nếu num là null hoặc undefined

    // Nếu num là kiểu string, loại bỏ dấu chấm
    const cleanedNum = typeof num === "string" ? num.replace(/\./g, "") : num.toString();

    // Chuyển cleanedNum thành số
    const number = Number(cleanedNum);
    if (isNaN(number)) return "";

    // Định dạng lại số theo "de-DE" (dấu chấm cho phần nghìn)
    return new Intl.NumberFormat("de-DE").format(number);
  };

  // Xử lý khi người dùng thay đổi giá trị
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalAdvancedCommissionsConfig({
      ...localAdvancedCommissionsConfig,
      minSpendToApproved: e.target.value,
    });
  };

  const handleSelfReferralCommission = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedValue = event.target.value === "true";

    if (selectedValue && commissionsConfigData.basicCommissionsConfig.isActiveLevelTwo) {
      setOpenConfirm(true);
    } else if (selectedValue && !commissionsConfigData.basicCommissionsConfig.isActiveLevelTwo) {
      setLocalAdvancedCommissionsConfig((prev) => ({
        ...prev,
        isSelfReferralCommission: true,
      }));
    } else {
      setLocalAdvancedCommissionsConfig((prev) => ({
        ...prev,
        isSelfReferralCommission: false,
      }));
    }
  };

  return (
    <Box sx={{ padding: 3 }}>
      <Box
        sx={{
          display: "flex",
          alignItems: "start",
          "@media (max-width: 600px)": { flexDirection: "column" },
        }}
      >
        <Box sx={{ width: "35%", "@media (max-width: 600px)": { width: "100%" } }}>
          <Typography variant="h6" fontWeight="bold">
            Cài đặt quy tắc nâng cao
          </Typography>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Thiết lập các quy tắc đặc biệt cho đối tác kinh doanh của bạn
          </Typography>
        </Box>

        <Box
          sx={{
            width: "65%",
            paddingLeft: "20px",
            "@media (max-width: 600px)": { width: "100%", paddingLeft: "0px" },
          }}
        >
          <FormControl component="fieldset">
            <Typography variant="subtitle1" fontWeight="bold">
              Hoa hồng tự giới thiệu
            </Typography>
            <RadioGroup
              value={localAdvancedCommissionsConfig.isSelfReferralCommission}
              onChange={handleSelfReferralCommission}
            >
              <FormControlLabel value="true" control={<Radio />} label="Cho phép" />
              {localAdvancedCommissionsConfig.isSelfReferralCommission && (
                <Box sx={{ display: "flex", alignItems: "center", gap: 1, pl: "30px" }}>
                  <Box>
                    <TextField
                      value={localAdvancedCommissionsConfig.selfReferralCommissionPercentage}
                      type="number"
                      onChange={(e) => handleCommissionChange(e.target.value)}
                      size="small"
                      InputProps={{
                        endAdornment: <InputAdornment position="end">%</InputAdornment>,
                      }}
                      inputProps={{
                        min: 0,
                        max: 100,
                        step: 1,
                      }}
                      sx={{
                        mt: 1,
                        "& fieldset": { borderRadius: "8px" },
                        width: "150px",
                        "@media (max-width: 600px)": { width: "100%" },
                      }}
                    />
                    <Tooltip
                      title="Khi cho phép hoa hồng tự giới thiệu, mỗi khi đối tác mua hàng họ sẽ được hoa hồng ngay trên đơn hàng họ mua với tỷ lệ hoa hồng được bạn cài đặt tại đây."
                      arrow
                    >
                      <IconButton size="small" sx={{ color: "#D9D9D9" }}>
                        <InfoIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Typography sx={{ color: "#787878", fontSize: "10px", fontWeight: "400" }}>
                      Nhập tỷ lệ hoa hồng tự giới thiệu cho đối tác của bạn
                    </Typography>
                  </Box>
                </Box>
              )}
              <FormControlLabel value="false" control={<Radio />} label="Không cho phép" />
            </RadioGroup>
          </FormControl>
          <Dialog open={openConfirm} onClose={handleCancel} fullWidth maxWidth="sm">
            <DialogTitle>Cho phép hoa hồng tự giới thiệu</DialogTitle>

            <DialogContent dividers>
              <Typography>
                Việc kích hoạt tính năng này sẽ khiến mô hình kinh doanh của bạn được quy vào hình
                thức kinh doanh đa cấp theo quy định pháp luật. Bạn có trách nhiệm đăng ký hoạt động
                với Bộ Công Thương nếu áp dụng.
              </Typography>
              <br />
              <Typography>
                Bằng việc sử dụng tính năng này, bạn xác nhận rằng Evotech sẽ không chịu bất kỳ
                trách nhiệm nào liên quan đến hoạt động kinh doanh của bạn.
              </Typography>

              <FormControlLabel
                control={
                  <Checkbox checked={checked} onChange={(e) => setChecked(e.target.checked)} />
                }
                label="Tôi đồng ý với điều khoản sử dụng"
              />
            </DialogContent>

            <DialogActions sx={{ padding: 2 }}>
              <Button onClick={handleCancel}>Hủy</Button>
              <Button variant="contained" onClick={handleConfirm} disabled={!checked}>
                Đồng ý
              </Button>
            </DialogActions>
          </Dialog>

          <Typography variant="subtitle1" fontWeight="bold" sx={{ mt: 3 }}>
            Thời gian hiệu lực cho các đối tác
          </Typography>
          <Box display="flex" flexWrap="wrap">
            <Typography variant="body2">
              Đối tác được nhận hoa hồng cho các đơn hàng do khách hàng mua trong
            </Typography>
            <Select
              value={localAdvancedCommissionsConfig.partnerCommExpiry}
              onChange={(e) => changePartnerCommExpiry(e.target.value)}
              size="small"
              sx={{ ml: 1, minWidth: 150, overflow: "visible", marginRight: "5px" }}
              MenuProps={{
                disablePortal: true,
                anchorOrigin: {
                  vertical: "bottom",
                  horizontal: "right",
                },
                transformOrigin: {
                  vertical: "top",
                  horizontal: "right",
                },
              }}
            >
              <MenuItem value="99">Không giới hạn</MenuItem>
              {process.env.NEXT_PUBLIC_ENVIRONMENT !== "production" && (
                <MenuItem value="0.00274">1 ngày</MenuItem>
              )}
              <MenuItem value="1">1 năm</MenuItem>
              <MenuItem value="2">2 năm</MenuItem>
              <MenuItem value="5">5 năm</MenuItem>
            </Select>
            <Typography variant="body2">
              sau khi khách hàng truy cập vào liên kết và trở thành cấp dưới của đối tác
            </Typography>
          </Box>

          <FormControl component="fieldset" sx={{ mt: 3 }}>
            <Typography variant="subtitle1" fontWeight="bold">
              Phê duyệt đối tác
            </Typography>
            <RadioGroup
              value={localAdvancedCommissionsConfig.isAutoApproved}
              onChange={(e) => {
                setLocalAdvancedCommissionsConfig({
                  ...localAdvancedCommissionsConfig,
                  isAutoApproved: e.target.value === "true",
                });
              }}
              sx={{
                width: "100%",
                maxWidth: "100%",
              }}
            >
              <FormControlLabel
                sx={{
                  ".& span": { fontSize: "16px", fontWeight: "400", color: "#000000" },
                  width: "100%",
                  maxWidth: "100%",
                }}
                value={true}
                control={<Radio />}
                label="Phê duyệt tự động"
              />
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 0,
                  flexWrap: "wrap",
                  marginLeft: "30px",
                }}
              >
                {localAdvancedCommissionsConfig.isAutoApproved && (
                  <Box>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={selected}
                          onChange={(e) => {
                            if (!e.target.checked) {
                              setLocalAdvancedCommissionsConfig({
                                ...localAdvancedCommissionsConfig,
                                minSpendToApproved: null,
                              });
                            }

                            setSelected(!selected);
                          }}
                        />
                      }
                      label={
                        <Typography fontSize={"14px"} color="#000">
                          Tổng số tiền chi tiêu tối thiểu
                        </Typography>
                      }
                      sx={{ marginRight: "10px" }}
                    />
                    <TextField
                      value={formatNumber(localAdvancedCommissionsConfig.minSpendToApproved)}
                      onChange={handleChange}
                      size="small"
                      disabled={!selected}
                      InputProps={{
                        endAdornment: <InputAdornment position="end">đ</InputAdornment>,
                      }}
                      sx={{
                        width: "200px",
                        "& input": {
                          textAlign: "right",
                          borderRight: "1px solid #D9D9D9",
                          padding: "5px 10px",
                          // Nếu disabled, thay đổi màu chữ và nền
                          backgroundColor: !selected ? "#f0f0f0" : "transparent", // Màu nền khi disabled
                          color: !selected ? "#b0b0b0" : "inherit", // Màu chữ khi disabled
                        },
                        "& fieldset": {
                          borderColor: !selected ? "#d0d0d0" : "inherit", // Màu border khi disabled
                        },
                      }}
                    />
                    <Typography sx={{ marginLeft: "10px", fontSize: "14px", color: "#000" }}>
                      để đủ điều kiện tham gia đối tác kinh doanh
                    </Typography>
                  </Box>
                )}
              </Box>

              <FormControlLabel value={false} control={<Radio />} label="Phê duyệt thủ công" />
              {!localAdvancedCommissionsConfig.isAutoApproved && (
                <Typography fontSize={"14px"} sx={{ marginLeft: "30px", color: "#000" }}>
                  Sau khi đối tác gửi đơn đăng ký, admin sẽ xem xét lại các đơn đăng ký và phê duyệt
                  trên hệ thống
                </Typography>
              )}

              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 0,
                  flexWrap: "wrap",
                  marginLeft: "30px",
                }}
              >
                {!localAdvancedCommissionsConfig.isAutoApproved && (
                  <Box>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={selected}
                          onChange={(e) => {
                            if (!e.target.checked) {
                              setLocalAdvancedCommissionsConfig({
                                ...localAdvancedCommissionsConfig,
                                minSpendToApproved: null,
                              });
                            }

                            setSelected(!selected);
                          }}
                        />
                      }
                      label={
                        <Typography color="#000" fontSize={"14px"}>
                          Tổng số tiền chi tiêu tối thiểu
                        </Typography>
                      }
                      sx={{ marginRight: "10px" }}
                    />
                    <TextField
                      value={formatNumber(localAdvancedCommissionsConfig.minSpendToApproved)}
                      disabled={!selected}
                      onChange={(e) => {
                        setLocalAdvancedCommissionsConfig({
                          ...localAdvancedCommissionsConfig,
                          minSpendToApproved: e.target.value,
                        });
                      }}
                      size="small"
                      InputProps={{
                        endAdornment: <InputAdornment position="end">đ</InputAdornment>,
                      }}
                      sx={{
                        width: "200px",
                        "& input": {
                          textAlign: "right",
                          borderRight: "1px solid #D9D9D9",
                          padding: "5px 10px",
                          // Nếu disabled, thay đổi màu chữ và nền
                          backgroundColor: !selected ? "#f0f0f0" : "transparent", // Màu nền khi disabled
                          color: !selected ? "#b0b0b0" : "inherit", // Màu chữ khi disabled
                        },
                        "& fieldset": {
                          borderColor: !selected ? "#d0d0d0" : "inherit", // Màu border khi disabled
                        },
                      }}
                    />
                    <Typography color="#000" fontSize={"14px"} sx={{ marginLeft: "10px" }}>
                      để đủ điều kiện tham gia đối tác kinh doanh
                    </Typography>
                  </Box>
                )}
              </Box>
            </RadioGroup>
          </FormControl>
        </Box>
      </Box>

      <Box
        sx={{
          display: "flex",
          alignItems: "start",
          "@media (max-width: 600px)": { flexDirection: "column" },
        }}
      >
        <Box sx={{ width: "35%", "@media (max-width: 600px)": { width: "100%" } }}>
          <Typography variant="h6" fontWeight="500" sx={{ mt: 4 }}>
            Cài đặt thanh toán
          </Typography>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Thiết lập phương thức bạn sẽ thanh toán hoa hồng cho đối tác của bạn
          </Typography>
        </Box>

        <Box
          sx={{
            display: "flex",
            alignItems: "start",
            width: "65%",
            paddingLeft: "20px",
            flexDirection: "column",
            "@media (max-width: 600px)": { width: "100%", paddingLeft: "0px" },
          }}
        >
          <Typography variant="h6" fontWeight="500" sx={{ mt: 4 }}>
            Ngày thanh toán
          </Typography>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              flexDirection: "row",
            }}
          >
            <Typography
              variant="body2"
              sx={{ mb: 2, marginTop: "16px", ".& p": { margin: "0 !important" } }}
            >
              Ngày thanh toán hàng tháng:
            </Typography>
            <TextField
              size="small"
              type="number"
              value={localAdvancedCommissionsConfig.paymentDue || 1}
              onChange={(e) => {
                const raw = e.target.value;
                const intValue = parseInt(raw, 10);

                if (!isNaN(intValue)) {
                  const clamped = Math.max(1, Math.min(28, intValue)); // giới hạn từ 1 → 28
                  setLocalAdvancedCommissionsConfig({
                    ...localAdvancedCommissionsConfig,
                    paymentDue: clamped,
                  });
                }
              }}
              sx={{ ml: 2, width: "100px" }}
              inputProps={{
                min: 1,
                max: 28,
                step: 1,
              }}
            />{" "}
          </Box>
        </Box>
      </Box>

      <Box
        sx={{
          display: "flex",
          justifyContent: "end",
          alignItems: "center",
          marginTop: "20px",
          paddingBottom: "30px",
          borderBottom: "1px solid #787878",
        }}
      >
        <Button
          variant="contained"
          sx={{
            bgcolor: "#2654FE",
            color: "#fff",
            fontWeight: "400",
            fontSize: "14px",
            textTransform: "none",
            borderRadius: "10px",
          }}
          onClick={handleSubmitAdvanceConfig}
        >
          Lưu
        </Button>
      </Box>

      <Box
        sx={{ marginTop: "30px" }}
        display="flex"
        alignItems="center"
        justifyContent="space-between"
      >
        <Box>
          <Typography sx={{ fontWeight: "700", fontSize: "16px", color: "#000" }}>
            Trạng thái chương trình đối tác
          </Typography>
          <Typography sx={{ fontWeight: "400", fontSize: "14px", color: "#000" }}>
            Sau khi ngừng kích hoạt, các đối tác mới sẽ không thể tham gia chương trình đối tác kinh
            doanh của bạn. Các đối tác cũ vẫn hoạt động theo chính sách của bạn.
          </Typography>
        </Box>
        {/* <Switch checked={isActive} onChange={() => setIsActive(!isActive)} color="primary" /> */}
        <CustomSwitch
          checked={commissionsConfigData.isActive}
          onChange={handleChangeActiveStatus}
        />
      </Box>
    </Box>
  );
};

export default AdvancedSettings;
