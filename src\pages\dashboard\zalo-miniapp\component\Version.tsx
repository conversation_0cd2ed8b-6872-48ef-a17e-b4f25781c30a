import React, { useState, useEffect, useRef } from "react";
import axios from "axios";
import {
  Box,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TablePagination,
  Popover,
} from "@mui/material";
import { shopSettingService } from "@/src/api/services/shop-setting/shop-setting.service";
import { useShop } from "@/src/api/hooks/shop/use-shop";
import ZaloIntegrationPopup from "./Popup";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useStoreId } from "@/src/hooks/use-store-id";
import {
  Cancel,
  CheckCircle,
  CloudDone,
  Code,
  DeveloperMode,
  DeveloperModeOutlined,
  FileDownloadOutlined,
  HourglassEmpty,
} from "@mui/icons-material";
import { useAppSelector } from "@/src/redux/hooks";
import { useZaloMiniApp } from "@/src/api/hooks/zalo-mini-app/use-zalo-mini-app";
import {
  GetListVersionParams,
  MiniAppVersionDto,
} from "@/src/api/services/zalo-mini-app/zalo-mini-app.service";
import { QRCode } from "zmp-qrcode";
import { toPng } from "html-to-image";
import dayjs from "dayjs";

function bytesToMB(bytes) {
  const MB = 1024 * 1024;
  return (bytes / MB).toFixed(2);
}
const Version = ({ tabValue }) => {
  const [versions, setVersions] = useState<MiniAppVersionDto[]>();
  const { getListVersion } = useZaloMiniApp();
  const storeId = useStoreId();
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(20);

  const [isPopupOpen, setIsPopupOpen] = useState(false);

  const [isLoading, setIsLoading] = useState(false);
  const { profile } = useAppSelector((state) => state.profile);
  const { getShop } = useShop();
  const snackbar = useSnackbar();

  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [selectedQrCode, setSelectedQrCode] = useState<{
    versionCode: string;
    versionLink: string;
  } | null>(null);
  const qrRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  useEffect(() => {
    const fetchVersionApp = async () => {
      const data: GetListVersionParams = {
        shopId: storeId,
        param: {
          Offset: page,
          Limit: rowsPerPage,
        },
      };
      try {
        const res = await getListVersion(data);

        if (res) {
          if (res.status === 200) {
            setVersions(res.data?.data?.versions || []);
            setTotalCount(res.data?.data?.total || 0);
          } else if (res.detail) {
            snackbar.error(res.detail);
          } else {
            snackbar.error("Có lỗi khi lấy ra dữ liệu");
          }
        } else {
          // snackbar.error("Không nhận được phản hồi từ server");
        }
      } catch (error) {
        snackbar.error("Có lỗi khi lấy ra dữ liệu");
      }
    };
    fetchVersionApp();
  }, [storeId, tabValue, page, rowsPerPage]);

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handlePointVoucherCodeClick = (
    event: React.MouseEvent<HTMLElement>,
    pointVoucherCode: { versionCode: string; versionLink: string }
  ) => {
    setAnchorEl(event.currentTarget);
    setSelectedQrCode(pointVoucherCode);
  };
  const handleCloseQrCode = () => {
    setAnchorEl(null);
    setSelectedQrCode(null);
  };

  const handleDownload = async (versionCode: string) => {
    const node = qrRefs.current[versionCode];
    if (!node) return;
    try {
      const dataUrl = await toPng(node, {
        skipAutoScale: true,
        skipFonts: true,
        filter: (node) => node.tagName !== "SCRIPT",
      });
      const link = document.createElement("a");
      link.href = dataUrl;
      link.download = `${versionCode}.png`;
      link.click();
    } catch (err) {
      console.error("Download failed", err);
    }
  };

  const getStatusDisplay = (status) => {
    switch (status) {
      case "DEVELOPMENT":
        return (
          <Box display="flex" alignItems="center" flexDirection="column" color="">
            <DeveloperMode sx={{ fontSize: 20 }} />
            <Typography sx={{ fontSize: 15, mt: 0.4 }}>Development</Typography>
          </Box>
        );

      case "TESTING":
        return (
          <>
            <Box display="flex" alignItems="center" flexDirection="column" color="warning.main">
              <Typography sx={{ color: "#000", fontSize: 15 }}>Testing</Typography>
            </Box>
          </>
        );
      case "WAITING_APPROVAL":
        return (
          <Box display="flex" alignItems="center" flexDirection="column" color="#ffc107">
            <HourglassEmpty sx={{ fontSize: 18 }} />
            <Typography sx={{ fontSize: 15, mt: 0.4 }}>Waiting for approval</Typography>
          </Box>
        );
      case "REJECTED":
        return (
          <Box display="flex" alignItems="center" flexDirection="column" color="error.main">
            <Cancel sx={{ fontSize: 18, color: "error.main" }} />
            <Typography sx={{ fontSize: 15, mt: 0.4 }}>Rejected</Typography>
          </Box>
        );
      case "READY_TO_PRODUCTION":
        return (
          <Box display="flex" alignItems="center" flexDirection="column" color="success.main">
            <CheckCircle sx={{ fontSize: 18, color: "success.main" }} />
            <Typography sx={{ fontSize: 15, mt: 0.4 }}>Ready to production</Typography>
          </Box>
        );
      case "PRODUCTION":
        return (
          <Box display="flex" alignItems="center" flexDirection="column" color="success.main">
            <CheckCircle sx={{ fontSize: 18, color: "success.main" }} />
            <Typography sx={{ fontSize: 15, mt: 0.4 }}>Live</Typography>
          </Box>
        );
      default:
        return (
          <Box display="flex" alignItems="center" color="text.secondary">
            <Typography variant="body1">Không xác định</Typography>
          </Box>
        );
    }
  };
  return (
    <Box sx={{ px: 4, py: 2 }}>
      <Box sx={{ paddingBottom: "35px", borderBottom: "1px solid #D8D8D8" }}>
        <Typography color="#000000" fontSize={"20px"} fontWeight={"700"}>
          Danh sách phiên bản
        </Typography>
        <Typography color="#000000" fontSize={"14px"} fontWeight={"400"}>
          Phiên bản mới sẽ được cập nhật tự động và gửi đến Zalo để xem xét và phê duyệt. Nếu trường
          hợp xét duyệt không thành công cần thao tác gửi phê duyệt thủ công
        </Typography>

        <Box sx={{ width: "100%", overflowX: "auto", mt: 1 }}>
          <Table sx={{ minWidth: 700 }} aria-label="setup table">
            <TableHead>
              <TableRow>
                <TableCell
                  sx={{
                    background: "#dbdfe2 !important",
                    fontWeight: "700",
                    fontSize: { xs: "12px", md: "14px" },
                    whiteSpace: "nowrap",
                  }}
                  align="center"
                >
                  STT
                </TableCell>
                <TableCell
                  align="center"
                  sx={{
                    background: "#dbdfe2 !important",
                    fontWeight: "700",
                    fontSize: { xs: "12px", md: "14px" },
                    minWidth: "120px",
                    whiteSpace: "nowrap",
                  }}
                >
                  Phiên bản
                </TableCell>
                <TableCell
                  sx={{
                    background: "#dbdfe2 !important",
                    fontWeight: "700",
                    fontSize: { xs: "12px", md: "14px" },
                    minWidth: "100px",
                    whiteSpace: "nowrap",
                  }}
                  align="center"
                >
                  Kích cỡ (MB)
                </TableCell>
                <TableCell
                  sx={{
                    background: "#dbdfe2 !important",
                    fontWeight: "700",
                    fontSize: { xs: "12px", md: "14px" },
                    minWidth: "120px",
                    whiteSpace: "nowrap",
                  }}
                  align="center"
                >
                  Ghi chú
                </TableCell>
                <TableCell
                  sx={{
                    background: "#dbdfe2 !important",
                    fontWeight: "700",
                    fontSize: { xs: "12px", md: "14px" },
                    minWidth: "160px",
                    whiteSpace: "nowrap",
                  }}
                  align="center"
                >
                  Sửa đổi lần cuối
                </TableCell>
                <TableCell
                  sx={{
                    background: "#dbdfe2 !important",
                    fontWeight: "700",
                    fontSize: { xs: "12px", md: "14px" },
                    minWidth: "100px",
                    whiteSpace: "nowrap",
                  }}
                  align="center"
                >
                  Trạng thái
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {Array.isArray(versions) && versions.length > 0 ? (
                versions.map((row, index) => (
                  <TableRow key={row.versionId}>
                    <TableCell sx={{ fontSize: 15 }} align="center">
                      {page * rowsPerPage + index + 1}
                    </TableCell>
                    <TableCell component="th" scope="row">
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          gap: 1,
                          flexDirection: "column",
                        }}
                      >
                        <Typography sx={{ fontSize: { xs: "12px", md: "15px" } }}>
                          {row.versionId}
                        </Typography>
                        {row?.entrypoint && (
                          <Button
                            variant="outlined"
                            size="small"
                            sx={{
                              fontSize: { xs: "10px", md: "12px" },
                              padding: { xs: "2px 6px", md: "4px 8px" },
                              display: "flex",
                              gap: "5px",
                              color: "#000",
                              border: "none",
                              textTransform: "none",
                              background: "#f4f4f5",
                              minWidth: "auto",
                              whiteSpace: "nowrap",
                            }}
                            onClick={(event) =>
                              handlePointVoucherCodeClick(event, {
                                versionCode: row.name,
                                versionLink: row.entrypoint,
                              })
                            }
                          >
                            <img
                              style={{ width: "16px", height: "16px" }}
                              src="/logo/logo-miniapp/qr.png"
                              alt="Logo"
                            />
                            QR Code
                          </Button>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell
                      align="center"
                      sx={{
                        fontSize: { xs: "12px", md: "15px" },
                        whiteSpace: "nowrap",
                      }}
                    >
                      {bytesToMB(row.size)} MB
                    </TableCell>
                    <TableCell
                      align="center"
                      sx={{
                        fontSize: { xs: "12px", md: "15px" },
                        whiteSpace: "nowrap",
                      }}
                    >
                      {row.description}
                    </TableCell>
                    <TableCell
                      align="center"
                      sx={{
                        fontSize: { xs: "12px", md: "15px" },
                        whiteSpace: "nowrap",
                      }}
                    >
                      {row.lastUpdatedTime}
                    </TableCell>
                    <TableCell
                      align="center"
                      sx={{
                        fontSize: { xs: "12px", md: "14px" },
                        whiteSpace: "nowrap",
                      }}
                    >
                      {getStatusDisplay(row.status)}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <>
                  <TableRow>
                    <TableCell colSpan={9} align="center" sx={{ py: 3 }}>
                      <Typography variant="body2" color="text.secondary">
                        Không có dữ liệu
                      </Typography>
                    </TableCell>
                  </TableRow>
                </>
              )}
            </TableBody>
          </Table>
        </Box>
        <TablePagination
          labelRowsPerPage="Số hàng mỗi trang"
          component="div"
          count={totalCount}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          rowsPerPageOptions={[20]}
        />
      </Box>
      <Popover
        open={!!anchorEl}
        anchorEl={anchorEl}
        onClose={handleCloseQrCode}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "center",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "center",
        }}
      >
        {selectedQrCode && (
          <Box sx={{ p: 3, minWidth: 200 }}>
            <Typography variant="h6" gutterBottom align="center">
              Mã QR
            </Typography>

            {/* QR Code for scanning */}
            <Box
              sx={{ display: "flex", justifyContent: "center", mb: 2 }}
              ref={(el) => {
                if (selectedQrCode) {
                  qrRefs.current[selectedQrCode.versionCode] = el as HTMLDivElement | null;
                }
              }}
            >
              {selectedQrCode && <QRCode value={selectedQrCode.versionLink} size={100} />}
            </Box>

            <Box sx={{ display: "flex", gap: 1, justifyContent: "center" }}>
              <Button
                variant="outlined"
                size="small"
                startIcon={<FileDownloadOutlined />}
                onClick={() => {
                  if (selectedQrCode) {
                    handleDownload(selectedQrCode.versionCode);
                  }
                  handleCloseQrCode();
                }}
              >
                Tải xuống
              </Button>
            </Box>
          </Box>
        )}
      </Popover>
    </Box>
  );
};

export default Version;
