import { useState, KeyboardEvent, useEffect } from "react";
import {
  Box,
  Button,
  Container,
  TextField,
  Typography,
  styled,
  DialogActions,
  DialogContent,
} from "@mui/material";
import BackspaceIcon from "@mui/icons-material/Backspace";
import { Grid } from "@mui/system";
import { useCart } from "@/src/api/hooks/cart/use-cart";
import { useUser } from "@/src/api/hooks/user/use-user";

const KeypadButton = styled(Button)(({ theme }) => ({
  width: "100%",
  height: "48px",
  fontSize: "1rem",
  borderRadius: "4px",
  border: "1px solid black",
  backgroundColor: "#fff",
  color: "#000",
  "&:hover": {
    backgroundColor: "#f5f5f5",
  },
}));

const AmountButton = styled(KeypadButton)({
  color: "#000",
  justifyContent: "flex-start",
  paddingLeft: "16px",
});

const EmptyButton = styled(But<PERSON>)(({ theme }) => ({
  width: "100%",
  height: "48px",
  fontSize: "1.25rem",
  borderRadius: "4px",
  backgroundColor: "#e0e0e0",
  color: "#000",
  "&:hover": {
    backgroundColor: "#d5d5d5",
  },
}));

export default function NumericKeypad({
  onClose,
  cart,
  onUsePoint,
}: {
  onClose: () => void;
  cart: any;
  onUsePoint: (point) => void;
}) {
  const [value, setValue] = useState("0");
  const [maxPointCanUse, setMaxPointCanUse] = useState(0);
  const [cartUser, setCartUser] = useState(null);
  const [errorPointMessage, setErrorPointMessage] = useState("");
  const [isValid, setIsValid] = useState(true);

  const { estimatePoint } = useCart();
  const { detailUser } = useUser();

  const formatNumber = (num: string) => {
    const number = parseInt(num.replace(/\./g, ""), 10);
    if (isNaN(number)) return "";
    return new Intl.NumberFormat("de-DE").format(number);
  };

  const validateValue = (newValue: string) => {
    const numericValue = parseInt(String(newValue).replace(/\./g, ""), 10) || 0;

    if (maxPointCanUse === 0) {
      setErrorPointMessage(`Điểm khả dụng không hợp lệ`);
      setIsValid(false);
      return false;
    }

    if (numericValue > maxPointCanUse) {
      setErrorPointMessage(`Số điểm không được vượt quá ${maxPointCanUse}`);
      setIsValid(false);
      return false;
    }

    setErrorPointMessage("");
    setIsValid(true);
    return true;
  };

  // Validate giá trị ban đầu
  useEffect(() => {
    validateValue(value);
  }, [value, maxPointCanUse]);

  const handleNumberClick = (num: string) => {
    const newValue = formatNumber(value + num);
    setValue(newValue);
  };

  const handleBackspace = () => {
    const newValue = value.slice(0, -1);
    const formattedValue = newValue === "" ? "0" : formatNumber(newValue);
    setValue(formattedValue);
  };

  function handleKeyDown(event): void {
    const newValue = event.target.value.replace(/\./g, "");
    if (/^\d*$/.test(newValue)) {
      const formattedValue = newValue ? formatNumber(newValue) : "0";
      setValue(formattedValue);
    }
  }

  const estimateCartPoint = async (cartId) => {
    try {
      const response = await estimatePoint({ cartId });
      if (response?.data) setMaxPointCanUse(response?.data.maxExchangePoint);
    } catch (error) {
      console.error("Error estimating cart point:", error);
    }
  };

  const fetchUserInfo = async (userId) => {
    try {
      const response = await detailUser(userId);
      if (response?.data) setCartUser(response?.data);
    } catch (error) {
      console.error("Error estimating cart point:", error);
    }
  };
  useEffect(() => {
    if (cart && cart.cartId && cart.userId) {
      estimateCartPoint(cart?.cartId);
      fetchUserInfo(cart?.userId);
      setValue(formatNumber(String(cart.exchangePoints)));
    }
  }, [cart]);

  const handleSubmitUsePoint = async () => {
    const numericValue = parseInt(value.replace(/\./g, ""), 10) || 0;
    if (validateValue(value)) {
      onUsePoint(numericValue);
    }
  };

  return (
    <Container maxWidth="sm" sx={{ backgroundColor: "#fff", p: 4 }}>
      <Box sx={{ maxWidth: 500, mx: "auto" }}>
        <Typography variant="h5" align="center" gutterBottom sx={{ fontWeight: "bold", mb: 2 }}>
          Xác nhận trừ điểm
        </Typography>
        <Typography gutterBottom sx={{ mb: 2 }}>
          Điểm người dùng: {cartUser?.point || 0}
        </Typography>
        <Typography gutterBottom sx={{ mb: 2 }}>
          Điểm dùng tối đa: {maxPointCanUse}
        </Typography>
        <TextField
          fullWidth
          value={value}
          variant="outlined"
          onChange={handleKeyDown}
          error={!!errorPointMessage}
          helperText={errorPointMessage}
          sx={{
            mb: 2,
            "& .MuiOutlinedInput-root": {
              "& fieldset": {
                borderColor: errorPointMessage ? "error.main" : "black",
              },
              "&:hover fieldset": {
                borderColor: errorPointMessage ? "error.main" : "black",
              },
              "&.Mui-focused fieldset": {
                borderColor: errorPointMessage ? "error.main" : "black",
              },
            },
          }}
          inputProps={{
            style: {
              textAlign: "center",
              fontSize: "1.1rem",
              padding: "12px",
            },
          }}
        />
        <Grid container spacing={1}>
          <Grid size={{ xs: 3 }}>
            <KeypadButton onClick={() => handleNumberClick("1")}>1</KeypadButton>
          </Grid>
          <Grid size={{ xs: 3 }}>
            <KeypadButton onClick={() => handleNumberClick("2")}>2</KeypadButton>
          </Grid>
          <Grid size={{ xs: 3 }}>
            <KeypadButton onClick={() => handleNumberClick("3")}>3</KeypadButton>
          </Grid>
          <Grid size={{ xs: 3 }}>
            <AmountButton onClick={() => setValue(formatNumber("100000"))}>100.000</AmountButton>
          </Grid>

          <Grid size={{ xs: 3 }}>
            <KeypadButton onClick={() => handleNumberClick("4")}>4</KeypadButton>
          </Grid>
          <Grid size={{ xs: 3 }}>
            <KeypadButton onClick={() => handleNumberClick("5")}>5</KeypadButton>
          </Grid>
          <Grid size={{ xs: 3 }}>
            <KeypadButton onClick={() => handleNumberClick("6")}>6</KeypadButton>
          </Grid>
          <Grid size={{ xs: 3 }}>
            <AmountButton onClick={() => setValue(formatNumber("200000"))}>200.000</AmountButton>
          </Grid>

          <Grid size={{ xs: 3 }}>
            <KeypadButton onClick={() => handleNumberClick("7")}>7</KeypadButton>
          </Grid>
          <Grid size={{ xs: 3 }}>
            <KeypadButton onClick={() => handleNumberClick("8")}>8</KeypadButton>
          </Grid>
          <Grid size={{ xs: 3 }}>
            <KeypadButton onClick={() => handleNumberClick("9")}>9</KeypadButton>
          </Grid>
          <Grid size={{ xs: 3 }}>
            <AmountButton onClick={() => setValue(formatNumber("500000"))}>500.000</AmountButton>
          </Grid>
          <Grid size={{ xs: 3 }}>
            <EmptyButton />
          </Grid>
          <Grid size={{ xs: 3 }}>
            <KeypadButton onClick={() => handleNumberClick("0")}>0</KeypadButton>
          </Grid>
          <Grid size={{ xs: 3 }}>
            <EmptyButton />
          </Grid>
          <Grid size={{ xs: 3 }}>
            <KeypadButton
              onClick={handleBackspace}
              sx={{
                backgroundColor: "#ff7675",
                color: "white",
                border: "none",
                "&:hover": {
                  backgroundColor: "#FF6E6E",
                },
              }}
            >
              <BackspaceIcon />
            </KeypadButton>
          </Grid>
        </Grid>
        <DialogActions sx={{ mt: 2 }}>
          <Button
            fullWidth
            variant="contained"
            onClick={onClose}
            sx={{
              height: "48px",
              backgroundColor: "#e0e0e0",
              color: "#000",
              "&:hover": {
                backgroundColor: "#C7C7C7",
              },
              mr: 1,
            }}
          >
            Đóng
          </Button>
          <Button
            fullWidth
            variant="contained"
            onClick={handleSubmitUsePoint}
            disabled={!isValid}
            sx={{
              height: "48px",
              backgroundColor: isValid ? "#4834d4" : "#cccccc",
              color: "white",
              "&:hover": {
                backgroundColor: isValid ? "#2654FE" : "#cccccc",
              },
              ml: 1,
            }}
          >
            Xác nhận
          </Button>
        </DialogActions>
      </Box>
    </Container>
  );
}
