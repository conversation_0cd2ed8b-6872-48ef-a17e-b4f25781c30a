import React, { useEffect } from "react";
import Button from "@mui/material/Button";
import { styled } from "@mui/material/styles";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import Typography from "@mui/material/Typography";
import { FormProvider, useForm, useFormContext } from "react-hook-form";

const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialogContent-root": {
    padding: theme.spacing(2),
  },
  "& .MuiDialogActions-root": {
    padding: theme.spacing(1),
  },
}));

export default function FormDialog({
  open,
  onClose,
  onSubmit,
  title,
  children,
  closeBtnTitle = "Hủy",
  submitBtnTitle = "Lưu",
}) {
  const { handleSubmit } = useFormContext();

  const handleFormSubmit = (data) => {
    if (onSubmit) onSubmit(data); // Gọi hàm submit
    onClose(); // Đóng dialog sau khi submit
  };

  useEffect(() => {}, [open]);
  return (
    <React.Fragment>
      <BootstrapDialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
          <Typography sx={{ fontSize: 18, fontWeight: 600 }}>{title}</Typography>
        </DialogTitle>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={(theme) => ({
            position: "absolute",
            right: 8,
            top: 8,
            color: theme.palette.grey[500],
          })}
        >
          <CloseIcon />
        </IconButton>

        <DialogContent dividers>{children}</DialogContent>
        <DialogActions>
          <Button variant="outlined" onClick={onClose}>
            {closeBtnTitle}
          </Button>
          <Button variant="contained" onClick={handleSubmit(handleFormSubmit)}>
            {submitBtnTitle}
          </Button>
        </DialogActions>
      </BootstrapDialog>
    </React.Fragment>
  );
}
