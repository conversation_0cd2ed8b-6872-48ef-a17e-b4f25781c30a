import { Box } from "@mui/material";
import React from "react";
interface AuthLayoutProps {
  children: React.ReactNode;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box
        sx={{
          flex: 1,
          backgroundColor: "background.paper",
          backgroundImage: "url('/assets/gradient-bg.svg')",
          backgroundPosition: "top center",
          backgroundRepeat: "no-repeat",
          color: "common.white",
          display: "flex",
          justifyContent: "center",
          p: {
            xs: 4,
            md: 8,
          },
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

export default AuthLayout;
