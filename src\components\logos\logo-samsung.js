export const <PERSON><PERSON><PERSON><PERSON><PERSON> = (props) => (
  <svg
    width={106}
    height={16}
    viewBox="0 0 106 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <g clipPath="url(#clip0_11275_169463)">
      <path
        d="M99.6098 7.02737V9.20543H101.136V11.3664C101.14 11.5595 101.13 11.7678 101.097 11.9344C101.036 12.3375 100.655 13.0238 99.5714 13.0238C98.4948 13.0238 98.12 12.3375 98.055 11.9344C98.0276 11.7678 98.016 11.5595 98.016 11.3664V4.54097C98.016 4.29953 98.0322 4.03528 98.0833 3.83527C98.1572 3.4715 98.4786 2.75411 99.5634 2.75411C100.702 2.75411 100.986 3.51043 101.052 3.83527C101.094 4.05043 101.097 4.41133 101.097 4.41133V5.24014H104.846V4.75018C104.846 4.75018 104.863 4.23894 104.818 3.76183C104.536 0.960353 102.235 0.0740204 99.5965 0.0740204C96.9533 0.0740204 94.699 0.968599 94.3706 3.76183C94.341 4.01726 94.2957 4.47672 94.2957 4.75018V11.0289C94.2957 11.3024 94.3045 11.5141 94.3549 12.0138C94.5996 14.7382 96.9533 15.7039 99.586 15.7039C102.235 15.7039 104.572 14.7382 104.821 12.0138C104.865 11.5141 104.87 11.3024 104.876 11.0289V7.02737H99.6098ZM73.7587 0.475766H69.9937V11.5239C69.9996 11.7164 69.9937 11.9327 69.9606 12.0913C69.8821 12.4622 69.5688 13.1759 68.53 13.1759C67.5047 13.1759 67.1831 12.4622 67.1105 12.0913C67.0728 11.9327 67.0681 11.7164 67.0728 11.5239V0.475766H63.309V11.181C63.3042 11.4569 63.3258 12.0207 63.342 12.1684C63.6018 14.958 65.7934 15.8637 68.53 15.8637C71.272 15.8637 73.4623 14.958 73.7268 12.1684C73.7477 12.0207 73.775 11.4569 73.7587 11.181V0.475766ZM39.1651 0.475766L37.287 12.1496L35.41 0.475766H29.3368L29.0147 15.4099H32.7353L32.8361 1.58493L35.396 15.4099H39.171L41.7333 1.58493L41.8344 15.4099H45.5644L45.2314 0.475766H39.1651ZM16.6791 0.475766L13.9215 15.4099H17.9428L20.0209 1.58493L22.0491 15.4099H26.0425L23.2964 0.475766H16.6791ZM86.9168 12.4995L83.411 0.475766H77.8866V15.2542H81.5411L81.3289 2.84577L85.094 15.2542H90.3923V0.475766H86.7138L86.9168 12.4995ZM53.113 4.36243C53.0469 4.0675 53.066 3.75416 53.1002 3.59039C53.2067 3.11233 53.5269 2.59092 54.4494 2.59092C55.3092 2.59092 55.8133 3.12862 55.8133 3.93557V4.84875H59.4835V3.80785C59.4835 0.590056 56.6078 0.084568 54.5257 0.084568C51.9034 0.084568 49.7619 0.951724 49.3708 3.37293C49.2673 4.03183 49.2417 4.61671 49.4063 5.36306C50.044 8.38199 55.283 9.25778 56.0435 11.167C56.1777 11.5286 56.1392 11.9898 56.0708 12.2616C55.9569 12.7584 55.6237 13.2582 54.636 13.2582C53.7096 13.2582 53.152 12.7239 53.152 11.9187L53.1509 10.4854H49.2004V11.6247C49.2004 14.9259 51.778 15.922 54.5542 15.922C57.213 15.922 59.409 15.0105 59.7613 12.5398C59.9293 11.2632 59.8026 10.4318 59.7351 10.1186C59.1196 7.0222 53.5357 6.0908 53.113 4.36243ZM4.69816 4.32581C4.62609 4.02474 4.64291 3.70698 4.68305 3.5413C4.78475 3.06554 5.10684 2.53492 6.04562 2.53492C6.91634 2.53492 7.42902 3.07781 7.42902 3.89127V4.81557H11.1422V3.76643C11.1422 0.517763 8.22251 0 6.11769 0C3.47341 0 1.31277 0.88058 0.917457 3.31924C0.809262 3.98869 0.790714 4.57759 0.94708 5.33218C1.59166 8.38545 6.8825 9.26833 7.65229 11.2003C7.79642 11.5595 7.75284 12.0207 7.68135 12.303C7.5592 12.805 7.22334 13.3119 6.22741 13.3119C5.29743 13.3119 4.74117 12.7684 4.74117 11.9521L4.736 10.515H0.741211V11.6569C0.741211 14.9959 3.35528 16 6.15305 16C8.84282 16 11.0516 15.0805 11.412 12.5812C11.5911 11.293 11.4567 10.4546 11.3963 10.1366C10.768 7.00225 5.12195 6.07393 4.69816 4.32581Z"
        fill="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_11275_169463">
        <rect
          width={105}
          height={16}
          fill="white"
          transform="translate(0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);
