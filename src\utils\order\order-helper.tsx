import { CartStatusDelivery, CartTransportService } from "@/src/api/types/cart.types";
import { TypeOrderStatus, TypePayStatus, TypeTransportStatus } from "@/src/api/types/order.type";
import { Box, Chip, Typography } from "@mui/material"; // Import Box nếu chưa
import { JSX } from "react";
export const getTransportStatusText = (status: TypeTransportStatus): string => {
  switch (status) {
    case "Created":
      return "Chờ xác nhận";
    case "Verified":
      return "Đã xác nhận";
    case "WaitingForDelivery":
      return "Chờ vận chuyển";
    case "Delivered":
      return "Đã giao hàng";
    case "Transporting":
      return "Đang vận chuyển";
    case "Delivering":
      return "Đang giao hàng";
    case "Success":
      return "Thành công";
    case "Waiting":
      return "Đang chờ";
    case "Refunding":
      return "Đang hoàn tiền";
    case "Refunded":
      return "Trả hàng";
    case "Cancel":
      return "Đã hủy";
    default:
      return "Trạng thái không xác định";
  }
};

export const getOrderStatusText = (status: TypeOrderStatus): string => {
  switch (status) {
    case "Pending":
      return "Chưa thanh toán";
    case "Paid":
      return "Đã thanh toán";
    case "Success":
      return "Thành công";
    case "Failed":
      return "Thất bại";
    case "Refund":
      return "Đã hoàn tiền";
    default:
      return "Trạng thái không xác định";
  }
};

// Trạng thái đơn hàng trả về JSX component (với Box)
export const getOrderStatusLabel = (status: TypeOrderStatus): JSX.Element => {
  switch (status) {
    case "Pending":
      return (
        <Box display="flex" sx={{ flexShrink: 0, alignItems: "center" }} gap={1}>
          <Box
            sx={{
              width: 14, // Chiều rộng của box
              height: 14, // Chiều cao của box (phải bằng chiều rộng để tạo hình tròn)
              borderRadius: "50%", // Bo góc 50% để tạo hình tròn
              border: "2px solid #FCC21B", // Màu nền cho box (có thể thay đổi),
              display: "flex", // Đảm bảo hộp có đủ không gian
              justifyContent: "center", // Căn giữa nội dung bên trong nếu có
              alignItems: "center", // Căn giữa nội dung bên trong nếu có
            }}
          />
          <Typography color="FCC21B">Chưa thanh toán</Typography>
        </Box>
      );
    case "Paid":
      return (
        <Box display="flex" sx={{ flexShrink: 0, alignItems: "center" }} gap={1}>
          <Box
            sx={{
              width: 14, // Chiều rộng của box
              height: 14, // Chiều cao của box (phải bằng chiều rộng để tạo hình tròn)
              borderRadius: "50%", // Bo góc 50% để tạo hình tròn
              background: "#FCC21B", // Màu nền cho box (có thể thay đổi),
              display: "flex", // Đảm bảo hộp có đủ không gian
              justifyContent: "center", // Căn giữa nội dung bên trong nếu có
              alignItems: "center", // Căn giữa nội dung bên trong nếu có
            }}
          />
          <Typography>Đã thanh toán</Typography>
        </Box>
      );
    case "Success":
      return <Box>Thành công</Box>;
    case "Failed":
      return <Box>Thất bại</Box>;
    case "Refund":
      return <Box>Đã hoàn tiền</Box>;
    default:
      return <Box>Trạng thái không xác định</Box>;
  }
};

export const getStatusDeliveryLabel = (
  status: CartStatusDelivery,
  transportService: CartTransportService | null
): string => {
  switch (status) {
    case "InShop":
      return "Nhận hàng tại quầy";
    case "ExpressDelivery":
      switch (transportService) {
        case "LCOD":
          return "Shop tự giao hàng";
        case "NCOD":
          return "Giao hàng nhanh";
        case "VHT":
          return "Viettel Cargo";
        case "PHS":
          return "PHS";
        case "PTN":
          return "Bưu điện";
        case "VCN":
          return "Vietnam Post";
        case "VTK":
          return "Viettel Post";
        case "AHAMOVE":
          return "Ahamove";
        case "JTEXPRESS":
          return "J&T Express";
        case "EMS":
          return "EMS Vietnam";
        case "NINJAVAN":
          return "Ninjavan";
        case "BESTEXPRESS":
          return "Best Express";
        default:
          return "Dịch vụ vận chuyển không xác định";
      }
    default:
      return "Trạng thái không xác định";
  }
};

const orderColor = {
  green: "#00BF50",
  yellow: "#FCC21B",
  gray: "#D9D9D9",
};

const OrderStatusWithBorder = ({ colorValue, statusText }) => {
  return (
    <Box
      sx={{
        color: `${colorValue}`,
        display: "inline-flex",
        position: "relative",
        alignItems: "center",
        padding: "0 4px",
        lineHeight: "24px",
        borderRadius: 1, // Chú ý: borderRadius nên là số nếu không có đơn vị px
        overflow: "hidden",
        whiteSpace: "nowrap",
        "&::after": {
          content: "''",
          display: "flex",
          background: `${colorValue}`, // Gán giá trị trực tiếp cho background
          opacity: 0.1,
          position: "absolute",
          left: 0,
          right: 0,
          bottom: 0,
          top: 0,
        },
      }}
    >
      <Box
        sx={{
          display: "inline-block",
          width: "12px",
          height: "12px",
          boxSizing: "border-box",
          borderRadius: "50%",
          marginRight: "6px",
          border: "2px solid red",
        }}
      ></Box>
      <Box
        sx={{
          color: "#000", // Sử dụng giá trị biến CSS
          fontSize: "14px", // Dùng 'fontSize' thay vì 'font-size'
          fontWeight: 400, // Dùng 'fontWeight' thay vì 'font-weight'
        }}
      >
        <Typography>{statusText}</Typography>
      </Box>
    </Box>
  );
};

const OrderStatusWithBackground = ({ colorValue, statusText }) => {
  return (
    <Box display="flex" sx={{ flexShrink: 0, alignItems: "center" }} gap={1}>
      <Box
        sx={{
          width: 14, // Chiều rộng của box
          height: 14, // Chiều cao của box (phải bằng chiều rộng để tạo hình tròn)
          borderRadius: "50%", // Bo góc 50% để tạo hình tròn
          backgroundColor: colorValue, // Màu nền cho box (có thể thay đổi),
          display: "flex", // Đảm bảo hộp có đủ không gian
          justifyContent: "center", // Căn giữa nội dung bên trong nếu có
          alignItems: "center", // Căn giữa nội dung bên trong nếu có
        }}
      />
      <Typography>{statusText}</Typography>
    </Box>
  );
};

export const getTransportStatusLabel = (status: TypeTransportStatus) => {
  switch (status) {
    case "Created":
      return <OrderStatusWithBorder colorValue={orderColor.green} statusText="Chờ xác nhận" />;
    case "Verified":
      return "Đã xác nhận";
    case "WaitingForDelivery":
      return <OrderStatusWithBorder colorValue={orderColor.green} statusText="Chờ vận chuyển" />;
    case "Delivered":
      return "Đã giao hàng";
    case "Transporting":
      return (
        <OrderStatusWithBackground colorValue={orderColor.green} statusText="Đang vận chuyển" />
      );
    case "Delivering":
      return (
        <OrderStatusWithBackground colorValue={orderColor.green} statusText="Đang giao hàng" />
      );
    case "Success":
      return <OrderStatusWithBackground colorValue={orderColor.gray} statusText="Hoàn thành" />;
    case "Waiting":
      return "Đang chờ";
    case "Refunding":
      return "Đang hoàn tiền";
    case "Refunded":
      return "Trả hàng";
    case "Cancel":
      return "Đã hủy";
    default:
      return "Trạng thái không xác định";
  }
};

export const getFilterTransportStatusText = (status): string => {
  switch (status) {
    case "All":
      return "Tất cả";
    case "Created":
      return "Chờ xác nhận";
    case "Verified":
      return "Đã xác nhận";
    case "WaitingForDelivery":
      return "Chờ vận chuyển";
    case "Delivered":
      return "Đã giao hàng";
    case "Transporting":
      return "Đang vận chuyển";
    case "Delivering":
      return "Đang giao hàng";
    case "Success":
      return "Hoàn thành";
    case "Waiting":
      return "Đang chờ";
    case "Refunding":
      return "Đang hoàn tiền";
    case "Refunded":
      return "Trả hàng";
    case "Cancel":
      return "Đã hủy";
    default:
      return "Trạng thái không xác định";
  }
};

export const getOrderStatusPayLabel = (status: TypePayStatus) => {
  switch (status) {
    case "NotPaid":
      return <OrderStatusWithBorder colorValue={orderColor.yellow} statusText="Chưa thanh toán" />;
    case "Paid":
      return (
        <OrderStatusWithBackground colorValue={orderColor.yellow} statusText="Đã thanh toán" />
      );
    case "Refund":
      return <OrderStatusWithBorder colorValue={orderColor.gray} statusText="Đã hủy" />;

    default:
      return "Trạng thái không xác định";
  }
};

export const getFilterOrderStatusText = (status): string => {
  switch (status) {
    case "All":
      return "Tất cả";
    case "Pending":
      return "Chờ xác nhận";
    case "WaitingForDelivery":
      return "Chờ vận chuyển";
    case "Delivering":
      return "Đang vận chuyển";
    case "Success":
      return "Hoàn thành";
    case "Waiting":
      return "Đang chờ";
    case "Refund":
      return "Trả hàng";
    case "Failed":
      return "Đã hủy";
    default:
      return "Trạng thái không xác định";
  }
};

export const OrderProductPayStatusWithBg = ({ status }: { status: TypePayStatus }) => {
  switch (status) {
    case "NotPaid":
      return (
        <OrderProductStatusWithBg
          iconStyle="empty"
          colorValue={orderColor.yellow}
          statusText="Chưa thanh toán"
        />
      );
    case "Paid":
      return (
        <OrderProductStatusWithBg
          iconStyle="full"
          colorValue={orderColor.yellow}
          statusText="Đã thanh toán"
        />
      );
    case "Refund":
      return (
        <OrderProductStatusWithBg
          iconStyle="full"
          colorValue={orderColor.gray}
          statusText="Đã hoàn tiền"
        />
      );

    default:
      return "Trạng thái không xác định";
  }
};

export const OrderProductTransportStatusWithBg = ({ status }: { status: TypeTransportStatus }) => {
  switch (status) {
    case "Created":
    case "WaitingForDelivery":
      return (
        <OrderProductStatusWithBg
          iconStyle="empty"
          colorValue={orderColor.green}
          statusText="Chờ vận chuyển"
        />
      );
    case "Delivering":
      return (
        <OrderProductStatusWithBg
          iconStyle="full"
          colorValue={orderColor.green}
          statusText="Đang vận chuyển"
        />
      );
    case "Refunded":
      return (
        <OrderProductStatusWithBg
          iconStyle="full"
          colorValue={orderColor.gray}
          statusText="Đã trả hàng"
        />
      );
    case "Success":
      return (
        <OrderProductStatusWithBg
          iconStyle="full"
          colorValue={orderColor.gray}
          statusText="Hoàn thành"
        />
      );

    case "Cancel":
      return (
        <OrderProductStatusWithBg
          iconStyle="full"
          colorValue={orderColor.gray}
          statusText="Đã hủy"
        />
      );

    default:
      return "Trạng thái không xác định";
  }
};

const OrderProductStatusWithBg = ({ colorValue, statusText, iconStyle }) => {
  return (
    <Box
      sx={{
        color: `${colorValue}`,
        display: "inline-flex",
        position: "relative",
        alignItems: "center",
        paddingTop: 0.5,
        paddingLeft: 1,
        paddingRight: 1,
        paddingBottom: 0.5,
        lineHeight: "24px",
        borderRadius: 1, // Chú ý: borderRadius nên là số nếu không có đơn vị px
        overflow: "hidden",
        whiteSpace: "nowrap",
        "&::after": {
          content: "''",
          display: "flex",
          background: `${colorValue}`, // Gán giá trị trực tiếp cho background
          opacity: 0.1,
          position: "absolute",
          left: 0,
          right: 0,
          bottom: 0,
          top: 0,
        },
      }}
    >
      <Box
        sx={{
          display: "inline-block",
          width: "12px",
          height: "12px",
          boxSizing: "border-box",
          borderRadius: "50%",
          marginRight: 0.8,
          ...(iconStyle === "empty" && { border: `2px solid ${colorValue}` }), // Chỉ thêm border khi hasBorder là true
          ...(iconStyle === "full" && { backgroundColor: `${colorValue}` }), // Chỉ thêm border khi hasBorder là true
        }}
      ></Box>
      <Box
        sx={{
          color: "#000", // Sử dụng giá trị biến CSS
        }}
      >
        <Typography>{statusText}</Typography>
      </Box>
    </Box>
  );
};
