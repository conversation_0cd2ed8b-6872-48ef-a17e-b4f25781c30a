import { Box, Radio, RadioGroup, FormControlLabel, Typography } from "@mui/material";
import { styled } from "@mui/material/styles";
import React, { useEffect, useState } from "react";
import { usePayment } from "@/src/api/hooks/payment/use-payment";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useCart } from "@/src/api/hooks/cart/use-cart";
import useSnackbar from "@/src/hooks/use-snackbar";

const StyledContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  maxWidth: 500,
  margin: "10px auto",
}));

const boxStyles = (selected: boolean) => ({
  backgroundColor: selected ? "#BBDEFB" : "#E0E0E0",
  borderRadius: "4px",
  mb: 1,
  p: 1,
  cursor: "pointer",
  border: selected ? "2px solid #42a5f5" : "none",
});

const formControlLabelStyles = {
  width: "100%",
  m: 0,
  display: "flex",
  flexDirection: "row-reverse",
  justifyContent: "space-between",
  alignItems: "center",
};

export default function PaymentMethods({
  cart,
  setCart,
}: {
  cart: any;
  setCart: (cart: any) => void;
}) {
  const [method, setMethod] = useState("");
  const [paymentMethods, setPaymentMethods] = useState<any[]>([]);
  const { getListPayment } = usePayment();
  const { createOrUpdateCart } = useCart();
  const snackbar = useSnackbar();
  const storeId = useStoreId();

  useEffect(() => {
    if (storeId && cart) {
      fetchPaymentMethods();
    }
  }, [storeId]);

  const fetchPaymentMethods = async () => {
    if (storeId) {
      try {
        const response = await getListPayment(storeId, 0, 10);
        if (response && response.data) {
          const posPaymentMethods = response.data.data.filter(
            (method: any) => Array.isArray(method.platform) && method.platform.includes("Pos")
          );
          posPaymentMethods.sort((a, b) => (a.position ?? Infinity) - (b.position ?? Infinity));
          setPaymentMethods(posPaymentMethods);
          if (posPaymentMethods.length > 0) {
            setMethod(posPaymentMethods[0].paymentId);

            const newCart = {
              ...cart,
              paymentId: posPaymentMethods[0].paymentId,
              typePay: posPaymentMethods[0].typePay,
            };
            const response = await createOrUpdateCart(newCart);
            if (response?.data) {
              setCart({
                ...response.data,
                paymentSelected: posPaymentMethods[0],
              });
            }
          } else {
            console.warn("No payment methods found for platform 'Pos'.");
          }
        }
      } catch (error) {
        console.error("Failed to fetch payment methods:", error);
      }
    } else {
      console.warn("Store ID is not defined.");
    }
  };

  const handlePaymentMethodChange = async (paymentId: string) => {
    setMethod(paymentId);

    const selectedPaymentMethod = paymentMethods.find((method) => method.paymentId === paymentId);
    const updatedCart = {
      ...cart,
      paymentSelected: selectedPaymentMethod || cart.paymentSelected,
      paymentId: selectedPaymentMethod.paymentId,
      typePay: selectedPaymentMethod.typePay,
    };

    setCart(updatedCart);

    try {
      const response = await createOrUpdateCart(updatedCart);
      if (response && response.data) {
        setCart({
          ...response.data,
          paymentSelected: updatedCart.paymentSelected,
        });
      } else {
        console.error("Cập nhật phương thức thanh toán thất bại.");
      }
    } catch (error) {
      console.error("Lỗi khi cập nhật phương thức thanh toán:", error);
    }
  };

  return (
    <StyledContainer>
      <Typography
        variant="body1"
        sx={{
          mb: 2,
          color: "#2654FE",
          mt: -4,
          fontWeight: "bold",
          fontSize: "1.3rem",
        }}
      >
        Phương thức thanh toán
      </Typography>

      <Box sx={{ mb: 3, color: "#2654FE" }}>
        <RadioGroup value={method} onChange={(e) => handlePaymentMethodChange(e.target.value)}>
          {paymentMethods.map((paymentMethod) => (
            <Box
              key={paymentMethod.paymentId}
              sx={boxStyles(method === paymentMethod.paymentId)}
              onClick={() => handlePaymentMethodChange(paymentMethod.paymentId)}
            >
              <FormControlLabel
                value={paymentMethod.paymentId}
                control={<Radio />}
                label={paymentMethod.name}
                sx={{
                  ...formControlLabelStyles,
                  "& .MuiTypography-root": {
                    fontSize: "0.7rem",
                  },
                }}
              />
            </Box>
          ))}
        </RadioGroup>
      </Box>
    </StyledContainer>
  );
}
