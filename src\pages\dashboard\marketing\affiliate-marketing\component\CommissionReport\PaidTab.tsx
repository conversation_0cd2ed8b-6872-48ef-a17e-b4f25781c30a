import React, { useState } from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Button,
} from '@mui/material';

const ordersData = [
  {
    id: '0349487888BB',
    partnerName: '<PERSON><PERSON>',
    f1: 10,
    f2: 10,
    revenue: '10,000,000',
    commission: '<PERSON><PERSON><PERSON>',
    PersonalIncomeTax: '1.000.000',
    ActuallyReceived: '9.000.000',
    Receiver: '<PERSON><PERSON>',
    Receiver2: '<PERSON><PERSON><PERSON>',
    paymentMethod: 'BANK',
    paymentInfor: 'MBBANK',
    paymentAccount: '*********',
    Status: '',
    time: '8:09 22/12/2024',
  },
  {
    id: '#12333',
    partnerName: '<PERSON><PERSON><PERSON>',
    f1: 10,
    f2: 10,
    revenue: '10,000,000',
    commission: '<PERSON><PERSON><PERSON>',
    PersonalIncomeTax: '1.000.000',
    ActuallyReceived: '9.000.000',
    Receiver: '<PERSON><PERSON>',
    Receiver2: '<PERSON><PERSON>',
    paymentMethod: 'BANK',
    paymentInfor: 'MBBANK',
    paymentAccount: '*********',
    Status: '',
    time: '8:09 22/12/2024',
  },
  {
    id: '#53',
    partnerName: 'Ngô Anh Tuấn Anh',
    f1: 10,
    f2: 10,
    revenue: '10,000,000',
    commission: 'Tuấn Anh',
    PersonalIncomeTax: '1.000.000',
    ActuallyReceived: '9.000.000',
    Receiver: 'Ngô Văn Tuấn',
    Receiver2: 'Ngô Văn Tuấn',
    paymentMethod: 'BANK',
    paymentInfor: 'MBBANK',
    paymentAccount: '*********',
    Status: '',
    time: '8:09 22/12/2024',
  },
];

const PaidTab = ({ searchQuery }) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);

  const filteredData = ordersData.filter((order) =>
    searchQuery
      ? order.partnerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.id.includes(searchQuery)
      : true
  );

  const paginatedData = filteredData.slice(page * rowsPerPage, (page + 1) * rowsPerPage);

  const handleConfirmPayment = (id) => {
    console.log(`Xác nhận chi cho đơn hàng: ${id}`);
  };

  return (
    <TableContainer sx={{ maxWidth: '100%', mt: 2, overflowX: 'auto' }}>
      <Table size="small">
        <TableHead>
          <TableRow>
            {[
              'ID Đối tác',
              'Tên đối tác',
              'F1',
              'F2',
              'Doanh thu',
              'Hoa hồng',
              'Thuế TNCN',
              'Thực nhận',
              'Người nhận',
              'Người nhận',
              'Phương thức thanh toán',
              'Thông tin thanh toán',
              'Tài khoản thanh toán',
              'Trạng thái',
              'Thời gian',
            ].map((header) => (
              <TableCell key={header} sx={{ fontWeight: 'bold', minWidth: '150px' }}>
                {header}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {paginatedData.length > 0 ? (
            paginatedData.map((order) => {
              return (
                <TableRow key={order.id}>
                  <TableCell>{order.id}</TableCell>
                  <TableCell>{order.partnerName}</TableCell>
                  <TableCell>{order.f1}</TableCell>
                  <TableCell>{order.f2}</TableCell>
                  <TableCell>{order.revenue}</TableCell>
                  <TableCell>{order.commission}</TableCell>
                  <TableCell>{order.PersonalIncomeTax}</TableCell>
                  <TableCell>{order.ActuallyReceived}</TableCell>
                  <TableCell>{order.Receiver}</TableCell>
                  <TableCell>{order.Receiver2}</TableCell>
                  <TableCell>{order.paymentMethod}</TableCell>
                  <TableCell>{order.paymentInfor}</TableCell>
                  <TableCell>{order.paymentAccount}</TableCell>
                  <TableCell>
                    <Button
                      variant="contained"
                      size="small"
                      onClick={() => handleConfirmPayment(order.id)}
                      sx={{
                        color: '#2654FE',
                        textTransform: 'none',
                        background: '#fff ',
                        fontSize: '14px',
                        padding: '2px 8px',
                        boxShadow: 'none',
                      }}
                    >
                      Xác nhận chi
                    </Button>
                  </TableCell>
                  <TableCell>{order.time}</TableCell>
                </TableRow>
              );
            })
          ) : (
            <TableRow>
              <TableCell colSpan={14} align="center">
                Không có dữ liệu phù hợp
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      <Box display="flex" justifyContent="flex-end" mt={2} pr={2}>
        <TablePagination
          rowsPerPageOptions={[1, 2, 3]}
          component="div"
          count={filteredData.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={(event, newPage) => setPage(newPage)}
          onRowsPerPageChange={(event) => {
            setRowsPerPage(parseInt(event.target.value, 10));
            setPage(0);
          }}
        />
      </Box>
    </TableContainer>
  );
};

export default PaidTab;
