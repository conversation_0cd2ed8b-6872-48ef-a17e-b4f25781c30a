import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  <PERSON>,
  Typography,
  TextField,
  Switch,
  Button,
  Divider,
  CircularProgress,
  IconButton,
  debounce,
} from "@mui/material";
import SettingLayout from "@/src/components/settings/settings-page/SettingLayout";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import dynamic from "next/dynamic";
import DeleteIcon from "@mui/icons-material/Delete";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useRouter } from "next/router";
import { paths } from "@/src/paths";
import { useStoreId } from "@/src/hooks/use-store-id";
import { shopPolicyService } from "@/src/api/services/shop-policy/shop-policy.service";
import { v4 as uuidv4 } from "uuid";
import { useMedia } from "@/src/api/hooks/media/use-media";
import { stripHtmlAndSpaces } from "@/src/components/react-quill-editor";
import DOMPurify from "dompurify";

const ReactQuillEditor = dynamic(() => import("@/src/components/react-quill-editor"), {
  ssr: false,
  loading: () => <p>Loading editor...</p>,
});

const validationSchema = Yup.object({
  title: Yup.string()
    .trim()
    .required("Tiêu đề là bắt buộc")
    .max(255, "Tiêu đề không được vượt quá 255 ký tự"),
  content: Yup.string()
    .trim()
    .required("Nội dung là bắt buộc")
    .test(
      "max-length-without-html-and-spaces",
      "Nội dung không được vượt quá 4000 ký tự",
      function (value) {
        const textOnly = stripHtmlAndSpaces(value);
        return textOnly.length <= 4000;
      }
    ),
});

const initialValues = {
  title: "",
  content: "",
};

interface ShopPolicyDto {
  shopId: string;
  shopPolicy: string;
  updated: string;
}

export default function SettingClause() {
  const storeId = useStoreId();
  const snackbar = useSnackbar();
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const [listShopPolicy, setListShopPolicy] = useState([]);
  const [defaultGroupId, setDefaultGroupId] = useState<string>("");
  const { getGroups } = useMedia();
  useEffect(() => {
    const fetchDefaultGroup = async () => {
      try {
        const data = {
          ShopId: storeId,
          Skip: 0,
          Limit: 1,
        };
        const response = await getGroups(data);
        if (response?.data?.data?.length > 0) {
          setDefaultGroupId(response.data.data[0].groupFileId);
        }
      } catch (error) {
        console.error("Error fetching groups:", error);
      }
    };
    if (storeId) fetchDefaultGroup();
  }, [storeId]);
  const fetchShopPolicy = async () => {
    if (storeId) {
      const res = await shopPolicyService.getListShopPolicy(storeId, 0, 99);

      setListShopPolicy(res?.data?.data);
    }
  };
  useEffect(() => {
    fetchShopPolicy();
  }, [storeId]);

  function getCurrentDateTime() {
    const now = new Date();

    const day = now.getDate().toString().padStart(2, "0");
    const month = (now.getMonth() + 1).toString().padStart(2, "0");
    const year = now.getFullYear();

    return `${day}/${month}/${year}`;
  }

  const handleSubmit = async (values: any) => {
    const created = getCurrentDateTime();
    const id = uuidv4();
    let listConvertP = [];
    listConvertP =
      listShopPolicy[0]?.shopPolicy === null ? [] : JSON.parse(listShopPolicy[0]?.shopPolicy);

    values.id = id;
    values.created = created;
    values.updated = created;
    values.content = DOMPurify.sanitize(values.content);

    listConvertP.push(values);

    var data = {
      shopId: storeId,
      shopPolicy: JSON.stringify(listConvertP),
    };
    const res = await shopPolicyService.createShopPolicy(data);
    if (res?.status === 200) {
      snackbar.success("Tạo điều khoản chính sách thành công");
      router.push(paths.settings.clause.index);
    }
  };

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: handleSubmit,
  });

  const handleBack = () => {
    router.push(paths.settings.clause.index);
  };

  const debouncedSetFieldValue = useCallback(
    debounce((content: string) => {
      formik.setFieldValue("content", content);
    }, 500),
    [formik.setFieldValue]
  );
  const handleChangeContent = useCallback(
    (content: string) => {
      if (!formik.touched.content) {
        formik.setFieldTouched("content", true);
      }
      debouncedSetFieldValue(content);
    },
    [formik.touched.content, formik.setFieldTouched, debouncedSetFieldValue]
  );

  return (
    <SettingLayout>
      <Box sx={{ p: 3, pt: 0, bgcolor: "#fff", borderRadius: 2, boxShadow: 1 }}>
        <Box sx={{ display: "flex", alignItems: "center", mb: 0 }}>
          <IconButton onClick={handleBack} sx={{ mr: 2 }}>
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h5" sx={{ fontSize: "20px", fontWeight: 600 }}>
            Điều khoản, chính sách
          </Typography>
        </Box>
        <Box sx={{ p: 3, bgcolor: "#fff", borderRadius: 2, boxShadow: 1 }}>
          <Box
            sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 0.5 }}
          >
            <Typography variant="body2" sx={{ display: "flex", fontSize: "16px", fontWeight: 500 }}>
              Tiêu đề <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
            </Typography>
          </Box>

          <TextField
            fullWidth
            variant="outlined"
            placeholder="Nhập tiêu đề"
            name="title"
            value={formik.values.title}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={!!(formik.touched.title && formik.errors.title)}
            helperText={
              formik.touched.title && typeof formik.errors.title === "string"
                ? formik.errors.title
                : ""
            }
            sx={{
              mb: 3,
              "& .MuiOutlinedInput-root": {
                height: "40px",
              },
            }}
          />
          <Typography
            variant="body2"
            sx={{ display: "flex", fontSize: "16px", fontWeight: 500, mb: 0.5 }}
          >
            Nội dung <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
          </Typography>
          <Box
            sx={{
              borderRadius: 1,
              overflow: "hidden",
              mb: 3,
            }}
          >
            <ReactQuillEditor
              value={formik.values.content || ""}
              onChange={handleChangeContent}
              shopId={storeId}
              defaultGroupId={defaultGroupId}
              error={typeof formik.errors.content === "string" ? formik.errors.content : ""}
            />
          </Box>
          <Divider sx={{ my: 2 }} />
          <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}>
            <Button
              variant="outlined"
              color="error"
              onClick={formik.handleReset}
              disabled={isLoading}
              startIcon={<DeleteIcon />}
            >
              Xoá
            </Button>
            <Button variant="contained" onClick={() => formik.handleSubmit()} disabled={isLoading}>
              {isLoading ? <CircularProgress size={24} /> : "Lưu"}
            </Button>
          </Box>
        </Box>
      </Box>
    </SettingLayout>
  );
}
