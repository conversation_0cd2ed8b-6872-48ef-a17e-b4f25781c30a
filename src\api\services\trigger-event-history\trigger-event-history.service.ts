import { apiClient } from "../../config/api-client";
import { API_PATHS } from "../../constants/api-paths";
import type { ApiError, ErrorConfig } from "../../types/error.types";
import type { ExtendedRequestConfig } from "../../types/api.types";
import { logger } from "@/src/utils/logger";
import { UpdateUserInfoBody } from "../../types/user.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }

  throw error;
}
export interface TriggerEventHistoryDto {
  partnerId: string;
  shopId: string;
  triggerEventId: string;
  triggerEventCode: string;
  triggerEventName: string;
  refId: string;
  channelType?: string;
  channelName: string;
  templateId: string;
  status: string;
  price: number;
  recipient: string | null;
  retryCount: number;
  sendTime: string;
  description: string;
}

interface PagingParams {
  NameType?: string;
  SortType?: string;
  PageSize?: number;
  PageIndex?: number;
  Search?: string;
  Name?: string;
  Sort?: string;
}

export interface ParamGetListTriggerEventHistory {
  ShopId: string;
  TriggerEventCode?: string;
  FromDate?: string;
  ToDate?: string;
  ChannelType?: string;
  Search?: string;
  Paging?: PagingParams;
}
export const userTriggerEventHistoryService = {
  getListHistorySendMessageByTriggerEvent: async <T = any>(
    params: ParamGetListTriggerEventHistory,
    errorConfig?: ErrorConfig
  ): Promise<T | undefined> => {
    const config: any = {
      errorHandling: errorConfig,
    };

    try {
      if (params.ShopId) {
        const searchParams = new URLSearchParams();

        searchParams.append("ShopId", params.ShopId);
        searchParams.append("TriggerEventCode", params.TriggerEventCode);

        searchParams.append("ChannelType", params.ChannelType);
        searchParams.append("FromDate", params.FromDate);
        searchParams.append("ToDate", params.ToDate);

        if (params.Paging) {
          Object.entries(params.Paging).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
              searchParams.append(`Paging.${key}`, value.toString());
            }
          });
        }

        const queryString = searchParams.toString();
        const url = `${API_PATHS.TRIGGER_EVENT_HISTORY.API_TRIGGER_EVENT_HISTORY}?${queryString}`;

        const response = await apiClient.get<T>(url, config);
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
