import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  IconButton,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@mui/material";
import { Menu } from "@mui/icons-material";
import { styled } from "@mui/system";
import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import OrderManagement from "./management";
import { paths } from "src/paths";
import { orderService } from "@/src/api/services/order/order.service";
import { useAppSelector } from "@/src/redux/hooks";

const TopButton = styled(Button)({
  backgroundColor: "#2654FE",
  color: "white",
  "&:hover": { backgroundColor: "#0040FF" },
  borderRadius: 4,
  textTransform: "none",
  padding: "8px 16px",
  height: 40,
  width: 360,
});

export default function Header() {
  const [open, setOpen] = useState(false);
  const [tabIndex, setTabIndex] = useState(0);
  const [anchorEl, setAnchorEl] = useState(null);
  const [hasNewInShopOrders, setHasNewInShopOrders] = useState(false);
  const [hasNewDeliveryOrders, setHasNewDeliveryOrders] = useState(false);
  const { profile } = useAppSelector((state) => state.profile);
  const router = useRouter();
  const LargeBadge = styled(Badge)(({ theme }) => ({
    "& .MuiBadge-dot": {
      height: "12px", // Adjusted size
      minWidth: "12px", // Adjusted size
      borderRadius: "6px",
    },
  }));

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        const branch = localStorage.getItem("selectedBranch");
        if (!branch) {
          console.error("No branch selected");
          return;
        }
        const selectedBranch = JSON.parse(branch);
        const today = new Date().toISOString().split("T")[0];

        // Lấy tất cả các đơn trong ngày với orderSource tương ứng
        const paramsInShop = {
          skip: 0,
          limit: 1000, // Lấy đủ các đơn trong ngày
          data: { shopId: selectedBranch.shopId, orderSource: "Partner" },
        };
        const paramsDelivery = {
          skip: 0,
          limit: 1000,
          data: { shopId: selectedBranch.shopId, orderSource: "User" },
        };

        const [responseInShop, responseDelivery] = await Promise.all([
          orderService.listOrder(paramsInShop),
          orderService.listOrder(paramsDelivery),
        ]);

        if (responseInShop?.data?.data) {
          // Lọc các đơn được tạo trong ngày và chưa hoàn thành
          const inShopOrdersToday = responseInShop.data.data.filter((order) =>
            order.created.startsWith(today)
          );
          const hasInShop = inShopOrdersToday.some((order) => order.statusOrder !== "Success");
          setHasNewInShopOrders(hasInShop);
        } else {
          setHasNewInShopOrders(false);
        }

        if (responseDelivery?.data?.data) {
          const deliveryOrdersToday = responseDelivery.data.data.filter((order) =>
            order.created.startsWith(today)
          );
          const hasDelivery = deliveryOrdersToday.some((order) => order.statusOrder !== "Success");
          setHasNewDeliveryOrders(hasDelivery);
        } else {
          setHasNewDeliveryOrders(false);
        }
      } catch (error) {
        console.error("Failed to fetch orders:", error);
      }
    };

    fetchOrders();
    const interval = setInterval(fetchOrders, 3000);
    return () => clearInterval(interval);
  }, []);

  const handleOpen = (index) => {
    setTabIndex(index);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setTabIndex(0);
  };

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    console.log("Logout clicked");
    handleMenuClose();
    router.push(paths.pos.listLocation);
  };

  return (
    <>
      <AppBar position="static" sx={{ bgcolor: "white", boxShadow: "0 1px 3px rgba(0,0,0,0.1)" }}>
        <Box sx={{ display: "flex", alignItems: "center", px: 1, py: 1, gap: 1 }}>
          <Box component="img" src="/logo/logo.jpg" alt="Logo" sx={{ height: 40, ml: 1 }} />
          <LargeBadge
            color="error"
            variant={hasNewInShopOrders ? "dot" : "standard"}
            overlap="circular"
            anchorOrigin={{ vertical: "top", horizontal: "right" }}
            sx={{ "& .MuiBadge-dot": { top: "5px", right: "5px" } }}
          >
            <TopButton onClick={() => handleOpen(1)}>QUẢN LÝ ĐƠN HÀNG</TopButton>
          </LargeBadge>
          <LargeBadge
            color="error"
            variant={hasNewDeliveryOrders ? "dot" : "standard"}
            overlap="circular"
            anchorOrigin={{ vertical: "top", horizontal: "right" }}
            sx={{ "& .MuiBadge-dot": { top: "5px", right: "5px" } }}
          >
            <TopButton onClick={() => handleOpen(2)}>QUẢN LÝ ĐƠN APP</TopButton>
          </LargeBadge>
          <Box sx={{ display: "flex", alignItems: "center", ml: "auto", gap: 1 }}>
            <Typography sx={{ color: "black", fontWeight: "bold" }}>
              {`${profile?.firstname} ${profile?.lastname}`}
            </Typography>
            <IconButton onClick={handleMenuOpen}>
              <Menu />
            </IconButton>
            <MuiMenu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleMenuClose}>
              <MenuItem onClick={handleLogout}>Thoát</MenuItem>
            </MuiMenu>
          </Box>
        </Box>
      </AppBar>
      <Dialog open={open} onClose={handleClose} maxWidth="lg" fullWidth keepMounted>
        <DialogContent>
          <OrderManagement initialTab={tabIndex} />
        </DialogContent>
      </Dialog>
    </>
  );
}
