import {
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormLabel,
  IconButton,
  Pagination,
  Radio,
  RadioGroup,
  TextField,
  Typography,
} from "@mui/material";
import { useEffect, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { VoucherPromotionFormData } from "./PromotionForm";
import Grid from "@mui/material/Grid2";
import { DateTimePicker } from "../date-time-picker";
export default function PromotionVoucherBox4() {
  const {
    setValue,
    getValues,
    watch,
    control,
    formState: { errors },
  } = useFormContext<VoucherPromotionFormData>(); // Use context to get control

  const startDate = getValues("startDate");
  const endDate = getValues("endDate");
  const isLongTerm = watch("isLongTerm");
  const [isDisableEndDate, setIsDisableEndDate] = useState(false);

  useEffect(() => {
    if (isLongTerm) {
      setIsDisableEndDate(true);
      setValue("startDate", null);
      setValue("endDate", null);
      return;
    }
    setIsDisableEndDate(false);
  }, [isLongTerm]);
  return (
    <Box>
      <Typography marginBottom={3} variant="h6">
        Ngày có hiệu lực
      </Typography>
      <Box>
        <Grid container spacing={2} sx={{ mb: 4 }}>
          <Grid size={{ xs: 12, md: 6 }}>
            <Typography gutterBottom>Thời gian bắt đầu</Typography>
            <DateTimePicker
              label=""
              value={startDate}
              onChange={(newValue) => setValue("startDate", newValue)}
              //   onBlur={() => formik.setFieldTouched('startDate', true)}

              error={!!errors.startDate}
              helperText={errors.startDate?.message}
              sx={{ width: "100%" }}
              disabled={isLongTerm}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6 }}>
            <Typography gutterBottom>
              Thời gian kết thúc <span style={{ color: "red" }}>*</span>
            </Typography>
            <DateTimePicker
              label=""
              value={endDate}
              onChange={(newValue) => setValue("endDate", newValue)}
              // onBlur={() => formik.setFieldTouched('startDate', true)}
              error={!!errors.endDate}
              helperText={errors.endDate?.message}
              sx={{ width: "100%" }}
              disabled={isLongTerm}
            />
          </Grid>
        </Grid>
        <Box>
          <Controller
            name="isLongTerm"
            control={control}
            defaultValue={false} // Checkbox is unchecked by default
            render={({ field }) => (
              <FormControlLabel
                control={
                  <Checkbox
                    {...field}
                    checked={field.value} // The checkbox is controlled by the form state
                  />
                }
                label="Dài hạn"
              />
            )}
          />
        </Box>
      </Box>
    </Box>
  );
}
