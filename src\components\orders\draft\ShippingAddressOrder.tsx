import { <PERSON>, <PERSON>ton, Chip, Divider, IconButton, Paper, Stack, Typography } from "@mui/material";
import React, { useState } from "react";
import TitleDialog from "../../dialog/TitleDialog";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import SearchCustomerOrder from "./SearchCustomerOrder";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import SearchShippingAddressOrder from "./SearchShippingAddressOrder";
import { Edit, MapsHomeWork } from "@mui/icons-material";

export default function ShippingAddressOrder({
  handleSelectAddress,
  selectedAddress,
  selectedCustomer,
  selectCustomerFromList,
}) {
  const [openDialog, setOpenDialog] = useState(false);

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const onClickAddItem = () => {
    setOpenDialog(true);
  };

  const handleSubmit = async (item) => {
    handleSelectAddress(item);
    // addItemToCart(items);

    handleCloseDialog();
  };

  const hadAddress = selectedAddress && selectedAddress.shippingAddressId;
  return (
    <Box sx={{ width: "100%" }}>
      <Box display="flex" marginBottom={2} justifyContent="space-between" alignItems="center">
        <Typography variant="h6" fontWeight="600">
          <MapsHomeWork sx={{ fontSize: 22, marginRight: 0.5, color: "rgb(36, 18, 168)" }} /> Địa
          chỉ giao hàng
        </Typography>

        {hadAddress && (
          <Box>
            <IconButton
              onClick={onClickAddItem}
              size="small"
              sx={{
                mr: 0.5,
                "&:hover": {
                  color: "primary.main",
                  bgcolor: "primary.lighter",
                },
              }}
            >
              <Edit fontSize="small" />
            </IconButton>
          </Box>
        )}
      </Box>

      <Box>
        {hadAddress ? null : (
          <Button
            startIcon={<AddCircleIcon />}
            onClick={onClickAddItem}
            variant="outlined"
            fullWidth
            sx={{
              borderStyle: "dashed",
              paddingTop: 2,
              paddingBottom: 2,
              color: "#2654FE",
              borderColor: "#2654FE",
              "&:hover": {
                borderStyle: "dashed",
                bgcolor: "primary.lighter",
              },
            }}
          >
            Chọn địa chỉ
          </Button>
        )}
      </Box>

      {hadAddress && (
        <>
          <Box
            sx={{
              maxWidth: 400,
              mx: "auto",
              bgcolor: "background.paper",
            }}
          >
            <>
              <Box sx={{ display: "flex" }} gap={2} alignItems="center" mb={0.5}>
                <Typography
                  sx={{
                    fontWeight: 500,
                    mb: 0.5,
                    color: "text.primary",
                    fontSize: "0.95rem",
                  }}
                >
                  {selectedAddress.fullName}
                </Typography>
                <Typography
                  sx={{
                    mb: 0.5,
                    color: "text.secondary",
                    fontSize: "0.9rem",
                  }}
                >
                  {selectedAddress.phoneNumber}
                </Typography>
              </Box>
              <Typography
                fontWeight="bold"
                sx={{
                  mb: 0.5,
                  color: "text.secondary",
                  fontSize: "0.9rem",
                }}
              >
                {selectedAddress.address}
              </Typography>
              <Typography
                sx={{
                  mb: 2,
                  color: "text.secondary",
                  fontSize: "0.9rem",
                }}
              >
                {[
                  selectedAddress.wardName,
                  selectedAddress.districtName,
                  selectedAddress.provinceName,
                ].join(", ")}
              </Typography>
            </>
          </Box>
        </>
      )}

      <TitleDialog
        title="Chọn địa chi giao hàng"
        open={openDialog}
        handleClose={handleCloseDialog}
        submitBtnTitle="Xác nhận"
        showActionDialog={false}
        maxWidth="xl"
      >
        <SearchShippingAddressOrder
          handleSubmit={handleSubmit}
          handleClose={handleCloseDialog}
          selectedAddress={selectedAddress}
          selectedCustomer={selectedCustomer}
        />
      </TitleDialog>
    </Box>
  );
}
