import React, { useState } from "react";
import {
  Stack,
  Box,
  TextField,
  IconButton,
  Tooltip,
  Button,
  Chip,
  Paper,
  FormControl,
  FormHelperText,
} from "@mui/material";
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  AddCircle as AddCircleIcon,
} from "@mui/icons-material";
import { useTranslation } from "react-i18next";
import { tokens } from "@/src/locales/tokens";
import { Specification } from "../../../../../../types/product/variant";
import useSnackbar from "@/src/hooks/use-snackbar";

const MAX_LABEL_LENGTH = 15;

interface SpecificationFormProps {
  specifications: Specification[];
  errors: Record<string, string>;
  setErrors: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  onSpecNameChange: (index: number, value: string) => void;
  onValueChange: (index: number, value: string) => void;
  onValueBlur: (index: number, value: string) => void;
  onAddValue: (specIndex: number, value: string) => void;
  onRemoveValue: (specIndex: number, valueIndex: number) => void;
  onAddSpecification: () => void;
  onRemoveSpecification: (index: number) => void;
  handleBlur?: (index: number, value: string) => void;
  handleSpecFocus?: (index: number) => void;
  errorsVariant?: any;
  setErrorsVariant?: any;
  hasErrors: boolean;
}

const SpecificationForm: React.FC<SpecificationFormProps> = ({
  specifications,
  errors,
  setErrors,
  onSpecNameChange,
  onValueChange,
  onValueBlur,
  onAddValue,
  onRemoveValue,
  onAddSpecification,
  onRemoveSpecification,
  handleBlur,
  handleSpecFocus,
  errorsVariant,
  setErrorsVariant,
  hasErrors,
}) => {
  const { t } = useTranslation();
  const [localValues, setLocalValues] = useState<Record<number, string>>({});

  const handleLocalValueChange = (index: number, value: string) => {
    setLocalValues((prev) => ({
      ...prev,
      [index]: value,
    }));
    onValueChange(index, value);
  };

  const handleAddValueClick = (index: number) => {
    const hasError = specifications.some((item, i) => {
      if (!item.name) {
        setErrors((prev) => ({ ...prev, [`spec${i}`]: "Tên không được để trống" }));
        return true;
      }
      if (!item.values) {
        setErrors((prev) => ({ ...prev, [`specValue${i}`]: "Giá trị không được để trống" }));
        return true;
      }
      return false;
    });
    if (hasError) return;
    if (hasErrors) return;

    const value = localValues[index]?.trim();
    if (!value) return;

    const existed = specifications[index].values.some(
      (v) => v.trim().toLowerCase() === value.toLowerCase()
    );

    if (existed) {
      setErrors((prev) => ({ ...prev, [`specValue${index}`]: "Giá trị không được trùng" }));

      return;
    }

    onAddValue(index, value);
    setLocalValues((prev) => ({
      ...prev,
      [index]: "",
    }));
  };

  const handleKeyPress = (index: number) => (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddValueClick(index);
    }
  };

  return (
    <Stack spacing={2}>
      {specifications.map((spec, index) => (
        <Paper
          key={index}
          elevation={0}
          sx={{
            p: 1.5,
            border: "1px solid",
            borderColor: errors[`spec${index}`] ? "error.main" : "divider",
            borderRadius: 1,
            "&:hover": {
              borderColor: errors[`spec${index}`] ? "error.main" : "primary.main",
              boxShadow: "0 0 0 1px rgba(25, 118, 210, 0.1)",
            },
          }}
        >
          <Stack spacing={1}>
            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr auto auto",
                gap: 1,
                alignItems: "start",
              }}
            >
              <FormControl error={!!errors[`spec${index}`]} sx={{ minHeight: "65px" }}>
                <TextField
                  size="small"
                  label={t(tokens.contentManagement.product.variant.dialog.specificationName)}
                  value={spec.name}
                  onChange={(e) => onSpecNameChange(index, e.target.value)}
                  onBlur={(e) => handleBlur?.(index, e.target.value)}
                  onFocus={() => handleSpecFocus(index)}
                  placeholder={t(
                    tokens.contentManagement.product.variant.dialog.specificationNamePlaceholder
                  )}
                  error={!!errors[`spec${index}`]}
                />
                {errors[`spec${index}`] && (
                  <FormHelperText sx={{ mx: 0, mt: 0.5 }}>{errors[`spec${index}`]}</FormHelperText>
                )}
              </FormControl>

              <FormControl error={!!errors[`specValue${index}`]} sx={{ minHeight: "65px" }}>
                <TextField
                  size="small"
                  label={t(tokens.contentManagement.product.variant.dialog.specificationValue)}
                  value={localValues[index] || ""}
                  onChange={(e) => handleLocalValueChange(index, e.target.value)}
                  onBlur={(e) => onValueBlur(index, e.target.value)}
                  onFocus={() => handleSpecFocus(index)}
                  onKeyPress={handleKeyPress(index)}
                  placeholder={t(
                    tokens.contentManagement.product.variant.dialog.specificationValuePlaceholder
                  )}
                  error={!!errors[`specValue${index}`]}
                />
                {errors[`specValue${index}`] && (
                  <FormHelperText sx={{ mx: 0, mt: 0.5 }}>
                    {errors[`specValue${index}`]}
                  </FormHelperText>
                )}
              </FormControl>

              <Box sx={{ mt: "2px" }}>
                <Tooltip
                  title={t(tokens.contentManagement.product.variant.dialog.addValue)}
                  arrow
                  placement="top"
                >
                  <IconButton
                    disabled={hasErrors || !localValues[index]?.trim()}
                    onClick={() => handleAddValueClick(index)}
                    size="small"
                    sx={{
                      bgcolor: "primary.lighter",
                      color: "primary.main",
                      "&:hover": { bgcolor: "primary.light" },
                      width: 32,
                      height: 32,
                    }}
                  >
                    <AddIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>

              <Box sx={{ mt: "2px" }}>
                {specifications.length > 1 && (
                  <Tooltip
                    title={t(tokens.contentManagement.product.variant.dialog.deleteSpecification)}
                    arrow
                    placement="top"
                  >
                    <IconButton
                      onClick={() => onRemoveSpecification(index)}
                      size="small"
                      sx={{
                        bgcolor: "error.lighter",
                        color: "error.main",
                        "&:hover": { bgcolor: "error.light" },
                        width: 32,
                        height: 32,
                      }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}
              </Box>
            </Box>

            {spec.values.length > 0 && (
              <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5, pl: 0, mt: 0.5 }}>
                {spec.values.map((value, valueIndex) => {
                  const isLong = value.length > MAX_LABEL_LENGTH;
                  const displayLabel = isLong ? value.slice(0, MAX_LABEL_LENGTH) + "..." : value;

                  return (
                    <Tooltip key={valueIndex} title={value} arrow>
                      <Chip
                        label={displayLabel}
                        size="small"
                        onDelete={() => onRemoveValue(index, valueIndex)}
                        sx={{
                          height: 24,
                          "& .MuiChip-label": {
                            px: 1,
                            maxWidth: 150,
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                          },
                          bgcolor: "primary.lighter",
                          "& .MuiChip-deleteIcon": {
                            color: "primary.main",
                            "&:hover": { color: "error.main" },
                          },
                        }}
                      />
                    </Tooltip>
                  );
                })}
              </Box>
            )}
          </Stack>
        </Paper>
      ))}

      {specifications.length < 3 && (
        <Button
          startIcon={<AddCircleIcon />}
          onClick={onAddSpecification}
          variant="outlined"
          sx={{
            borderStyle: "dashed",
            height: 40,
            "&:hover": {
              borderStyle: "dashed",
              bgcolor: "primary.lighter",
            },
          }}
        >
          {t(tokens.contentManagement.product.variant.dialog.addSpecification)}
        </Button>
      )}
    </Stack>
  );
};

export default SpecificationForm;
