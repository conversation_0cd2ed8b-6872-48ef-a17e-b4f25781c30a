import React, { useEffect, useState } from "react";
import { useCart } from "@/src/api/hooks/cart/use-cart";
import { useRouter } from "next/router";
import { paths } from "@/src/paths";
import OrderDraftForm from "@/src/components/orders/draft/OrderDraftForm";

export default function OrderDraftDetail() {
  const router = useRouter();
  const { getCart } = useCart();

  const [cart, setCart] = useState(null);
  // Changed: Get id from query parameter instead of dynamic route
  const { id } = router.query;
  
  const fetchCart = async () => {
    try {
      const response = await getCart(id);
      if (response && response.data) {
        const { data } = response;
        setCart(data);
      } else {
        await router.push(paths.orders.draft.listdraftorder);
      }
    } catch (error) {}
  };
  
  useEffect(() => {
    if (!id) return; // Đảm bảo `id` đã sẵn sày
    fetchCart();
  }, [id]);

  return <OrderDraftForm cart={cart} />;
}
