import { apiClient } from '@/src/api/config/api-client';
import { API_PATHS } from '@/src/api/constants/api-paths';
import type { ApiError, ErrorConfig } from '@/src/api/types/error.types';
import type { ExtendedRequestConfig } from '@/src/api/types/api.types';
import { GetWarehouseRequest, GetWarehouseRequestBody } from '@/src/api/types/warehouse.types';

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export const warehouseService = {
  getWarehouse: async <T = any>(params: GetWarehouseRequest, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<GetWarehouseRequestBody, T>(
        `${API_PATHS.WAREHOUSE.GET_WAREHOUSE}?skip=${params.skip}&limit=${params.limit}&partnerId=${params.partnerId}&shopId=${params.shopId}&search=${params.search}`,
        {
          partnerId: params.partnerId,
          shopId: params.shopId,
          search: params.search,
        },
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
