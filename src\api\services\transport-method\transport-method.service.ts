import { apiClient } from "../../config/api-client";
import { API_PATHS } from "../../constants/api-paths";
import type { ApiError, ErrorConfig } from "../../types/error.types";
import type { ExtendedRequestConfig } from "../../types/api.types";
import { logger } from "@/src/utils/logger";
import { UpdateTransportSettingRequest } from "../../types/transport-method.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export const transportMethodService = {
  getListTransportMethod: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const queryParams = new URLSearchParams({
        shopId: data.shopId,
      });
      const response = await apiClient.get<any, T>(
        `${API_PATHS.TRANSPORT_METHOD.LIST_TRANSPORT_METHOD}?${queryParams.toString()}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  updateTransportMethod: async <T = any>(
    data: UpdateTransportSettingRequest,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.TRANSPORT_METHOD.UPDATE_TRANSPORT_METHOD,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  signUpAhamove: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.TRANSPORT_METHOD.SIGN_UP_AHAMOVE,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
