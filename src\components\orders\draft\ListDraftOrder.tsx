import { customersData } from "@/src/_mock/customers-data";
import { EditNotifications } from "@mui/icons-material";
import {
  Box,
  Button,
  Checkbox,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TablePagination,
  TableRow,
  Tooltip,
} from "@mui/material";
import { useRouter } from "next/router";
import { paths } from "@/src/paths";
import React, { useCallback, useEffect, useState } from "react";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import { formatMoney } from "@/src/utils/format-money";
import _ from "lodash";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useCart } from "@/src/api/hooks/cart/use-cart";
import dayjs from "dayjs";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import { usePathname } from "next/navigation";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";

export default function ListDraftOrder({
  searchText,
  isGranted,
}: {
  searchText: string;
  isGranted: any;
}) {
  const { listCart, loading } = useCart();
  const storeId = useStoreId();
  const pathname = usePathname();
  const [selected, setSelected] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const router = useRouter();
  const [carts, setCarts] = useState([]);
  const [totalCount, setTotalCount] = useState(0); // Total carts count

  const handleSelectAll = (event) => {
    if (event.target.checked) {
      setSelected(carts.map((c) => c.userId));
    } else {
      setSelected([]);
    }
  };
  const handleSelectOne = (event, id) => {
    if (event.target.checked) {
      setSelected((prevSelected) => [...prevSelected, id]);
    } else {
      setSelected((prevSelected) => prevSelected.filter((item) => item !== id));
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleEdit = (cId) => {
    router.push(`${paths.orders.draft.detail}?id=${cId}`);
  };

  const fetListCart = async (currentPage, pageSize, searchQuery, shopId) => {
    const skip = currentPage * pageSize;
    const limit = pageSize;

    const response = await listCart({ skip, limit, shopId });

    if (response && response.data) {
      setCarts(response.data.data || []);
      setTotalCount(response.data.total || 0);
    }
  };

  // Wrap fetListCart with debounce
  const debouncedFetchUserList = useCallback(
    _.debounce((currentPage, pageSize, searchQuery, shopId) => {
      fetListCart(currentPage, pageSize, searchQuery, shopId);
    }, 400), // Delay 1s
    []
  );

  useEffect(() => {
    debouncedFetchUserList(page, rowsPerPage, searchText, storeId);
    return () => {
      debouncedFetchUserList.cancel(); // Cancel debounce if component unmounts or searchText changes
    };
  }, [page, rowsPerPage, searchText, storeId, debouncedFetchUserList]);

  return (
    <>
      <Box sx={{ width: "100%", overflowX: "auto" }}>
        <Table sx={{ width: "max-content", minWidth: "100%" }}>
          <TableHead>
            <TableRow>
              <TableCell>STT</TableCell>
              <TableCell>Mã đơn nháp</TableCell>
              <TableCell>Ngày</TableCell>
              <TableCell>Được đặt bởi</TableCell>
              <TableCell>Tổng tiền</TableCell>
              <TableCell>Trạng thái</TableCell>
              <TableCell></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {carts.length > 0 ? (
              carts.map((c, index) => {
                const totalPrice = c.listItems.reduce((total, item) => {
                  return total + item.price * item.quantity;
                }, 0);
                const taxPrice = c.listItems.reduce((total, item) => {
                  const taxRateTemp = item.customTaxRate || item.taxRate || 0;
                  return total + item.price * item.quantity * (taxRateTemp / 100);
                }, 0);

                const totalPriceWithTax =
                  totalPrice +
                  taxPrice +
                  c.transportPrice -
                  c.voucherTransportPrice -
                  c.voucherPromotionPrice;
                return (
                  <TableRow key={c.cartId}>
                    <TableCell>{page * rowsPerPage + index + 1}</TableCell>
                    <TableCell>{c.cartNo}</TableCell>
                    <TableCell>{dayjs(c.created).format("DD/MM/YYYY HH:mm:ss")}</TableCell>
                    <TableCell>{c.fullname}</TableCell>
                    <TableCell>{formatMoney(totalPriceWithTax) || 0}đ</TableCell>
                    <TableCell>Chưa hoàn thành</TableCell>

                    <TableCell>
                      <Tooltip
                        title={
                          !isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)
                            ? "Bạn không có quyền import"
                            : "Sửa đơn nháp"
                        }
                      >
                        <span>
                          <Button
                            onClick={() => handleEdit(c.cartId)}
                            color="primary"
                            disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                          >
                            <EditOutlinedIcon />
                          </Button>
                        </span>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell colSpan={12} align="center">
                  Không có đơn hàng
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </Box>
      <TablePagination
        component="div"
        count={totalCount}
        page={page}
        onPageChange={handleChangePage}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        rowsPerPageOptions={rowPerPageOptionsDefault}
        labelRowsPerPage="Số dòng mỗi trang" // Thay đổi văn bản ở phần này
      />
    </>
  );
}
