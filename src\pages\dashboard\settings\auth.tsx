import React from "react";
import SettingLayout from "@/src/components/settings/settings-page/SettingLayout";
import Grid from "@mui/system/Grid";
import { IconButton, Typography } from "@mui/material";
import EmployeeManagement from "@/src/components/settings/settings-page/Auth";
import { usePartnerEmployee } from "@/src/api/hooks/partner-employee/use-partner-employee";

export default function AuthSetting() {
  return (
    <SettingLayout>
      <Grid>
        <Typography variant="h5" sx={{ ml: 1 }}>
          Quản lý quyền nhân viên
        </Typography>
      </Grid>
      <Grid>
        <EmployeeManagement />
      </Grid>
    </SettingLayout>
  );
}
