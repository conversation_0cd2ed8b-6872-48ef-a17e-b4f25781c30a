import React, { useState } from "react";
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  InputAdornment,
  Link,
  TextField,
  Modal,
  List,
  ListItem,
  ListItemText,
  Checkbox,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import SearchIcon from "@mui/icons-material/Search";
import DeleteIcon from "@mui/icons-material/Delete";

const CommissionSettings = () => {
  const [isAddingGroup, setIsAddingGroup] = useState(false);
  const [groups, setGroups] = useState([]);
  const [newGroup, setNewGroup] = useState({
    name: "",
    members: [],
    commissionLevel1: "",
    commissionLevel2: "",
  });
  const [openModal, setOpenModal] = useState(false);
  const [selectedMembers, setSelectedMembers] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");

  const availableMembers = ["F2 Henry", "F1 Henry", "Tuấn <PERSON>", "Thành viên 4"];

  const handleAddGroup = () => {
    setIsAddingGroup(true);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    // Validate commission fields to be between 0-100
    if (name === "commissionLevel1" || name === "commissionLevel2") {
      const numValue = Number(value);
      if (value === "" || (numValue >= 0 && numValue <= 100)) {
        setNewGroup((prev) => ({
          ...prev,
          [name]: value,
        }));
      }
    } else {
      setNewGroup((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleOpenModal = () => {
    setOpenModal(true);
    setSelectedMembers(newGroup.members);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setSearchTerm("");
  };

  const handleSelectMember = (member) => {
    setSelectedMembers((prev) =>
      prev.includes(member) ? prev.filter((m) => m !== member) : [...prev, member]
    );
  };

  const handleSelectAll = (event) => {
    if (event.target.checked) {
      const filteredMembers = availableMembers.filter((member) =>
        member.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setSelectedMembers(filteredMembers);
    } else {
      setSelectedMembers([]);
    }
  };

  const handleConfirmSelection = () => {
    setNewGroup((prev) => ({
      ...prev,
      members: selectedMembers,
    }));
    handleCloseModal();
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleRemoveMember = (member) => {
    setSelectedMembers((prev) => prev.filter((m) => m !== member));
  };

  const handleSaveGroup = () => {
    if (
      newGroup.name &&
      newGroup.members.length > 0 &&
      newGroup.commissionLevel1 &&
      newGroup.commissionLevel2
    ) {
      setGroups((prev) => [
        ...prev,
        {
          ...newGroup,
          id: Date.now(),
        },
      ]);
      setNewGroup({ name: "", members: [], commissionLevel1: "", commissionLevel2: "" });
      setIsAddingGroup(false);
    } else {
      alert("Vui lòng điền đầy đủ thông tin!");
    }
  };

  const filteredMembers = availableMembers.filter((member) =>
    member.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDeleteGroup = (groupId) => {
    setGroups((prev) => prev.filter((group) => group.id !== groupId));
  };

  return (
    <Box sx={{ width: "100%", padding: "70px 15px" }}>
      <Link
        href="/dashboard/marketing/affiliate/setup/commission-policy/commission-policy"
        sx={{
          fontSize: "20px",
          textDecoration: "none",
          fontWeight: "700",
          color: "#000",
          paddingBottom: "30px",
        }}
      >
        {"< Hoa hồng theo nhóm đối tác"}
      </Link>
      <Box>
        <TableContainer sx={{ marginTop: 2, border: "1px solid #e0e0e0", borderRadius: "8px" }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell sx={{ minWidth: "200px" }}>STT</TableCell>
                <TableCell sx={{ minWidth: "200px" }}>Tên nhóm</TableCell>
                <TableCell sx={{ minWidth: "200px" }}>Thành viên</TableCell>
                <TableCell sx={{ minWidth: "200px" }}>Hoa hồng bậc 1</TableCell>
                <TableCell sx={{ minWidth: "200px" }}>Hoa hồng bậc 2</TableCell>
                <TableCell>Quản lý</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {groups.length === 0 && !isAddingGroup && (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ padding: 4 }}>
                    <Typography sx={{ marginTop: 1, color: "#757575" }}>
                      Không có nhóm nào
                    </Typography>
                    <Button
                      variant="contained"
                      sx={{ marginTop: 2, backgroundColor: "#2654FE" }}
                      onClick={handleAddGroup}
                    >
                      Thêm nhóm
                    </Button>
                  </TableCell>
                </TableRow>
              )}

              {isAddingGroup && (
                <TableRow>
                  <TableCell>{groups.length + 1}</TableCell>
                  <TableCell>
                    <TextField
                      name="name"
                      value={newGroup.name}
                      onChange={handleInputChange}
                      size="small"
                      variant="outlined"
                      placeholder="Nhập tên nhóm"
                      sx={{
                        border: "1px solid #D9D9D9",
                        borderRadius: "8px",
                        "& fieldset": { border: "none" },
                        "& input::placeholder": {
                          color: "#D9D9D9",
                          fontSize: "16px",
                          fontWeight: "400",
                          opacity: "1",
                        },
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography
                      onClick={handleOpenModal}
                      sx={{
                        color: "#2654FE",
                        cursor: "pointer",
                        "&:hover": { textDecoration: "underline" },
                      }}
                    >
                      {newGroup.members.length > 0
                        ? `Đã thêm (${newGroup.members.length})`
                        : "Thêm thành viên"}
                    </Typography>
                  </TableCell>
                  <TableCell
                    sx={{ display: "flex", alignItems: "center", border: 0, boxShadow: "none" }}
                  >
                    <TextField
                      name="commissionLevel1"
                      value={newGroup.commissionLevel1}
                      onChange={handleInputChange}
                      size="small"
                      variant="outlined"
                      placeholder="Nhập tỷ lệ hoa hồng"
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <Typography sx={{ color: "#000", fontSize: "16px" }}>%</Typography>
                          </InputAdornment>
                        ),
                      }}
                      inputProps={{
                        min: 0,
                        max: 100,
                        step: 1,
                      }}
                      type="number"
                      sx={{
                        "& fieldset": { border: "1px solid #E0E0E0", borderRadius: "8px" },
                        "& .MuiOutlinedInput-root": {
                          paddingRight: "8px",
                        },
                        "& input::placeholder": {
                          color: "#D9D9D9",
                          fontSize: "16px",
                          fontWeight: "400",
                          opacity: "1",
                        },
                        "& input": {
                          borderRight: "1px solid #D9D9D9",
                        },
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <TextField
                      name="commissionLevel2"
                      value={newGroup.commissionLevel2}
                      onChange={handleInputChange}
                      size="small"
                      placeholder="Nhập tỷ lệ hoa hồng"
                      variant="outlined"
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <Typography sx={{ color: "#000", fontSize: "16px" }}>%</Typography>
                          </InputAdornment>
                        ),
                      }}
                      inputProps={{
                        min: 0,
                        max: 100,
                        step: 1,
                      }}
                      type="number"
                      sx={{
                        "& fieldset": { border: "1px solid #E0E0E0", borderRadius: "8px" },
                        "& .MuiOutlinedInput-root": {
                          paddingRight: "8px",
                        },
                        "& input::placeholder": {
                          color: "#D9D9D9",
                          fontSize: "16px",
                          fontWeight: "400",
                          opacity: "1",
                        },
                        "& input": {
                          borderRight: "1px solid #D9D9D9",
                        },
                      }}
                    />
                  </TableCell>

                  <TableCell>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ backgroundColor: "#2654FE" }}
                      onClick={handleSaveGroup}
                    >
                      Thêm
                    </Button>
                  </TableCell>
                </TableRow>
              )}

              {groups.map((group, index) => (
                <TableRow key={group.id}>
                  <TableCell>{index + 1}</TableCell>
                  <TableCell>{group.name}</TableCell>
                  <TableCell>{group.members.join(", ")}</TableCell>
                  <TableCell>{group.commissionLevel1}</TableCell>
                  <TableCell>{group.commissionLevel2}</TableCell>
                  <TableCell>
                    <IconButton onClick={() => handleDeleteGroup(group.id)}>
                      <DeleteIcon sx={{ color: "#757575" }} />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>

      <Modal open={openModal} onClose={handleCloseModal}>
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            width: "80%",
            bgcolor: "background.paper",
            borderRadius: "8px",
            boxShadow: 24,
            p: 3,
          }}
        >
          <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
            <Typography sx={{ fontSize: "16px", fontWeight: "400", color: "#000" }}>
              Chọn thành viên
            </Typography>
            <IconButton onClick={handleCloseModal}>
              <CloseIcon />
            </IconButton>
          </Box>
          <Box
            sx={{
              display: "flex",
              alignItems: "start",
              "@media (max-width: 600px)": { flexDirection: "column" },
            }}
          >
            <Box
              sx={{
                width: "50%",
                paddingRight: "20px",
                borderRight: "1px solid #E0E0E0",
                "@media (max-width: 600px)": { width: "100%" },
              }}
            >
              <TextField
                fullWidth
                placeholder="Tìm thành viên"
                value={searchTerm}
                onChange={handleSearchChange}
                size="small"
                variant="outlined"
                sx={{ mt: 2 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon sx={{ color: "#757575" }} />
                    </InputAdornment>
                  ),
                }}
              />
              <Box sx={{ mt: 2 }}>
                <ListItem>
                  <Checkbox
                    checked={
                      filteredMembers.length > 0 &&
                      filteredMembers.every((member) => selectedMembers.includes(member))
                    }
                    onChange={handleSelectAll}
                  />
                  <ListItemText primary="Chọn tất cả" />
                </ListItem>
                <Box sx={{ maxHeight: 200, overflow: "auto" }}>
                  <List dense>
                    {filteredMembers.map((member, index) => (
                      <ListItem key={index}>
                        <Checkbox
                          checked={selectedMembers.includes(member)}
                          onChange={() => handleSelectMember(member)}
                        />
                        <ListItemText primary={member} />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              </Box>
            </Box>
            {selectedMembers.length > 0 && (
              <Box
                sx={{
                  mt: 2,
                  width: "50%",
                  paddingLeft: "20px",
                  "@media (max-width: 600px)": { width: "100%" },
                }}
              >
                <Typography variant="body2" color="textSecondary">
                  Đã chọn {selectedMembers.length} người
                </Typography>
                <List dense sx={{ overflow: "auto", mt: 1 }}>
                  {selectedMembers.map((member, index) => (
                    <ListItem
                      key={index}
                      secondaryAction={
                        <IconButton edge="end" onClick={() => handleRemoveMember(member)}>
                          <Box
                            sx={{
                              backgroundColor: "#D9D9D9",
                              borderRadius: "50%",
                              width: 20,
                              height: 20,
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                            }}
                          >
                            <CloseIcon sx={{ fontSize: 14, color: "#FFFFFF" }} />
                          </Box>
                        </IconButton>
                      }
                    >
                      <ListItemText primary={member} />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}
          </Box>
          <Box sx={{ mt: 3, display: "flex", justifyContent: "flex-end", gap: 2 }}>
            <Button
              variant="outlined"
              onClick={handleCloseModal}
              sx={{ borderColor: "#E0E0E0", color: "#757575", textTransform: "none" }}
            >
              Hủy bỏ
            </Button>
            <Button
              variant="contained"
              onClick={handleConfirmSelection}
              sx={{ backgroundColor: "#2654FE", textTransform: "none" }}
            >
              Xác nhận
            </Button>
          </Box>
        </Box>
      </Modal>
    </Box>
  );
};

export default CommissionSettings;
