import React, { useState, useEffect, useCallback } from "react";
import { Box } from "@mui/material";
import {
  GetFileGroupRequest,
  GetGroupFileRequest,
  MediaFile,
  MediaGroup,
  RefType,
} from "@/src/api/types/media.types";
import { useMedia } from "@/src/api/hooks/media/use-media";
import MediaGroupsSidebar from "./media-groups-sidebar";
import MediaContent from "./media-content";
import { useStoreId } from "@/src/hooks/use-store-id";
import { ExistingMediaFile } from "../../create/create";

interface MediaLibraryContentProps {
  type: "image" | "video";
  selectedItems: ExistingMediaFile[];
  onSelect: (items: ExistingMediaFile[]) => void;
  maxSelect: number;
}

const ITEMS_PER_PAGE = 25;

const MediaLibraryContent: React.FC<MediaLibraryContentProps> = ({
  type,
  selectedItems,
  onSelect,
  maxSelect,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(ITEMS_PER_PAGE);

  const [groups, setGroups] = useState<MediaGroup[]>([]);
  const [selectedGroup, setSelectedGroup] = useState<MediaGroup | null>(null);
  const [loading, setLoading] = useState(false);
  const [items, setItems] = useState<ExistingMediaFile[]>([]);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedRefType, setSelectedRefType] = useState<string>("ALL");
  const storeId = useStoreId();
  const { getGroups, getFiles } = useMedia();

  // Memoize fetchInitialData
  const fetchInitialData = useCallback(async () => {
    if (!storeId) return;

    setLoading(true);
    try {
      const params: GetGroupFileRequest = {
        ShopId: storeId,
        Skip: 0,
        Limit: 100,
      };
      const response = await getGroups(params);
      const groupList = response.data.data || [];

      setGroups(groupList);

      if (groupList.length === 0) {
        setSelectedGroup(null);
        setItems([]);
      } else if (
        !selectedGroup ||
        !groupList.find((g) => g.groupFileId === selectedGroup.groupFileId)
      ) {
        setSelectedGroup(groupList[0]);
      }
    } catch (error) {
      console.error("Error fetching groups:", error);
    } finally {
      setLoading(false);
    }
  }, [storeId, selectedGroup]);

  // Fetch initial data when component mounts
  useEffect(() => {
    fetchInitialData();
  }, [fetchInitialData]);

  // Handle group selection
  const handleSelectGroup = (group: MediaGroup | null) => {
    setSelectedGroup(group);
    setPage(1);
    setSearchQuery("");
  };

  // Memoize fetchFiles function
  const fetchFiles = useCallback(async () => {
    if (!selectedGroup) return;

    try {
      const params: GetFileGroupRequest = {
        ShopId: storeId,
        ...(selectedGroup && { GroupFileId: selectedGroup.groupFileId }),
        Skip: (page - 1) * ITEMS_PER_PAGE,
        Limit: rowsPerPage,
        ...(selectedRefType !== "ALL" && { RefType: selectedRefType as RefType }),
      };
      const response = await getFiles(params);
      setItems(response?.data?.data || []);
      setTotalItems(response?.data?.total || 0);
    } catch (error) {
      console.error("Error fetching files:", error);
    }
  }, [selectedGroup?.groupFileId, page, searchQuery, selectedRefType, rowsPerPage]);

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage + 1);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(1);
  };

  useEffect(() => {
    fetchFiles();
  }, [fetchFiles]);

  // Handle item selection
  const handleSelect = useCallback(
    (item: ExistingMediaFile) => {
      const isSelected = selectedItems.some(
        (selected) => selected.mediaFileId === item.mediaFileId
      );

      let newSelectedItems: ExistingMediaFile[];

      if (isSelected) {
        newSelectedItems = selectedItems.filter(
          (selected) => selected.mediaFileId !== item.mediaFileId
        );
      } else {
        if (selectedItems.length >= maxSelect) {
          return;
        }
        newSelectedItems = [...selectedItems, item];
      }

      onSelect(newSelectedItems);
    },
    [selectedItems, maxSelect, onSelect]
  );

  return (
    <Box sx={{ display: "flex", height: "100%" }}>
      <MediaGroupsSidebar
        groups={groups}
        selectedGroup={selectedGroup}
        onSelectGroup={handleSelectGroup}
        loading={loading}
        type={type}
        onRefresh={fetchInitialData}
      />
      {/* <MediaContent
        selectedGroup={selectedGroup}
        selectedItems={selectedItems}
        onSelect={handleSelect}
        page={page}
        onPageChange={setPage}
        totalPages={Math.ceil(totalItems / ITEMS_PER_PAGE)}
        items={items}
        type={type}
        onRefresh={fetchFiles}
        onSearchChange={setSearchQuery}
        selectedRefType={selectedRefType}
        setSelectedRefType={setSelectedRefType}
      /> */}

      <MediaContent
        selectedGroup={selectedGroup}
        selectedItems={selectedItems}
        onSelect={handleSelect}
        page={page - 1}
        onPageChange={handleChangePage}
        totalPages={Math.ceil(totalItems / rowsPerPage)}
        items={items}
        type={type}
        onRefresh={fetchFiles}
        onSearchChange={setSearchQuery}
        selectedRefType={selectedRefType}
        setSelectedRefType={setSelectedRefType}
        totalCount={totalItems}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Box>
  );
};

export default MediaLibraryContent;
