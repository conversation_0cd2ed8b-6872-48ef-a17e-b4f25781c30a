import { FC } from "react";
import {
  IconButton,
  InputAdornment,
  Stack,
  TextField,
  TextFieldProps,
  Typography,
} from "@mui/material";
import { Eye, EyeOff } from "@untitled-ui/icons-react";

interface PasswordTextFieldProps extends Omit<TextFieldProps, "variant"> {
  label: string;
  showPassword: boolean;
  onTogglePassword: () => void;
}

export const PasswordTextField: FC<PasswordTextFieldProps> = ({
  label,
  showPassword,
  onTogglePassword,
  ...props
}) => {
  return (
    <Stack spacing={1}>
      <Typography variant="subtitle2">{label}</Typography>
      <TextField
        fullWidth
        type={showPassword ? "text" : "password"}
        {...props}
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <IconButton onClick={onTogglePassword} edge="end">
                {showPassword ? <EyeOff /> : <Eye />}
              </IconButton>
            </InputAdornment>
          ),
        }}
      />
    </Stack>
  );
};
