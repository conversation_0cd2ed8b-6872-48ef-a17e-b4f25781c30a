import { useTranslation } from "react-i18next";
import Box from "@mui/material/Box";
import IconButton from "@mui/material/IconButton";
import Tooltip from "@mui/material/Tooltip";

import { usePopover } from "src/hooks/use-popover";

import { LanguagePopover } from "./language-popover";

const languages = {
  en: "/assets/flags/flag-uk.svg",
  de: "/assets/flags/flag-de.svg",
  es: "/assets/flags/flag-es.svg",
  vi: "/assets/flags/flag-vi.svg",
};

export const LanguageSwitch = () => {
  const { i18n } = useTranslation();
  const popover = usePopover();

  const flag = languages[i18n.language];

  return (
    <Box
      sx={{
        margin: "0 !important",
        padding: "0 4px",
        borderRight: "1px solid #DCDCDC",
        "@media(max-width: 440px)": {
          padding: "0 ",
        },
      }}
    >
      <Tooltip title="Language">
        <IconButton onClick={popover.handleOpen} ref={popover.anchorRef}>
          <Box
            sx={{
              width: 28,
              padding: 0,
              "& img": {
                width: "100%",
              },
            }}
          >
            <img src={flag} />
          </Box>
        </IconButton>
      </Tooltip>
      <LanguagePopover
        anchorEl={popover.anchorRef.current}
        onClose={popover.handleClose}
        open={popover.open}
      />
    </Box>
  );
};
