export const LogoAtt = (props) => (
  <svg
    width={59}
    height={24}
    viewBox="0 0 59 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <g clipPath="url(#clip0_11275_169477)">
      <path
        d="M54.162 16.81C54.0069 16.81 53.9 16.7017 53.9 16.5459V8.80852H51.2895C51.1342 8.80852 51.0272 8.70069 51.0272 8.54475V7.47745C51.0272 7.32116 51.1343 7.21304 51.2895 7.21304H58.2382C58.3933 7.21304 58.5 7.32128 58.5 7.47745V8.54469C58.5 8.70052 58.3933 8.80852 58.2382 8.80852H55.6279V16.5458C55.6279 16.7017 55.5205 16.81 55.3659 16.81H54.162ZM34.165 12.947L32.7946 8.98869L31.412 12.947H34.165ZM37.2043 16.4974C37.2642 16.6537 37.1687 16.81 37.0018 16.81H35.7625C35.5836 16.81 35.4762 16.7263 35.4164 16.5576L34.7135 14.5189H30.8642L30.16 16.5576C30.1009 16.7263 29.9932 16.81 29.8148 16.81H28.6472C28.492 16.81 28.3846 16.6537 28.4442 16.4974L31.6741 7.45346C31.7339 7.28497 31.8411 7.21345 32.0195 7.21345H33.6166C33.7956 7.21345 33.9148 7.28502 33.9743 7.45346L37.2043 16.4974ZM46.4624 15.5142C47.2248 15.5142 47.7376 15.1429 48.1548 14.5069L46.2243 12.4193C45.485 12.8395 45.0081 13.2588 45.0081 14.0988C45.0082 14.9266 45.6753 15.5142 46.4624 15.5142ZM46.9986 8.48456C46.379 8.48456 46.0215 8.8808 46.0215 9.40852C46.0215 9.81633 46.2356 10.1763 46.7245 10.7042C47.5706 10.212 47.9283 9.91231 47.9283 9.38464C47.9283 8.89242 47.6186 8.48456 46.9986 8.48456ZM52.279 16.4741C52.4337 16.6422 52.3386 16.81 52.1474 16.81H50.6337C50.4312 16.81 50.3239 16.7618 50.1929 16.6057L49.2871 15.5984C48.6793 16.4142 47.8324 17.0258 46.4264 17.0258C44.6864 17.0258 43.3153 15.9703 43.3153 14.1592C43.3153 12.7673 44.0547 12.0234 45.1752 11.3997C44.6265 10.764 44.3767 10.0922 44.3767 9.50468C44.3767 8.01679 45.4135 6.99731 46.9745 6.99731C48.5717 6.99731 49.5492 7.94533 49.5492 9.34856C49.5492 10.5482 48.6912 11.2193 47.7851 11.7235L49.1202 13.1755L49.8709 11.8555C49.9662 11.6998 50.0735 11.6396 50.264 11.6396H51.4198C51.6108 11.6396 51.7181 11.7719 51.5993 11.9757L50.2641 14.2785L52.279 16.4741ZM40.3845 16.81C40.5395 16.81 40.6473 16.7017 40.6473 16.5459V8.80852H43.257C43.412 8.80852 43.5191 8.70069 43.5191 8.54475V7.47745C43.5191 7.32116 43.412 7.21304 43.257 7.21304H36.3083C36.1531 7.21304 36.0461 7.32128 36.0461 7.47745V8.54469C36.0461 8.70052 36.1532 8.80852 36.3083 8.80852H38.918V16.5458C38.918 16.7017 39.0257 16.81 39.1804 16.81H40.3845Z"
        fill="currentColor"
      />
      <path
        d="M5.11134 21.4772C7.13263 23.0526 9.66969 23.9991 12.4223 23.9991C15.4345 23.9991 18.1806 22.8731 20.2764 21.0247C20.3018 21.0021 20.2893 20.9872 20.2642 21.0021C19.3238 21.6345 16.6434 23.015 12.4224 23.015C8.75417 23.015 6.436 22.1909 5.12688 21.4552C5.10183 21.4427 5.09256 21.4617 5.11134 21.4772ZM13.2316 22.0906C16.1655 22.0906 19.3895 21.2853 21.3177 19.6913C21.8454 19.2569 22.348 18.6789 22.7981 17.9021C23.0572 17.4551 23.3107 16.924 23.5171 16.402C23.5263 16.3766 23.5107 16.3642 23.4917 16.3928C21.6985 19.0497 16.5061 20.7069 11.1446 20.7069C7.35488 20.7069 3.27723 19.4867 1.6808 17.1568C1.66509 17.1353 1.64937 17.1445 1.65894 17.1692C3.14613 20.3519 7.65791 22.0906 13.2316 22.0906ZM10.026 16.8106C3.92405 16.8106 1.04685 13.9492 0.524933 11.9965C0.518497 11.9682 0.5 11.9744 0.5 11.9998C0.5 12.6572 0.565349 13.5055 0.677781 14.0686C0.731417 14.3427 0.952976 14.7729 1.27781 15.1158C2.75537 16.6665 6.43913 18.8394 12.8188 18.8394C21.5109 18.8394 23.4983 15.9241 23.9042 14.9654C24.1944 14.2798 24.3447 13.0407 24.3447 11.9998C24.3447 11.748 24.3385 11.5468 24.329 11.3493C24.329 11.3172 24.3106 11.3146 24.3042 11.3458C23.8698 13.6917 16.4435 16.8106 10.026 16.8106ZM1.64937 6.85206C1.29972 7.55073 0.912155 8.72942 0.796939 9.33946C0.746435 9.60084 0.767947 9.72636 0.858983 9.92142C1.59034 11.4838 5.2897 13.9836 13.9189 13.9836C19.1833 13.9836 23.2728 12.6814 23.9354 10.3051C24.0574 9.86759 24.0639 9.40572 23.9072 8.78336C23.7321 8.08791 23.4041 7.27691 23.1265 6.70745C23.1174 6.68888 23.1012 6.69163 23.1045 6.71358C23.2076 9.8304 14.575 11.8392 10.2194 11.8392C5.50146 11.8392 1.56541 9.94652 1.56541 7.55668C1.56541 7.32706 1.61261 7.09738 1.67152 6.85837C1.67744 6.83653 1.65888 6.83291 1.64937 6.85206ZM20.2957 3.02524C20.3458 3.10429 20.3708 3.18866 20.3708 3.30221C20.3708 4.63544 16.3182 6.99399 9.86701 6.99399C5.12688 6.99399 4.23943 5.22347 4.23943 4.0975C4.23943 3.69501 4.39274 3.28318 4.73039 2.86492C4.74882 2.84005 4.73317 2.83048 4.71189 2.84899C4.09609 3.37443 3.53028 3.96567 3.03393 4.60391C2.79677 4.90575 2.64955 5.17315 2.64955 5.33335C2.64955 7.66673 8.46054 9.35855 13.894 9.35855C19.6834 9.35855 22.2672 7.45562 22.2672 5.78331C22.2672 5.18564 22.0361 4.83674 21.445 4.16032C21.0613 3.72029 20.6984 3.36199 20.3143 3.0093C20.2957 2.99389 20.2828 3.00644 20.2957 3.02524ZM18.5209 1.69231C16.7341 0.613975 14.6593 0.000488281 12.4224 0.000488281C10.1699 0.000488281 8.03255 0.635401 6.23938 1.74228C5.70151 2.07559 5.39877 2.34269 5.39877 2.68604C5.39877 3.69816 7.74802 4.78642 11.9159 4.78642C16.0405 4.78642 19.2396 3.59442 19.2396 2.44702C19.2396 2.17315 19.0019 1.98153 18.5209 1.69231Z"
        fill="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_11275_169477">
        <rect
          width={58}
          height={24}
          fill="white"
          transform="translate(0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);
