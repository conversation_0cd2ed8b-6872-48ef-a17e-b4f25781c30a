import React from 'react';
import { Box, Stack, Typography, Avatar, Grid, Card } from '@mui/material';

const Monthly = ({ topRank }) => {
  const topAffiliate = topRank[0];

  const secondAffiliate = topRank.slice(1, 2);
  const thirdAffiliate = topRank.slice(2, 3);
  const remainingAffiliates = topRank.slice(3);

  if (topRank.length <= 0) {
    return (
      <Typography sx={{ textAlign: 'center', marginTop: 20 }}>
        Không có dữ liệu
      </Typography>
    )
  }
  return <Box>
    <Stack>
      {topAffiliate && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            marginBottom: '-25px',
            '@media(max-width: 480px)': {
              marginBottom: '20px',
            },
          }}
        >
          <Stack alignItems="center">
            <Box
              sx={{
                position: 'relative',
                mb: 1,
              }}
            >
              <img src="/logo/logo-dashboard/Group 48472.png" alt="Logo" />

              <Avatar
                sx={{
                  width: 58,
                  height: 58,
                  borderRadius: '50%',
                  position: 'absolute',
                  top: '28px',
                  left: '27%',
                }}
                alt={topAffiliate.fullName}
                src={topAffiliate.image}
              />
            </Box>
            <Box position={'relative'}>
              <img
                src="/logo/logo-dashboard/Group 48473.png"
                alt="Logo"
                style={{ minWidth: '120px' }}
              />

              <Typography
                position={'absolute'}
                top={'3px'}
                left={'25%'}
                fontSize={'14px'}
                fontWeight={'400'}
                color="#fff"
              >
                {topAffiliate.fullName}
              </Typography>
            </Box>
          </Stack>
        </Box>
      )}
    </Stack>
    <Stack flexDirection={'row'} justifyContent={'space-between'}>
      <Box sx={{ display: 'flex', justifyContent: 'center', gap: 3, mb: 3 }}>
        {secondAffiliate.map((affiliate) => (
          <Stack key={affiliate.rank} alignItems="center">
            <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
              <Stack alignItems="center">
                <Box
                  sx={{
                    position: 'relative',
                    mb: 1,
                  }}
                >
                  <img src="/logo/logo-dashboard/Union.png" alt="Logo" />

                  <Avatar
                    sx={{
                      width: 58,
                      height: 58,
                      borderRadius: '50%',
                      position: 'absolute',
                      top: '28px',
                      left: '27%',
                    }}
                    alt={affiliate.fullName}
                    src={affiliate.image}
                  />
                </Box>
                <Box position={'relative'}>
                  <img
                    src="/logo/logo-dashboard/Group 48473.png"
                    alt="Logo"
                    style={{ minWidth: '120px' }}
                  />

                  <Typography
                    position={'absolute'}
                    top={'3px'}
                    left={'25%'}
                    fontSize={'14px'}
                    fontWeight={'400'}
                    color="#fff"
                  >
                    {affiliate.fullName}
                  </Typography>
                </Box>
              </Stack>
            </Box>
          </Stack>
        ))}
      </Box>

      <Box
        sx={{ display: 'flex', justifyContent: 'center', alignContent: 'center', gap: 3, mb: 3 }}
      >
        {thirdAffiliate.map((affiliate) => (
          <Stack key={affiliate.rank} alignItems="center" justifyContent={'center'}>
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <Stack alignItems="center">
                <Box
                  sx={{
                    position: 'relative',
                    mb: 1,
                  }}
                >
                  <img src="/logo/logo-dashboard/frame.png" alt="Logo" />

                  <Avatar
                    sx={{
                      width: 58,
                      height: 58,
                      borderRadius: '50%',
                      position: 'absolute',
                      top: '4px',
                      left: '8%',
                    }}
                    alt={affiliate.fullName}
                    src={affiliate.image}
                  />
                </Box>
                <Box position={'relative'}>
                  <img
                    src="/logo/logo-dashboard/Group 48473.png"
                    alt="Logo"
                    style={{ minWidth: '120px' }}
                  />

                  <Typography
                    position={'absolute'}
                    top={'3px'}
                    left={'25%'}
                    fontSize={'14px'}
                    fontWeight={'400'}
                    color="#fff"
                  >
                    {affiliate.fullName}
                  </Typography>
                </Box>
              </Stack>
            </Box>
          </Stack>
        ))}
      </Box>
    </Stack>

    {remainingAffiliates.length > 0 && (
      <Grid container spacing={2} sx={{ mb: 3 }}>
        {remainingAffiliates.map((affiliate, index) => (
          <Grid item xs={12} sm={6} key={affiliate.rank}>
            <Card sx={{ display: 'flex', alignItems: 'center', gap: '10px', padding: '20px' }}>
              <Typography color="#000000" fontSize={'20px'} fontWeight={'400'}>
                {index + 4}.
              </Typography>
              <Typography color="#000000" fontSize={'20px'} fontWeight={'400'}>
                {affiliate.fullName}
              </Typography>
            </Card>
          </Grid>
        ))}
      </Grid>
    )}
  </Box>
};

export default Monthly;
