import { apiClient } from "@/src/api/config/api-client";
import { API_PATHS } from "@/src/api/constants/api-paths";
import type { ApiError, ErrorConfig } from "@/src/api/types/error.types";
import type { ExtendedRequestConfig } from "@/src/api/types/api.types";
import { GetServiceRequest, GetServiceRequestBody } from "@/src/api/types/service.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export const serviceService = {
  getService: async <T = any>(params: GetServiceRequest, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const queryParams = [];
      if (params.skip !== undefined) queryParams.push(`skip=${params.skip}`);
      if (params.limit !== undefined) queryParams.push(`limit=${params.limit}`);
      if (params.partnerId !== undefined) queryParams.push(`partnerId=${params.partnerId}`);
      if (params.shopId !== undefined) queryParams.push(`shopId=${params.shopId}`);
      if (params.itemsType !== undefined) queryParams.push(`itemsType=${params.itemsType}`);
      if (params.categoryId !== null) queryParams.push(`categoryId=${params.categoryId}`);
      if (params.subCategoryId !== null) queryParams.push(`subCategoryId=${params.subCategoryId}`);
      if (params.search !== undefined && params.search !== "")
        queryParams.push(`search=${params.search}`);
      // Kết hợp các tham số thành query string
      const queryString = queryParams.join("&");
      const response = await apiClient.get<GetServiceRequestBody, T>(
        `${API_PATHS.SERVICE.GET_SERVICE}?${queryString}`,
        {
          partnerId: params.partnerId,
          shopId: params.shopId,
          itemsType: params.itemsType,
          categoryId: params.categoryId,
          subCategoryId: params.subCategoryId,
          search: params.search,
        },
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createService: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(API_PATHS.SERVICE.CREATE_SERVICE, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateService: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<any, T>(API_PATHS.SERVICE.UPDATE_SERVICE, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateImageService: async <T = any>(
    serviceId: string,
    imageFile: File,
    errorConfig?: ErrorConfig
  ) => {
    const formData = new FormData();
    formData.append("ServiceId", serviceId);
    formData.append("FileUpload", imageFile);

    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    };

    try {
      const response = await apiClient.post<FormData, T>(
        API_PATHS.SERVICE.UPDATE_IMAGE_SERVICE,
        formData,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteService: async <T = any>(itemsCode: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete<T>(
        `${API_PATHS.SERVICE.DELETE_SERVICE}?itemsCode=${itemsCode}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getServiceDetail: async <T = any>(serviceId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.SERVICE.GET_SERVICE_DETAIL}?itemsCode=${serviceId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
