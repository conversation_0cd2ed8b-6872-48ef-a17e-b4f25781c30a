import { ProductFormValues } from "../types/product/form";
import { FileType, MediaFile } from "@/src/constants/file-types";

interface VariantType {
  itemsId: string;
  variantImage: {
    type: FileType;
    link: string;
    mediaFileId: string;
  };
  variantNameOne: string;
  variantValueOne: string;
  variantNameTwo: string;
  variantValueTwo: string;
  variantNameThree: string;
  variantValueThree: string;
  priceCapital: number;
  priceReal: number;
  price: number;
  quantity: number;
  quantityPurchase: number;
}

export const mockProductData = (
  defaultWarehouseId: string,
  storeId: string,
  partnerId: string,
  categoryIds: string[],
  hasVariants = false,
  itemsType: "Product" | "Service" = "Product",
  itemsInfo?: "",
  variants?: VariantType[],
  existingImages?: MediaFile[]
): ProductFormValues => {
  // Log để debug
  console.log("Mock Data - Default Warehouse ID:", defaultWarehouseId);

  return {
    // Basic info
    itemsId: "",
    itemsCode: "",
    itemsType,
    itemsInfo,
    itemsName: "iPhone 14 Pro Max",
    description: "<p>Sản phẩm điện thoại cao cấp từ Apple</p>",

    // Categories - Use defaults if available, otherwise use mock values
    categoryIds: categoryIds || null,
    // subCategoryId: null,

    // Display settings
    isVisible: true,
    isTop: true,
    typePublish: "Publish",
    status: "Actived",
    itemsPosition: 0,

    // Media - Now using array of image URLs
    images: existingImages || [
      {
        type: FileType.VIDEO,
        link: "https://dev-file.evotech.vn/ecommerce-dev/sample/items/quan_1_clip.mp4",
        mediaFileId: "",
      },
      {
        type: FileType.IMAGE,
        mediaFileId: "",
        link: "https://dev-file.evotech.vn/ecommerce-dev/sample/items/quan_1_1.webp",
      },
      {
        type: FileType.IMAGE,
        mediaFileId: "",
        link: "https://dev-file.evotech.vn/ecommerce-dev/sample/items/quan_1_2.webp",
      },
      {
        type: FileType.IMAGE,
        mediaFileId: "",
        link: "https://dev-file.evotech.vn/ecommerce-dev/sample/items/quan_1_3.webp",
      },
    ],

    // Variants
    isVariant: hasVariants,
    listVariant: hasVariants
      ? variants || [
          {
            itemsId: "VAR001",
            variantImage: {
              type: FileType.IMAGE,
              link: "https://dev-file.evotech.vn/ecommerce-dev/sample/items/iphone-black.webp",
              mediaFileId: "",
            },
            variantNameOne: "Màu sắc",
            variantValueOne: "Đen",
            variantNameTwo: "Dung lượng",
            variantValueTwo: "256GB",
            variantNameThree: "",
            variantValueThree: "",
            priceCapital: 23000000,
            priceReal: 27000000,
            price: 25000000,
            quantity: 1000,
            quantityPurchase: 10,
          } as VariantType,
          {
            itemsId: "VAR002",
            variantImage: {
              type: FileType.IMAGE,
              mediaFileId: "",
              link: "https://dev-file.evotech.vn/ecommerce-dev/sample/items/iphone-white.webp",
            },
            variantNameOne: "Màu sắc",
            variantValueOne: "Trắng",
            variantNameTwo: "Dung lượng",
            variantValueTwo: "256GB",
            variantNameThree: "",
            variantValueThree: "",
            priceCapital: 23000000,
            priceReal: 27000000,
            price: 25000000,
            quantity: 1000,
            quantityPurchase: 10,
          } as VariantType,
          {
            itemsId: "VAR003",
            variantImage: {
              type: FileType.IMAGE,
              mediaFileId: "",
              link: "https://dev-file.evotech.vn/ecommerce-dev/sample/items/iphone-purple.webp",
            },
            variantNameOne: "Màu sắc",
            variantValueOne: "Tím",
            variantNameTwo: "Dung lượng",
            variantValueTwo: "512GB",
            variantNameThree: "",
            variantValueThree: "",
            priceCapital: 26000000,
            priceReal: 30000000,
            price: 28000000,
            quantity: 1000,
            quantityPurchase: 10,
          } as VariantType,
        ]
      : [],

    //Yêu cầu:
    // Price >= PriceCapital
    // PriceReal >= PriceCapital
    // Price <= PriceReal
    // PriceCapital ≤ Price ≤ PriceReal

    priceCapital: 23000000, // Giá vốn
    priceReal: 27000000, // Giá thực tế (giá niêm yết)
    price: 25000000, // Giá bán (giá bán giá đã khuyến mãi)

    // Quantity
    quantity: 1000,
    quantityPurchase: 500,

    // Shipping Information at root level
    warehouseId: defaultWarehouseId,
    itemsWeight: 0.5,
    itemsLength: 15,
    itemsWidth: 8,
    itemsHeight: 2,

    // SEO
    seoTags: [
      {
        pageTitle: "iPhone 14 Pro Max",
        pageDesc: "Mua iPhone 14 Pro Max chính hãng",
        tags: "iphone, apple, smartphone",
        url: "iphone-14-pro-max",
      },
    ],

    // System fields
    shopId: storeId,
    partnerId: partnerId,

    // Purchase settings
    quantityPurchaseMin: 1,
    quantityPurchaseMax: 5,

    // Display
    sold: 0,
    transportType: [],
  };
};
