import { apiClient } from "@/src/api/config/api-client";
import { API_PATHS } from "@/src/api/constants/api-paths";
import type { ApiError, ErrorConfig } from "@/src/api/types/error.types";
import type { ExtendedRequestConfig } from "@/src/api/types/api.types";
import { ExchangeHistoryType, GetExchangeHistoryParams } from "../../types/membership.types";

const formatDateToISO = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
};

const getStartOfDay = (date: Date): string => {
  const newDate = new Date(date);
  newDate.setHours(0, 0, 0, 0);
  return formatDateToISO(newDate);
};

const getEndOfDay = (date: Date): string => {
  const newDate = new Date(date);
  newDate.setHours(23, 59, 59, 999);
  return formatDateToISO(newDate);
};

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export const MembershipLevelService = {
  getMembershipLevel: async <T = any>(
    skip: number,
    limit: number,
    shopId: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (shopId) {
        const response = await apiClient.get<{ skip: number; limit: number; search: string }, T>(
          `${API_PATHS.MEMBERSHIP_LEVEL.GetListMembershipLevel}?shopId=${shopId}&skip=${skip}&limit=${limit}`,
          { skip, limit },
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createMembershipLevel: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.MEMBERSHIP_LEVEL.CreateMembershipLevel,
        data,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateMembershipLevel: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<any, T>(
        API_PATHS.MEMBERSHIP_LEVEL.UpdateMembershipLevel,
        data,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteMembershipLevel: async <T = any>(data: { levelId: string }, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const { levelId } = data;
      const url = `${API_PATHS.MEMBERSHIP_LEVEL.DeleteMembershipLevel}?levelId=${levelId}`;
      const response = await apiClient.delete<any, T>(url, config);

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateLogoMembershipLevel: async <T = any>(
    shopId: string,
    logoFile: File,
    errorConfig?: ErrorConfig
  ) => {
    const formData = new FormData();
    formData.append("ShopId", shopId);
    formData.append("FileUpload", logoFile);

    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    };

    try {
      const response = await apiClient.post<FormData, T>(
        `${API_PATHS.SHOP.UPDATE_LOGO_SHOP}`,
        formData,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  summaryMembershipLevel: async <T = any>(data: { shopId: string }, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const { shopId } = data;
      const url = `${API_PATHS.MEMBERSHIP_LEVEL.SummaryPoints_MembershipLevel}?shopId=${shopId}`;
      const response = await apiClient.get<any, T>(url, config);

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getUserPoints: async <T = any>(params: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    const queryParams = new URLSearchParams();
    if (params?.shopId) queryParams.append("shopId", params?.shopId);
    if (params?.search) queryParams.append("search", params?.search);
    if (params?.skip) queryParams.append("skip", params?.skip);
    if (params?.limit) queryParams.append("limit", params?.limit);
    try {
      if (params?.shopId) {
        const url = `${API_PATHS.MEMBERSHIP_LEVEL.GetUserPoints}?${queryParams.toString()}`;
        const response = await apiClient.get<T>(url, config);
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getDetailUserPoint: async <T = any>(shopId: string, userId?: string) => {
    try {
      const url = `${API_PATHS.MEMBERSHIP_LEVEL.GetUserPoint}?shopId=${shopId}&userId=${userId}`;
      const response = await apiClient.get<any, T>(url);

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateUserPoints: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<any, T>(
        API_PATHS.MEMBERSHIP_LEVEL.UpdateUserPoints,
        data,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getExchangeHistory: async <T = any>(
    { skip, limit, shopId, search, type, fromDate, toDate }: GetExchangeHistoryParams,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const queryParams = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
        shopId: shopId,
        type: type || ExchangeHistoryType.Adjust,
        fromDate: getStartOfDay(fromDate),
        toDate: getEndOfDay(toDate),
      });

      // Chỉ thêm search vào query nếu có giá trị
      if (search) {
        queryParams.append("search", search);
      }

      const url = `${API_PATHS.MEMBERSHIP_LEVEL.GetExchangeHistory}?${queryParams.toString()}`;

      const response = await apiClient.get<GetExchangeHistoryParams, T>(url, config);

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getSpendingHistory: async <T = any>(
    shopId: string,
    userId: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (shopId && userId) {
        const url = `${
          API_PATHS.MEMBERSHIP_LEVEL.GetSpendingHistory
        }?shopId=${shopId}&userId=${userId}&skip=${0}&limit=${99}`;
        const response = await apiClient.get<{ skip: number; limit: number }, T>(url, config);

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getIncomeHistory: async <T = any>(shopId: string, userId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (shopId && userId) {
        const url = `${
          API_PATHS.MEMBERSHIP_LEVEL.GetIncomeHistory
        }?shopId=${shopId}&userId=${userId}&skip=${0}&limit=${99}`;
        const response = await apiClient.get<{ skip: number; limit: number }, T>(url, config);

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getConfig: async <T = any>(shopId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const url = `${API_PATHS.MEMBERSHIP_LEVEL.GetConfig}?shopId=${shopId}`;
      const response = await apiClient.get<any, T>(url, config);

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  updateConfig: async <T = any>(data: any, shopId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const url = `${API_PATHS.MEMBERSHIP_LEVEL.UpdateConfig}?shopId=${shopId}`;
      const response = await apiClient.put<any, T>(url, data, config);

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  updateMembershipLevelImage: async <T = any>(
    levelId: string,
    logoFile: File,
    errorConfig?: ErrorConfig
  ) => {
    const formData = new FormData();
    formData.append("LevelId", levelId);
    formData.append("FileUpload", logoFile);

    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    };

    try {
      const response = await apiClient.post<FormData, T>(
        `${API_PATHS.MEMBERSHIP_LEVEL.UPDATE_IMAGE_MEMBERSHIP_LEVEL}`,
        formData,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
