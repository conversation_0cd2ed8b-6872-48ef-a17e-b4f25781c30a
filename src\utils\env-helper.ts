/**
 * Check if the application is running in development mode
 */
export const isDevelopment = process.env.NODE_ENV === 'development';

/**
 * Check if the application is running in production mode
 */
export const isProduction = process.env.NODE_ENV === 'production';

/**
 * Check if the application is running in test mode
 */
export const isTest = process.env.NODE_ENV === 'test';

/**
 * Get current environment
 */
export const getEnvironment = () => process.env.NODE_ENV;

/**
 * Check if the application is running on client side
 */
export const isClient = typeof window !== 'undefined';

/**
 * Check if the application is running on server side
 */
export const isServer = typeof window === 'undefined'; 