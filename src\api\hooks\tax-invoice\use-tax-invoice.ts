import { useState } from "react";
import { ErrorHandlerService } from "../../services/error-handler.service";
import { BodyTaxInvoiceDto, userTaxInvoiceService } from "../../services/tax-invoice/tax-invoice";

export interface TriggerParameterDto {
  name: string;
  value: string;
  dataType: string;
  orderNumber: number;
}

export const useTaxInvoice = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getListConfigTaxInvoice = async (shopId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userTaxInvoiceService.getListConfigTaxInvoice(shopId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const createConfigTaxInvoice = async (data: BodyTaxInvoiceDto) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userTaxInvoiceService.createConfigTaxInvoice(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const updateConfigTaxInvoice = async (data: BodyTaxInvoiceDto) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userTaxInvoiceService.updateConfigTaxInvoice(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const deleteConfigTaxInvoice = async (shopId: string, id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userTaxInvoiceService.deleteConfigTaxInvoice(shopId, id);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const setDefaultConfigTaxInvoice = async (shopId: string, userId: string, id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userTaxInvoiceService.setDefaultConfigTaxInvoice(shopId, userId, id);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getInforBusinessConfigTaxInvoice = async (taxCode: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userTaxInvoiceService.getInforBusinessConfigTaxInvoice(taxCode);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const getInforIndividualConfigTaxInvoice = async (taxCode: string, name: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userTaxInvoiceService.getInforIndividualConfigTaxInvoice(
        taxCode,
        name
      );
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  return {
    getListConfigTaxInvoice,
    createConfigTaxInvoice,
    updateConfigTaxInvoice,
    deleteConfigTaxInvoice,
    setDefaultConfigTaxInvoice,
    getInforBusinessConfigTaxInvoice,
    getInforIndividualConfigTaxInvoice,
    loading,
    error,
  };
};
