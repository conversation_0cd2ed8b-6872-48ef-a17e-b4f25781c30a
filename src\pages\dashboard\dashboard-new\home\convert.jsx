import React, { useState } from 'react';
import { Box, Typography, Select, MenuItem, FormControl, InputLabel } from '@mui/material';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';

const convertData = Array.from({ length: 31 }, (_, index) => ({
  day: index + 1,
  source: Math.floor(Math.random() * 8000) + 2000,
  customer: Math.floor(Math.random() * 6000) + 1000,
}));

const monthOptions = [
  { value: '12', label: 'Tháng 12' },
  { value: '11', label: 'Tháng 11' },
  { value: '10', label: 'Tháng 10' },
];

const Convert = () => {
  const [month, setMonth] = useState('12');

  const handleMonthChange = (event) => {
    setMonth(event.target.value);
  };

  return (
    <Box
      sx={{
        p: 2,
        boxShadow: '0 0px 20px 0 #00000026!important',
        borderRadius: '15px',
        marginTop: '35px',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 1,
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontSize: '18px',
            fontWeight: 600,
            color: '#333',
          }}
        >
          Chuyển đổi
        </Typography>
        <FormControl sx={{ minWidth: 100 }}>
          <Select
            value={month}
            onChange={handleMonthChange}
            label="Tháng"
            sx={{
              border: '1px solid #D5D5D5',
              borderRadius: '15px',
              height: '36px',
              fontSize: '14px',
              color: '#333',
              '& .MuiOutlinedInput-notchedOutline': {
                border: 'none',
              },
            }}
          >
            {monthOptions.map((option) => (
              <MenuItem key={option.value} value={option.value} sx={{ fontSize: '14px' }}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      <ResponsiveContainer width="100%" height={250}>
        {' '}
        <AreaChart data={convertData} margin={{ top: 10, right: 10, left: -20, bottom: 0 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb00" />
          <XAxis
            dataKey="day"
            tick={{ fontSize: 12, fill: '#2B303466', fontWeight: '600' }}
            tickLine={false}
            axisLine={{ stroke: '#e5e7eb00' }}
          />
          <YAxis
            tickFormatter={(value) => `${value / 1000}k`}
            domain={[0, 10000]}
            tick={{ fontSize: 12, fill: '#2B303466', fontWeight: '600' }}
            tickLine={false}
            axisLine={{ stroke: '#e5e7eb00' }}
          />
          <Tooltip
            formatter={(value) => `${value}`}
            contentStyle={{
              fontSize: '12px',
              borderRadius: '4px',
              border: 'none',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
            }}
          />
          <Legend
            verticalAlign="bottom"
            height={36}
            iconType="circle"
            formatter={(value) => <span style={{ fontSize: '12px', color: '#666' }}>{value}</span>}
          />
          <Area
            type="monotone"
            dataKey="source"
            name="Người dùng"
            stackId="1"
            stroke="#FF8F6D"
            fill="#FF8F6D"
            // fillOpacity={0.4}
          />
          <Area
            type="monotone"
            dataKey="customer"
            name="Khách hàng"
            stackId="1"
            stroke="#8280FF"
            fill="#8280FF"
            // fillOpacity={0.4}
          />
        </AreaChart>
      </ResponsiveContainer>
    </Box>
  );
};

export default Convert;
