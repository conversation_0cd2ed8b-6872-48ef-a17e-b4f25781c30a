import React, { useState } from "react";
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Button,
  Checkbox,
} from "@mui/material";
import { formatPrice } from "@/src/api/types/membership.types";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";

const UnpaidCommissionTab = ({
  commissionReport,
  setCommissionReport,
  totalCount,
  setTotalCount,
  page,
  setPage,
  rowsPerPage,
  setRowsPerPage,
  selected,
  setSelected,
}) => {
  // const [page, setPage] = useState(0);
  // const [rowsPerPage, setRowsPerPage] = useState(5);

  // const filteredData = ordersData.filter((order) =>
  //   searchQuery
  //     ? order.agentName.toLowerCase().includes(searchQuery.toLowerCase()) ||
  //     order.id.includes(searchQuery)
  //     : true
  // );

  // const paginatedData = filteredData.slice(page * rowsPerPage, (page + 1) * rowsPerPage);

  const handleConfirmPayment = (id) => {
    console.log(`Xác nhận chi cho đơn hàng: ${id}`);
  };

  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelected = commissionReport.map((rp, index) => index);
      setSelected(newSelected);
      return;
    }
    setSelected([]);
  };

  const handleSelectRow = (id) => {
    const selectedIndex = selected.indexOf(id);
    let newSelected = [];

    if (selectedIndex === -1) {
      newSelected = [...selected, id];
    } else {
      newSelected = selected.filter((selectedId) => selectedId !== id);
    }

    setSelected(newSelected);
  };

  const isSelected = (id) => selected.indexOf(id) !== -1;

  return (
    <TableContainer sx={{ maxWidth: "100%", mt: 2, overflowX: "auto" }}>
      <Table size="small">
        <TableHead>
          <TableRow>
            {[
              { header: "STT", width: "50px" },
              { header: "ID Đối tác", width: "100px" },
              { header: "Tên đối tác", width: "150px" },
              { header: "F1", width: "70px" },
              { header: "F2", width: "70px" },
              { header: "Doanh thu", width: "120px" },
              { header: "Hoa hồng", width: "120px" },
              { header: "Thuế TNCN", width: "100px" },
              { header: "Thực nhận", width: "120px" },
              { header: "Người nhận", width: "150px" },
              { header: "Phương thức thanh toán", width: "180px" },
              { header: "Thông tin thanh toán", width: "180px" },
              { header: "Tài khoản thanh toán", width: "160px" },
              { header: "Trạng thái", width: "120px" },
              { header: "Thời gian", width: "120px" },
            ].map((item, index) => (
              <TableCell
                key={index}
                sx={{
                  fontWeight: "bold",
                  minWidth: item.width,
                }}
              >
                {item.header}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {commissionReport?.length > 0 ? (
            commissionReport?.map((rp, index) => {
              const isItemSelected = isSelected(index);
              return (
                <TableRow
                  key={index}
                  hover
                  role="checkbox"
                  aria-checked={isItemSelected}
                  selected={isItemSelected}
                >
                  <TableCell>{page * rowsPerPage + index + 1}</TableCell>
                  <TableCell>{rp.referralCode}</TableCell>
                  <TableCell>{rp.fullName}</TableCell>
                  <TableCell>{rp.f1Quantity}</TableCell>
                  <TableCell>{rp.f2Quantity}</TableCell>
                  <TableCell>{formatPrice(rp.revenue)} VNĐ</TableCell>
                  <TableCell>{formatPrice(rp.commissionValue)} VNĐ</TableCell>
                  <TableCell>{rp.personalIncomeTax}</TableCell>
                  <TableCell>{formatPrice(rp.netAmount)} VNĐ</TableCell>
                  <TableCell>{rp.bankAccountName}</TableCell>
                  <TableCell>{rp.typePay}</TableCell>
                  <TableCell>{rp.bankName}</TableCell>
                  <TableCell>{rp.bankAccountNumber}</TableCell>
                  <TableCell>
                    {rp.paymentStatus === "Unpaid" ? "Chưa thanh toán" : "Đã thanh toán"}
                  </TableCell>
                  <TableCell>{rp.created}</TableCell>
                </TableRow>
              );
            })
          ) : (
            <TableRow>
              <TableCell colSpan={15} align="center">
                Không có dữ liệu phù hợp
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      <Box display="flex" justifyContent="flex-end" mt={2} pr={2}>
        <TablePagination
          labelRowsPerPage="Số hàng mỗi trang"
          rowsPerPageOptions={rowPerPageOptionsDefault}
          component="div"
          count={totalCount}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={(event, newPage) => setPage(newPage)}
          onRowsPerPageChange={(event) => {
            setRowsPerPage(parseInt(event.target.value, 10));
            setPage(0);
          }}
        />
      </Box>
    </TableContainer>
  );
};

export default UnpaidCommissionTab;
