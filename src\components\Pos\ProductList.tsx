import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  Paper,
  TextField,
  Typography,
  Pagination,
} from '@mui/material';
import { useState, useEffect, useRef } from 'react';
import ProductOptionsDialog from './popupVariant';
import { useStoreId } from '@/src/hooks/use-store-id';
import { useCart } from '@/src/api/hooks/cart/use-cart';
import useSnackbar from '@/src/hooks/use-snackbar';
import { CreateOrUpdateCartData } from '@/src/api/types/cart.types';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
interface Product {
  itemsName: string;
  price: number;
  priceReal: number;
  image: string;
  itemsCode: string;
  categoryId: string;
  partnerId: string;
  shopId: string;
  itemsId: string;
  variants: any[];
  extraItemOptionGroups: string[];
  listVariant: any[];
}

interface CartItem {
  note: any;
  extraOptions: any;
  itemsId: any;
  id: number | string;
  itemsName: string;
  quantity: number;
  price: number;
  originalPrice: number;
  variant: string;
}

interface ProductListProps {
  selectedCustomer: any;
  onAddToCart: (item: CartItem) => void;
  cart: any;
}

export default function ProductList({ selectedCustomer, onAddToCart, cart }: ProductListProps) {
  const [tab, setTab] = useState(1);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [imageErrors, setImageErrors] = useState<{ [key: string]: boolean }>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);
  const itemsPerPage = 12;

  const storeId = useStoreId();
  const mounted = useRef(false);
  const { searchItems, createOrUpdateCart } = useCart();
  const snackbar = useSnackbar();

  useEffect(() => {
    const initData = async () => {
      if (!storeId || mounted.current) return;
      try {
        await fetchProducts();
        mounted.current = true;
      } catch (error) {
        console.error('Lỗi khi khởi tạo dữ liệu:', error);
      }
    };
    initData();
  }, [storeId]);

  const fetchProducts = async (
    search: string = '',
    page: number = 1,
    limit: number = itemsPerPage
  ) => {
    try {
      const response = await searchItems((page - 1) * limit, limit, storeId, search);
      if (response && response.data && Array.isArray(response.data.data)) {
        const fetchedProducts = response.data.data.map((item: any) => ({
          itemsName: item.itemsName,
          price: item.price || item.listVariant[0]?.price || 0,
          priceReal: item.priceReal || 0,
          image: item.images[0]?.link || '',
          itemsCode: item.itemsCode,
          categoryId: item.categoryId,
          partnerId: item.partnerId,
          shopId: item.shopId,
          itemsId: item.itemsId,
          variants: item.listVariant || [],
          extraItemOptionGroups: item.extraItemOptionGroups.flatMap((item) => item.itemOptionIds),
        }));
        setProducts(fetchedProducts);
        setTotalProducts(response.data.total);
      } else {
        setError('Phản hồi API không hợp lệ');
      }
    } catch (error) {
      setError('Lỗi khi lấy sản phẩm. Vui lòng thử lại sau.');
    }
  };

  const handleProductClick = (product: Product) => {
    setSelectedProduct(product);
    setDialogOpen(true);
  };

  const handleImageError = (itemsCode: string) => {
    setImageErrors((prev) => ({ ...prev, [itemsCode]: true }));
  };

  const handleSearchChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const search = event.target.value;
    setSearchTerm(search);
    setCurrentPage(1);
    await fetchProducts(search, 1);
  };

  const handleAddToCart = async (itemToAdd: any) => {
    const existingItemIndex = cart.listItems.findIndex(
      (item: CartItem) =>
        item.itemsId === itemToAdd.itemsId &&
        item.variant === itemToAdd.variant &&
        item.extraOptions.sort().toString() === itemToAdd.extraOptions.sort().toString() &&
        item.note === itemToAdd.note
    );

    const updatedCartItems: CartItem[] =
      existingItemIndex === -1
        ? [...cart.listItems, itemToAdd]
        : cart.listItems.map((item, index) =>
          index === existingItemIndex
            ? { ...item, quantity: item.quantity + itemToAdd.quantity }
            : item
        );

    const branch = localStorage.getItem('selectedBranch');
    const selectedBranch = branch ? JSON.parse(branch) : null;

    const cartData: CreateOrUpdateCartData = {
      cartNo: '',
      cartId: cart?.cartId || '',
      transactionId: '',
      partnerId: itemToAdd.partnerId,
      userId: cart?.userId || '',
      addressId: '',
      shopId: itemToAdd.shopId,
      branchId: selectedBranch?.branchId || '',
      listItems: updatedCartItems.map((item: any) => ({
        ...item,
        categoryIds: item.categoryId ? [item.categoryId] : [],
      })),
      voucherPromotion: [],
      voucherTransport: [],
      price: updatedCartItems.reduce((total, item) => total + item.price * item.quantity, 0),
      exchangePoints: 0,
      pointPrice: 0,
      voucherPromotionPrice: 0,
      voucherTransportPrice: 0,
      transportPrice: 0,
      transportService: 'LCOD',
      statusDelivery: 'InShop',
      typePay: 'COD',
      cartOrigin: 'Pos',
      created: new Date().toISOString(),
      updated: new Date().toISOString(),
    };

    try {
      const response = await createOrUpdateCart(cartData);

      if (response && response.data) {
        snackbar.success('Thêm sản phẩm vào giỏ hàng thành công');
        onAddToCart({ ...response.data, userInfo: cart.userInfo });
        setDialogOpen(false);
        setSelectedProduct(null);
      } else {
        console.error('Response không hợp lệ:', response);
      }
    } catch (error) {
      console.error('Lỗi API:', error);
      snackbar.error('Lỗi khi thêm sản phẩm vào giỏ hàng');
    }
  };

  const handlePageChange = (event: React.ChangeEvent<unknown>, page: number) => {
    setCurrentPage(page);
    fetchProducts(searchTerm, page);
  };

  const filteredProducts = products.filter((product) =>
    product.itemsName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Paper sx={{ height: '100%', overflow: 'auto', p: 1, bgcolor: 'white' }}>
      <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
        <Grid container spacing={2}>
          <Grid item xs={6} md={3}>
            <Button
              variant="contained"
              onClick={() => setTab(1)}
              sx={{ textTransform: 'none', bgcolor: '#24B25F', height: 50, width: '100%' }}
            >
              Sản phẩm
            </Button>
          </Grid>
          <Grid item xs={6} md={3}>
            <Button
              variant="contained"
              onClick={() => setTab(2)}
              sx={{ textTransform: 'none', bgcolor: '#7232B5', height: 50, width: '100%' }}
            >
              Dịch vụ
            </Button>
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Tìm kiếm sản phẩm"
              size="small"
              value={searchTerm}
              onChange={handleSearchChange}
              sx={{ bgcolor: 'white', height: 50 }}
              InputProps={{ sx: { height: 50 } }}
            />
          </Grid>
        </Grid>
      </Box>

      {error ? (
        <Typography color="error" sx={{ mt: 2 }}>
          {error}
        </Typography>
      ) : (
        <>
          <Box sx={{ height: 'calc(100vh - 250px)', overflow: 'auto' }}>
            <Grid container spacing={2}>
              {tab === 1 &&
                filteredProducts.map((product, index) => (
                  <Grid item xs={12} md={3} key={index}>
                    <Card
                      sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        borderRadius: 0,
                      }}
                      onClick={() => handleProductClick(product)}
                    >
                      <Box
                        sx={{
                          bgcolor: '#eee',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        {!imageErrors[product.itemsCode] ? (
                          <img
                            src={product.image || '/logo/logo.png'}
                            alt={product.itemsName}
                            style={{
                              height: '100%',
                              width: '100%',
                              aspectRatio: 1,
                              objectFit: 'cover',
                            }}
                            onError={() => handleImageError(product.itemsCode)}
                          />
                        ) : (
                          <img
                            src="/logo/logo.jpg"
                            alt="default"
                            style={{
                              height: '100%',
                              width: '100%',
                              aspectRatio: 1,
                              objectFit: 'cover',
                            }}
                          />
                        )}
                      </Box>
                      <CardContent
                        sx={{
                          p: 1,
                          flex: 1,
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'space-between',
                        }}
                      >
                        <Typography
                          variant="body2"
                          sx={{
                            fontSize: '0.875rem',
                            display: '-webkit-box',
                            WebkitBoxOrient: 'vertical',
                            WebkitLineClamp: 2,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            height: '3em',
                          }}
                        >
                          {product.itemsName}
                        </Typography>
                      </CardContent>
                      <Typography
                        sx={{
                          p: 1,
                          fontWeight: 'bold',
                          fontSize: '1rem',
                          color: 'black',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                        }}
                      >
                        <span>{product.price.toLocaleString()}đ</span>
                        <span style={{ marginLeft: 'auto' }}>
                          <ShoppingCartIcon />
                        </span>
                      </Typography>
                    </Card>
                  </Grid>
                ))}
            </Grid>
          </Box>
          <Pagination
            count={Math.ceil(totalProducts / itemsPerPage)}
            page={currentPage}
            onChange={handlePageChange}
            sx={{ mt: 5, display: 'flex', justifyContent: 'center' }}
          />
        </>
      )}
      {selectedProduct && (
        <ProductOptionsDialog
          open={dialogOpen}
          onClose={() => {
            setDialogOpen(false);
            setSelectedProduct(null);
          }}
          onSubmit={handleAddToCart}
          product={selectedProduct}
        />
      )}
    </Paper>
  );
}
