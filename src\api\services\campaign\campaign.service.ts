import { apiClient } from "../../config/api-client";
import { API_PATHS } from "../../constants/api-paths";
import type { ApiError, ErrorConfig } from "../../types/error.types";
import type { ExtendedRequestConfig } from "../../types/api.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }

  throw error;
}

export interface IBodyImportExcelListUserCampaign {
  file: File;
  ShopId: string;
  CampaignId: string;
  TemplateId: string;
}

export interface IBodyCreateCampaign {
  shopId: string;
  campaignId?: string;
  campaignName: string;
  runTimeType: string;
  runTime?: string;
  description?: string;
  templateType?: string;
  templateId: string;
  status?: string;
}

export interface CampaignDto {
  partnerId: string;
  shopId: string;
  campaignId: string;
  campaignName: string;
  runTimeType: string;
  runTime: string;
  description: string;
  targetType: string;
  userGroupId: string;
  templateType: string;
  templateId: string;
  status: string;
  createdDate: string; // ISO datetime
  modifiedDate: string;
  createdBy: string;
  modifiedBy: string;
  isDeleted: boolean;
  deletedAt: string;
  id: string;
}

export interface CampaignUserQueryParams {
  ShopId: string;
  CampaignId: string;
  FromDate?: string;
  ToDate?: string;
  Status?: string;
  Paging?: {
    NameType?: string;
    SortType?: string;
    PageSize?: number;
    PageIndex?: number;
    Search?: string;
    Name?: string;
    Sort?: string;
  };
}

export interface CampaignQueryParams {
  shopId: string;
  Status?: string;
  Paging?: {
    NameType?: string;
    SortType?: string;
    PageSize?: number;
    PageIndex?: number;
    Search?: string;
    Name?: string;
    Sort?: string;
  };
}

export const campaignService = {
  importFileExcelListUserCampaign: async <T = any>(
    data: IBodyImportExcelListUserCampaign,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    };

    try {
      if (data.file && data?.ShopId && data?.CampaignId && data?.TemplateId) {
        const formData = new FormData();
        formData.append("file", data.file);
        formData.append("ShopId", data.ShopId);
        formData.append("CampaignId", data.CampaignId);
        formData.append("TemplateId", data.TemplateId);

        const response = await apiClient.post<any, T>(
          `${API_PATHS.CAMPAIGN.API_CAMPAIGN_AUTOMATION}/import`,
          formData,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getListCampaign: async <T = any>(
    params: CampaignQueryParams,
    errorConfig?: ErrorConfig
  ): Promise<T | undefined> => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const searchParams = new URLSearchParams();
      if (params.shopId) {
        searchParams.append("ShopId", params.shopId);
      }
      if (params.Status) {
        searchParams.append("Status", params.Status);
      }
      const paging = params.Paging || {};
      if (paging.NameType) searchParams.append("Paging.NameType", paging.NameType);
      if (paging.SortType) searchParams.append("Paging.SortType", paging.SortType);
      if (paging.PageSize !== undefined)
        searchParams.append("Paging.PageSize", paging.PageSize.toString());
      if (paging.PageIndex !== undefined)
        searchParams.append("Paging.PageIndex", paging.PageIndex.toString());
      if (paging.Search) searchParams.append("Paging.Search", paging.Search);
      if (paging.Name) searchParams.append("Paging.Name", paging.Name);
      if (paging.Sort) searchParams.append("Paging.Sort", paging.Sort);

      const queryString = searchParams.toString();
      const response = await apiClient.get<any, T>(
        `${API_PATHS.CAMPAIGN.API_CAMPAIGN_AUTOMATION}?${queryString}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createCampaign: async <T = any>(data: IBodyCreateCampaign, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        `${API_PATHS.CAMPAIGN.API_CAMPAIGN_AUTOMATION}`,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateCampaign: async <T = any>(data: IBodyCreateCampaign, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (data.campaignId) {
        const response = await apiClient.put<any, T>(
          `${API_PATHS.CAMPAIGN.API_CAMPAIGN_AUTOMATION}`,
          data,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getDetailCampaign: async <T = any>(
    shopId: string,
    campaignId: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (shopId && campaignId) {
        const response = await apiClient.get<any, T>(
          `${API_PATHS.CAMPAIGN.API_CAMPAIGN_AUTOMATION}/${shopId}/${campaignId}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteCampaign: async <T = any>(
    shopId: string,
    campaignId: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (shopId && campaignId) {
        const response = await apiClient.delete<any, T>(
          `${API_PATHS.CAMPAIGN.API_CAMPAIGN_AUTOMATION}/${shopId}/${campaignId}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getListUserOfCampaign: async <T = any>(
    params: CampaignUserQueryParams,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (params.ShopId && params.CampaignId) {
        const buildCampaignUserUrl = (baseUrl: string, params: CampaignUserQueryParams): string => {
          const query = new URLSearchParams();

          if (params.ShopId) query.append("ShopId", params.ShopId);
          if (params.CampaignId) query.append("CampaignId", params.CampaignId);
          if (params.Status) query.append("Status", params.Status);
          if (params.FromDate) query.append("FromDate", params.FromDate);
          if (params.ToDate) query.append("ToDate", params.ToDate);

          if (params.Paging) {
            if (params.Paging.NameType) query.append("Paging.NameType", "Created");
            if (params.Paging.SortType) query.append("Paging.SortType", "asc");
            if (params.Paging.PageSize !== undefined)
              query.append("Paging.PageSize", params.Paging.PageSize.toString());
            if (params.Paging.PageIndex !== undefined)
              query.append("Paging.PageIndex", params.Paging.PageIndex.toString());
            if (params.Paging.Search) query.append("Paging.Search", params.Paging.Search);
            if (params.Paging.Name) query.append("Paging.Name", "Created");
            if (params.Paging.Sort) query.append("Paging.Sort", "asc");
          }

          return `${baseUrl}?${query.toString()}`;
        };

        const url = buildCampaignUserUrl(
          `${API_PATHS.CAMPAIGN.API_CAMPAIGN_AUTOMATION}/users`,
          params
        );
        const response = await apiClient.get<any, T>(url, config);
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  changeStatusOfCampaign: async <T = any>(
    shopId: string,
    campaignId: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (shopId && campaignId) {
        const response = await apiClient.get<any, T>(
          `${API_PATHS.CAMPAIGN.API_CAMPAIGN_AUTOMATION}/${shopId}/${campaignId}/status`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  rerunCampaign: async <T = any>(
    shopId: string,
    campaignId: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (shopId && campaignId) {
        const response = await apiClient.get<any, T>(
          `${API_PATHS.CAMPAIGN.API_CAMPAIGN_AUTOMATION}/${shopId}/${campaignId}/rerun`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
