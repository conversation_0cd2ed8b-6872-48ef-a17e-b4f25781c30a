import React from "react";
import { useFormContext } from "react-hook-form";
import { VoucherPromotionFormData } from "../PromotionForm";
import { Box, List, ListItem, ListItemText, Typography } from "@mui/material";
import { formatMoney } from "@/src/utils/format-money";
import dayjs from "dayjs";
import { VoucherTransportFormData } from "../VoucherTransportForm";
import {
  conditionTypeText,
  typeLimitText,
  useTimeVoucherEffect,
} from "../promotions/PromotionSummaryBox";
import { getValue } from "@mui/system";

export default function VoucherTransportSummaryBox() {
  const {
    watch,
    getValues,
    formState: { errors },
  } = useFormContext<VoucherTransportFormData>();
  const limitType = watch("limitType");
  const minOrder = watch("minOrder");
  const maxUsagePerUser = watch("maxUsagePerUser");
  return (
    <Box>
      <Typography variant="h6">Tóm tắt voucher</Typography>
      <Typography fontWeight="bold" marginTop={1}>
        Loại voucher
      </Typography>
      <List sx={{ p: 0, marginLeft: 1 }}>
        <ListItem disablePadding>
          <ListItemText primary={`• Miễn phí vận chuyển`} />
        </ListItem>
      </List>

      <Typography fontWeight="bold" marginTop={2}>
        Chi tiết áp dụng
      </Typography>
      <List sx={{ p: 0, marginLeft: 1 }}>
        <ListItem disablePadding>
          <ListItemText
            primary={`• ${
              limitType == "NotRequired"
                ? "Không có yêu cầu tối thiểu"
                : `${typeLimitText(limitType)} ${formatMoney(minOrder)}đ`
            }`}
          />
        </ListItem>
        {/* <ListItem disablePadding>
          <ListItemText primary={`• ${conditionTypeText(conditionType)}`} />
        </ListItem> */}

        <ListItem disablePadding>
          <ListItemText
            primary={`• ${
              maxUsagePerUser === 0
                ? "Không giới hạn số lần dùng voucher mỗi khách hàng"
                : `Mỗi khách hàng được sử dụng voucher ${maxUsagePerUser} lần`
            }`}
          />
        </ListItem>
        <ListItem disablePadding>
          <ListItemText primary={`• ${useTimeVoucherEffect(getValues())}`} />
        </ListItem>
      </List>
    </Box>
  );
}
