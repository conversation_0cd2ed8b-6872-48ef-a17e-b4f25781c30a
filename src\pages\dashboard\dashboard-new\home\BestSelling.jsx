import React, { useState } from 'react';
import {
  Box,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Avatar,
} from '@mui/material';

const bestSellingData = [
  {
    product: 'Apple Watch',
    image: '/path-to-apple-watch.jpg',
    stock: 1002,
    sold: 1002,
    revenue: '23,234,295',
  },
  {
    product: 'Apple Watch',
    image: '/path-to-apple-watch.jpg',
    stock: 1002,
    sold: 423,
    revenue: '23,234,295',
  },
  {
    product: 'Apple Watch',
    image: '/path-to-apple-watch.jpg',
    stock: 1002,
    sold: 423,
    revenue: '23,234,295',
  },
];

const monthOptions = [
  { value: '12', label: 'Tháng 12' },
  { value: '11', label: 'Tháng 11' },
  { value: '10', label: 'Tháng 10' },
];

const BestSelling = () => {
  const [month, setMonth] = useState('12');

  const handleMonthChange = (event) => {
    setMonth(event.target.value);
  };

  return (
    <Box
      sx={{
        p: 3,
        boxShadow: '0 0px 20px 0 #00000026!important',
        borderRadius: '15px',
        marginTop: '35px',
        '@media(max-width: 600px)': {
          padding: '15px',
        },
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Top 10 sản phẩm bán chạy</Typography>
        <FormControl sx={{ minWidth: 120, border: '1px solid #D5D5D5', borderRadius: '12px' }}>
          <Select
            value={month}
            onChange={handleMonthChange}
            label="Tháng"
            sx={{
              height: '36px',
              '& .MuiOutlinedInput-root': {
                '& fieldset': {
                  border: 'none',
                },
                '&:hover fieldset': {
                  border: 'none',
                },
                '&.Mui-focused fieldset': {
                  border: 'none',
                },
              },
            }}
          >
            {monthOptions.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      <Table sx={{ backgroundColor: '#fff', borderRadius: '8px' }}>
        <TableHead sx={{ background: '#F1F4F9', borderRadius: '12px' }}>
          <TableRow sx={{ borderRadius: '12px' }}>
            <TableCell
              sx={{
                '@media(max-width: 480px)': {
                  fontSize: '14px',
                },
              }}
            >
              Sản phẩm
            </TableCell>
            <TableCell
              sx={{
                '@media(max-width: 480px)': {
                  fontSize: '14px',
                },
              }}
            >
              Tồn kho
            </TableCell>
            <TableCell
              sx={{
                '@media(max-width: 480px)': {
                  fontSize: '14px',
                },
              }}
            >
              Số lượng bán
            </TableCell>
            <TableCell
              sx={{
                '@media(max-width: 480px)': {
                  fontSize: '14px',
                },
              }}
            >
              Doanh thu
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {bestSellingData.map((item, index) => (
            <TableRow key={index}>
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Avatar
                    src={item.image}
                    alt={item.product}
                    sx={{
                      width: 40,
                      height: 40,
                      '@media(max-width: 480px)': {
                        height: '25px',
                        width: '25px',
                      },
                    }}
                  />
                  <Typography
                    sx={{
                      '@media(max-width: 480px)': {
                        fontSize: '14px',
                      },
                    }}
                  >
                    {item.product}
                  </Typography>
                </Box>
              </TableCell>
              <TableCell
                sx={{
                  '@media(max-width: 480px)': {
                    fontSize: '14px',
                    padding: '10px',
                  },
                }}
              >
                {item.stock}
              </TableCell>
              <TableCell
                sx={{
                  '@media(max-width: 480px)': {
                    fontSize: '14px',
                    padding: '10px',
                  },
                }}
              >
                {item.sold}
              </TableCell>
              <TableCell
                sx={{
                  '@media(max-width: 480px)': {
                    fontSize: '14px',
                    padding: '10px',
                  },
                }}
              >
                {item.revenue}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Box>
  );
};

export default BestSelling;
