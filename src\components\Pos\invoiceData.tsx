import { useItemOption } from "@/src/api/hooks/item-option/use-item-option";
import { Box } from "@mui/system";
import { useEffect, useState } from "react";

export default function OptionName({
  optionIds
}) {
  const [optionDetails, setOptionDetails] = useState([]);
  const [fetchedData, setFetchedData] = useState(null);

  const { listItemOptionByIds } = useItemOption();

  const fetchItemOptions = async (itemOptionIds) => {
    const response = await listItemOptionByIds(itemOptionIds);
    if (response?.data) {
      const details = response.data.data.flatMap(group =>
        group.itemOptions.map(option => ({ groupName: group.name, optionName: option.name }))
      );
      setOptionDetails(details);
      setFetchedData(response.data);
    }
  };

  useEffect(() => {
    if (optionIds && optionIds.length > 0) {
      fetchItemOptions(optionIds);
    }
  }, [optionIds]);

  return (
    <Box>
      {/* <div>{optionIds}</div> */}
      <div>
        {optionDetails.map((detail, index) => (
          <div key={index} style={{ fontSize: '12px' }}>
            {detail.groupName} - {detail.optionName}
          </div>
        ))}
      </div>
    </Box>
  )
}