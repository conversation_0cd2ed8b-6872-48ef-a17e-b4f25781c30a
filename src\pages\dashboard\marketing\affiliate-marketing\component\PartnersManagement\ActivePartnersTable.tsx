import React, { useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Stack,
  Chip,
  Box,
  Typography,
  TablePagination,
  Tooltip,
  IconButton,
} from "@mui/material";
import EditPartnerModal from "./EditPartnerModal";
import QuickReportModal from "./QuickReportModal";
import { AffilateParterDto } from "./PartnersManagement";
import dayjs from "dayjs";
import { useAffiliation } from "@/src/api/hooks/affiliation/use-affiliation";
import { useStoreId } from "@/src/hooks/use-store-id";
import { ReportOverviewInterface } from "@/src/api/types/affiliation.type";
import { formatCurrency } from "@/src/utils/format-number";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import EditIcon from "@mui/icons-material/Edit";
import ReportProblemIcon from "@mui/icons-material/ReportProblem";
import { AssessmentOutlined } from "@mui/icons-material";

const stats = [
  { label: "Tổng lượt truy cập", value: "0" },
  { label: "Khách hàng qua liên kết", value: "0" },
  { label: "Tổng số đối tác", value: "0" },
  { label: "Tổng đơn hàng đối tác", value: "0" },
  { label: "Đơn hàng thành công", value: "0" },
  { label: "Doanh thu", value: "0 đ" },
  { label: "Hoa hồng đã duyệt", value: "0 đ" },
  { label: "Hoa hồng chờ duyệt", value: "0 đ" },
];

function getPaymentDisplayName(type) {
  switch (type) {
    case "COD":
      return "Thanh toán khi nhận hàng (COD)";
    case "Vnpay":
      return "Thanh toán qua VNPAY";
    case "Momo":
      return "Thanh toán qua Momo";
    case "Zalo":
      return "Thanh toán qua ZaloPay";
    case "Transfer":
      return "Chuyển khoản ngân hàng";
    case "Cash":
      return "Tiền mặt";
    case "Other":
      return "Hình thức khác";
    default:
      return "Không xác định";
  }
}

const ActivePartnersTable = ({
  searchQuery,
  page,
  setPage,
  rowsPerPage,
  setRowsPerPage,
  affiliationPartner,
  totalCount,
  fetchAffiliationPartner,
}) => {
  const [openEditModal, setOpenEditModal] = useState(false);
  const [selectedPartner, setSelectedPartner] = useState<any>();
  const [isOpenModalQuickReport, setIsOpenModalQuickReport] = useState(false);
  const [reportOverviews, setReportOverviews] = useState(stats);
  const { getQuickReportAffiliationPartner } = useAffiliation();

  const handleEditClick = (partner) => {
    setSelectedPartner(partner);
    setOpenEditModal(true);
  };

  const handleOpenReport = (partner) => {
    setIsOpenModalQuickReport(true);
    setSelectedPartner(partner);
    fetchOverview(partner?.userId);
  };

  const fetchOverview = async (userId) => {
    const response = await getQuickReportAffiliationPartner(userId);
    console.log({ response });
    if (response?.data) {
      const { data }: { data: any } = response?.data;
      const newStats = [
        { label: "Tổng khách hàng", value: `${formatCurrency(data.totalCustomers)}` },
        { label: "Khách đã mua", value: `${formatCurrency(data.customersPurchased)}` },
        { label: "Đơn hàng thành công", value: `${formatCurrency(data.successfulOrders)}` },
        { label: "Doanh thu", value: `${formatCurrency(data.totalRevenue)} đ` },
        { label: "Hoa hồng chờ duyệt", value: `${formatCurrency(data.pendingCommission)} đ` },
        { label: "Hoa hồng đã duyệt", value: `${formatCurrency(data.paidCommission)} đ` },
      ];
      setReportOverviews(newStats);
    }
  };

  return (
    <TableContainer sx={{ maxWidth: "100%", mt: 2 }}>
      <Table size="small">
        <TableHead>
          <TableRow>
            {[
              { header: "STT", width: "50px" },
              { header: "ID Đối tác", width: "100px" },
              { header: "Tên đối tác", width: "150px" },
              { header: "Email", width: "180px" },
              { header: "Số điện thoại", width: "120px" },
              { header: "Ngày tham gia", width: "120px" },
              { header: "ID Người giới thiệu", width: "150px" },
              { header: "Tên Người giới thiệu", width: "150px" },
              { header: "CMND/CCCD", width: "120px" },
              { header: "Mã số thuế", width: "120px" },
              { header: "Phương thức thanh toán", width: "180px" },
              { header: "Thông tin thanh toán", width: "180px" },
              { header: "Tài khoản thanh toán", width: "180px" },
              { header: "Trạng thái", width: "100px" },
              { header: "Thời hạn hiệu lực", width: "100px" },
              { header: "Quản lý", width: "50px", isLastColumn: true },
            ].map((item) => (
              <TableCell
                key={item.header}
                sx={{
                  fontWeight: "bold",
                  minWidth: item.width,
                  ...(item.isLastColumn && {
                    position: "sticky",
                    right: "0px",
                    backgroundColor: "#fff",
                    zIndex: 3,
                    boxShadow: "-2px 0 5px rgba(0,0,0,0.1)",
                    padding: "20px 16px",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "start",
                  }),
                }}
              >
                {item.header}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {affiliationPartner.length > 0 &&
            affiliationPartner.map((partner, index) => (
              <TableRow key={partner.userId}>
                <TableCell>{page * rowsPerPage + index + 1}</TableCell>
                <TableCell>{partner.referralCode}</TableCell>
                <TableCell>{partner.fullname}</TableCell>
                <TableCell>{partner.email}</TableCell>
                <TableCell>{partner.phoneNumber}</TableCell>
                <TableCell>{dayjs(partner.created).format("DD/MM/YYYY")}</TableCell>
                <TableCell>{partner.referrerCode}</TableCell>
                <TableCell>{partner.referrerName}</TableCell>
                <TableCell>{partner.identityCardNumber}</TableCell>
                <TableCell>{partner.taxCode}</TableCell>
                <TableCell>{getPaymentDisplayName(partner.paymentType)}</TableCell>
                <TableCell>{partner.bankName}</TableCell>
                <TableCell>{partner.bankAccountNumber}</TableCell>
                <TableCell>
                  <Box
                    component="span"
                    sx={{
                      display: "inline-block",
                      px: 2,
                      py: 0.5,
                      borderRadius: 2,
                      fontWeight: 600,
                      color:
                        partner.affiliationStatus === "Actived"
                          ? "green.700"
                          : partner.affiliationStatus === "InActived"
                          ? "grey.700"
                          : "grey.500",
                      bgcolor:
                        partner.affiliationStatus === "Actived"
                          ? "green.100"
                          : partner.affiliationStatus === "InActived"
                          ? "grey.200"
                          : "grey.100",
                      whiteSpace: "nowrap",
                      lineHeight: 1.5,
                    }}
                  >
                    {partner.affiliationStatus === "Actived" ? "Active" : "Inactive"}
                  </Box>
                </TableCell>
                <TableCell>
                  {dayjs(partner.affiliationExpireAt).format("DD/MM/YYYY HH:mm")}
                </TableCell>
                <TableCell
                  sx={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "flex-start",
                    position: "sticky",
                    right: 0,
                    backgroundColor: "#fff",
                    zIndex: 2,
                    boxShadow: "-2px 0 5px rgba(0,0,0,0.1)",
                    py: 2,
                  }}
                >
                  <Tooltip title="Sửa" arrow>
                    <IconButton
                      aria-label="Sửa"
                      tabIndex={0}
                      onClick={() => handleEditClick(partner)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" || e.key === " ") handleEditClick(partner);
                      }}
                      sx={{ color: "#2654FE", mr: 1 }}
                      size="small"
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Báo cáo nhanh" arrow>
                    <IconButton
                      aria-label="Báo cáo nhanh"
                      tabIndex={0}
                      onClick={() => handleOpenReport(partner)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" || e.key === " ") handleOpenReport(partner);
                      }}
                      sx={{ color: "red" }}
                      size="small"
                    >
                      <AssessmentOutlined fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
        </TableBody>
      </Table>

      <Box display="flex" justifyContent="flex-end" mt={2} pr={2}>
        <TablePagination
          labelRowsPerPage="Số hàng mỗi trang"
          rowsPerPageOptions={rowPerPageOptionsDefault}
          component="div"
          count={totalCount}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={(event, newPage) => setPage(newPage)}
          onRowsPerPageChange={(event) => {
            setRowsPerPage(parseInt(event.target.value, 10));
            setPage(0);
          }}
        />
      </Box>

      <EditPartnerModal
        open={openEditModal}
        onClose={() => setOpenEditModal(false)}
        partner={selectedPartner}
        fetchAffiliationPartner={fetchAffiliationPartner}
        page={page}
        rowsPerPage={rowsPerPage}
      />
      <QuickReportModal
        open={isOpenModalQuickReport}
        onClose={() => setIsOpenModalQuickReport(false)}
        partner={selectedPartner}
        reportOverviews={reportOverviews}
      />
    </TableContainer>
  );
};

export default ActivePartnersTable;
