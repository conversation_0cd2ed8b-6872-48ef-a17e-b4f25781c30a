import { forwardRef } from 'react';
import NextLink from 'next/link';
import { Link as MuiLink } from '@mui/material';
import type { LinkProps as MuiLinkProps } from '@mui/material/Link';
import type { LinkProps as NextLinkProps } from 'next/link';

type RouterLinkProps = Omit<MuiLinkProps, 'href' | 'component'> & 
  NextLinkProps & {
    component?: typeof MuiLink;
};

export const RouterLink = forwardRef<HTMLAnchorElement, RouterLinkProps>(
  function RouterLink({ component: Component, href, ...props }, ref) {
    if (Component) {
      return (
        <NextLink href={href} passHref legacyBehavior>
          <MuiLink ref={ref} {...props} />
        </NextLink>
      );
    }

    return (
      <NextLink
        ref={ref}
        href={href}
        {...props}
      />
    );
  }
); 