import React, { useState, useRef, useEffect, useCallback } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  IconButton,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  LinearProgress,
  Stack,
  Card,
} from "@mui/material";
import { Close as CloseIcon, CloudUpload as CloudUploadIcon } from "@mui/icons-material";
import {
  CreateFileGroupRequest,
  GetGroupFileRequest,
  MediaGroup,
  RefType,
} from "@/src/api/types/media.types";
import { useMedia } from "@/src/api/hooks/media/use-media";
import useSnackbar from "@/src/hooks/use-snackbar";
import { ImageProcessor } from "@/src/utils/image-processor";
import { useStoreId } from "@/src/hooks/use-store-id";
import { isValidImageFile } from "../../../category-management/create";

interface DialogUploadMediaProps {
  open: boolean;
  onClose: () => void;
  groupId: string; // ID của group được truyền vào
  onSuccess: () => void;
  type: "image" | "video";
}

interface UploadPreview {
  file: File;
  preview: string;
}

const DialogUploadMedia: React.FC<DialogUploadMediaProps> = ({
  open,
  onClose,
  groupId,
  onSuccess,
  type,
}) => {
  const [selectedFiles, setSelectedFiles] = useState<UploadPreview[]>([]);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [isUploading, setIsUploading] = useState(false);
  const [selectedGroupId, setSelectedGroupId] = useState<string>(groupId);
  const [groups, setGroups] = useState<MediaGroup[]>([]);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const { uploadFile, getGroups } = useMedia();
  const snackbar = useSnackbar();
  const storeId = useStoreId();

  // Fetch groups when dialog opens
  const fetchGroups = useCallback(async () => {
    if (!storeId) return;
    try {
      const data: GetGroupFileRequest = {
        ShopId: storeId,
        Skip: 0,
        Limit: 100,
      };
      const response = await getGroups(data);
      setGroups(response.data.data || []);
    } catch (error) {
      console.error("Error fetching groups:", error);
      snackbar.error("Có lỗi xảy ra khi tải danh sách nhóm");
    }
  }, [storeId]);

  useEffect(() => {
    if (open) {
      fetchGroups();
    }
  }, [open, fetchGroups]);

  // Set default group when groupId changes
  useEffect(() => {
    setSelectedGroupId(groupId);
  }, [groupId]);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (selectedFiles.length + files.length > 10) {
      snackbar.error("Chỉ được chọn tối đa 10 file");
      return;
    }
    try {
      const processedFiles = await Promise.all(
        files.map(async (file) => {
          if (type === "image") {
            if (!isValidImageFile(file)) {
              snackbar.error(
                "Định dạng file không hợp lệ. Hãy chọn file định dạng PNG, JPG, JPEG hoặc JFIF."
              );
              return null;
            }

            const processedFile = await ImageProcessor.processImage(file);
            return {
              file: processedFile,
              preview: URL.createObjectURL(processedFile),
            };
          } else {
            return {
              file: file,
              preview: URL.createObjectURL(file),
            };
          }
        })
      );

      const validProcessedFiles = processedFiles.filter((item) => item !== null);

      setSelectedFiles([...selectedFiles, ...validProcessedFiles]);
    } catch (error) {
      console.error("Error processing files:", error);
      snackbar.error("Có lỗi xảy ra khi xử lý file");
    }
  };

  const handleUpload = async () => {
    if (!selectedGroupId) {
      snackbar.error("Vui lòng chọn nhóm");
      return;
    }

    setIsUploading(true);
    const totalFiles = selectedFiles.length;
    let completedFiles = 0;
    let hasError = false;

    try {
      for (const { file } of selectedFiles) {
        try {
          const data: CreateFileGroupRequest = {
            ShopId: storeId,
            GroupFileId: selectedGroupId,
            FileUpload: file,
            RefType: RefType.Article,
          };
          await uploadFile(data);
          completedFiles++;
          setUploadProgress((completedFiles / totalFiles) * 100);
        } catch (error) {
          console.error("Error uploading file:", error);
          hasError = true;
          break;
        }
      }

      if (!hasError) {
        snackbar.success("Tải lên thành công");
        onSuccess();
        handleClose();
      }
    } catch (error) {
      hasError = true;
      snackbar.error("Có lỗi xảy ra khi tải lên");
    } finally {
      setIsUploading(false);
    }
  };

  const handleClose = () => {
    selectedFiles.forEach((file) => URL.revokeObjectURL(file?.preview));
    setSelectedFiles([]);
    setUploadProgress(0);
    setIsUploading(false);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
          <Typography variant="h6">Tải {type === "image" ? "ảnh" : "video"} lên</Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        {/* Group Selection */}
        <FormControl fullWidth size="small" sx={{ mb: 2, mt: 1 }}>
          <InputLabel id="group-select-label">Chọn nhóm</InputLabel>
          <Select
            labelId="group-select-label"
            id="group-select"
            value={selectedGroupId}
            label="Chọn nhóm"
            onChange={(e) => setSelectedGroupId(e.target.value)}
            disabled={isUploading}
            sx={{
              minHeight: 40,
              "& .MuiSelect-select": {
                display: "flex",
                alignItems: "center",
              },
            }}
          >
            {groups.map((group) => (
              <MenuItem
                key={group.groupFileId}
                value={group.groupFileId}
                sx={{
                  minHeight: 40,
                  display: "flex",
                  alignItems: "center",
                }}
              >
                {group.groupName}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <Typography variant="subtitle1" gutterBottom>
          Tải lên từ thiết bị
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Bạn có thể tải lên tối đa 10 {type === "image" ? "hình ảnh" : "video"}.
          {type === "image"
            ? " Chỉ hỗ trợ định dạng JPG, PNG, GIF và dung lượng tối đa 10MB mỗi ảnh."
            : " Chỉ hỗ trợ định dạng MP4 và dung lượng tối đa 100MB mỗi video."}
        </Typography>

        {/* File Selection Area */}
        <input
          type="file"
          multiple
          accept={type === "image" ? "image/*" : "video/*"}
          hidden
          ref={fileInputRef}
          onChange={handleFileSelect}
          disabled={isUploading}
        />

        {/* Preview Area */}
        {selectedFiles.length === 0 ? (
          <Box
            sx={{
              border: "2px dashed",
              borderColor: "divider",
              borderRadius: 1,
              p: 3,
              textAlign: "center",
              cursor: isUploading ? "not-allowed" : "pointer",
              opacity: isUploading ? 0.5 : 1,
              "&:hover": {
                borderColor: "primary.main",
                bgcolor: "primary.lighter",
              },
            }}
            onClick={() => !isUploading && fileInputRef.current?.click()}
          >
            <CloudUploadIcon sx={{ fontSize: 40, color: "text.secondary", mb: 1 }} />
            <Typography>Nhấp để chọn hoặc kéo thả file vào đây</Typography>
          </Box>
        ) : (
          <Stack spacing={2}>
            {isUploading && (
              <Box sx={{ width: "100%" }}>
                <LinearProgress variant="determinate" value={uploadProgress} />
                <Typography variant="body2" color="text.secondary" align="center" sx={{ mt: 1 }}>
                  Đang tải lên... {Math.round(uploadProgress)}%
                </Typography>
              </Box>
            )}
            <Box
              sx={{
                display: "flex",
                gap: 2,
                flexWrap: "wrap",
              }}
            >
              {selectedFiles.map((file, index) => (
                <Card
                  key={index}
                  sx={{
                    position: "relative",
                    width: 120,
                    height: 120,
                    flexShrink: 0,
                  }}
                >
                  {type === "image" ? (
                    <img
                      src={file?.preview}
                      alt={`Preview ${index}`}
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                      }}
                    />
                  ) : (
                    <video
                      src={file.preview}
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                      }}
                    />
                  )}
                  {!isUploading && (
                    <IconButton
                      size="small"
                      onClick={() => {
                        URL.revokeObjectURL(file.preview);
                        setSelectedFiles(selectedFiles.filter((_, i) => i !== index));
                      }}
                      sx={{
                        position: "absolute",
                        top: 4,
                        right: 4,
                        bgcolor: "rgba(0,0,0,0.5)",
                        color: "white",
                        "&:hover": {
                          bgcolor: "rgba(0,0,0,0.7)",
                        },
                      }}
                    >
                      <CloseIcon fontSize="small" />
                    </IconButton>
                  )}
                </Card>
              ))}

              {/* Add New Image Card */}
              {!isUploading && selectedFiles.length < 10 && (
                <Card
                  sx={{
                    position: "relative",
                    width: 120,
                    height: 120,
                    flexShrink: 0,
                    border: "2px dashed",
                    borderColor: "divider",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    cursor: "pointer",
                    "&:hover": {
                      borderColor: "primary.main",
                      bgcolor: "primary.lighter",
                    },
                  }}
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      color: "text.secondary",
                    }}
                  >
                    <CloudUploadIcon sx={{ fontSize: 24, mb: 1 }} />
                    <Typography variant="caption">
                      Thêm {type === "image" ? "ảnh" : "video"}
                    </Typography>
                  </Box>
                </Card>
              )}
            </Box>
          </Stack>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2.5 }}>
        <Button onClick={handleClose} disabled={isUploading}>
          Hủy
        </Button>
        <Button
          variant="contained"
          onClick={handleUpload}
          disabled={selectedFiles.length === 0 || isUploading || !selectedGroupId}
        >
          {isUploading ? "Đang tải lên..." : "Tải lên"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DialogUploadMedia;
