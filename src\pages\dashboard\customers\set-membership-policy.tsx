import React, { useState } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  IconButton,
  useTheme,
  useMediaQuery,
  TextField,
  InputAdornment
} from '@mui/material';
import {
  Edit,
  ShoppingCart,
  PersonAdd,
  Share,
  LocalOffer,
  CardGiftcard,
  ToggleOn,
  ToggleOff,
  ArrowBack
} from '@mui/icons-material';
import DashboardLayout from '../../../layouts/dashboard';
import TitleDialog from '@/src/components/dialog/TitleDialog';
import Overview from '@/src/components/policy/overview';
import Point from '@/src/components/policy/point';
import CustomerDetail from '@/src/components/policy/customer-detail';
import { useRouter } from 'next/router';
import Grid from '@mui/system/Grid';

const PointAdjustmentForm = ({ open, handleClose, title, description, extraDescription }) => {
  const theme = useTheme();
  return (
    <TitleDialog
      open={open}
      handleClose={handleClose}
      handleSubmit={handleClose}
      closeBtnTitle="Hủy bỏ"
      submitBtnTitle="Xác nhận"
    >
      <Box sx={{ display: 'flex', alignItems: 'center', marginBottom: 3 }}>
        <Typography variant="h5" component="h2" margin={1}>
          Kiếm điểm
        </Typography>
        <Typography variant="body2" sx={{ color: theme.palette.text.secondary }}>
          {title}
        </Typography>
      </Box>
      {extraDescription && (
        <Typography variant="body2" sx={{ marginBottom: 2, textAlign: 'left' }}>
          {extraDescription}
        </Typography>
      )}
      <Typography
        variant="subtitle1"
        sx={{ marginBottom: 3, alignSelf: 'flex-start', textAlign: 'left', width: '100%' }}
      >
        Quy tắc:
      </Typography>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%', alignItems: 'center', mb: 2 }}>
        <Typography id="modal-description" variant="body2" component="p" sx={{ flex: 1, textAlign: 'left' }}>
          {description}
        </Typography>
        <TextField
          fullWidth
          defaultValue="10"
          size="medium"
          InputProps={{
            endAdornment: <InputAdornment position="end">điểm</InputAdornment>,
            style: { textAlign: 'left', padding: '5px' }
          }}
          inputProps={{ style: { textAlign: 'left', padding: '5px' } }}
          sx={{ flex: 1, ml: 2 }}
        />
      </Box>
    </TitleDialog>
  );
};

export default function SetMembershipPolicy() {
  const [activeTab, setActiveTab] = useState(0);
  const [programStatus, setProgramStatus] = useState(true);
  const [rules, setRules] = useState({
    order: true,
    registration: true,
    sharing: false,
    discount: true,
    voucher: false
  });
  const [pointAdjustmentOpen, setPointAdjustmentOpen] = useState(false);
  const [pointAdjustmentOpen1, setPointAdjustmentOpen1] = useState(false);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const router = useRouter();

  const handleTabChange = (event, newValue) => setActiveTab(newValue);
  const handleToggle = (rule) => setRules((prev) => ({ ...prev, [rule]: !prev[rule] }));
  const handlePointAdjustmentOpen = () => setPointAdjustmentOpen(true);
  const handlePointAdjustmentClose = () => setPointAdjustmentOpen(false);
  const handlePointAdjustmentOpen1 = () => setPointAdjustmentOpen1(true);
  const handlePointAdjustmentClose1 = () => setPointAdjustmentOpen1(false);

  const handleBackClick = () => {
    router.push('/dashboard/customers/membership-policy');
  };

  const tabTitles = ['Tổng quan', 'Chi tiết điểm', 'Hồ sơ', 'Thiết lập'];

  return (
    <DashboardLayout>
      <Box>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <IconButton onClick={handleBackClick}>
            <ArrowBack
              sx={{
                fontSize: 30,
                marginLeft: 1,
                cursor: 'pointer',
                marginTop: -2
              }}
            />
          </IconButton>
          <Typography
            variant={isMobile ? 'h6' : 'h5'}
            sx={{
              fontWeight: 'bold',
              padding: { xs: 2, md: 3 },
              marginBottom: 2,
              fontSize: { xs: '1.1rem', sm: '1.25rem', md: '1.5rem' }
            }}
          >
            Chính sách thành viên/Tích điểm/{tabTitles[activeTab]}
          </Typography>
        </Box>
      </Box>
      <Box
        sx={{
          padding: { xs: 2, md: 3 },
          margin: '0 auto',
          backgroundColor: theme.palette.background.paper,
          marginBottom: 4,
          ml: { xs: 0, md: 3 }
        }}
      >
        <Typography
          variant={isMobile ? 'h6' : 'h5'}
          sx={{
            fontWeight: 'bold',
            marginBottom: 2,
            fontSize: { xs: '1.1rem', sm: '1.25rem', md: '1.5rem' }
          }}
        >
          Tích điểm
        </Typography>

        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant={isMobile ? 'scrollable' : 'standard'}
          scrollButtons="auto"
          sx={{
            marginBottom: 3,
            borderBottom: `1px solid ${theme.palette.divider}`,
            '& .MuiTab-root': {
              fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' },
              minWidth: { xs: 'auto', md: 160 },
              padding: { xs: '6px 12px', md: '12px 16px' },
              color: theme.palette.text.secondary
            },
            '& .Mui-selected': {
              color: theme.palette.text.primary,
              fontWeight: 'bold'
            },
            '& .MuiTabs-indicator': {
              backgroundColor: theme.palette.primary.main
            }
          }}
        >
          <Tab label="Tổng quan" />
          <Tab label="Chi tiết điểm người dùng" />
          <Tab label="Hồ sơ đổi điểm" />
          <Tab label="Thiết lập quy tắc điểm" />
        </Tabs>
        {activeTab === 0 && <Overview />}
        {activeTab === 1 && <CustomerDetail />}
        {activeTab === 2 && <Point />}
        {activeTab === 3 && (
          <Grid container spacing={3}>
            <Grid size={{ xs: 12, md: 4 }}>
              <Box sx={{ mb: 4 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
                  Trạng thái chương trình
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Bật gói, người dùng có thể bắt đầu sử dụng điểm. Sau khi đóng, việc tích lũy và sử dụng điểm sẽ dừng
                  lại.
                </Typography>
              </Box>
            </Grid>
            <Grid size={{ xs: 12, md: 8 }}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  mb: 4,
                  p: 2,
                  backgroundColor: theme.palette.action.hover,
                  borderRadius: 3,
                  border: `1px solid ${theme.palette.divider}`
                }}
              >
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                  Trạng thái
                </Typography>
                <IconButton onClick={() => setProgramStatus(!programStatus)} color="primary">
                  {programStatus ? (
                    <ToggleOn sx={{ color: theme.palette.primary.main, fontSize: '2.5rem' }} />
                  ) : (
                    <ToggleOff sx={{ color: theme.palette.text.secondary, fontSize: '2.5rem' }} />
                  )}
                </IconButton>
              </Box>
            </Grid>
            <Grid size={{ xs: 12, md: 4 }}>
              <Box sx={{ mb: 4 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
                  Kiếm điểm
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Đặt nhiệm vụ cho người dùng để kiếm điểm
                </Typography>
              </Box>
            </Grid>
            <Grid size={{ xs: 12, md: 8 }}>
              <Box sx={{ mb: 4, border: `2px solid ${theme.palette.divider}`, borderRadius: 3 }}>
                {[
                  {
                    id: 'order',
                    icon: <ShoppingCart />,
                    title: 'Đặt hàng',
                    description: 'Người dùng kiếm được 100 điểm cho mỗi chi tiêu 1.000.000đ'
                  },
                  {
                    id: 'registration',
                    icon: <PersonAdd />,
                    title: 'Đăng ký mới',
                    description: 'Đăng ký thành công để nhận 10 điểm'
                  },
                  {
                    id: 'sharing',
                    icon: <Share />,
                    title: 'Chia sẻ',
                    description:
                      'Mời bạn bè truy cập cửa hàng và kích hoạt tài khoản thành công để được thưởng điểm, mỗi lượt mời thành công người tiêu dùng sẽ được thưởng điểm'
                  }
                ].map((item) => (
                  <Box
                    key={item.id}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      mb: 2,
                      p: 2,
                      border: `1px solid ${theme.palette.divider}`,
                      borderRadius: 1
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      {item.icon}
                      <Box>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                          {item.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {item.description}
                        </Typography>
                      </Box>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {item.id === 'registration' && (
                        <IconButton size="medium" onClick={handlePointAdjustmentOpen}>
                          <Edit />
                        </IconButton>
                      )}
                      {item.id === 'sharing' && (
                        <IconButton size="medium" onClick={handlePointAdjustmentOpen1}>
                          <Edit />
                        </IconButton>
                      )}
                      <IconButton onClick={() => handleToggle(item.id)} color="primary">
                        {rules[item.id] ? (
                          <ToggleOn sx={{ color: theme.palette.primary.main, fontSize: '2.5rem' }} />
                        ) : (
                          <ToggleOff sx={{ color: theme.palette.text.secondary, fontSize: '2.5rem' }} />
                        )}
                      </IconButton>
                    </Box>
                  </Box>
                ))}
              </Box>
            </Grid>
            <Grid size={{ xs: 12, md: 4 }}>
              <Box sx={{ mb: 4 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
                  Đổi điểm
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Thiết lập cách người dùng sử dụng điểm và đổi điểm để nhận lợi ích
                </Typography>
              </Box>
            </Grid>
            <Grid size={{ xs: 12, md: 8 }}>
              <Box sx={{ mb: 4, border: `1px solid ${theme.palette.divider}`, borderRadius: 3 }}>
                {[
                  {
                    id: 'discount',
                    icon: <LocalOffer />,
                    title: 'Giảm giá mua sắm',
                    description: '100 điểm = 100.000đ'
                  },
                  {
                    id: 'voucher',
                    icon: <CardGiftcard />,
                    title: 'Quy đổi voucher',
                    description: 'Người tiêu dùng sử dụng điểm để đổi lấy voucher theo tỷ lệ của cửa hàng phát hành'
                  }
                ].map((item) => (
                  <Box
                    key={item.id}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      mb: 2,
                      p: 2,
                      border: `1px solid ${theme.palette.divider}`,
                      borderRadius: 1
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      {item.icon}
                      <Box>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                          {item.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {item.description}
                        </Typography>
                      </Box>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {item.id === 'registration' && (
                        <IconButton size="medium" onClick={handlePointAdjustmentOpen}>
                          <Edit />
                        </IconButton>
                      )}
                      {item.id === 'sharing' && (
                        <IconButton size="medium" onClick={handlePointAdjustmentOpen1}>
                          <Edit />
                        </IconButton>
                      )}
                      <IconButton onClick={() => handleToggle(item.id)} color="primary">
                        {rules[item.id] ? (
                          <ToggleOn sx={{ color: theme.palette.primary.main, fontSize: '2.5rem' }} />
                        ) : (
                          <ToggleOff sx={{ color: theme.palette.text.secondary, fontSize: '2.5rem' }} />
                        )}
                      </IconButton>
                    </Box>
                  </Box>
                ))}
              </Box>
            </Grid>
          </Grid>
        )}
      </Box>
      <PointAdjustmentForm
        open={pointAdjustmentOpen}
        handleClose={handlePointAdjustmentClose}
        title="(đăng ký mới)"
        description="Kiếm điểm khi đăng ký thành công:"
        extraDescription=""
      />
      <PointAdjustmentForm
        open={pointAdjustmentOpen1}
        handleClose={handlePointAdjustmentClose1}
        title="(chia sẻ cửa hàng)"
        description="chia sẻ cửa hàng để nhận điểm "
        extraDescription="Mời bạn bè truy cập cửa hàng và kích hoạt tài khoản thành công để được thưởng điểm, mỗi lượt mời thành công người tiêu dùng sẽ được thưởng điểm"
      />
    </DashboardLayout>
  );
}
