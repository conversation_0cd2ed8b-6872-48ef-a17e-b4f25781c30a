import { useState } from "react";
import { Box, Paper, Button, styled, TextField, Dialog, DialogActions, DialogContent, DialogTitle } from "@mui/material";
import { Grid } from "@mui/system";

const TableBox = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(2),
    textAlign: "center",
    height: 60,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#D8E7FF",
    color: "black",
    border: "1px solid #2654FE",
    cursor: "pointer",
    "&.selected": {
        backgroundColor: "#1a73e8",
        color: "white",
    },
    "&.disabled": {
        backgroundColor: "#D4D4D4",
        color: "#2654FE",
        cursor: "pointer",
    },
}));

const CancelButton = styled(Button)(({ theme }) => ({
    bgcolor: "black",
    color: "white",
    "&:hover": { bgcolor: theme.palette.grey[800] },
    minWidth: "calc(25% - 8px)",
}));

const ConfirmButton = styled(Button)(({ theme }) => ({
    bgcolor: "#1a73e8",
    color: "white",
    "&:hover": { bgcolor: "#1558b0" },
    minWidth: "calc(25% - 8px)",
}));

interface TableSelectionProps {
    open: boolean;
    onClose: (selectedTable: number | null) => void;
}

export default function TableSelection({ open, onClose }: TableSelectionProps) {
    const [selectedTable, setSelectedTable] = useState<number | null>(null);
    const [tables, setTables] = useState<number[]>([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
    const [newTableNumber, setNewTableNumber] = useState<number | "">("");
    const [openAddTable, setOpenAddTable] = useState(false);

    const handleTableClick = (index: number) => {
        setSelectedTable(tables[index]);
    };

    const handleAddTable = () => {
        setOpenAddTable(true);
    };

    const handleAddTableConfirm = () => {
        if (newTableNumber !== "" && newTableNumber <= 30 && !tables.includes(newTableNumber)) {
            setTables((prev) => [...prev, newTableNumber]);
        }
        setOpenAddTable(false);
        setNewTableNumber("");
    };

    const handleCancel = () => {
        setOpenAddTable(false);
        setNewTableNumber("");
    };

    const handleConfirm = () => {
        onClose(selectedTable);
    };

    return (
        <Dialog open={open} onClose={() => onClose(null)} maxWidth="xs" fullWidth>
            <DialogContent>
                <Box sx={{ maxWidth: 600, p: 2, mt: 4, borderRadius: 1 }}>
                    <Grid container spacing={1} sx={{ mb: 2 }}>
                        {tables.map((num, index) => (
                            <Grid size={{ xs: 3 }} key={index}>
                                <TableBox
                                    className={selectedTable === num ? "selected" : ""}
                                    onClick={() => handleTableClick(index)}
                                >
                                    Bàn {num}
                                </TableBox>
                            </Grid>
                        ))}
                        <Grid size={{ xs: 3 }}>
                            <TableBox
                                className="disabled"
                                onClick={handleAddTable}
                            >
                                Thêm
                            </TableBox>
                        </Grid>
                    </Grid>
                </Box>
            </DialogContent>
            <DialogActions>
                <CancelButton sx={{ bgcolor: "black", color: "white", "&:hover": { bgcolor: '#454545' } }} onClick={() => onClose(null)}>
                    Hủy
                </CancelButton>
                <ConfirmButton sx={{ bgcolor: "#1a73e8", color: "white", "&:hover": { bgcolor: '#2654FE' } }} onClick={handleConfirm}>
                    Xác nhận
                </ConfirmButton>
            </DialogActions>
            <Dialog open={openAddTable} onClose={handleCancel}>
                <DialogTitle>Thêm Bàn Mới</DialogTitle>
                <DialogContent>
                    <TextField
                        autoFocus
                        margin="dense"
                        label="Số Bàn"
                        type="number"
                        fullWidth
                        variant="outlined"
                        value={newTableNumber}
                        onChange={(e) => setNewTableNumber(Number(e.target.value))}
                        inputProps={{ min: 1, max: 30 }}
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCancel} sx={{ bgcolor: "black", color: "white", "&:hover": { bgcolor: "grey.800" } }}>
                        Hủy
                    </Button>
                    <Button onClick={handleAddTableConfirm} sx={{ bgcolor: "#1a73e8", color: "white", "&:hover": { bgcolor: "#1558b0" } }}>
                        Xác nhận
                    </Button>
                </DialogActions>
            </Dialog>
        </Dialog>
    );
}