import { apiClient } from "../../config/api-client";
import { API_PATHS } from "../../constants/api-paths";
import type { ApiError, ErrorConfig } from "../../types/error.types";
import type { ExtendedRequestConfig } from "../../types/api.types";
import { logger } from "@/src/utils/logger";
import { UpdateUserInfoBody } from "../../types/user.types";
import { Interface } from "readline";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }

  throw error;
}

export interface PartnerEmployeeBodyCreateApi {
  password?: string;
  fullname: string;
  roleIds: string[];
  email?: string;
  phoneNumber: string;
  avatar?: string;
  username?: string;
  status?: string;
}
export interface PartnerEmployeeBodyUpdateApi {
  employeeId: string;
  body: PartnerEmployeeBodyCreateApi;
}
export interface PartnerEmployeeParamDto {
  PageSize?: number;
  PageIndex?: number;
  Search?: string;
  RoleId?: string;
}

export const partnerEmployeeService = {
  createPartnerEmployee: async <T = any>(
    data: PartnerEmployeeBodyCreateApi,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<T>(
        API_PATHS.PARTNER_EMPLOYEE.PARTNER_EMPLOYEE_API,
        data,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updatePartnerEmployee: async <T = any>(
    data: PartnerEmployeeBodyUpdateApi,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<T>(
        `${API_PATHS.PARTNER_EMPLOYEE.PARTNER_EMPLOYEE_API}/${data.employeeId}`,
        data.body,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getPartnerEmployeeById: async <T = any>(employeeId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<T>(
        `${API_PATHS.PARTNER_EMPLOYEE.PARTNER_EMPLOYEE_API}/${employeeId}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  listPartnerEmployee: async <T = any>(
    data: PartnerEmployeeParamDto = {},
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    const queryParams = new URLSearchParams();

    queryParams.append("NameType", "Created");
    queryParams.append("SortType", "asc");
    queryParams.append("Name", "Created");
    queryParams.append("Sort", "desc");

    if (data.PageSize !== undefined) queryParams.append("PageSize", String(data.PageSize));
    if (data.PageIndex !== undefined) queryParams.append("PageIndex", String(data.PageIndex));
    if (data.Search) queryParams.append("Search", data.Search);
    if (data.RoleId) queryParams.append("roleId", data.RoleId);

    const queryString = queryParams.toString();
    const url = `${API_PATHS.PARTNER_EMPLOYEE.PARTNER_EMPLOYEE_API}?${queryString}`;

    try {
      const response = await apiClient.get<T>(url, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteMultiPartnerEmployee: async <T = any>(employeeIds: string[], errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete<T>(
        `${API_PATHS.PARTNER_EMPLOYEE.PARTNER_EMPLOYEE_API}`,
        { data: employeeIds },
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  changePasswordEmployee: async <T = any>(
    data: PartnerEmployeeBodyUpdateApi,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<T>(
        `${API_PATHS.PARTNER_EMPLOYEE.PARTNER_EMPLOYEE_API}/changepassword/${data.employeeId}`,
        data.body,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
