import React from 'react';
import { Box, Typography, Button } from '@mui/material';
import { useSidebar } from 'src/contexts/sidebar-context';

export const SidebarTest = () => {
  const { isCollapsed, toggleSidebar, collapseSidebar, expandSidebar } = useSidebar();

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Sidebar Test Component
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 2 }}>
        Current sidebar state: {isCollapsed ? 'Collapsed' : 'Expanded'}
      </Typography>

      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        <Button 
          variant="contained" 
          onClick={toggleSidebar}
          color="primary"
        >
          Toggle Sidebar
        </Button>
        
        <Button 
          variant="outlined" 
          onClick={collapseSidebar}
          disabled={isCollapsed}
        >
          Collapse Sidebar
        </Button>
        
        <Button 
          variant="outlined" 
          onClick={expandSidebar}
          disabled={!isCollapsed}
        >
          Expand Sidebar
        </Button>
      </Box>

      <Box sx={{ mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          Test Instructions:
        </Typography>
        <Typography variant="body2" component="div">
          <ul>
            <li>On desktop (screen ≥ 1200px): You should see a toggle button in the top navigation</li>
            <li>On mobile (screen &lt; 1200px): You should see the hamburger menu button</li>
            <li>When collapsed: Sidebar shows only icons with tooltips</li>
            <li>When expanded: Sidebar shows full menu with text</li>
            <li>Layout should adjust smoothly with transitions</li>
          </ul>
        </Typography>
      </Box>
    </Box>
  );
};
