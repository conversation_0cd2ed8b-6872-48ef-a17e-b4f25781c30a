import React, { useEffect, useState } from "react";
import DashboardLayout from "@/src/layouts/dashboard";
import {
  Box,
  Button,
  CircularProgress,
  Divider,
  IconButton,
  InputAdornment,
  TextField,
  Typography,
} from "@mui/material";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useRouter } from "next/router";
import { paths } from "@/src/paths";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { shopSettingService } from "@/src/api/services/shop-setting/shop-setting.service";
import {
  defaultShopSetting,
  IShopSettingCreate,
} from "@/src/api/hooks/shop-setting/use-shop-setting";
import { useStoreId } from "@/src/hooks/use-store-id";
import useSnackbar from "@/src/hooks/use-snackbar";
import { StorageService } from "nextjs-api-lib";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import TitleTypography from "@/src/components/title-typography/title-typography";

const validationSchema = Yup.object({
  miniAppId: Yup.string().trim().required("MiniAppId là bắt buộc"),
  secretKey: Yup.string().trim(),
  checkoutSecretKey: Yup.string().trim(),
});

const initialValues = {
  miniAppId: "",
  secretKey: "",
  checkoutSecretKey: "",
};

const Statistics = () => {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const storeId = useStoreId();
  const snackbar = useSnackbar();
  const partnerId = StorageService.get("partnerId") as string | null;
  const [showSecretKey, setShowSecretKey] = useState(false);
  const [showCheckoutSecretKey, setShowCheckoutSecretKey] = useState(false);

  const fetchShopSettingByShopId = async () => {
    if (storeId) {
      const res = await shopSettingService.getDetailShopSetting(storeId);
      formik.setFieldValue("miniAppId", res?.data?.miniAppId);
      formik.setFieldValue("secretKey", res?.data?.secretKey);
      formik.setFieldValue("checkoutSecretKey", res?.data?.checkoutSecretKey);
    }
  };
  useEffect(() => {
    fetchShopSettingByShopId();
  }, [storeId]);

  const handleSubmit = async (values) => {
    setIsLoading(true);
    const data: IShopSettingCreate = {
      checkoutSecretKey: values.checkoutSecretKey,
      miniAppId: values.miniAppId,
      secretKey: values.secretKey,
      shopId: storeId,
      partnerId: partnerId,
    };
    const res = await shopSettingService.createAndUpdateShopSetting(data);
    if (res?.status === 200) {
      setIsLoading(false);
      snackbar.success("Cập nhật cấu hình Mini App thành công!");
    }
  };

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: handleSubmit,
  });

  const handleBack = () => {
    router.push(paths.settings.clause.index);
  };
  return (
    <DashboardLayout>
      <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
        <IconButton onClick={handleBack} sx={{ mr: 2 }}>
          <ArrowBackIcon />
        </IconButton>
        <TitleTypography>Zalo Mini App</TitleTypography>
      </Box>
      <Box sx={{ p: 3, bgcolor: "#fff", borderRadius: 2, boxShadow: 1 }}>
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 1 }}>
          <Typography variant="subtitle2" sx={{ fontWeight: "bold" }}>
            Mini App Id
          </Typography>
        </Box>
        <TextField
          fullWidth
          variant="outlined"
          name="miniAppId"
          value={formik.values.miniAppId}
          onChange={formik.handleChange}
          error={!!(formik.touched.miniAppId && formik.errors.miniAppId)}
          helperText={
            formik.touched.miniAppId && typeof formik.errors.miniAppId === "string"
              ? formik.errors.miniAppId
              : undefined
          }
          sx={{ mb: 3 }}
        />
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 1 }}>
          <Typography variant="subtitle2" sx={{ fontWeight: "bold" }}>
            Secret Key
          </Typography>
        </Box>
        <TextField
          fullWidth
          variant="outlined"
          name="secretKey"
          value={formik.values.secretKey}
          onChange={formik.handleChange}
          type={showSecretKey ? "text" : "password"}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconButton onClick={() => setShowSecretKey((prev) => !prev)} edge="end">
                  {showSecretKey ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </InputAdornment>
            ),
          }}
          sx={{ mb: 3 }}
        />
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 1 }}>
          <Typography variant="subtitle2" sx={{ fontWeight: "bold" }}>
            Checkout Secret Key
          </Typography>
        </Box>
        <TextField
          fullWidth
          variant="outlined"
          name="checkoutSecretKey"
          value={formik.values.checkoutSecretKey}
          onChange={formik.handleChange}
          type={showCheckoutSecretKey ? "text" : "password"}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconButton onClick={() => setShowCheckoutSecretKey((prev) => !prev)} edge="end">
                  {showCheckoutSecretKey ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </InputAdornment>
            ),
          }}
          sx={{ mb: 3 }}
        />
        <Divider sx={{ my: 2 }} />
        <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={() => formik.handleSubmit()}
            disabled={isLoading}
          >
            {isLoading ? <CircularProgress size={24} /> : "Lưu"}
          </Button>
        </Box>
      </Box>
    </DashboardLayout>
  );
};

export default Statistics;
