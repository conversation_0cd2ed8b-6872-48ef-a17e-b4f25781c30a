import { useEffect, useState } from "react";
import {
  Box,
  Button,
  FormControl,
  IconButton,
  Menu,
  MenuItem,
  Paper,
  Popover,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import {
  FormatAlignLeft as FormatAlignLeftIcon,
  FormatAlignCenter as FormatAlignCenterIcon,
  FormatAlignRight as FormatAlignRightIcon,
} from "@mui/icons-material";
import EmojiPicker from "emoji-picker-react";
import { Grid } from "@mui/material";
import { ClearIcon } from "@mui/x-date-pickers";
import { useMedia } from "@/src/api/hooks/media/use-media";
import { IZaloElement, ZaloElementTypeEnum } from "@/src/api/types/zalo-template.types";
import {
  FILE_SIZE_2MB,
  TEMPLATE_TYPE,
  TEXT_ALIGN_CENTER,
  TEXT_ALIGN_LEFT,
  TEXT_ALIGN_RIGHT,
} from "@/src/constants/constant";
import Enhanced<PERSON>ext<PERSON>ield from "./highlightedTextField";
import useSnackbar from "@/src/hooks/use-snackbar";
import { CreateFileGroupRequest, GetGroupFileRequest, RefType } from "@/src/api/types/media.types";
import { useStoreId } from "@/src/hooks/use-store-id";

const LeftPanelNewText = ({
  bannerImage,
  setBannerImage,
  content,
  setContent,
  triggerPrameters,
  templateType,
  setTemplateType,
}) => {
  const [emojiAnchorEl, setEmojiAnchorEl] = useState(null);
  const [defaultGroupId, setDefaultGroupId] = useState<string>("");
  const { uploadFile, getGroups } = useMedia();
  const [contentAnchorEl, setContentAnchorEl] = useState(null);
  const snackbar = useSnackbar();
  const storeId = useStoreId();
  const handleEmojiClick = (emojiData) => {
    const updateState = (prev: IZaloElement) => ({
      ...prev,
      content: (prev.content || "") + emojiData.emoji,
    });
    setContent(updateState);
    setEmojiAnchorEl(null);
  };

  const handleRemoveBannerImage = () => {
    setBannerImage(null);
  };

  useEffect(() => {
    const fetchDefaultGroup = async () => {
      try {
        const params: GetGroupFileRequest = {
          ShopId: storeId,
          Limit: 100,
          Skip: 0,
        };
        const response = await getGroups(params);
        if (response?.data?.data?.length > 0) {
          setDefaultGroupId(response?.data?.data?.[0]?.groupFileId);
        }
      } catch (error) {
        console.error("Error fetching default group:", error);
      }
    };
    fetchDefaultGroup();
  }, [storeId]);

  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (file) {
      if (file.size > FILE_SIZE_2MB) {
        snackbar.error(
          `Kích thước ảnh không được vượt quá ${(FILE_SIZE_2MB / 1024 / 1024).toFixed(1)}MB.`
        );
        return;
      }
      const allowedTypes = ["image/png", "image/jpeg", "image/jpg", "image/gif"];
      if (!allowedTypes.includes(file.type)) {
        alert("Định dạng ảnh không hợp lệ. Chỉ chấp nhận PNG, JPG, JPEG, GIF.");
        return;
      }
      // const reader = new FileReader();
      // reader.onload = (e) => {
      //     setBannerImage(e.target.result); // Truyền ảnh đã tải lên
      // };
      // reader.readAsDataURL(file);
      try {
        const data: CreateFileGroupRequest = {
          FileUpload: file,
          GroupFileId: defaultGroupId,
          ShopId: storeId,
          RefType: RefType.ZaloUID,
        };

        const response = await uploadFile(data);
        if (response?.data?.link) {
          setBannerImage({
            ...bannerImage,
            imageUrl: response?.data?.link,
            mediaType: response?.data?.type,
            type: ZaloElementTypeEnum.BANNER,
          });
        }
      } catch (error) {
        console.error("Lỗi khi upload file:", error);
        alert("Có lỗi xảy ra khi tải lên ảnh. Vui lòng thử lại.");
      }
    }
  };
  const handleContentFlashClick = (event) => setContentAnchorEl(event.currentTarget);

  const handleClose = () => {
    setContentAnchorEl(null);
  };

  const handleClickParam = (param) => {
    setContent({
      ...content,
      content: (content?.content || "") + " " + param.value,
    });
    handleClose();
  };

  const handleContentAlignmentChange = (newAlignment) => {
    setContent({
      ...content,
      align: newAlignment,
    });
  };
  return (
    <Grid
      item
      xs={12}
      md={8}
      // sx={{ display: "flex", flexDirection: "column", gap: 2 }}
      sx={{
        overflowY: "auto",
        maxHeight: "700px",
        // maxHeight: "1200px",
        // minHeight: "200px",
        width: "55%",
        display: "flex",
        flexDirection: "column",
        border: "1px solid #ccc",
        borderRadius: 0.8,
        padding: 2,
        "&::-webkit-scrollbar": {
          width: "8px",
        },
        "&::-webkit-scrollbar-track": {
          background: "#f1f1f1",
        },
        "&::-webkit-scrollbar-thumb": {
          background: "#888",
          borderRadius: "4px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          background: "#555",
        },
      }}
    >
      <Grid item xs={12} md={12} sx={{ marginBottom: 1 }}>
        {" "}
        <Typography variant="subtitle1" gutterBottom>
          Loại tin <span style={{ color: "red" }}>*</span>
        </Typography>
        <FormControl fullWidth size="small">
          <Select
            value={2}
            defaultValue={2}
            onChange={(e) => {
              setTemplateType(e.target.value);
            }}
          >
            {TEMPLATE_TYPE.map((option, index) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Box sx={{ mb: 1 }}>
        <Typography variant="subtitle1" gutterBottom>
          Banner
        </Typography>
        <Paper
          variant="outlined"
          sx={{
            p: 2,
            height: 100,
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            border: "1px solid #ccc",
            position: "relative",
          }}
        >
          {bannerImage?.imageUrl ? (
            <Box
              sx={{
                position: "relative",
                width: "100%",
                height: "100%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <img
                src={bannerImage.imageUrl}
                alt="Uploaded Banner"
                style={{ maxWidth: "100%", maxHeight: "100%", objectFit: "contain" }}
              />
              <IconButton
                size="small"
                sx={{
                  position: "absolute",
                  top: 4,
                  right: 4,
                  backgroundColor: "#fff",
                  "&:hover": { backgroundColor: "#f0f0f0" },
                }}
                onClick={handleRemoveBannerImage}
              >
                <ClearIcon fontSize="small" />
              </IconButton>
            </Box>
          ) : (
            <>
              <Typography variant="body2" color="primary" align="center">
                Thả file của bạn vào đây hoặc
              </Typography>
              <Button size="small" variant="text" color="primary" component="label">
                click để tải lên
                <input
                  type="file"
                  accept="image/png, image/jpeg, image/jpg, image/gif"
                  hidden
                  size={FILE_SIZE_2MB}
                  onChange={handleFileUpload}
                />
              </Button>
              <Typography variant="caption" color="textSecondary" align="center">
                Định dạng cho phép: PNG, JPG, JPEG, GIF
              </Typography>
              <Typography variant="caption" color="textSecondary" align="center">
                Kích thước ảnh không vượt quá 1MB
              </Typography>
            </>
          )}
        </Paper>
      </Box>
      <Box sx={{ flex: 1 }}>
        <Typography variant="subtitle1" gutterBottom>
          Nội dung <span style={{ color: "red" }}>*</span>
        </Typography>

        <EnhancedTextField
          fullWidth
          variant="outlined"
          multiline
          rows={10}
          maxLength={1000}
          size="small"
          value={content?.content}
          onChange={(e) => {
            const value = e.target.value;
            if (value.length <= 1000) {
              setContent({
                ...content,
                content: e.target.value,
              });
            }
          }}
          sx={{
            width: "100%",
            fontSize: "1rem",
            backgroundColor: "#f9f9f9",
            borderRadius: "4px",
          }}
          inputProps={{
            style: { textAlign: content?.align },
          }}
        />
        <Box sx={{ display: "flex", justifyContent: "flex-end", alignItems: "center", mt: 1 }}>
          {/* <IconButton
            size="small"
            color={content?.align === TEXT_ALIGN_LEFT ? "primary" : "default"}
            onClick={() => handleContentAlignmentChange("left")}
          >
            <FormatAlignLeftIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            color={content?.align === TEXT_ALIGN_CENTER ? "primary" : "default"}
            onClick={() => handleContentAlignmentChange("center")}
          >
            <FormatAlignCenterIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            color={content?.align === TEXT_ALIGN_RIGHT ? "primary" : "default"}
            onClick={() => handleContentAlignmentChange("right")}
          >
            <FormatAlignRightIcon fontSize="small" />
          </IconButton> */}
          {/* <IconButton
            size="small"
            color="default"
            onClick={(e) => setEmojiAnchorEl(e.currentTarget)}
          >
            <span role="img" aria-label="emoji">
              😊
            </span>
          </IconButton> */}
          <IconButton size="small" color="default" onClick={handleContentFlashClick}>
            <span role="img" aria-label="flash">
              ⚡
            </span>
          </IconButton>
          <Menu
            anchorEl={contentAnchorEl}
            open={Boolean(contentAnchorEl)}
            onClose={handleClose}
            anchorOrigin={{
              vertical: "bottom",
              horizontal: "left",
            }}
            transformOrigin={{
              vertical: "top",
              horizontal: "left",
            }}
          >
            {triggerPrameters.map((param, index) => (
              <MenuItem key={index} value={param.value} onClick={() => handleClickParam(param)}>
                {param.name}
              </MenuItem>
            ))}
          </Menu>
          <Box
            sx={{
              px: 1,
              py: 0.5,
              border: "1px solid #ccc",
              color: "black",
              borderRadius: "4px",
              fontSize: "0.75rem",
              textAlign: "center",
            }}
          >
            {1000 - (content?.content?.length || 0)}
          </Box>
        </Box>
      </Box>

      {/* Bảng Emoji */}
      <Popover
        open={Boolean(emojiAnchorEl)}
        anchorEl={emojiAnchorEl}
        onClose={() => setEmojiAnchorEl(null)}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
      >
        <EmojiPicker onEmojiClick={handleEmojiClick} />
      </Popover>
    </Grid>
  );
};

export default LeftPanelNewText;
