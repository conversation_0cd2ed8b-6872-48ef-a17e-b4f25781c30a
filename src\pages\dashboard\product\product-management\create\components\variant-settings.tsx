import React, { useState } from "react";
import { Box, Card, Typography, FormControlLabel, FormHelperText, Button } from "@mui/material";
import { useTranslation } from "react-i18next";
import { FormikProps } from "formik";
import EditIcon from "@mui/icons-material/Edit";
import { tokens } from "@/src/locales/tokens";
import CustomSwitch from "@/src/components/custom-switch";
import { ProductFormValues, VariantType } from "../../../../../../types/product/form";
import DialogAddVariant from "../../variant/dialog-add-variant";
import { convertVariantsToSpecifications } from "@/src/utils/variant-utils";

interface VariantSettingsProps {
  formik: FormikProps<ProductFormValues>;
}

const VariantSettings: React.FC<VariantSettingsProps> = ({ formik }) => {
  const { t } = useTranslation();
  const [openDialog, setOpenDialog] = useState(false);

  const handleVariantChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = event.target.checked;

    // Nếu bật variant, mở dialog
    if (isChecked) {
      setOpenDialog(true);
    } else {
      // Nếu tắt variant, reset các giá trị liên quan
      formik.setValues(
        {
          ...formik.values,
          isVariant: false,
          listVariant: [],
        },
        false
      );
    }
  };

  const handleCloseDialog = () => {
    // Khi đóng dialog, kiểm tra nếu không có variants thì set isVariant = false
    if (!formik.values.listVariant?.length) {
      formik.setFieldValue("isVariant", false);
    }
    setOpenDialog(false);
  };

  const handleSaveVariants = (variants: VariantType[]) => {
    const validVariants = Array.isArray(variants) ? variants : [];

    setTimeout(() => {
      formik.setFieldValue("isVariant", validVariants.length > 0);
      formik.setFieldValue("listVariant", validVariants);
    }, 0);
  };

  return (
    <Card sx={{ p: 2.5 }}>
      <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2.5 }}>
        <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
          {t(tokens.contentManagement.product.create.variant.title)}
        </Typography>
        {formik.values.isVariant && formik.values.listVariant?.length > 0 && (
          <Button
            startIcon={<EditIcon />}
            onClick={() => setOpenDialog(true)}
            variant="outlined"
            size="small"
          >
            {t(tokens.contentManagement.product.create.variant.editButton)}
          </Button>
        )}
      </Box>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2.5 }}>
        <FormControlLabel
          control={
            <CustomSwitch checked={formik.values.isVariant} onChange={handleVariantChange} />
          }
          label={
            <Typography variant="body2" sx={{ color: "text.primary", ml: 1.5 }}>
              {t(tokens.contentManagement.product.create.variant.hasVariants)}
            </Typography>
          }
          sx={{ mx: 0 }}
        />
        <FormHelperText sx={{ mt: -1.5, ml: 0 }}>
          {t(tokens.contentManagement.product.create.variant.description)}
        </FormHelperText>

        {formik.values.isVariant && formik.values.listVariant?.length > 0 && (
          <Box sx={{ mt: 1 }}>
            <Typography variant="body2" color="text.secondary">
              {t(tokens.contentManagement.product.variant.dialog.variantsCreated, {
                count: formik.values.listVariant.length,
              })}
            </Typography>
          </Box>
        )}
      </Box>

      <DialogAddVariant
        open={openDialog}
        onClose={handleCloseDialog}
        onSave={handleSaveVariants}
        initialVariants={formik.values.listVariant}
      />
    </Card>
  );
};

export default VariantSettings;
