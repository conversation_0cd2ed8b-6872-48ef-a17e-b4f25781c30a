// Request types
export interface GetArticleRequest {
  shopId: string;
  search?: string;
  skip: number;
  limit: number;
}

export interface GetArticleRequestBody {
  shopId: string;
  search?: string;
}

export interface CreateArticleRequest {
  shopId: string;
  articleCategoryId: string;
  title: string;
  content: string;
  description: string;
  typePublish: 'Publish' | 'UnPublish';
  status: 'Actived' | 'InActived';
}

export interface UpdateArticleRequest extends CreateArticleRequest {
  articleId: string;
}

// Response types
export interface Article {
  articleId: string;
  articleCategoryId: string;
  shopId: string;
  title: string;
  content: string;
  description: string;
  imageUrl: string;
  typePublish: 'Publish' | 'UnPublish';
  created: string;
  updated: string;
}