import {
  Box,
  Container,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  Button,
  Card,
  CardContent,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useFunction } from "@/src/api/hooks/function/use-function";
import { formatPrice } from "@/src/api/types/membership.types";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useRouter } from "next/router";
import { useAppSelector } from "@/src/redux/hooks";
import Link from "next/link";

export enum TypeStatus {
  Pending = "Pending",
  Actived = "Actived",
  InActived = "InActived",
}

interface PackageHistoryDto {
  packageHistoryId: string;
  packageName: string;
  packageCode: string;
  price: number;
  startDate: string;
  endDate: string;
  status: TypeStatus;
  paymentMethod: string;
  invoiceNumber: string;
  createdDate: string;
}

const PackageManagement: React.FC = () => {
  const { profile } = useAppSelector((state) => state.profile);
  const { getPackageHistory } = useFunction();
  const [packageHistories, setPackageHistories] = useState<PackageHistoryDto[]>([]);
  const [activePackage, setActivePackage] = useState<PackageHistoryDto | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const snackbar = useSnackbar();
  const router = useRouter();

  useEffect(() => {
    const fetchPackageHistory = async () => {
      setIsLoading(true);
      try {
        const res = await getPackageHistory();
        if (res && res.status === 200) {
          const histories = res.data.data;
          setPackageHistories(histories);
          // Find the active package
          const active =
            histories.find((pkg: PackageHistoryDto) => pkg.status === TypeStatus.Actived) || null;
          setActivePackage(active);
        }
      } catch (err) {
        setError("Không thể tải lịch sử mua gói");
        snackbar.error("Không thể tải lịch sử mua gói");
      } finally {
        setIsLoading(false);
      }
    };

    fetchPackageHistory();
  }, []);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("vi-VN", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const calculateCycleDays = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays === 365 ? "1 năm" : `${diffDays} ngày`;
  };

  const getStatusText = (status: TypeStatus) => {
    switch (status) {
      case TypeStatus.Actived:
        return { text: "Đang sử dụng", color: "green" };
      case TypeStatus.Pending:
        return { text: "Chờ duyệt", color: "orange" };
      case TypeStatus.InActived:
        return { text: "Dừng hoạt động", color: "red" };
      default:
        return { text: "Không xác định", color: "grey" };
    }
  };

  return (
    <>
      <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 4 }}>
        <Box>
          <Typography component="h1" sx={{ fontSize: "26px", fontWeight: "700", mb: 1 }}>
            Quản lý gói dịch vụ
          </Typography>
          <Typography sx={{ fontSize: "16px", color: "text.secondary" }}>
            Xem lịch sử mua gói dịch vụ của bạn
          </Typography>
        </Box>
        {/* {!isLoading && (
          <Link
            style={{
              borderRadius: "50px",
              textTransform: "none",
              fontSize: "16px",
              paddingInline: 20,
              paddingBlock: 5,
              backgroundColor: "#6366F1",
              color: "white",
            }}
            href="/dashboard/package/"
          >
            {activePackage ? "Nâng cấp gói dịch vụ" : "Mua gói"}
          </Link>
        )} */}
      </Box>

      {isLoading ? (
        <Box sx={{ display: "flex", justifyContent: "center", py: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error">{error}</Alert>
      ) : packageHistories.length === 0 ? (
        <Alert severity="info">Bạn chưa mua gói dịch vụ nào.</Alert>
      ) : (
        <>
          {/* Active Package Section */}
          <Box sx={{ mb: 4 }}>
            <Typography sx={{ fontSize: "20px", fontWeight: "600", mb: 2 }}>
              Gói dịch vụ hiện tại
            </Typography>
            {activePackage ? (
              <Card sx={{ boxShadow: "0 4px 4px 0 #00000040", p: 2 }}>
                <CardContent>
                  <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
                    <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                      <Typography sx={{ fontWeight: "500" }}>Gói đăng ký:</Typography>
                      <Typography>
                        {activePackage.packageName} ({activePackage.packageCode})
                      </Typography>
                    </Box>
                    <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                      <Typography sx={{ fontWeight: "500" }}>Giá:</Typography>
                      <Typography>{formatPrice(activePackage.price)} đ</Typography>
                    </Box>
                    <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                      <Typography sx={{ fontWeight: "500" }}>Chu kỳ:</Typography>
                      <Typography>
                        {calculateCycleDays(activePackage.startDate, activePackage.endDate)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                      <Typography sx={{ fontWeight: "500" }}>Phương thức thanh toán:</Typography>
                      <Typography>{activePackage.paymentMethod}</Typography>
                    </Box>
                    <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                      <Typography sx={{ fontWeight: "500" }}>Trạng thái:</Typography>
                      <Typography
                        sx={{ color: getStatusText(activePackage.status).color, fontWeight: "500" }}
                      >
                        {getStatusText(activePackage.status).text}
                      </Typography>
                    </Box>
                    <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                      <Typography sx={{ fontWeight: "500" }}>Ngày hết hạn:</Typography>
                      <Typography>{formatDate(activePackage.endDate)}</Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            ) : (
              <Alert severity="info">Hiện tại bạn không có gói dịch vụ nào đang hoạt động.</Alert>
            )}
          </Box>

          {/* Package History Table */}
          <Typography sx={{ fontSize: "20px", fontWeight: "600", mb: 2 }}>
            Lịch sử mua gói dịch vụ
          </Typography>
          <TableContainer component={Paper} sx={{ boxShadow: "0 4px 4px 0 #00000040" }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: "600" }}>Gói đăng ký</TableCell>
                  <TableCell sx={{ fontWeight: "600" }}>Giá</TableCell>
                  <TableCell sx={{ fontWeight: "600" }}>Chu kỳ</TableCell>
                  <TableCell sx={{ fontWeight: "600" }}>Phương thức thanh toán</TableCell>
                  <TableCell sx={{ fontWeight: "600" }}>Trạng thái</TableCell>
                  <TableCell sx={{ fontWeight: "600" }}>Ngày hết hạn</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {packageHistories.map((history) => (
                  <TableRow
                    key={history.packageHistoryId}
                    sx={{
                      "&:hover": {
                        backgroundColor: "rgba(0, 0, 0, 0.04)",
                      },
                    }}
                  >
                    <TableCell>
                      {history.packageName} ({history.packageCode})
                    </TableCell>
                    <TableCell>{formatPrice(history.price)} đ</TableCell>
                    <TableCell>{calculateCycleDays(history.startDate, history.endDate)}</TableCell>
                    <TableCell>{history.paymentMethod}</TableCell>
                    <TableCell>
                      <Typography
                        sx={{
                          color: getStatusText(history.status).color,
                          fontWeight: "500",
                        }}
                      >
                        {getStatusText(history.status).text}
                      </Typography>
                    </TableCell>
                    <TableCell>{formatDate(history.endDate)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </>
      )}
    </>
  );
};

export default PackageManagement;
