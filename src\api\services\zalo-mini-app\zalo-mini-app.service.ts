import { apiClient } from "@/src/api/config/api-client";
import { API_PATHS } from "@/src/api/constants/api-paths";
import type { ApiError, ErrorConfig } from "@/src/api/types/error.types";
import type { ExtendedRequestConfig } from "@/src/api/types/api.types";
import { GetProductRequest, GetProductRequestBody } from "@/src/api/types/product.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}
export interface GetListVersionParams {
  shopId: string;
  param?: {
    RequestId?: string;
    MiniAppId?: string;
    Offset?: number;
    Limit?: number;
  };
}

export interface MiniAppVersionDto {
  description: string;
  entrypoint: string;
  lastUpdated: string;
  lastUpdatedTime: string;
  name: string;
  size: number;
  status: string;
  versionId: number;
}

export const zaloMiniAppService = {
  getListVersion: async <T = any>(params?: GetListVersionParams, errorConfig?: ErrorConfig) => {
    const config: any = {
      errorHandling: errorConfig,
      params: params?.param,
    };

    try {
      if (params.shopId) {
        const response = await apiClient.get<T>(
          `${API_PATHS.ZALO_MINI_APP.GET_LIST_VERSION}/${params.shopId}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      return error;
      // handleApiError(error);
    }
  },
};
