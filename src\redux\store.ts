import { configureStore } from "@reduxjs/toolkit";
import profileReducer from "./slices/profileSlice";
import shopReducer from "./slices/shopSlice";
import passwordResetReducer from "./slices/passwordResetSlice";

export const store = configureStore({
  reducer: {
    profile: profileReducer,
    shop: shopReducer,
    passwordReset: passwordResetReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
