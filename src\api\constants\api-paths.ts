export const API_PATHS = {
  AUTH: {
    LOGIN: "/api/partner/authpartner/login",
    REFRESH_TOKEN: "/api/partner/authpartner/refreshtoken",
    LOGOUT: "/api/partner/authpartner/logout",
    REGISTER_DEVICE: "/api/partner/authpartner/registerdevice",
    REVOKE_DEVICE: "/api/partner/authpartner/revokedevice",
    FORGOT_PASSWORD: "/api/Partner/AuthPartner/ForgotPassword",
    RESET_PASSWORD: "/api/partner/ProfilePartner/ResetPassword",
  },
  OTP: {
    SEND_OTP: "/api/partner/otppartner/sendotp",
    VERIFY_OTP: "/api/partner/otppartner/verifyotp",
  },
  PROFILE: {
    GET_PROFILE: "/api/partner/profilepartner/getprofile",
    UPDATE_PROFILE: "/api/partner/profilepartner/updateprofile",
    UPDATE_AVATAR: "/api/partner/profilepartner/updateavatar",
    UPDATE_TOKEN_FCM: "/api/partner/profilepartner/updatetokenfcm",
    CHECK_PHONE_NUMBER: "/api/partner/profilepartner/check",
    RESET_PASSWORD: "/api/partner/profilepartner/resetpass",
    GET_BUSINESS_TYPES: "/api/partner/ProfilePartner/typebusiness",
    CHANGE_PASSWORD: "/api/partner/ProfilePartner/ChangePassword",
  },
  SHOP: {
    GET_SHOP: "/api/partner/shoppartner/listshop",
    CREATE_SHOP: "/api/partner/shoppartner/createshop",
    UPDATE_LOGO_SHOP: "/api/partner/shoppartner/updatelogoshop",
    UPDATE_SHOP: "/api/partner/shoppartner/updateshop",
    DETAIL_SHOP: "/api/partner/shoppartner/detailshop",
    DELETE_SHOP: "/api/partner/shoppartner/deleteshop",
    UPDATE_SHOP_DELIVERY: "/api/partner/shoppartner/updateshopdelivery",
    UPDATE_SHOP_TAX_RATE: "/api/partner/shoppartner/taxrate",
  },
  ADVERTISE: {
    GET_ADVERTISE: "/api/partner/advertisepartner/listadvertise",
    CREATE_ADVERTISE: "/api/partner/advertisepartner/createadvertise",
    GET_ADVERTISE_DETAIL: "/api/partner/advertisepartner/detailadvertise",
    UPDATE_IMAGE_ADVERTISE: "/api/partner/advertisepartner/updateimageadvertise",
    UPDATE_ADVERTISE: "/api/partner/advertisepartner/updateadvertise",
    DELETE_ADVERTISE: "/api/partner/advertisepartner/deleteadvertise",
  },
  ARTICLE_CATEGORY: {
    GET_ARTICLE_CATEGORY: "/api/partner/articlepartner/listarticlecategory",
    CREATE_ARTICLE_CATEGORY: "/api/partner/articlepartner/createarticlecategory",
    GET_ARTICLE_CATEGORY_DETAIL: "/api/partner/articlepartner/articlecategory",
    UPDATE_IMAGE_ARTICLE_CATEGORY: "/api/partner/articlepartner/updateimagearticlecategory",
    UPDATE_ARTICLE_CATEGORY: "/api/partner/articlepartner/updatearticlecategory",
    DELETE_ARTICLE_CATEGORY: "/api/partner/articlepartner/deletearticlecategory",
  },
  ARTICLE: {
    GET_ARTICLE: "/api/partner/articlepartner/listarticle",
    CREATE_ARTICLE: "/api/partner/articlepartner/createarticle",
    GET_ARTICLE_DETAIL: "/api/partner/articlepartner/detailarticle",
    UPDATE_IMAGE_ARTICLE: "/api/partner/articlepartner/updateimagearticle",
    UPDATE_ARTICLE: "/api/partner/articlepartner/updatearticle",
    DELETE_ARTICLE_CATEGORY: "/api/partner/articlepartner/deletearticle",
  },
  PRODUCT_CATEGORY: {
    GET_PRODUCT_CATEGORY: "/api/partner/categorypartner/listcategory",
    GET_PRODUCT_CATEGORY_TREE: "/api/partner/categorypartner/treecategory",
    CREATE_PRODUCT_CATEGORY: "/api/partner/categorypartner/createcategory",
    GET_PRODUCT_CATEGORY_DETAIL: "/api/partner/categorypartner/detailcategory",
    UPDATE_IMAGE_PRODUCT_CATEGORY: "/api/partner/categorypartner/updateiconcategory",
    UPDATE_PRODUCT_CATEGORY: "/api/partner/categorypartner/updatecategory",
    DELETE_PRODUCT_CATEGORY: "/api/partner/categorypartner/deletecategory",
    EXPORT_TEMPLATE_PRODUCT_CATEGORY: "/api/partner/categorypartner/exporttemplate",
    IMPORT_PRODUCT_CATEGORY: "/api/partner/categorypartner/importcategory",
    EXPORT_LIST_PRODUCT_CATEGORY: "/api/partner/categorypartner/exportlistcategory",
  },
  PRODUCT: {
    GET_PRODUCT: "/api/partner/itemspartner/listitems",
    CREATE_PRODUCT: "/api/partner/itemspartner/createproduct",
    GET_PRODUCT_DETAIL: "/api/partner/itemspartner/detailgroupitems",
    UPDATE_IMAGE_PRODUCT: "/api/partner/itemspartner/updateproduct",
    UPDATE_PRODUCT: "/api/partner/itemspartner/updateproduct",
    DELETE_PRODUCT: "/api/partner/itemspartner/deleteproduct",
    LIST_ITEMS_BY_ITEMS_CODE: "/api/partner/itemspartner/listitemsbyitemscode",
    LIST_ITEMS_BY_ITEM_ID: "/api/partner/itemspartner/listitemsbyitemsid",
    EXPORT_PRODUCT_TEMPLATE: "/api/partner/itemspartner/exportproducttemplate",
    IMPORT_PRODUCT: "/api/partner/itemspartner/import",
    EXPORT_LIST_PRODUCT: "/api/partner/itemspartner/exportitems",
    DELETE_LIST_ITEM: "/api/partner/itemspartner/deleteitems",
  },
  SERVICE: {
    GET_SERVICE: "/api/partner/itemspartner/listitems",
    CREATE_SERVICE: "/api/partner/itemspartner/createservice",
    GET_SERVICE_DETAIL: "/api/partner/itemspartner/detailgroupitems",
    UPDATE_IMAGE_SERVICE: "/api/partner/itemspartner/updateservice",
    UPDATE_SERVICE: "/api/partner/itemspartner/updateservice",
    DELETE_SERVICE: "/api/partner/itemspartner/deleteservice",
  },
  FILE_MANAGE: {
    GET_GROUP_FILE: "/api/partner/filemanage/listgroup",
    CREATE_GROUP_FILE: "/api/partner/filemanage/creategroup",
    UPDATE_GROUP_FILE: "/api/partner/filemanage/updategroup",
    DELETE_GROUP_FILE: "/api/partner/filemanage/deletegroup",
    GET_FILE_GROUP: "/api/partner/filemanage/listfile",
    CREATE_FILE_GROUP: "/api/partner/filemanage/createfile",
    DELETE_FILE_GROUP: "/api/partner/filemanage/deletefile",
  },
  WAREHOUSE: {
    GET_WAREHOUSE: "/api/partner/warehousepartner/listwarehouse",
  },
  USER: {
    CREATE: "/api/partner/userpartner/createuser",
    DETAIL: "/api/partner/userpartner/userdetail",
    UPDATE_USER: "/api/partner/userpartner/updateuser",
    DELETE_USERS: "/api/partner/userpartner/deleteusers",
    LIST_TAG: "/api/partner/userpartner/listtag",
    LIST_USER: "/api/partner/userpartner/listuser",
    LIST_ADDRESS: "/api/partner/shippingaddresspartner/listaddress",
    DELETE_USER_ADDRESS: "/api/partner/shippingaddresspartner/delete",
    CREATE_USER_ADDRESS: "/api/partner/shippingaddresspartner/create",
    UPDATE_USER_ADDRESS: "/api/partner/shippingaddresspartner/update",
    LIST_USER_BY_USER_IDS: "/api/partner/userpartner/listuserbyuserids",
    UPDATE_USER_INFO: "/api/partner/userpartner/updateuserinfo",
    EXPORT_USER_TEMPLATE: "/api/partner/userpartner/exportusertemplate",
    IMPORT_LIST_USER: "/api/partner/userpartner/importusers",
    EXPORT_LIST_USER: "/api/partner/userpartner/exportuser",
  },
  DOMAIN_NAME: {
    CREATE_DOMAIN_NAME: "/api/partner/domainnamepartner/createdomainname",
    UPDATE_DOMAIN_NAME: "/api/partner/domainnamepartner/updatedomainname",
    LIST_DOMAIN_NAME: "/api/partner/domainnamepartner/listshopdomain",
    DELETE_DOMAIN_NAME: "/api/partner/domainnamepartner/deletedomainnames",
    CHECK_DOMAIN_NAME_EXIST: "/api/partner/domainnamepartner/checkdomainnameexist",
  },
  CART: {
    SEARCH_ITEMS: "/api/partner/cartpartner/searchitems",
    SEARCH_USERS: "/api/partner/cartpartner/searchuser",
    CREATE_OR_UPDATE_CART: "/api/partner/cartpartner/createorupdatecart",
    GET_CART: "/api/partner/cartpartner/getcart",
    LIST_CART: "/api/partner/cartpartner/listcart",
    DELETE_CART: "/api/partner/cartpartner/deletecart",
    ESTIMATE_POINT_CART: "/api/partner/cartpartner/estimatepointcart",
  },
  ORDER: {
    CREATE_ORDER: "/api/partner/orderpartner/createorder",
    LIST_ORDER: "/api/partner/orderpartner/listorder",
    GET_ORDER: "/api/partner/orderpartner/orderdetail",
    UPDATE_ORDER_INFO: "/api/partner/orderpartner/updateorderinfo",
    LIST_ORDER_BY_USER_ID: "/api/partner/orderpartner/listorderbyuserid",
    PRINT_TRANSPORT_ORDERS: "/api/partner/orderpartner/printtransportorders",
    EXPORT_ORDER_EXCEL: "/api/partner/orderpartner/ExportOrderExcel",
  },
  VOUCHER: {
    VOUCHER: "/api/partner/voucherpartner",
    VOUCHER_DETAIL: "/api/partner/voucherpartner/details",
    EXPORT_PDF: "/api/partner/voucherpartner/exportpdf",
    LIST_ACTIVE_VOUCHER: "/api/partner/voucherpartner/listactivevoucher",
    CHANGE_ACTIVE_VOUCHER: "/api/partner/voucherpartner/changeactivevoucher",
    LIST_ITEMS_BY_ITEMS_ID: "/api/partner/itemspartner/listitemsbyitemsid",
  },
  SHOP_MINI_APP_PARTNER: {
    CREATE_SHOP_MINI_APP: "/api/partner/shopminiapppartner/updateshopminiapp",
    GET_SHOP_MINI_APP: "/api/partner/shopminiapppartner/currentshopminiapp",
  },
  BRANCH: {
    GET_BRANCHS: "/api/partner/branch",
    CREATE_BRANCH: "/api/partner/branch",
    GET_BRANCH_DETAIL: "/api/partner/branch",
    UPDATE_BRANCH: "/api/partner/branch",
    DELETE_BRANCH: "/api/partner/branch",
  },
  PROVINCE: {
    GET_PROVINCE: "/api/user/province/provinces",
    GET_DISTRICT: "/api/user/province/districts",
    GET_WARD: "/api/user/province/wards",
  },
  DASHBOARD: {
    GET_DASHBOARD_PARTNER: "/api/partner/dashboardpartner",
    GET_DASHBOARD_PARTNER_V2: "/api/partner/dashboardpartner/dashboardv2",
  },
  SHOP_POLICY: {
    GET_SHOP_POLICY: "/api/partner/shoppartner/listshoppolicy",
    CREATE_SHOP_POLICY: "/api/partner/shoppartner/upsertshoppolicy",
  },
  ITEM_OPTION_GROUP: {
    URL_ITEM_OPTION_GROUP: "/api/partner/itemoptiongroup",
  },
  ITEM_OPTION: {
    LIST_ITEM_OPTION_BY_GROUP_ID:
      "/api/partner/itemoption/getitemoptionsbygroup/{itemOptionGroupId}",
    ITEM_OPTION_USER: "/api/user/itemoptionuser",
    ITEM_OPTION_BY_IDS: "/api/user/itemoptionuser/getitemoptionbyids",
    URL_ITEM_OPTION: "/api/partner/itemoption",
  },
  SHOP_SETTING: {
    URL_SHOP_SETTING: "/api/partner/shopsetting",
    BUILD_MINIAPP: "/api/partner/shopsetting/build-miniapp",
  },
  PAYMENT_PARTNER: {
    URL_PAYMENT_PARTNER: "/api/partner/paymentpartner",
  },
  MEMBERSHIP_LEVEL: {
    GetListMembershipLevel: "/api/partner/membershiplevel",
    CreateMembershipLevel: "/api/partner/membershiplevel",
    UpdateMembershipLevel: "/api/partner/membershiplevel",
    DeleteMembershipLevel: "/api/partner/membershiplevel",
    SummaryPoints_MembershipLevel: "/api/partner/membershiplevel/summary",
    GetUserPoints: "/api/partner/membershiplevel/userpoints",
    GetUserPoint: "/api/partner/membershiplevel/userpoint",
    UpdateUserPoints: "/api/partner/membershiplevel/points",
    GetExchangeHistory: "/api/partner/membershiplevel/exchangehistory",
    GetSpendingHistory: "/api/partner/membershiplevel/spendinghistory",
    GetIncomeHistory: "/api/partner/membershiplevel/incomehistory",
    GetConfig: "/api/partner/membershiplevel/config",
    UpdateConfig: "/api/partner/membershiplevel/config",
    UPDATE_IMAGE_MEMBERSHIP_LEVEL: "/api/partner/membershiplevel/updateimagemembershiplevel",
  },
  ZALO_TEMPLATE: {
    LIST_TEMPLATE: "/api/partner/zalo_template/listTemplates",
    GET_TEMPLATE_BY_TEMPLATE_ID: "/api/partner/zalo_template/info",
    ZALO_TRIGGER: "/api/partner/zalo_trigger/trigger-events",
    ZALO_TRIGGER_PARAMETER: "/api/partner/zalo_trigger/get-list-parameter-trigger-event",
    LIST_SEND_TYPE: "/api/partner/zalo_sendType/listSendTypes",
    LIST_BUTTON_ACTON: "/api/partner/zalo_button/button-actions",
    LIST_MESSAGE_TYPE_BY_TEMPLATE_TYPE: "/api/partner/zalo_messageType/messageTypes/byTemplateType",
    CREATE_TEMPLATE: "/api/partner/zalo_template",
    UPDATE_TEMPLATE: "/api/partner/zalo_template",
    ZALO_TEMPLATE: "/api/partner/zalo_template/templates",
    GET_OAUTH_URL: "/api/partner/zalo/getoauthurl",
  },
  BANK: {
    LIST_BANK: "/api/partner/bank",
    LIST_BANK_PARTNER: "/api/partner/bank/listBankPartner",
  },
  AFFILIATION: {
    REPORT_OVERVIEW: "/api/affiliation/dashboard/reportoverview",
    DASHBOARD_STATISTIC: "/api/affiliation/dashboard/statistic",
    GET_COMMISSIONS_CONFIG: "/api/affiliation/commissionsconfig",
    UPDATE_COMMISSIONS_CONFIG: "/api/affiliation/commissionsconfig",
    UPDATE_LEVEL_TWO_STATUS: "/api/affiliation/commissionsconfig/basic/level2status",
    UPDATE_ACTIVE_STATUS: "/api/affiliation/commissionsconfig/activestatus",
    PARTNER_OVERVIEW: "/api/affiliation/dashboard/partneroverview",
    PARTNER_OVERVIEW_EXPORT_EXCEL: "/api/affiliation/dashboard/partneroverview/excelfile",
    UPDATE_RECRUITMENT_PAGE: "/api/affiliation/recruitmentpage",
    GET_RECRUITMENT_PAGE: "/api/affiliation/recruitmentpage",

    // affiliationpartner
    GET_LIST_AFFILIATION_PARTNER: "/api/affiliation/affiliationpartner/partners",
    GET_DETAIL_AFFILIATION_PARTNER: "/api/affiliation/affiliationpartner/partner",
    GET_REFERERS_AFFILIATION_PARTNER: "/api/affiliation/affiliationpartner/referers",
    UPDATE_AFFILIATION_PARTNER: "/api/affiliation/affiliationpartner/updatepartner",
    APPROVE_AFFILIATION_PARTNER: "/api/affiliation/affiliationpartner/approval",
    GET_QUICK_REPORT_AFFILIATION_PARTNER:
      "/api/affiliation/affiliationpartner/partners/:id/quickreport",
    EXPORT_EXCEL_LIST_AFFILIATION_PARTNER: "/api/affiliation/affiliationpartner/excelfile",

    // affiliationcommissionsOrder
    GET_COMMISSTION_ORDER: "/api/affiliation/commissionsorder",
    EXPORT_EXCEL_COMMISSTION_ORDER: "/api/affiliation/commissionsorder/excelfile",

    // affiliationcommissiosReport
    GET_COMMISSTION_REPORT: "/api/affiliation/commissionsreport/commissions",
    EXPORT_EXCEL_COMMISSTION_REPORT: "/api/affiliation/commissionsreport/excelfile",
    GET_OAUTH_URL: "/api/partner/zalo/getoauthurl",
  },
  ZALO_AUTOMATION: {
    MESSAGE_REPORT: "/api/partner/zalo/sentmessagereport",
    EXPORT_MESSAGE_REPORT: "/api/partner/zalo/exportsentmessagereport",
    API_ZALO_TEMPLATE_ZNS: "/api/partner/zalozns",
  },
  USER_CATEGORY: {
    GET_USER_CATEGORY: "/api/user/categoryuser/listcategory",
  },
  ROLE: {
    PARTNER_ROLE_API: "/api/partner/partnerrole",
  },
  PARTNER_EMPLOYEE: {
    PARTNER_EMPLOYEE_API: "/api/partner/partneremployee",
  },
  PARTNER_FUNCTION: {
    PURCHASE_PACKAGE: "/api/partner/partnerfunction/purchase-package",
    UPGRADE_PACKAGE: "/api/partner/partnerfunction/upgrade-package",
    ACTIVE_PACKAGE_FUNCTIONS: "/api/partner/partnerfunction/active-package-functions",
    AVAILABLE_PACKAGES: "/api/partner/partnerfunction/available-packages",
    CHECK_PERMISSION: "/api/partner/partnerfunction/check-permission",
    PACKAGE_HISTORY: "/api/partner/partnerfunction/package-history",
    ALL_PERMISSIONS: "/api/partner/partnerfunction/get-all-permissions",
  },
  USER_GROUP: {
    GET_LIST_USER_GROUP_BY_SHOP_ID: "/api/partner/usergroup/getlist",
    API_USER_GROUP: "/api/partner/usergroup",
    GET_LIST_FILTER_USER_GROUP: "/api/partner/usergroup/listfilter",
    FILTER_LIST_USER: "/api/partner/usergroup/filter",
    EXPORT_TEMPLATE_EXCEL_USER: "/api/partner/usergroup/export/template",
    IMPORT_EXCEL_LIST_USER: "/api/partner/usergroup/import",
    EXPORT_LIST_USER: "/api/partner/usergroup/export/user",
  },
  TRIGGER_EVENT: {
    API_TRIGGER_EVENT: "/api/partner/triggerevent",
  },
  TRIGGER_EVENT_HISTORY: {
    API_TRIGGER_EVENT_HISTORY: "api/partner/triggereventhistory",
  },
  TRANSPORT_METHOD: {
    LIST_TRANSPORT_METHOD: "/api/partner/transportmethodpartner/transport-methods",
    UPDATE_TRANSPORT_METHOD: "/api/partner/transportmethodpartner/update-transport-method",
    SIGN_UP_AHAMOVE: "/api/partner/transportmethodpartner/signupahamove",
  },
  SEPAY: {
    GET_ACCOUNT_HOLDER_NAME: "api/sepay/{bankShortCode}/lookupAccountHolderName",
    CREATE_BANK_ACCOUNT_ASYNC: "api/sepay/{bankShortCode}/create",
    CONFIRM_BANK_ACCOUNT_ASYNC: "api/sepay/{bankShortCode}/confirmApiConnection",
    RESENT_OTP: "api/sepay/{bankShortCode}/requestApiConnection",
    REQUEST_DELETE_BANK_ACCOUNT: "api/sepay/{bankShortCode}/requestDelete",
    CONFIRM_DELETE_BANK_ACCOUNT: "api/sepay/{bankShortCode}/confirmDelete",
    GET_BANK_ACCOUNT: "api/sepay/{bankShortCode}/bankAccount",
    FORCE_DELETE_BANK_ACCOUNT: "api/sepay/{bankShortCode}/forceDelete",
  },
  INVOICE: {
    GET_INVOICE: "/api/partner/invoiceconfig",
    API_INVOICE: "/api/partner/invoice",
  },
  CAMPAIGN: {
    API_CAMPAIGN_AUTOMATION: "api/partner/campaign",
  },
  GAMIFICATION: {
    GAME_BRAND: "/api/partner/gamebrand",
    CAMPAIGN: "/api/partner/gamecampaign",
    PRIZE: "/api/partner/gameprize",
    GAME: "/api/partner/game",
  },
  BALANCE_LOG: {
    GET_LIST_BALANCE_LOG: "/api/partner/balance_log",
  },
  TAX_INVOICE: {
    GET_LIST_CONFIG_TAX_INVOICE: "/api/partner/taxinvoice",
    CREATE_CONFIG_TAX_INVOICE: "/api/partner/taxinvoice",
    UPDATE_CONFIG_TAX_INVOICE: "/api/partner/taxinvoice",
    DELETE_CONFIG_TAX_INVOICE: "/api/partner/taxinvoice",
    SET_DEFAULT_CONFIG_TAX_INVOICE: "/api/partner/taxinvoice/default",
    GET_INFOR_BUSINESS_CONFIG_TAX_INVOICE: "/api/partner/taxinvoice/business/info",
    GET_INFOR_INDIVIDUAL_CONFIG_TAX_INVOICE: "/api/partner/taxinvoice/individual/info",
  },
  PRICE_LIST: {
    PRICE_LIST_API: "/api/partner/pricelistpartner",
    DELETE_MANY_PRICE_LIST: "/api/partner/pricelistpartner/deletemany",
    UPDATE_PRODUCTS_FOR_PRICE_LIST: "/api/partner/pricelistpartner/items",
    UPDATE_BRANCHS_FOR_PRICE_LIST: "/api/partner/pricelistpartner/branches",
    UPDATE_RANKS_FOR_PRICE_LIST: "/api/partner/pricelistpartner/ranks",
    ACTIVE_STATUS_FOR_PRICE_LIST: "/api/partner/pricelistpartner/active",
    GET_LIST_PRODUCT_BY_PRICE_LIST_ID: "/api/partner/pricelistpartner/items/bypricelistid",
    GET_LIST_PRODUCT_NOT_IN_PRICE_LIST: "/api/partner/pricelistpartner/items/notinanypricelist",
    GET_LIST_PRODUCT_BY_TIERS: "/api/partner/pricelistpartner/items/tiers",
  },
  SYNC_SERVICE: {
    CONNFIG: "/api/partner/syncservice",
  },
  ZALO_MINI_APP: {
    GET_LIST_VERSION: "/api/partner/zalominiapp/versions",
  },
} as const;
