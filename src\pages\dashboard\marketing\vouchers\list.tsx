import { useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>ack,
  Tab,
  Tabs,
  Tooltip,
  Typography,
  Chip,
  Fade,
  Skeleton,
  Alert,
  CircularProgress,
  Container,
  Paper,
  Divider,
} from "@mui/material";
import { useRouter } from "next/router";
import { paths } from "@/src/paths";
import DashboardLayout from "@/src/layouts/dashboard";
import { useVoucher } from "@/src/api/hooks/voucher/use-voucher";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import {
  CODE_TYPE,
  VOUCHER_STATUS,
  VOUCHER_STATUS_LABLE,
  VOUCHER_TAB,
  VOUCHER_TYPE,
} from "@/src/api/types/voucher.type";
import StoreIcon from "@mui/icons-material/Store";
import LocalShippingIcon from "@mui/icons-material/LocalShipping";
import QrCodeIcon from "@mui/icons-material/QrCode";
import { Add, DeleteOutline, Refresh } from "@mui/icons-material";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import SingleCodeVoucherList from "./single-code/list";
import VoucherList from "./voucher_promotions/list";
import CustomVoucherList from "./custom/list";
import { Padding } from "@/src/styles/CommonStyle";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { usePathname } from "next/navigation";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import TitleDialog from "@/src/components/dialog/TitleDialog";
import NavigateNextOutlinedIcon from "@mui/icons-material/NavigateNextOutlined";
import ActionButton from "@/src/components/common/ActionButton";
import TitleTypography from "@/src/components/title-typography/title-typography";
import {
  FadeTransition,
  SlideUpTransition,
  TabTransition,
  StaggeredList,
  HoverLift,
} from "@/src/components/vouchers/VoucherAnimations";
import { useLazyTabs } from "@/src/hooks/use-lazy-tabs";

// Tab values as constants for better maintainability
const TAB_INDEX_TO_NAME = {
  0: VOUCHER_TAB.VOUCHER,
  1: VOUCHER_TAB.UNIQUE_VOUCHER,
  2: VOUCHER_TAB.CUSTOM_VOUCHER,
};

const TAB_NAME_TO_INDEX = {
  [VOUCHER_TAB.VOUCHER]: 0,
  [VOUCHER_TAB.UNIQUE_VOUCHER]: 1,
  [VOUCHER_TAB.CUSTOM_VOUCHER]: 2,
};

export default function ListVoucher() {
  const pathname = usePathname();
  const [tabIndex, setTabIndex] = useState(0);
  const router = useRouter();
  const { tab: tabParam, searchTab } = router.query;
  const { deleteVoucher, loading } = useVoucher();
  const snackbar = useSnackbar();

  const initialTabIndex = tabParam !== undefined ? TAB_NAME_TO_INDEX[tabParam as string] || 0 : 0;

  const {
    currentTab: voucherTabIndex,
    changeTab: setVoucherTabIndex,
    shouldRenderTab,
    markTabLoaded,
  } = useLazyTabs(3, {
    initialTab: initialTabIndex,
    preloadNext: true,
    cacheSize: 2,
  });

  const [searchTabIndex, setSearchTabIndex] = useState(() => {
    return searchTab ? (searchTab as string) : "All";
  });

  const [openDialog, setOpenDialog] = useState(false);
  const [selected, setSelected] = useState<string[]>([]);
  const [isDeleteMany, setIsDeleteMany] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [isPageLoading, setIsPageLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleTabChange = (event, newValue) => {
    setTabIndex(newValue);
  };

  const handleVoucherTabChange = (event, newValue) => {
    setVoucherTabIndex(newValue);
    setSelected([]);
    const tabName = TAB_INDEX_TO_NAME[newValue] || VOUCHER_TAB.VOUCHER;
    router.push(
      {
        pathname: router.pathname,
        query: { ...router.query, tab: tabName },
      },
      undefined,
      { shallow: true }
    );
  };

  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  const handleAddNew = () => {
    setOpenDialog(true);
  };

  const handleSearchTabChange = (event, newTabIndex) => {
    setSearchTabIndex(newTabIndex);
    router.push(
      {
        pathname: router.pathname,
        query: { ...router.query, searchTab: newTabIndex },
      },
      undefined,
      { shallow: true }
    );
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleDeleteMany = () => {
    if (selected.length > 0) {
      setIsDeleteMany(true);
    }
  };

  const handleCloseDeleteMany = () => {
    setIsDeleteMany(false);
  };

  const handleConfirmDeleteMany = async () => {
    if (selected.length === 0) return;

    const response = await deleteVoucher(selected);
    if (response?.data?.result) {
      snackbar.success(`Xóa ${selected.length} voucher thành công`);
      setSelected([]);
      setIsDeleteMany(false);
      setRefreshKey((prev) => prev + 1);
    } else {
      snackbar.error(response?.data?.message || "Xóa voucher thất bại");
    }
  };

  useEffect(() => {
    if (tabParam !== undefined) {
      const newTabIndex = TAB_NAME_TO_INDEX[tabParam as string] || 0;
      setVoucherTabIndex(newTabIndex);
    }

    if (searchTab !== undefined) {
      setSearchTabIndex(searchTab as string);
    }
  }, [tabParam, searchTab]);

  return (
    <DashboardLayout>
      <Box sx={{ p: Padding, paddingTop: "20px" }}>
        {/* Header Section */}
        <Paper
          elevation={0}
          sx={{
            p: 3,
            mb: 3,
            color: "white",
            borderRadius: 1,
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
            flexDirection={{
              xs: "column",
              sm: "row",
            }}
          >
            <Box sx={{ color: "black" }}>
              <Typography
                variant="h4"
                sx={{
                  fontWeight: "700",
                  mb: 1,
                  fontSize: { xs: "1.5rem", sm: "2rem" },
                }}
              >
                Quản lý khuyến mãi
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  opacity: 0.9,
                  fontSize: "1rem",
                }}
              >
                Tạo và quản lý các chương trình khuyến mãi cho cửa hàng
              </Typography>
            </Box>
          </Box>
        </Paper>

        {/* Main Content Card */}
        <Card
          elevation={2}
          sx={{
            borderRadius: 1,
            overflow: "hidden",
            border: "1px solid",
            borderColor: "divider",
            boxShadow: "none",
          }}
        >
          {/* Tab Navigation */}
          <Box sx={{ borderBottom: 1, borderColor: "divider", bgcolor: "grey.50" }}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                p: 2,
                flexDirection: { xs: "column", sm: "row" },
                gap: { xs: 2, sm: 0 },
              }}
            >
              <Tabs
                value={voucherTabIndex}
                onChange={handleVoucherTabChange}
                sx={{
                  "& .MuiTabs-indicator": {
                    backgroundColor: "#2654FE",
                    height: 3,
                    borderRadius: "3px 3px 0 0",
                  },
                  order: { xs: 2, sm: 1 },
                  "& .MuiTab-root": {
                    fontWeight: 600,
                    fontSize: "0.95rem",
                  },
                }}
              >
                <Tab
                  label="Voucher khuyến mãi"
                  sx={{
                    textTransform: "none",
                    minWidth: 140,
                    "&.Mui-selected": {
                      color: "#2654FE",
                    },
                  }}
                />
                <Tab
                  label="Voucher một mã"
                  sx={{
                    textTransform: "none",
                    minWidth: 140,
                    "&.Mui-selected": {
                      color: "#2654FE",
                    },
                  }}
                />
                <Tab
                  label="Voucher tuỳ chỉnh"
                  sx={{
                    textTransform: "none",
                    minWidth: 140,
                    "&.Mui-selected": {
                      color: "#2654FE",
                    },
                  }}
                />
              </Tabs>

              <Box
                sx={{
                  display: "flex",
                  gap: 2,
                  order: { xs: 1, sm: 2 },
                  width: { xs: "100%", sm: "auto" },
                  justifyContent: { xs: "space-between", sm: "flex-end" },
                }}
              >
                {selected.length > 0 && (
                  <ActionButton
                    permission={PERMISSION_TYPE_ENUM.Delete}
                    tooltip="Bạn không có quyền xoá"
                    startIcon={<DeleteOutline />}
                    onClick={handleDeleteMany}
                    isGranted={isGranted}
                    pathname={pathname}
                    size="medium"
                    color="error"
                    variant="outlined"
                    customSx={{
                      height: "44px",
                      minWidth: { xs: "calc(50% - 8px)", sm: "140px" },
                      borderRadius: 2,
                      fontWeight: 600,
                      boxShadow: "none",
                      "&:hover": {
                        boxShadow: "0 4px 12px rgba(244, 67, 54, 0.2)",
                      },
                    }}
                  >
                    Xoá ({selected.length})
                  </ActionButton>
                )}

                <ActionButton
                  permission={PERMISSION_TYPE_ENUM.Add}
                  tooltip="Bạn không có quyền thêm mới"
                  variant="contained"
                  startIcon={<Add />}
                  onClick={handleAddNew}
                  isGranted={isGranted}
                  pathname={pathname}
                  size="medium"
                  color="primary"
                  customSx={{
                    height: "44px",
                    minWidth: {
                      xs: selected.length > 0 ? "calc(50% - 8px)" : "140px",
                      sm: "140px",
                    },
                    fontSize: "0.95rem",
                    fontWeight: 600,
                    borderRadius: 1,
                    background: "linear-gradient(45deg, #2654FE 30%, #4285F4 90%)",
                    boxShadow: "0 4px 12px rgba(38, 84, 254, 0.3)",
                    "&:hover": {
                      background: "linear-gradient(45deg, #1e3fcc 30%, #3367d6 90%)",
                      boxShadow: "0 6px 16px rgba(38, 84, 254, 0.4)",
                    },
                  }}
                >
                  {selected.length > 0 ? "Thêm mới" : "Tạo voucher"}
                </ActionButton>
              </Box>
            </Box>
          </Box>

          {/* Content Area */}
          <Box sx={{ p: 3 }}>
            {shouldRenderTab(0) && (
              <TabTransition value={voucherTabIndex} index={0}>
                <VoucherList
                  voucherTypes={[VOUCHER_TYPE.PROMOTION, VOUCHER_TYPE.TRANSPORT]}
                  codeType={CODE_TYPE.COMMON}
                  selected={selected}
                  setSelected={setSelected}
                  refreshKey={refreshKey}
                  onAddNew={handleAddNew}
                />
              </TabTransition>
            )}

            {shouldRenderTab(1) && (
              <TabTransition value={voucherTabIndex} index={1}>
                <SingleCodeVoucherList
                  voucherTypes={[
                    VOUCHER_TYPE.PROMOTION,
                    VOUCHER_TYPE.TRANSPORT,
                    VOUCHER_TYPE.CUSTOM,
                  ]}
                  codeType={CODE_TYPE.UNIQUE}
                  selected={selected}
                  setSelected={setSelected}
                  refreshKey={refreshKey}
                  onAddNew={handleAddNew}
                />
              </TabTransition>
            )}

            {shouldRenderTab(2) && (
              <TabTransition value={voucherTabIndex} index={2}>
                <CustomVoucherList
                  voucherTypes={[VOUCHER_TYPE.CUSTOM]}
                  codeType={CODE_TYPE.COMMON}
                  selected={selected}
                  setSelected={setSelected}
                  refreshKey={refreshKey}
                  onAddNew={handleAddNew}
                />
              </TabTransition>
            )}
          </Box>
        </Card>

        {/* Dialogs */}
        <TitleDialog
          showActionDialog={false}
          title="Tạo voucher mới"
          open={openDialog}
          handleClose={handleCloseDialog}
          color="primary"
        >
          <BoxNewVoucherDialog />
        </TitleDialog>

        <TitleDialog
          title="Xóa voucher"
          open={isDeleteMany}
          handleClose={handleCloseDeleteMany}
          handleSubmit={handleConfirmDeleteMany}
          submitBtnTitle="Xác nhận"
          color="error"
        >
          <Box>
            <Typography>
              Bạn có chắc muốn xóa {selected.length > 0 && `${selected.length}`} voucher này?
            </Typography>
          </Box>
        </TitleDialog>
      </Box>
    </DashboardLayout>
  );
}

const BoxNewVoucherDialog = () => {
  return (
    <Box sx={{ p: 1 }}>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3, textAlign: "center" }}>
        Chọn loại voucher bạn muốn tạo
      </Typography>

      <StaggeredList staggerDelay={150} animation="fade">
        <BoxNewVoucherDialogItem
          url={paths.marketing.vouchers.newPromotion}
          icon={<StoreIcon sx={{ fontSize: 28 }} />}
          title="Voucher Giảm giá"
          description="Tạo voucher giảm giá trực tiếp cho đơn hàng với điều kiện tối thiểu"
          color="#4CAF50"
          bgColor="#E8F5E9"
        />
        <BoxNewVoucherDialogItem
          url={paths.marketing.vouchers.newTransport}
          icon={<LocalShippingIcon sx={{ fontSize: 28 }} />}
          title="Voucher Vận chuyển"
          description="Tạo voucher giảm giá hoặc miễn phí vận chuyển cho khách hàng"
          color="#FF9800"
          bgColor="#FFF3E0"
        />
        <BoxNewVoucherDialogItem
          url={paths.marketing.vouchers.newCustomVoucher}
          icon={<QrCodeIcon sx={{ fontSize: 28 }} />}
          title="Voucher Tùy chỉnh"
          description="Tạo voucher tuỳ chỉnh cho sự kiện, khuyến mãi đặc biệt"
          color="#9C27B0"
          bgColor="#F3E5F5"
        />
      </StaggeredList>
    </Box>
  );
};

const BoxNewVoucherDialogItem = ({
  icon,
  title,
  description,
  url,
  color = "#2654FE",
  bgColor = "#f7f8fa",
}) => {
  const router = useRouter();
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Paper
      elevation={isHovered ? 4 : 1}
      sx={{
        p: 3,
        marginBottom: 1,
        borderRadius: 3,
        cursor: "pointer",
        transition: "all 0.3s ease",
        border: "2px solid transparent",
        "&:hover": {
          transform: "translateY(-2px)",
        },
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={() => {
        router.push(url);
      }}
    >
      <Box display="flex" alignItems="center" gap={3}>
        <Box
          sx={{
            background: bgColor,
            color: color,
            padding: 2,
            borderRadius: 2,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            minWidth: 64,
            minHeight: 64,
          }}
        >
          {icon}
        </Box>

        <Box flex={1}>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              mb: 1,
              color: "text.primary",
            }}
          >
            {title}
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.5 }}>
            {description}
          </Typography>
        </Box>

        <NavigateNextOutlinedIcon
          sx={{
            color: "text.secondary",
            transition: "transform 0.2s ease",
            transform: isHovered ? "translateX(4px)" : "translateX(0)",
          }}
        />
      </Box>
    </Paper>
  );
};

interface StatusBadgeProps {
  status: string;
  size?: "small" | "medium";
  variant?: "filled" | "outlined";
}

export const StatusBadge = ({ status, size = "small", variant = "filled" }: StatusBadgeProps) => {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case VOUCHER_STATUS.ACTIVED:
        return {
          bgcolor: variant === "filled" ? "#E8F5E9" : "transparent",
          color: "#2E7D32",
          border: "1px solid #4CAF50",
          icon: "●",
        };
      case VOUCHER_STATUS.INACTIVED:
        return {
          bgcolor: variant === "filled" ? "#FFF3E0" : "transparent",
          color: "#E65100",
          border: "1px solid #FF9800",
          icon: "⏸",
        };
      case VOUCHER_STATUS.EXPIRED:
        return {
          bgcolor: variant === "filled" ? "#FFEBEE" : "transparent",
          color: "#C62828",
          border: "1px solid #F44336",
          icon: "⏰",
        };
      default:
        return {
          bgcolor: variant === "filled" ? "#E3F2FD" : "transparent",
          color: "#1976D2",
          border: "1px solid #2196F3",
          icon: "●",
        };
    }
  };

  const statusConfig = getStatusConfig(status);

  return (
    <Chip
      label={
        <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
          <Typography component="span" sx={{ fontSize: "0.7rem" }}>
            {statusConfig.icon}
          </Typography>
          {VOUCHER_STATUS_LABLE[status] || status}
        </Box>
      }
      size={size}
      variant={variant === "outlined" ? "outlined" : "filled"}
      sx={{
        ...statusConfig,
        fontWeight: 600,
        fontSize: size === "small" ? "0.75rem" : "0.875rem",
        height: size === "small" ? "28px" : "32px",
        borderRadius: 2,
        "& .MuiChip-label": {
          px: size === "small" ? 1.5 : 2,
        },
        transition: "all 0.2s ease",
        "&:hover": {
          transform: "scale(1.05)",
          boxShadow: `0 2px 8px ${statusConfig.color}20`,
        },
      }}
    />
  );
};
