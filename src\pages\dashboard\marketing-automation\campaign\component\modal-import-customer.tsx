import { CloudUploadOutlined, Download } from "@mui/icons-material";
import {
  Box,
  Button,
  Dialog,
  FormControl,
  FormControlLabel,
  FormLabel,
  IconButton,
  MenuItem,
  Modal,
  Paper,
  Radio,
  RadioGroup,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import { DatePicker, DateTimePicker } from "@mui/x-date-pickers";
import React, { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { IParamsZns } from "./modal-create-campaign";
import { CampaignDto } from "@/src/api/services/campaign/campaign.service";
interface ModalImportCustomerProps {
  listParam: IParamsZns[];
  downloadTemplateFile: () => void;
  importListUserExcel: () => void;
  campaignCreated?: CampaignDto;
  isOpenModalUploadFile: boolean;
  setIsOpenModalUploadFile: (open: boolean) => void;
  file: File | null;
  setFile: (file: File | null) => void;
  isLoading: boolean;
}
const ModalImportCustomer: React.FC<ModalImportCustomerProps> = ({
  listParam,
  downloadTemplateFile,
  importListUserExcel,
  campaignCreated,
  isOpenModalUploadFile,
  setIsOpenModalUploadFile,
  file,
  setFile,
  isLoading,
}) => {
  // const [file, setFile] = useState(null);

  const onDrop = useCallback((acceptedFiles) => {
    if (acceptedFiles.length > 0) {
      setFile(acceptedFiles[0]);
    }
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [".xlsx"],
    },
    multiple: false,
  });
  const handleDownload = () => {
    const sampleFileUrl = "/files/sample.xlsx";
    const link = document.createElement("a");
    link.href = sampleFileUrl;
    link.setAttribute("download", "sample.xlsx");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  return (
    <Box sx={{ borderRadius: 2 }}>
      <Dialog
        open={isOpenModalUploadFile}
        onClose={() => setIsOpenModalUploadFile(false)}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            p: 1.5,
            maxHeight: "800px",
          },
        }}
      >
        <Box
          sx={{
            p: 1.5,
          }}
        >
          <Box sx={{ mb: 3 }}>
            <Typography sx={{ fontSize: 17, fontWeight: 500, mb: 1 }}>Danh sách tham số</Typography>
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 600 }}>Tên tham số</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>Loại dữ liệu</TableCell>

                    <TableCell align="center" sx={{ fontWeight: 600 }}>
                      Bắt buộc
                    </TableCell>
                    <TableCell align="center" sx={{ fontWeight: 600 }}>
                      Giá trị tối thiểu
                    </TableCell>
                    <TableCell align="center" sx={{ fontWeight: 600 }}>
                      Giá trị tối đa
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {Array.isArray(listParam) &&
                    listParam.length > 0 &&
                    listParam.map((param, index) => (
                      <TableRow key={index}>
                        <TableCell>{param.name}</TableCell>
                        <TableCell>{param.type}</TableCell>
                        <TableCell align="center">
                          {param.require ? (
                            <Box component="span" sx={{ color: "success.main" }}>
                              ✓
                            </Box>
                          ) : (
                            <Box component="span" sx={{ color: "error.main" }}>
                              ✗
                            </Box>
                          )}
                        </TableCell>
                        <TableCell align="center">{param.minLength}</TableCell>
                        <TableCell align="center">{param.maxLength}</TableCell>
                      </TableRow>
                    ))}
                  {listParam.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={5} align="center" sx={{ py: 2 }}>
                        Không có tham số nào
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>{" "}
          <Box>
            <Typography sx={{ fontSize: 17, fontWeight: 500 }}>Tải file mẫu</Typography>

            <Button
              variant="contained"
              color="secondary"
              startIcon={<Download />}
              onClick={downloadTemplateFile}
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                border: "1px solid #007bff",
                borderRadius: "8px",
                padding: "4px 8px",
                marginTop: "8px",
                color: "#007bff",
                background: "white",
              }}
            >
              Tải File Mẫu
            </Button>
          </Box>
          <Box sx={{ marginTop: 2 }}>
            <Typography sx={{ fontSize: 17, fontWeight: 500 }}>Tải lên danh sách</Typography>
            <Box
              {...getRootProps()}
              sx={{
                border: "2px dashed #ccc",
                borderRadius: "12px",
                padding: "24px",
                textAlign: "center",
                cursor: "pointer",
                maxWidth: "100%",
                margin: "auto",
                backgroundColor: "#fafafa",
                "&:hover": { backgroundColor: "#f1f1f1" },
              }}
            >
              <input {...getInputProps()} />
              <IconButton>
                <CloudUploadOutlined sx={{ fontSize: 40, color: "#007bff" }} />
              </IconButton>
              <Typography fontWeight="bold">
                Kéo và thả file vào đây, hoặc click để chọn file
              </Typography>
              <Typography fontSize={14} color="text.secondary">
                Định dạng tệp tải lên: <strong>XLSX</strong>
              </Typography>

              {file && (
                <Typography mt={2} color="green">
                  ✅ Đã chọn: {file.name}
                </Typography>
              )}
            </Box>
          </Box>
          <Box sx={{ display: "flex", justifyContent: "flex-end", marginTop: 2 }}>
            <Button
              variant="outlined"
              sx={{ marginRight: 2 }}
              onClick={() => setIsOpenModalUploadFile(false)}
            >
              Đóng
            </Button>
            <Button
              disabled={file === null ? true : isLoading ? true : false}
              variant="contained"
              onClick={() => importListUserExcel()}
            >
              Lưu
            </Button>
          </Box>
        </Box>
      </Dialog>
    </Box>
  );
};

export default ModalImportCustomer;
