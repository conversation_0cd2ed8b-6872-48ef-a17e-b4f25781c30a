import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  FormControl,
  MenuItem,
  Select,
  TextField,
  Typography,
  Paper,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { Controller, FormProvider, useForm, useFormContext } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { useTranslation } from "react-i18next";
import { provinces } from "@/src/utils/address/provinces";
import { districts } from "@/src/utils/address/districts";
import { wards } from "@/src/utils/address/wards";
import { VietnamFlag } from "@/src/pages/dashboard/settings/employee-role";
import * as Yup from "yup";
import { LocationOn, PermContactCalendar } from "@mui/icons-material";

type defaultValuesType = {
  email?: string;
  name?: string;
  phone?: string;
  shippingAddressPhone?: string;
};

const defaultValues: defaultValuesType = {
  email: "",
  name: "",
  phone: "",
  shippingAddressPhone: "",
};
const createBasicCustomerInfoSchema = (t: any) => {
  return Yup.object().shape({
    name: Yup.string()
      .required("Vui lòng nhập họ tên")
      .max(255, "Họ tên không được vượt quá 255 ký tự")
      .trim(),

    email: Yup.string()
      .email("Email không hợp lệ")
      .max(255, "Email không được vượt quá 255 ký tự")
      .trim()
      .nullable(),

    phone: Yup.string()
      .required("Vui lòng nhập số điện thoại")
      .matches(/^[0-9]+$/, "Số điện thoại chỉ được chứa số")
      .min(10, "Số điện thoại phải có ít nhất 10 số")
      .max(11, "Số điện thoại không được vượt quá 11 số")
      .trim(),

    shippingAddressAddress: Yup.string()
      .required("Vui lòng nhập địa chỉ chi tiết")
      .max(255, "Địa chỉ không được vượt quá 255 ký tự")
      .trim(),

    shippingAddressProvince: Yup.string().required("Vui lòng chọn tỉnh/thành phố"),

    shippingAddressDistrict: Yup.string().required("Vui lòng chọn quận/huyện"),

    shippingAddressWard: Yup.string().required("Vui lòng chọn phường/xã"),
  });
};

export default function CreateCustomerOrder({ handleSubmitForm, handleClose }) {
  const { t } = useTranslation();
  const validationSchema = createBasicCustomerInfoSchema(t);

  const methods = useForm({
    defaultValues,
    resolver: yupResolver(validationSchema),
  });

  const {
    control,
    handleSubmit,
    register,
    setValue,
    formState: { errors },
  } = methods;
  const onSubmit = async (data) => {
    try {
      await handleSubmitForm(data);
    } catch (error) {
      console.error("Form submission error:", error);
    }
  };
  return (
    <FormProvider {...methods}>
      <Box sx={{ minHeight: "500px" }}>
        <Paper
          elevation={0}
          sx={{
            p: 2,
            pb: 2,
            pt: 2,
            mb: 3,
            background: "white",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
            <PermContactCalendar sx={{ color: "rgb(201, 91, 91)", mr: 1, fontSize: 24 }} />
            <Typography variant="h6" fontWeight="600">
              Thông tin cơ bản
            </Typography>
          </Box>
          <OrderCustomerBasic />
        </Paper>

        <Paper
          elevation={0}
          sx={{
            p: 2,
            pb: 2,
            pt: 2,
            mb: 1.5,
            background: "white",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
            <LocationOn sx={{ fontSize: 24, marginRight: 1, color: "#000081" }} />
            <Typography variant="h6" fontWeight="600">
              Địa chỉ giao hàng
            </Typography>
          </Box>
          <OrderFormShippingAddress />
        </Paper>
        <Box
          sx={{
            p: 2,
            background: "white",
            display: "flex",
            gap: 2,
            justifyContent: "flex-end",
            borderTop: "1px solid #e0e0e0",
          }}
        >
          <Button
            variant="outlined"
            onClick={handleClose}
            size="large"
            sx={{
              minWidth: 120,
              borderRadius: 1,
              textTransform: "none",
              fontWeight: 500,
            }}
          >
            Hủy bỏ
          </Button>
          <Button
            onClick={methods.handleSubmit(onSubmit)}
            variant="contained"
            color="primary"
            size="large"
            sx={{
              minWidth: 120,
              borderRadius: 1,
              textTransform: "none",
              fontWeight: 500,
              background: "linear-gradient(135deg, #1976d2 0%, #1565c0 100%)",
              "&:hover": {
                background: "linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)",
                transform: "translateY(-1px)",
                boxShadow: "0 4px 12px rgba(25, 118, 210, 0.3)",
              },
              transition: "all 0.2s ease",
            }}
          >
            Lưu thông tin
          </Button>
        </Box>
      </Box>
    </FormProvider>
  );
}

const OrderCustomerBasic = () => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const fieldStyle = {
    "& .MuiOutlinedInput-root": {
      borderRadius: 2,
      "&:hover .MuiOutlinedInput-notchedOutline": {
        borderColor: "#1976d2",
      },
      height: "45px",
    },
  };

  return (
    <Grid container spacing={3}>
      <Grid size={{ xs: 12 }}>
        <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
          <Typography variant="subtitle1" fontWeight="500">
            Họ tên{" "}
            <Typography component="span" sx={{ color: "error.main" }}>
              *
            </Typography>
          </Typography>
        </Box>
        <Controller
          name="name"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              placeholder="Nhập họ và tên"
              error={!!errors.name}
              helperText={errors.name?.message as string}
              variant="outlined"
              sx={fieldStyle}
            />
          )}
        />
      </Grid>

      <Grid size={{ xs: 12, md: 6 }}>
        <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
          <Typography variant="subtitle1" fontWeight="500">
            Số điện thoại{" "}
            <Typography component="span" sx={{ color: "error.main" }}>
              *
            </Typography>
          </Typography>
        </Box>

        <Controller
          name="phone"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              placeholder="Nhập số điện thoại"
              error={!!errors.phone}
              helperText={errors.phone?.message as string}
              variant="outlined"
              sx={fieldStyle}
              InputProps={{
                startAdornment: (
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 0.5,
                      mr: 1,
                      borderRight: "1px solid #ddd",
                      pr: 1,
                    }}
                  >
                    <VietnamFlag />
                    <Typography
                      sx={{
                        color: "text.secondary",
                        fontSize: "0.875rem",
                        fontWeight: 500,
                      }}
                    >
                      +84
                    </Typography>
                  </Box>
                ),
              }}
            />
          )}
        />
      </Grid>
      <Grid size={{ xs: 12, md: 6 }}>
        <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
          <Typography variant="subtitle1" fontWeight="500">
            Email
          </Typography>
        </Box>
        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              placeholder="Nhập email"
              error={!!errors.email}
              helperText={errors.email?.message as string}
              variant="outlined"
              sx={fieldStyle}
            />
          )}
        />
      </Grid>
    </Grid>
  );
};

const OrderFormShippingAddress = ({ shippingAddress }: { shippingAddress?: any }) => {
  const [listDistrict, setListDistrict] = useState([]);
  const [listWard, setListWard] = useState([]);
  const {
    control,
    setValue,
    getValues,
    formState: { errors },
    watch,
  } = useFormContext();

  const selectedProvince = watch("shippingAddressProvince");
  const selectedDistrict = watch("shippingAddressDistrict");

  const handleDistrictChange = (e) => {
    const district = e.target.value;
    setValue("shippingAddressDistrict", district);
  };

  useEffect(() => {
    if (selectedProvince) {
      const data = districts.filter(
        (district) => district.provinceID === parseInt(selectedProvince)
      );
      setListDistrict(data);
      setValue("shippingAddressDistrict", "");
      setValue("shippingAddressWard", "");
      setListWard([]);
    }
  }, [selectedProvince, setValue]);

  useEffect(() => {
    if (selectedDistrict) {
      const dataWards = wards.filter((ward) => ward.districtID === selectedDistrict.toString());
      setListWard(dataWards);
      setValue("shippingAddressWard", "");
    }
  }, [selectedDistrict, setValue]);

  const fieldStyle = {
    borderRadius: 2,
    "& .MuiOutlinedInput-root": {
      "&:hover .MuiOutlinedInput-notchedOutline": {
        borderColor: "#2e7d32",
      },
      height: "45px",
    },
  };
  console.log({ errors });

  return (
    <Grid container spacing={3}>
      <Grid size={{ xs: 12, md: 4 }}>
        <Typography variant="subtitle1" fontWeight="500" sx={{ mb: 1 }}>
          Tỉnh/Thành phố{" "}
          <Typography component="span" sx={{ color: "error.main" }}>
            *
          </Typography>
        </Typography>
        <FormControl fullWidth>
          <Controller
            name="shippingAddressProvince"
            control={control}
            render={({ field }) => (
              <>
                <Select
                  {...field}
                  value={field.value || ""}
                  displayEmpty
                  sx={{
                    ...fieldStyle,
                    height: "45px",
                    "& .MuiOutlinedInput-root": {
                      height: "45px",
                    },
                  }}
                >
                  <MenuItem value="" disabled>
                    <em>Chọn tỉnh/thành phố</em>
                  </MenuItem>
                  {provinces?.map((p, index) => (
                    <MenuItem key={`province-${index}`} value={p.provinceID}>
                      {p.provinceName}
                    </MenuItem>
                  ))}
                </Select>
                {errors.shippingAddressProvince && (
                  <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                    {typeof errors?.shippingAddressProvince?.message === "string"
                      ? errors.shippingAddressProvince.message
                      : ""}
                  </Typography>
                )}
              </>
            )}
          />
        </FormControl>
      </Grid>

      <Grid size={{ xs: 12, md: 4 }}>
        <Typography variant="subtitle1" fontWeight="500" sx={{ mb: 1 }}>
          Quận/Huyện{" "}
          <Typography component="span" sx={{ color: "error.main" }}>
            *
          </Typography>
        </Typography>
        <FormControl fullWidth>
          <Controller
            name="shippingAddressDistrict"
            control={control}
            render={({ field }) => (
              <>
                <Select
                  {...field}
                  value={field.value || ""}
                  onChange={handleDistrictChange}
                  displayEmpty
                  disabled={!selectedProvince}
                  sx={{
                    ...fieldStyle,
                    height: "45px",
                    "& .MuiOutlinedInput-root": {
                      height: "45px",
                    },
                  }}
                >
                  <MenuItem value="" disabled>
                    <em>Chọn quận/huyện</em>
                  </MenuItem>
                  {listDistrict?.map((d, index) => (
                    <MenuItem key={`district-${index}`} value={d.districtID}>
                      {d.districtName}
                    </MenuItem>
                  ))}
                </Select>
                {errors.shippingAddressDistrict && (
                  <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                    {typeof errors?.shippingAddressDistrict?.message === "string"
                      ? errors.shippingAddressDistrict.message
                      : ""}
                  </Typography>
                )}
              </>
            )}
          />
        </FormControl>
      </Grid>

      <Grid size={{ xs: 12, md: 4 }}>
        <Typography variant="subtitle1" fontWeight="500" sx={{ mb: 1 }}>
          Phường/Xã{" "}
          <Typography component="span" sx={{ color: "error.main" }}>
            *
          </Typography>
        </Typography>
        <FormControl fullWidth>
          <Controller
            name="shippingAddressWard"
            control={control}
            render={({ field }) => (
              <>
                <Select
                  {...field}
                  value={field.value || ""}
                  displayEmpty
                  disabled={!selectedDistrict}
                  sx={{
                    ...fieldStyle,
                    height: "45px",
                    "& .MuiOutlinedInput-root": {
                      height: "45px",
                    },
                  }}
                >
                  <MenuItem value="" disabled>
                    <em>Chọn phường/xã</em>
                  </MenuItem>
                  {listWard?.map((w, index) => (
                    <MenuItem key={`ward-${index}`} value={w.wardID}>
                      {w.wardName}
                    </MenuItem>
                  ))}
                </Select>
                {errors.shippingAddressWard && (
                  <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                    {typeof errors?.shippingAddressWard?.message === "string"
                      ? errors.shippingAddressWard.message
                      : ""}
                  </Typography>
                )}
              </>
            )}
          />
        </FormControl>
      </Grid>
      <Grid size={{ xs: 12 }}>
        <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
          <Typography variant="subtitle1" fontWeight="500">
            Địa chỉ chi tiết{" "}
            <Typography component="span" sx={{ color: "error.main" }}>
              *
            </Typography>
          </Typography>
        </Box>
        <Controller
          name="shippingAddressAddress"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              placeholder="Số nhà, tên đường..."
              variant="outlined"
              error={!!errors.shippingAddressAddress}
              helperText={errors.shippingAddressAddress?.message as string}
              sx={fieldStyle}
            />
          )}
        />
      </Grid>
    </Grid>
  );
};
