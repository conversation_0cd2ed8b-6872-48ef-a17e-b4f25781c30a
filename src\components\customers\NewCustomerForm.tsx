import React, { useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>, Card, Divider, <PERSON>Field, Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { FormProvider, useForm } from "react-hook-form";
import FormShippingAddress, { ShippingAddressFormData } from "./FormShippingAddress";
import { useDispatch } from "react-redux";
import CustomerTag from "./CustomerTag";
import CustomerBasicInfo from "./CustomerBasicInfo";
import { AppDispatch } from "@/src/store";
import { useRouter } from "next/router";
import { yupResolver } from "@hookform/resolvers/yup";
import { createBasicCustomerInfoSchema } from "@/src/utils/validations/validationSchema";
import { useTranslation } from "react-i18next";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import { paths } from "@/src/paths";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";

type CustomerFormData = {
  email: string;
  name: string;
  phone: string;
  tags: string[];
} & ShippingAddressFormData;

const defaultValues: CustomerFormData = {
  email: "",
  name: "",
  phone: "",
  tags: [],
  shippingAddressFullname: "",
  shippingAddressPhone: "",
  shippingAddressAddress: "",
  shippingAddressProvince: "",
  shippingAddressDistrict: "",
  shippingAddressWard: "",
};

type NewCustomerFormProps = {
  onSubmit: (data: CustomerFormData) => void;
};

export default function NewCustomerForm({ onSubmit }: NewCustomerFormProps) {
  const { t } = useTranslation();

  const validationSchema = createBasicCustomerInfoSchema(t);

  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  const methods = useForm({
    defaultValues,
    resolver: yupResolver(validationSchema),
  });

  const {
    control,
    handleSubmit,
    register,
    setValue,
    formState: { errors },
  } = methods;
  const dispatch = useDispatch<AppDispatch>();

  const onSubmitForm = (data) => {
    onSubmit(data); // Gọi hàm xử lý submit từ props
  };
  const router = useRouter();
  const handleCancel = () => {
    router.back();
  };

  const handleSubmitTag = (tags: string[]) => {
    setValue("tags", tags);
  };
  return (
    <>
      <FormProvider {...methods}>
        <Grid container spacing={2}>
          {/* Cột chiếm 8 phần */}
          <Grid size={{ xs: 12, md: 8 }}>
            <CustomerBasicInfo />
          </Grid>

          {/* Cột chiếm 4 phần */}
          <Grid size={{ xs: 12, md: 4 }}>
            <Card sx={{ p: 2 }}>
              <CustomerTag
                submitTag={handleSubmitTag}
                dataTags={[]}
                isGranted={isGranted(paths.customers.list, PERMISSION_TYPE_ENUM.Edit)}
              />
            </Card>
          </Grid>
        </Grid>

        <Grid container sx={{ mt: 4, mb: 4 }} spacing={2}>
          <Grid size={{ xs: 12, md: 8 }}>
            <Card sx={{ p: 2 }}>
              <Typography variant="h6" sx={{ mb: 4, mt: 2 }}>
                Địa chỉ giao hàng
              </Typography>
              <FormShippingAddress />
            </Card>
          </Grid>
        </Grid>
        <Divider />
        <Grid container sx={{ mt: 3 }}>
          <Box display="flex" gap={1}>
            <Button variant="outlined" onClick={handleCancel}>
              Hủy bỏ
            </Button>
            <Button onClick={handleSubmit(onSubmitForm)} variant="contained" color="primary">
              Lưu
            </Button>
          </Box>
        </Grid>
      </FormProvider>
    </>
  );
}
