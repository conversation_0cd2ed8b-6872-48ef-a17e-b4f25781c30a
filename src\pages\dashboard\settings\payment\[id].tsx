import React, { useEffect, useState } from "react";
import Box from "@mui/material/Box";
import {
  Typography,
  FormGroup,
  FormControlLabel,
  Switch,
  Button,
  Divider,
  TextField,
  Paper,
  Autocomplete,
  FormControl,
  FormLabel,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { useRouter } from "next/router";
import SettingLayout from "@/src/components/settings/settings-page/SettingLayout";
import { paymentService } from "@/src/api/services/payment/payment.service";
import { sepayService } from "@/src/api/services/sepay/sepay.service";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import { paths } from "@/src/paths";
import PageTitleWithBackBtn from "@/src/layouts/dashboard/page-title-with-back-btn/page-title";
import { IBankInfo, IPayment } from "@/src/types/payment/payment";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import { useForm } from "react-hook-form";
import { v4 as uuidv4 } from "uuid";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import { usePayment } from "@/src/api/hooks/payment/use-payment";
import Card from "@mui/material/Card";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";

const LeftColumn = ({ children }) => {
  return (
    <Grid size={{ xs: 12, md: 4 }}>
      <Box p={2}>{children}</Box>
    </Grid>
  );
};

const RightColumn = ({ children }) => {
  return (
    <Grid size={{ xs: 12, md: 8 }} sx={{ padding: 2 }}>
      <Box>{children}</Box>
    </Grid>
  );
};

function SepayAccountLookupForm({ basicAuth, setAccountNumber, setAccountName, bankShortCode }) {
  const [accountNumber, setAccountNumberState] = useState("");
  const [accountNameState, setAccountNameState] = useState("");
  const [loading, setLoading] = useState(false);
  const [accountError, setAccountError] = useState("");

  const handleAccountNumberChange = (e) => {
    const value = e.target.value;
    setAccountNumberState(value);
    setAccountError("");
    setAccountNameState("");
    setAccountNumber(value);
  };

  const handleAccountNumberBlur = async (e) => {
    const value = e.target.value;
    if (value) {
      setLoading(true);
      try {
        const clientMessageId = uuidv4();
        const lookupRes = await sepayService.lookupAccountHolderName(
          (bankShortCode || "MB").toLowerCase(),
          value,
          clientMessageId
        );
        const accountHolderName = lookupRes?.data?.data?.account_holder_name;
        setAccountNameState(accountHolderName);
        setAccountName(accountHolderName);
      } catch (err) {
        if (err?.status === 400) {
          setAccountError("Số tài khoản không tồn tại trên hệ thống ngân hàng.");
        }
      }
      setLoading(false);
    } else {
      setAccountNameState("");
      setAccountError("");
    }
  };

  return (
    <form>
      <TextField
        label="Số tài khoản"
        value={accountNumber}
        onChange={handleAccountNumberChange}
        onBlur={handleAccountNumberBlur}
        fullWidth
        error={!!accountError}
        helperText={
          accountError || `Điền chính xác số tài khoản ngân hàng ${bankShortCode || "MB"}.`
        }
      />
      <TextField
        label="Tên chủ tài khoản"
        value={accountNameState}
        fullWidth
        InputProps={{ readOnly: true }}
        sx={{ mt: 2 }}
      />
      {loading && <span>Đang tra cứu...</span>}
    </form>
  );
}

function SepayPaymentForm({
  defaultValues,
  onSubmit,
  basicAuth,
  payment,
  bankShortCode,
  onCloseBankDialog = () => {},
}): React.ReactElement {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
  } = useForm({ defaultValues });

  useEffect(() => {
    reset(defaultValues);
  }, [defaultValues, reset]);

  const [loading, setLoading] = useState(false);
  const snackbar = useSnackbar();
  const companyId = "11891";
  const [openTermsDialog, setOpenTermsDialog] = useState(false);
  const [openOtpDialog, setOpenOtpDialog] = useState(false);
  const [pendingFormData, setPendingFormData] = useState(null);
  const [otp, setOtp] = useState("");
  const [requestId, setRequestId] = useState("");
  const [clientMessageId, setClientMessageId] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [resendLoading, setResendLoading] = useState(false);
  const [bankAccountId, setBankAccountId] = useState("");
  const [openDeleteWarning, setOpenDeleteWarning] = useState(false);
  const [openDeleteOtp, setOpenDeleteOtp] = useState(false);
  const [deleteOtp, setDeleteOtp] = useState("");
  const [deleteRequestId, setDeleteRequestId] = useState("");
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [accountType, setAccountType] = useState<"personal" | "business">("personal");

  const getValidBankShortCode = () => (bankShortCode || "MB").toLowerCase();

  const isAllFilled =
    !!defaultValues.accountNumber &&
    !!defaultValues.accountName &&
    !!defaultValues.identityNumber &&
    !!defaultValues.phone;

  const handleFormSubmit = (data) => {
    setPendingFormData(data);
    setPhoneNumber(data.phone);
    setOpenTermsDialog(true);
  };

  const handleAgreeTerms = async () => {
    setOpenTermsDialog(false);
    setLoading(true);
    const data = pendingFormData;
    try {
      const clientMsgId = uuidv4();
      setClientMessageId(clientMsgId);
      const payload = {
        CompanyId: companyId,
        AccountHolderName: data.accountName,
        AccountNumber: data.accountNumber,
        IdentificationNumber: data.identityNumber,
        PhoneNumber: data.phone,
        Label: data.alias || null,
      };
      const bankShortCode = getValidBankShortCode();
      const res = await sepayService.createBankAccount(bankShortCode, clientMsgId, payload);
      if (res?.data?.code === 2011) {
        setRequestId(res.data.data?.request_id || "");
        setOtp("");
        setOpenOtpDialog(true);
        setBankAccountId(res.data.id || "");
      } else {
        snackbar.error(res?.data?.message || "Có lỗi khi thêm tài khoản ngân hàng.");
      }
    } catch (err) {
      snackbar.error(err?.message || "Số tài khoản này đã tồn tại trên hệ thống SePay.");
    }
    setLoading(false);
  };

  const handleConfirmOtp = async () => {
    setLoading(true);
    try {
      const bankShortCode = getValidBankShortCode();
      const res = await sepayService.confirmApiConnection(
        bankShortCode,
        requestId,
        clientMessageId,
        {
          otp,
        }
      );
      if (res?.data?.code === 200) {
        snackbar.success("Liên kết API thành công!");
        setOpenOtpDialog(false);
        // updatePayment
        if (pendingFormData && payment) {
          await paymentService.updatePayment({
            PaymentId: payment.paymentId,
            Name: payment.name,
            Detail: payment.detail,
            IsActive: payment.isActive,
            ShopId: payment.shopId,
            Position: payment.position,
            Platform: payment.platform,
            TypePay: payment.typePay,
            PaymentPhoto: payment.paymentPhoto,
            BankShortCode: pendingFormData.bankShortCode || "MB",
            BankAccountNumber: pendingFormData.accountNumber,
            BankAccountId: bankAccountId,
            CustomerBankName: pendingFormData.accountName || "",
            IdentificationNumber: pendingFormData.identityNumber,
            Alias: pendingFormData.alias || "",
            PhoneNumber: pendingFormData.phone || "",
          });
          setPendingFormData(null);
        } else {
          console.warn("Thiếu dữ liệu để gọi updatePayment:", { pendingFormData, payment });
        }
        window.location.reload();
      } else {
        snackbar.error(res?.data?.message || "Xác thực OTP thất bại.");
      }
    } catch (err) {
      snackbar.error(err?.message || "Xác thực OTP thất bại.");
    }
    setLoading(false);
  };

  const handleResendOtp = async () => {
    setResendLoading(true);
    try {
      const clientMsgId = uuidv4();
      setClientMessageId(clientMsgId);
      if (!bankAccountId) throw new Error("Không tìm thấy bankAccountId để gửi lại OTP");
      if (!requestId) throw new Error("Không tìm thấy requestId để gửi lại OTP");
      // ResendOtp: truyền bankAccountId là param, requestId là header 'Request-Id'
      const bankShortCode = getValidBankShortCode();
      const res = await sepayService.resendOtp(
        bankShortCode,
        bankAccountId,
        clientMsgId,
        requestId
      );
      if (res?.data?.code === 200) {
        setRequestId(res.data.data?.request_id || "");
        setOtp("");
        snackbar.success("Đã gửi lại mã OTP.");
      } else {
        snackbar.error(res?.data?.message || "Không gửi lại được mã OTP.");
      }
    } catch (err) {
      snackbar.error(err?.message || "Không gửi lại được mã OTP.");
    }
    setResendLoading(false);
  };

  const handleDeleteClick = () => {
    setOpenDeleteWarning(true);
  };

  const handleConfirmDeleteWarning = async (bankAccountId) => {
    setOpenDeleteWarning(false);
    setDeleteLoading(true);
    try {
      const clientMsgId = uuidv4();
      setClientMessageId(clientMsgId);
      const bankShortCode = getValidBankShortCode();
      const res = await sepayService.requestDeleteBankAccount(
        bankShortCode,
        bankAccountId,
        clientMsgId
      );
      if (res?.data?.code === 200) {
        setDeleteRequestId(res.data.data?.request_id || "");
        setOpenDeleteOtp(true);
        setDeleteOtp("");
      } else {
        snackbar.error(res?.data?.message || "Không thể gửi yêu cầu xoá tài khoản.");
      }
    } catch (error) {
      snackbar.error(error?.message || "Không thể gửi yêu cầu xoá tài khoản.");
    }
    setDeleteLoading(false);
  };

  const handleConfirmDeleteOtp = async () => {
    setDeleteLoading(true);
    // Update payment fields to empty immediately (run in parallel with confirmDeleteBankAccount)
    if (payment) {
      paymentService
        .updatePayment({
          PaymentId: payment.paymentId,
          Name: payment.name,
          Detail: payment.detail,
          IsActive: payment.isActive,
          ShopId: payment.shopId,
          Position: payment.position,
          Platform: payment.platform,
          TypePay: payment.typePay,
          PaymentPhoto: payment.paymentPhoto,
          BankShortCode: "",
          BankAccountNumber: "",
          BankAccountId: "",
          CustomerBankName: "",
          IdentificationNumber: "",
          Alias: "",
          PhoneNumber: "",
        })
        .catch(() => {});
    }
    try {
      const clientMsgId = uuidv4();
      setClientMessageId(clientMsgId);
      const bankShortCode = getValidBankShortCode();
      sepayService
        .confirmDeleteBankAccount(bankShortCode, clientMsgId, deleteRequestId, { otp: deleteOtp })
        .catch(() => {});
    } catch (err) {}
    await new Promise((resolve) => setTimeout(resolve, 10000));
    snackbar.success("Đã xoá liên kết tài khoản thành công.");
    setOpenDeleteOtp(false);
    reset({
      accountNumber: "",
      accountName: "",
      identityNumber: "",
      phone: "",
      alias: "",
    });
    setBankAccountId("");
    setRequestId("");
    setDeleteRequestId("");
    setDeleteLoading(false);
    // Điều hướng về URL không có bankShortCode và reload lại trang
    const url = window.location.pathname;
    window.location.replace(url);
  };

  const handleResendDeleteOtp = async () => {
    setDeleteLoading(true);
    try {
      const clientMsgId = uuidv4();
      setClientMessageId(clientMsgId);
      if (!payment.bankAccountId) throw new Error("Không tìm thấy bankAccountId để gửi lại OTP");
      if (!deleteRequestId) throw new Error("Không tìm thấy requestId để gửi lại OTP");
      const bankShortCode = getValidBankShortCode();
      const res = await sepayService.requestDeleteBankAccount(
        bankShortCode,
        payment.bankAccountId,
        clientMsgId
      );
      if (res?.data?.code === 200) {
        setDeleteRequestId(res.data.data?.request_id || "");
        setDeleteOtp("");
        snackbar.success("Đã gửi lại mã OTP.");
      } else {
        snackbar.error(res?.data?.message || "Không gửi lại được mã OTP.");
      }
    } catch (err) {
      snackbar.error(err?.message || "Không gửi lại được mã OTP.");
    }
    setDeleteLoading(false);
  };

  return (
    <Box display="flex" gap={3}>
      <Paper sx={{ flex: 1, p: 3 }}>
        <Typography fontWeight="bold" mb={2}>
          Thông tin cấu hình tài khoản {bankShortCode}
        </Typography>
        {!isAllFilled ? (
          <>
            <FormControl component="fieldset" sx={{ mb: 2 }}>
              <FormLabel component="legend">Loại tài khoản</FormLabel>
              <Box sx={{ display: "flex", gap: 2, mt: 1 }}>
                <Button
                  variant={accountType === "personal" ? "contained" : "outlined"}
                  onClick={() => setAccountType("personal")}
                  size="small"
                >
                  Cá nhân
                </Button>
                <Button
                  variant={accountType === "business" ? "contained" : "outlined"}
                  onClick={() => setAccountType("business")}
                  size="small"
                >
                  Doanh nghiệp
                </Button>
              </Box>
            </FormControl>
            <SepayAccountLookupForm
              basicAuth={basicAuth}
              setAccountNumber={(val) => setValue("accountNumber", val)}
              setAccountName={(val) => setValue("accountName", val)}
              bankShortCode={bankShortCode}
            />
            <form onSubmit={handleSubmit(handleFormSubmit)}>
              <TextField
                label={accountType === "business" ? "Mã số thuế (*)" : "Số CCCD/CMND (*)"}
                fullWidth
                margin="normal"
                {...register("identityNumber", { required: true })}
                error={!!errors.identityNumber}
                helperText={errors.identityNumber && "Bắt buộc"}
              />
              <TextField
                label="Số điện thoại (*)"
                fullWidth
                margin="normal"
                {...register("phone", { required: true })}
                error={!!errors.phone}
                helperText={errors.phone && "Bắt buộc"}
              />
              <TextField label="Tên gợi nhớ" fullWidth margin="normal" {...register("alias")} />
              <Button type="submit" variant="contained" sx={{ mt: 2, mr: 2 }} disabled={loading}>
                {loading ? "Đang xử lý..." : "Tiếp tục"}
              </Button>
            </form>
          </>
        ) : (
          <form>
            <TextField
              label="Số tài khoản"
              fullWidth
              margin="normal"
              value={defaultValues.accountNumber}
              disabled
            />
            <TextField
              label="Tên chủ tài khoản"
              fullWidth
              margin="normal"
              value={defaultValues.accountName}
              disabled
            />
            <TextField
              label="Số CCCD/CMND (*)"
              fullWidth
              margin="normal"
              value={defaultValues.identityNumber}
              disabled
            />
            <TextField
              label="Số điện thoại (*)"
              fullWidth
              margin="normal"
              value={defaultValues.phone}
              disabled
            />
            <TextField
              label="Tên gợi nhớ"
              fullWidth
              margin="normal"
              value={defaultValues.alias}
              disabled
            />
            <Button
              variant="outlined"
              color="error"
              sx={{ mt: 2 }}
              onClick={handleDeleteClick}
              disabled={deleteLoading}
            >
              Xóa tài khoản
            </Button>
          </form>
        )}
      </Paper>
      <Paper sx={{ width: 350, p: 3 }}>
        <Typography fontWeight="bold" mb={1}>
          Hướng dẫn
        </Typography>
        <Typography mb={1}>
          Điền thông tin số tài khoản ngân hàng {bankShortCode || "MB"} của bạn. Nếu chưa có tài
          khoản, vui lòng làm theo hướng dẫn sau:
        </Typography>
        <Typography
          color="primary"
          component="a"
          href="https://sepay.vn/blog/huong-dan-mo-tai-khoan-ngan-hang-mbbank-online-moi-nhat/"
          target="_blank"
        >
          Hướng dẫn mở tài khoản ngân hàng {bankShortCode || "MB"} cá nhân online.
        </Typography>
        <Typography fontWeight="bold" mt={2} mb={1}>
          Đặc điểm
        </Typography>
        <Box display="flex" alignItems="center" gap={1}>
          <CheckCircleIcon color="success" fontSize="small" />
          <Typography>API chính thức từ ngân hàng {bankShortCode || "MB"}</Typography>
        </Box>
        <Box display="flex" alignItems="center" gap={1}>
          <CheckCircleIcon color="success" fontSize="small" />
          <Typography>Nhận giao dịch tức thì</Typography>
        </Box>
      </Paper>

      {/* Popup điều khoản sử dụng */}
      <Dialog
        open={openTermsDialog}
        onClose={() => setOpenTermsDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ mt: 1 }}>Điều khoản sử dụng</DialogTitle>
        <DialogContent>
          <Typography>
            Quý khách hàng đang sử dụng Dịch vụ nhận biến động số dư tại <b>SePay</b> được cung cấp
            bởi <b>Công ty Cổ phần SePay</b>.<br />
            <br />
            Theo <b>Nghị định 13</b>, để đảm bảo quy tắc bảo mật thông tin Khách hàng, SePay cần Quý
            khách hàng xác nhận đồng ý các điều khoản khi đăng ký sử dụng dịch vụ nhận biến động
            giao dịch bởi <b>{bankShortCode || "MB"}Bank</b>, nội dung của xác nhận, bao gồm:
          </Typography>
          <Typography component="div" sx={{ mt: 2, mb: 2 }}>
            <ol>
              <li>
                Đồng ý cho phép <b>SePay</b> chia sẻ thông tin cho{" "}
                <b>{bankShortCode || "MB"}Bank</b> để đăng ký sử dụng dịch vụ từ{" "}
                {bankShortCode || "MB"}Bank.
              </li>
              <li>
                Đồng ý cho <b>SePay</b> nhận thông tin tuỳ theo dịch vụ khách hàng đã đăng ký với
                <b> {bankShortCode || "MB"}Bank</b> thông qua nền tảng của <b>SePay</b>.
              </li>
            </ol>
          </Typography>
          <Typography color="error" sx={{ fontSize: 13, mb: 2 }}>
            * Bằng việc nhấp vào nút "Có, tôi đồng ý" bên dưới, Quý khách hàng sẽ đồng ý chấp thuận
            các điều khoản trên.
          </Typography>
          <Box
            sx={{
              background: "#FFF3E0",
              border: "1px solid #FF9800",
              borderRadius: 2,
              p: 2,
              mb: 1,
            }}
          >
            <Typography color="error" fontWeight="bold" sx={{ fontSize: 15, mb: 0.5 }}>
              Lưu ý
            </Typography>
            <Typography sx={{ fontSize: 14 }}>
              Hiện chưa hỗ trợ hiển thị số dư cuối sau giao dịch.
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions sx={{ mb: 1 }}>
          <Button onClick={() => setOpenTermsDialog(false)} variant="outlined">
            Không đồng ý
          </Button>
          <Button onClick={handleAgreeTerms} variant="contained">
            Có, tôi đồng ý
          </Button>
        </DialogActions>
      </Dialog>

      {/* Popup xác thực OTP */}
      <Dialog open={openOtpDialog} onClose={undefined} maxWidth="sm" fullWidth disableEscapeKeyDown>
        <DialogTitle>Xác thực OTP</DialogTitle>
        <DialogContent>
          <Box sx={{ background: "#e3f0ff", borderRadius: 1, p: 2, mb: 2 }}>
            <Typography>
              Mã OTP đã gửi về số điện thoại <b>{phoneNumber}</b>. Vui lòng điền mã OTP để xác nhận
              bạn chia sẻ biến động số dư qua API với SePay. Mã OTP sẽ hết hạn trong vòng 2 phút.
            </Typography>
          </Box>
          <TextField
            label="Điền mã OTP"
            value={otp}
            onChange={(e) => setOtp(e.target.value)}
            fullWidth
            sx={{ mb: 2 }}
          />
          <Button
            variant="contained"
            fullWidth
            onClick={handleConfirmOtp}
            disabled={loading || !otp}
          >
            Liên kết API
          </Button>
          <Divider sx={{ my: 2 }} />
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            Trong trường hợp không nhận được mã OTP hoặc mã OTP hết hiệu lực, bạn có thể nhấn
          </Typography>
          <Button variant="outlined" onClick={handleResendOtp} disabled={resendLoading}>
            {resendLoading ? "Đang gửi lại..." : "Gửi lại mã"}
          </Button>
          {/* Xoá tài khoản liên kết*/}
          <Typography variant="body2" color="text.secondary" sx={{ my: 1 }}>
            Bạn muốn dừng việc liên kết mở API?
          </Typography>{" "}
          <Button
            variant="outlined"
            color="error"
            disabled={resendLoading || loading}
            onClick={async () => {
              if (!bankAccountId) {
                snackbar.error("Không tìm thấy thông tin tài khoản để xoá.");
                return;
              }
              setLoading(true);
              try {
                const clientMsgId = uuidv4();
                setClientMessageId(clientMsgId);
                sepayService
                  .forceDeleteBankAccount(
                    getValidBankShortCode(),
                    bankAccountId,
                    clientMsgId,
                    requestId
                  )
                  .catch(() => {});
              } catch (err) {}
              await new Promise((resolve) => setTimeout(resolve, 10000));
              snackbar.success("Đã xoá liên kết tài khoản thành công.");
              setOpenOtpDialog(false);
              reset({
                accountNumber: "",
                accountName: "",
                identityNumber: "",
                phone: "",
                alias: "",
              });
              setBankAccountId("");
              setRequestId("");
              setLoading(false);
              window.location.reload();
            }}
          >
            {loading ? "Đang xoá..." : "Xoá tài khoản liên kết"}
          </Button>
        </DialogContent>
      </Dialog>

      {/* Delete Warning Modal (image 1) */}
      <Dialog
        open={openDeleteWarning}
        onClose={() => setOpenDeleteWarning(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Xóa tài khoản</DialogTitle>
        <DialogContent>
          <Box sx={{ background: "#ffeaea", borderRadius: 1, p: 2, mb: 2 }}>
            <Typography color="error" fontWeight="bold">
              Việc xóa tài khoản ngân hàng này khỏi SePay đồng nghĩa với tất cả giao dịch, dữ liệu
              tích hợp liên quan đến tài khoản ngân hàng này sẽ bị xóa!
            </Typography>
          </Box>
          <Typography mb={2}>
            Bằng việc chấp thuận hủy liên kết API tài khoản này với ngân hàng, SePay sẽ thực hiện gỡ
            tài khoản ngân hàng của bạn khỏi hệ thống SePay.
            <br />
            Nhấn vào <b>"Hủy liên kết API"</b> để tiến hành gỡ tài khoản ngân hàng, bạn có thể nhận
            mã xác thực được gửi từ phía ngân hàng {bankShortCode || "MB"}.
          </Typography>
          <Box textAlign="center" sx={{ mt: 2 }}>
            <Button variant="outlined" onClick={() => setOpenDeleteWarning(false)} sx={{ mr: 2 }}>
              Huỷ bỏ
            </Button>
            <Button
              variant="contained"
              color="error"
              onClick={() => handleConfirmDeleteWarning(payment?.bankAccountId)}
              disabled={deleteLoading}
              sx={{ minWidth: 160 }}
            >
              {deleteLoading ? "Đang gửi yêu cầu..." : "Hủy liên kết API"}
            </Button>
          </Box>
        </DialogContent>
      </Dialog>

      {/* Delete OTP Modal (image 2) */}
      <Dialog open={openDeleteOtp} onClose={() => setOpenDeleteOtp(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Xóa tài khoản</DialogTitle>
        <DialogContent>
          <Box sx={{ background: "#ffeaea", borderRadius: 1, p: 2, mb: 2 }}>
            <Typography color="error" fontWeight="bold">
              Việc xóa tài khoản ngân hàng này khỏi SePay đồng nghĩa với tất cả giao dịch, dữ liệu
              tích hợp liên quan đến tài khoản ngân hàng này sẽ bị xóa!
            </Typography>
          </Box>
          <Typography mb={2}>
            Bằng việc chấp thuận hủy liên kết API tài khoản này với ngân hàng, SePay sẽ thực hiện gỡ
            tài khoản ngân hàng của bạn khỏi hệ thống SePay.
          </Typography>
          <TextField
            label="Điền mã OTP"
            value={deleteOtp}
            onChange={(e) => setDeleteOtp(e.target.value)}
            fullWidth
            sx={{ mb: 2 }}
          />
          <Button
            variant="contained"
            color="error"
            fullWidth
            onClick={handleConfirmDeleteOtp}
            disabled={deleteLoading || !deleteOtp}
            sx={{ mb: 2, minHeight: 48, fontWeight: 600 }}
          >
            {deleteLoading ? "Đang xác thực..." : "Hủy liên kết API và Xóa tài khoản"}
          </Button>
          <Divider sx={{ my: 2 }} />
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            Trong trường hợp không nhận được mã OTP hoặc mã OTP hết hiệu lực, bạn có thể nhấn
          </Typography>
          <Button
            variant="outlined"
            onClick={handleResendDeleteOtp}
            disabled={deleteLoading}
            sx={{ minWidth: 140 }}
          >
            Gửi lại mã
          </Button>
        </DialogContent>
      </Dialog>
    </Box>
  );
}

export default function PaymentDetail() {
  const router = useRouter();
  const snackbar = useSnackbar();
  const paymentId = router?.query?.id;
  const { getListBankPartner } = usePayment();
  const [listBankPartner, setListBankPartner] = useState<IBankInfo[]>([]);

  const [transferBankShortCode, setTransferBankShortCode] = useState("");
  const [bankAccountNumber, setBankAccountNumber] = useState("");
  const [customerBankName, setCustomerBankName] = useState("");

  const [errors, setErrors] = useState({
    transferBankShortCode: false,
    bankAccountNumber: false,
    customerBankName: false,
  });
  // --- Bank selection state ---
  const [bankShortCode, setBankShortCode] = useState(() => {
    if (typeof window !== "undefined") {
      return router?.query?.bankShortCode || "";
    }
    return "";
  });
  const [openBankDialog, setOpenBankDialog] = useState(false);
  // Supported banks
  const banks = [
    { code: "MB", name: "MBBank", logo: "/logo/mbbank-icon.png" },
    { code: "ACB", name: "ACB", logo: "/logo/acb-icon.png" },
    { code: "OCB", name: "OCB", logo: "/logo/ocb-icon.png" },
    { code: "KLB", name: "KienlongBank", logo: "/logo/kienlongbank-icon.png" },
  ];

  const [payment, setPayment] = useState<IPayment>();
  const [platforms, setPlatforms] = useState({
    zaloMiniApp: false,
    webApp: false,
    pos: false,
  });

  const validateBankFields = () => {
    const hasErrors = {
      transferBankShortCode: !transferBankShortCode,
      bankAccountNumber: !bankAccountNumber.trim(),
      customerBankName: !customerBankName.trim(),
    };
    setErrors(hasErrors);
    return !Object.values(hasErrors).some(Boolean);
  };

  const getDetailPayment = async () => {
    if (paymentId) {
      const res = await paymentService.getDetailPayment(paymentId.toString());
      setPayment(res?.data);
      if (res?.data?.platform) {
        setPlatforms({
          zaloMiniApp: res.data.platform.includes("ZaloMiniApp"),
          webApp: res.data.platform.includes("WebApp"),
          pos: res.data.platform.includes("Pos"),
        });
      }
      // Nếu là Sepay và đã có bankShortCode từ BE, luôn set lại state bankShortCode
      if (res?.data?.typePay === "Sepay" && res?.data?.bankShortCode) {
        setBankShortCode(res.data.bankShortCode);
      }
      if (res?.data.typePay === "Transfer") {
        setBankAccountNumber(res?.data.bankAccountNumber);
        setCustomerBankName(res?.data.customerBankName);
        setTransferBankShortCode(res?.data.bankShortCode);
        const listBankPartnerRes = await getListBankPartner();
        if (listBankPartnerRes?.data) {
          setListBankPartner(listBankPartnerRes?.data);
        }
      }
    }
  };

  const handleUpdatePlatformPayment = async () => {
    try {
      const selectedPlatforms = [];
      if (platforms.zaloMiniApp) selectedPlatforms.push("ZaloMiniApp");
      if (platforms.webApp) selectedPlatforms.push("WebApp");
      if (platforms.pos) selectedPlatforms.push("Pos");

      const updateData: any = {
        isActive: payment.isActive,
        paymentId: payment?.paymentId,
        platform: selectedPlatforms,
      };

      if (payment?.typePay === "Transfer" && !validateBankFields()) {
        return;
      }
      if (payment?.typePay === "Transfer") {
        updateData.bankShortCode = transferBankShortCode;
        updateData.bankAccountNumber = bankAccountNumber;
        updateData.customerBankName = customerBankName;
      }

      await paymentService.updatePayment(updateData);
      snackbar.success("Cập nhật thành công");
      router.push(paths.settings.payment.index);
    } catch (error) {
      snackbar.error("Có lỗi xảy ra");
    }
  };

  useEffect(() => {
    getDetailPayment();
  }, [paymentId]);

  useEffect(() => {
    // Nếu đã có tài khoản Sepay liên kết và có bankShortCode, tự động redirect nếu thiếu bankShortCode trên URL
    if (
      payment &&
      payment.typePay === "Sepay" &&
      payment.bankShortCode &&
      payment.bankAccountNumber &&
      !router?.query?.bankShortCode
    ) {
      router.replace(
        {
          pathname: router.pathname,
          query: { ...router.query, bankShortCode: payment.bankShortCode },
        },
        undefined,
        { shallow: true }
      );
      setOpenBankDialog(false);
      return;
    }
    // Nếu là Sepay, chưa có tài khoản liên kết và chưa chọn bankShortCode thì mở dialog chọn ngân hàng
    if (
      payment &&
      payment.typePay === "Sepay" &&
      !payment.bankAccountNumber &&
      !router?.query?.bankShortCode
    ) {
      setOpenBankDialog(true);
    } else {
      setOpenBankDialog(false);
    }
  }, [router.query.bankShortCode, payment]);

  const [openBankFormDialog, setOpenBankFormDialog] = useState(false);
  const [openSepayDialog, setOpenSepayDialog] = useState(false);

  return (
    <SettingLayout>
      {payment && (
        <>
          <PageTitleWithBackBtn
            title="Cài đặt thanh toán"
            backPath={paths.settings.payment.index}
          />
          {payment.typePay === "Sepay" ? (
            <Grid
              container
              spacing={2}
              sx={{ marginTop: 3, border: "1px solidrgb(234, 232, 232)" }}
            >
              <LeftColumn>
                <Box>
                  <Typography fontWeight="bold" marginBottom={2}>
                    Cấu hình Sepay
                  </Typography>
                  <Typography variant="subtitle2" color="text.secondary">
                    Đây là giao diện cấu hình riêng cho phương thức thanh toán Sepay. Bạn có thể
                    thêm các trường, hướng dẫn, hoặc form riêng cho Sepay tại đây.
                  </Typography>
                </Box>
                {/* Không render SepayPaymentForm ở đây nữa */}
              </LeftColumn>
              <RightColumn>
                <Typography fontWeight="bold" mb={1}>
                  Danh sách ngân hàng liên kết
                </Typography>
                <Box display="flex" flexDirection="column" gap={2}>
                  {banks.map((bank) => {
                    const isSelected = bankShortCode === bank.code;
                    const isLinked =
                      payment?.bankShortCode === bank.code && !!payment?.bankAccountNumber;
                    const linkedAccountNumber = isLinked ? payment?.bankAccountNumber : null;
                    const linkedAccountName = isLinked ? payment?.customerBankName : null;
                    return (
                      <Card
                        key={bank.code}
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          p: 2,
                          mb: 2,
                          border: isSelected ? "2px solid #6366f1" : "1px solid #e0e0e0",
                          boxShadow: isSelected ? 2 : 0,
                          background: isSelected ? "#f5f7ff" : "white",
                        }}
                      >
                        <Box
                          sx={{
                            width: 48,
                            height: 48,
                            borderRadius: 2,
                            border: `3px solid ${isLinked ? "#1976d2" : "#e0e0e0"}`,
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            mr: 2,
                            background: "#f5f5f5",
                          }}
                        >
                          <img src={bank.logo} alt={bank.name} width={32} height={32} />
                        </Box>

                        <Box flex={1} minWidth={0}>
                          <Box display="flex" alignItems="center" gap={1}>
                            <Typography fontWeight={700}>{bank.name}</Typography>
                            <Box
                              sx={{
                                backgroundColor: isLinked ? "#00ce00" : "#f1c232", // xanh nếu đã liên kết, đỏ nếu chưa
                                color: "#fff",
                                borderRadius: 1,
                                px: 1,
                                fontSize: 12,
                                fontWeight: 600,
                                lineHeight: "20px",
                                display: "inline-block",
                              }}
                            >
                              {isLinked ? "Đã liên kết" : "Chưa liên kết"}
                            </Box>
                          </Box>

                          {isLinked ? (
                            <>
                              <Typography variant="body2" color="text.secondary" noWrap>
                                {linkedAccountNumber} · {linkedAccountName}
                              </Typography>
                            </>
                          ) : (
                            <Typography variant="body2" color="text.secondary">
                              Bạn chưa liên kết tài khoản này. Hãy nhấn "Thiết lập" để liên kết
                              ngay.
                            </Typography>
                          )}
                        </Box>

                        <Button
                          variant="contained"
                          onClick={() => {
                            setBankShortCode(bank.code);
                            setOpenBankFormDialog(true);
                            setOpenBankDialog(false);
                            router.replace(
                              {
                                pathname: router.pathname,
                                query: { ...router.query, bankShortCode: bank.code },
                              },
                              undefined,
                              { shallow: true }
                            );
                          }}
                          sx={{ fontWeight: 600, minWidth: 140 }}
                        >
                          {isLinked ? "Thiết lập" : "Thiết lập"}
                        </Button>
                      </Card>
                    );
                  })}
                </Box>
                {/* Dialog popup cho form liên kết ngân hàng */}
                <Dialog
                  open={openBankFormDialog}
                  onClose={() => {
                    setOpenBankFormDialog(false);
                    setBankShortCode("");
                  }}
                  maxWidth="md"
                  fullWidth
                >
                  <Box p={2}>
                    {bankShortCode && (
                      <SepayPaymentForm
                        defaultValues={
                          payment && payment.bankShortCode === bankShortCode
                            ? {
                                accountNumber: payment.bankAccountNumber || "",
                                accountName: payment.customerBankName || "",
                                identityNumber: payment.identificationNumber || "",
                                phone: payment.phoneNumber || "",
                                alias: payment.alias || "",
                              }
                            : {
                                accountNumber: "",
                                accountName: "",
                                identityNumber: "",
                                phone: "",
                                alias: "",
                              }
                        }
                        onSubmit={() => {}}
                        basicAuth=""
                        payment={payment}
                        bankShortCode={bankShortCode}
                      />
                    )}
                  </Box>
                </Dialog>
              </RightColumn>
            </Grid>
          ) : (
            <Grid
              container
              spacing={2}
              sx={{ marginTop: 3, border: "1px solidrgb(234, 232, 232)" }}
            >
              <LeftColumn>
                <Box>
                  <Typography fontWeight="bold" marginBottom={2}>
                    Kích hoạt phương thức thanh toán
                  </Typography>
                  <Typography variant="subtitle2" color="text.secondary">
                    Bạn có thể kích hoạt các phương thức thanh toán trên các nền tảng
                  </Typography>
                </Box>
              </LeftColumn>

              <RightColumn>
                <Typography sx={{ fontWeight: 700 }}>{payment?.name}</Typography>
                <Box sx={{ mt: 3 }}>
                  <FormGroup>
                    <FormControlLabel
                      labelPlacement="start"
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        margin: 0,
                        padding: "8px 0",
                      }}
                      control={
                        <Switch
                          checked={platforms.zaloMiniApp}
                          onChange={(e) =>
                            setPlatforms((prev) => ({ ...prev, zaloMiniApp: e.target.checked }))
                          }
                        />
                      }
                      label="Cho phép thanh toán Zalo Mini App"
                    />
                    <FormControlLabel
                      labelPlacement="start"
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        margin: 0,
                        padding: "8px 0",
                      }}
                      control={
                        <Switch
                          checked={platforms.webApp}
                          onChange={(e) =>
                            setPlatforms((prev) => ({ ...prev, webApp: e.target.checked }))
                          }
                        />
                      }
                      label="Cho phép thanh toán Web App"
                    />
                    <FormControlLabel
                      labelPlacement="start"
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        margin: 0,
                        padding: "8px 0",
                      }}
                      control={
                        <Switch
                          checked={platforms.pos}
                          onChange={(e) =>
                            setPlatforms((prev) => ({ ...prev, pos: e.target.checked }))
                          }
                        />
                      }
                      label="Cho phép thanh toán POS"
                    />
                  </FormGroup>
                  {payment.typePay === "Transfer" && (
                    <>
                      <Button
                        variant="contained"
                        sx={{ mt: 2, fontWeight: 600 }}
                        onClick={() => setOpenSepayDialog(true)}
                      >
                        Liên kết Sepay
                      </Button>
                      <Dialog
                        open={openSepayDialog}
                        onClose={() => setOpenSepayDialog(false)}
                        maxWidth="md"
                        fullWidth
                      >
                        <Box p={2}>
                          {/* UI Sepay */}
                          <Typography fontWeight="bold" marginBottom={2}>
                            Cấu hình Sepay
                          </Typography>
                          <Typography variant="subtitle2" color="text.secondary" mb={2}>
                            Đây là giao diện cấu hình riêng cho phương thức thanh toán Sepay. Bạn có
                            thể thêm các trường, hướng dẫn, hoặc form riêng cho Sepay tại đây.
                          </Typography>
                          <Typography fontWeight="bold" mb={1}>
                            Danh sách ngân hàng liên kết
                          </Typography>
                          <Box display="flex" flexDirection="column" gap={2}>
                            {banks.map((bank) => {
                              const isSelected = bankShortCode === bank.code;
                              const isLinked =
                                payment?.bankShortCode === bank.code &&
                                !!payment?.bankAccountNumber;
                              const linkedAccountNumber = isLinked
                                ? payment?.bankAccountNumber
                                : null;
                              const linkedAccountName = isLinked ? payment?.customerBankName : null;
                              return (
                                <Card
                                  key={bank.code}
                                  sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    p: 2,
                                    mb: 2,
                                    border: isSelected ? "2px solid #6366f1" : "1px solid #e0e0e0",
                                    boxShadow: isSelected ? 2 : 0,
                                    background: isSelected ? "#f5f7ff" : "white",
                                  }}
                                >
                                  <Box
                                    sx={{
                                      width: 48,
                                      height: 48,
                                      borderRadius: 2,
                                      border: `3px solid ${isLinked ? "#1976d2" : "#e0e0e0"}`,
                                      display: "flex",
                                      alignItems: "center",
                                      justifyContent: "center",
                                      mr: 2,
                                      background: "#f5f5f5",
                                    }}
                                  >
                                    <img src={bank.logo} alt={bank.name} width={32} height={32} />
                                  </Box>
                                  <Box flex={1} minWidth={0}>
                                    <Box display="flex" alignItems="center" gap={1}>
                                      <Typography fontWeight={700}>{bank.name}</Typography>
                                      <Box
                                        sx={{
                                          backgroundColor: isLinked ? "#00ce00" : "#f1c232",
                                          color: "#fff",
                                          borderRadius: 1,
                                          px: 1,
                                          fontSize: 12,
                                          fontWeight: 600,
                                          lineHeight: "20px",
                                          display: "inline-block",
                                        }}
                                      >
                                        {isLinked ? "Đã liên kết" : "Chưa liên kết"}
                                      </Box>
                                    </Box>
                                    {isLinked ? (
                                      <Typography variant="body2" color="text.secondary" noWrap>
                                        {linkedAccountNumber} · {linkedAccountName}
                                      </Typography>
                                    ) : (
                                      <Typography variant="body2" color="text.secondary">
                                        Bạn chưa liên kết tài khoản này. Hãy nhấn "Thiết lập" để
                                        liên kết ngay.
                                      </Typography>
                                    )}
                                  </Box>
                                  <Button
                                    variant="contained"
                                    onClick={() => {
                                      setBankShortCode(bank.code);
                                      setOpenBankFormDialog(true);
                                      setOpenBankDialog(false);
                                      router.replace(
                                        {
                                          pathname: router.pathname,
                                          query: { ...router.query, bankShortCode: bank.code },
                                        },
                                        undefined,
                                        { shallow: true }
                                      );
                                    }}
                                    sx={{ fontWeight: 600, minWidth: 140 }}
                                  >
                                    {isLinked ? "Thiết lập" : "Thiết lập"}
                                  </Button>
                                </Card>
                              );
                            })}
                          </Box>
                          {/* Dialog popup cho form liên kết ngân hàng */}
                          <Dialog
                            open={openBankFormDialog}
                            onClose={() => {
                              setOpenBankFormDialog(false);
                              setBankShortCode("");
                            }}
                            maxWidth="md"
                            fullWidth
                          >
                            <Box p={2}>
                              {bankShortCode && (
                                <SepayPaymentForm
                                  defaultValues={
                                    payment && payment.bankShortCode === bankShortCode
                                      ? {
                                          accountNumber: payment.bankAccountNumber || "",
                                          accountName: payment.customerBankName || "",
                                          identityNumber: payment.identificationNumber || "",
                                          phone: payment.phoneNumber || "",
                                          alias: payment.alias || "",
                                        }
                                      : {
                                          accountNumber: "",
                                          accountName: "",
                                          identityNumber: "",
                                          phone: "",
                                          alias: "",
                                        }
                                  }
                                  onSubmit={() => {}}
                                  basicAuth=""
                                  payment={payment}
                                  bankShortCode={bankShortCode}
                                />
                              )}
                            </Box>
                          </Dialog>
                        </Box>
                      </Dialog>
                      <Box
                        mt={2}
                        sx={{
                          background: "#e3f2fd",
                          border: "1.5px solid #90caf9",
                          borderRadius: 2,
                          display: "flex",
                          alignItems: "center",
                          gap: 1.5,
                          p: 2,
                          width: "100%",
                        }}
                      >
                        <InfoOutlinedIcon
                          sx={{ color: "#1976d2", alignSelf: "center" }}
                          fontSize="small"
                        />
                        <Box>
                          <Typography
                            variant="body2"
                            fontWeight={700}
                            color="#1976d2"
                            display="inline"
                          >
                            Tính năng tự động kiểm tra giao dịch:
                          </Typography>
                          <Typography variant="body2" display="inline" color="text.primary">
                            &nbsp;Tự động kiểm tra giao dịch qua Sepay (450đ/giao dịch thành công,
                            trừ vào tài khoản đối tác)
                          </Typography>
                        </Box>
                      </Box>
                      <Box
                        sx={{
                          background: "#e8f5e9",
                          border: "1.5px solid #a5d6a7",
                          borderRadius: 2,
                          display: "flex",
                          alignItems: "center",
                          gap: 1.5,
                          p: 2,
                          mt: 1,
                          width: "100%",
                        }}
                      >
                        <CheckCircleOutlineIcon
                          sx={{ color: "#43a047", alignSelf: "center" }}
                          fontSize="small"
                        />
                        <Typography variant="body2" color="#388e3c">
                          <b>Khuyến nghị:</b> Liên kết tài khoản để đảm bảo kiểm tra giao dịch tự
                          động.
                        </Typography>
                      </Box>
                    </>
                  )}
                  <Box sx={{ mt: 4, display: "flex", justifyContent: "flex-end" }}>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={handleUpdatePlatformPayment}
                    >
                      Lưu
                    </Button>
                  </Box>
                </Box>
              </RightColumn>
            </Grid>
          )}
        </>
      )}
    </SettingLayout>
  );
}
