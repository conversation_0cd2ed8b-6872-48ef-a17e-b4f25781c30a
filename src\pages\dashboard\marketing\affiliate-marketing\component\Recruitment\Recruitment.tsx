import React, { useEffect, useState, useCallback } from "react";
import {
  Box,
  Button,
  Paper,
  TextField,
  Typography,
  Checkbox,
  FormControlLabel,
  IconButton,
  Stack,
  debounce,
} from "@mui/material";
import { Container } from "@mui/system";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import MoreHorizIcon from "@mui/icons-material/MoreHoriz";
import CloseIcon from "@mui/icons-material/Close";
import dynamic from "next/dynamic";
import { useFormik } from "formik";
import * as Yup from "yup";

import { useAffiliation } from "@/src/api/hooks/affiliation/use-affiliation";
import { useStoreId } from "@/src/hooks/use-store-id";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useMedia } from "@/src/api/hooks/media/use-media";
import { stripHtmlAndSpaces } from "@/src/components/react-quill-editor";
import { RecruitmentBody } from "@/src/api/types/affiliation.type";
import DOMPurify from "dompurify";

const ReactQuillEditor = dynamic(() => import("@/src/components/react-quill-editor"), {
  ssr: false,
  loading: () => <p>Loading editor...</p>,
});

const validationSchema = Yup.object({
  description: Yup.string()
    .test(
      "not-empty",
      "Nội dung không được để trống",
      (value) => !!value && stripHtmlAndSpaces(value).length > 0
    )
    .max(4000, "Nội dung không được vượt quá 4000 ký tự"),
  footerText: Yup.string()
    .test(
      "not-empty",
      "Nội dung không được để trống",
      (value) => !!value && stripHtmlAndSpaces(value).length > 0
    )
    .max(4000, "Nội dung không được vượt quá 4000 ký tự"),
  content: Yup.string()
    .test(
      "not-empty",
      "Nội dung không được để trống",
      (value) => !!value && stripHtmlAndSpaces(value).length > 0
    )
    .max(4000, "Nội dung không được vượt quá 4000 ký tự"),
});

const Dashboard = () => {
  const storeId = useStoreId();
  const { getGroups } = useMedia();
  const { getRecruitmentPage, updateRecruitmentPage } = useAffiliation();
  const snackbar = useSnackbar();

  const [image, setImage] = useState(null);
  const [imageFileUpload, setImageFileUpload] = useState(null);
  const [copied, setCopied] = useState(false);
  const [defaultGroupId, setDefaultGroupId] = useState("");

  const link = "https://cuahang.evotech.vn/";

  useEffect(() => {
    async function fetchDefaultGroup() {
      try {
        const data = {
          ShopId: storeId,
          Skip: 0,
          Limit: 1,
        };
        const response = await getGroups(data);
        if (response?.data?.data?.length > 0) {
          setDefaultGroupId(response.data.data[0].groupFileId);
        }
      } catch (err) {
        console.error(err);
      }
    }

    if (storeId) {
      fetchDefaultGroup();
    }
  }, [storeId]);

  const [initialValues, setInitialValues] = useState({
    description: "Chào mừng bạn đến với chương trình Affiliate",
    footerText: "Đăng ký Affiliate",
    content: "",
  });

  const fetchRecruitmentPage = useCallback(async () => {
    if (!storeId) return;
    try {
      const response = await getRecruitmentPage({ shopId: storeId });
      if (response?.data?.data) {
        const data = response.data.data;
        setInitialValues({
          description: data.title ?? "",
          footerText: data.navBarContent ?? "",
          content: DOMPurify.sanitize(data.content) ?? "",
        });
        setImage(data.bannerFilePath ?? "");
      }
    } catch (error) {
      console.error(error);
      snackbar.error("Lỗi khi tải dữ liệu trang tuyển dụng");
    }
  }, [storeId]);

  useEffect(() => {
    fetchRecruitmentPage();
  }, [storeId]);

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const url = URL.createObjectURL(file);
      setImage(url);
      setImageFileUpload(file);
    }
  };

  const formik = useFormik({
    enableReinitialize: true,
    initialValues,
    validationSchema,
    onSubmit: async (values) => {
      const dataToSend: RecruitmentBody = {
        shopId: storeId,
        title: values.description,
        navBarContent: values.footerText,
        content: values.content,
      };

      if (imageFileUpload) {
        dataToSend.bannerFileImage = imageFileUpload;
      }

      try {
        const response = await updateRecruitmentPage(dataToSend);
        if (response?.data) {
          snackbar.success("Cập nhật thành công");
          fetchRecruitmentPage();
        }
      } catch (error) {
        snackbar.error("Cập nhật thất bại, vui lòng thử lại");
        console.error(error);
      }
    },
  });

  const handleCopy = () => {
    navigator.clipboard.writeText(link);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const debouncedSetContent = useCallback(
    debounce((value) => {
      formik.setFieldValue("content", value);
    }, 500),
    [formik.setFieldValue]
  );

  const handleContentChange = (value) => {
    debouncedSetContent(value);
  };

  return (
    <Container maxWidth={false} sx={{ display: "flex", flexDirection: "column" }}>
      <Box sx={{ display: "flex", gap: 4, flexDirection: { xs: "column", lg: "row" } }}>
        <Box
          sx={{
            flexGrow: 1,
            bgcolor: "#fff",
            borderRadius: 2,
            width: { xs: "100%", lg: "70%" },
          }}
          component="form"
          onSubmit={formik.handleSubmit}
        >
          <Paper
            sx={{
              mb: 3,
              boxShadow: "none",
              backgroundColor: "#fff",
              width: "100%",
            }}
          >
            <Stack direction="row" justifyContent="space-between" alignItems="center" mb={1}>
              <Typography fontWeight="bold" fontSize={18}>
                Liên kết trang
              </Typography>
              <Button
                onClick={handleCopy}
                sx={{
                  fontSize: 14,
                  fontWeight: 400,
                  color: "#2654FE",
                  textTransform: "none",
                  "&:hover": { textDecoration: "underline" },
                }}
              >
                {copied ? "Đã sao chép" : "Sao chép"}
              </Button>
            </Stack>
            <TextField
              fullWidth
              value={link}
              InputProps={{
                readOnly: true,
                sx: { fontSize: 14, color: "#757575", borderRadius: 1 },
              }}
            />
          </Paper>
          <Typography variant="h6" fontWeight="700" mb={2}>
            Thiết lập trang tuyển dụng
          </Typography>

          <Box
            sx={{
              textAlign: "center",
              minHeight: 200,
              my: 2,
              background: "#F5F5F5",
              p: 3,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              flexDirection: "column",
              borderRadius: 2,
            }}
          >
            <input
              id="upload-button"
              type="file"
              accept="image/*"
              style={{ display: "none" }}
              onChange={handleImageUpload}
            />
            {!image ? (
              <>
                <label htmlFor="upload-button">
                  <Button
                    variant="outlined"
                    component="span"
                    sx={{
                      color: "#2654FE",
                      fontWeight: "700",
                      fontSize: 16,
                      textTransform: "none",
                      boxShadow: "none",
                      border: "1px solid #2654FE",
                      p: "15px 30px",
                      "&:hover": {
                        backgroundColor: "#2654FE",
                        color: "#fff",
                      },
                    }}
                  >
                    Tải ảnh lên
                  </Button>
                </label>
                <Typography color="textSecondary" fontSize={12} mt={2}>
                  Bạn có thể tải lên hình ảnh JPG, PNG hoặc GIF. Kích thước tiêu chuẩn 16:9
                </Typography>
              </>
            ) : (
              <label htmlFor="upload-button" style={{ cursor: "pointer" }}>
                <img
                  src={image}
                  alt="Banner"
                  style={{ width: "100%", maxHeight: 200, objectFit: "contain", marginTop: 10 }}
                />
              </label>
            )}
          </Box>

          {/* Mô tả */}
          <Typography fontWeight="bold" fontSize={18} mb={1}>
            Tiêu đề{" "}
            <Typography component="span" color="error">
              *
            </Typography>
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={1}
            name="description"
            value={formik.values.description}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.description && Boolean(formik.errors.description)}
            helperText={formik.touched.description && formik.errors.description}
            placeholder="Chào mừng bạn đến với chương trình Affiliate"
            sx={{ mb: 3 }}
          />

          <Typography fontWeight="bold" fontSize={18} mb={1}>
            Nội dung{" "}
            <Typography component="span" color="error">
              *
            </Typography>
          </Typography>
          <ReactQuillEditor
            value={formik.values.content}
            onChange={handleContentChange}
            shopId={storeId}
            defaultGroupId={defaultGroupId}
            error={formik.touched.content && formik.errors.content ? formik.errors.content : ""}
          />
          {formik.touched.content && formik.errors.content && (
            <Typography color="error" variant="caption" sx={{ mt: 1, mb: 2 }}>
              {formik.errors.content}
            </Typography>
          )}

          <Typography fontWeight="bold" fontSize={18} mt={3} mb={1}>
            Nội dung thanh điều hướng{" "}
            <Typography component="span" color="error">
              *
            </Typography>
          </Typography>
          <TextField
            fullWidth
            name="footerText"
            value={formik.values.footerText}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.footerText && Boolean(formik.errors.footerText)}
            helperText={formik.touched.footerText && formik.errors.footerText}
            sx={{ mb: 3 }}
          />
        </Box>

        <Box
          sx={{
            width: { xs: "100%", lg: "40%" },
            bgcolor: "#FAFAFA",
            border: "1px solid #D9D9D9",
            borderRadius: 2,
            mt: { xs: 3, lg: 0 },
            height: "100%",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Box
            sx={{
              bgcolor: "primary.main",
              color: "white",
              p: 2,
              borderTopLeftRadius: 8,
              borderTopRightRadius: 8,
              display: "flex",
              alignItems: "center",
              gap: 1,
            }}
          >
            <IconButton sx={{ color: "white", p: 0 }}>
              <ArrowBackIcon />
            </IconButton>
            <Typography variant="subtitle1" fontWeight={600}>
              Đăng ký đối tác
            </Typography>
            <Box sx={{ flexGrow: 1 }} />
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                bgcolor: "#EAEAEA",
                borderRadius: 50,
                padding: "4px 8px",
              }}
            >
              <MoreHorizIcon sx={{ color: "gray" }} />
              <Box sx={{ width: 1, height: 12, backgroundColor: "gray", mx: 1 }} />
              <IconButton size="small" sx={{ color: "gray" }}>
                <CloseIcon fontSize="small" />
              </IconButton>
            </Box>
          </Box>

          {image && (
            <Box
              component="img"
              src={image}
              alt="Banner"
              sx={{ width: "100%", objectFit: "cover", mt: 1, maxHeight: 250 }}
            />
          )}

          <Box
            sx={{
              p: 2,
              bgcolor: "white",
              flexGrow: 1,
              display: "flex",
              flexDirection: "column",
              justifyContent: "space-between",
            }}
          >
            <Box>
              <Typography
                variant="h6"
                fontWeight={700}
                textAlign="center"
                borderBottom={1}
                borderColor="#ddd"
                pb={1}
              >
                {formik.values.description}
              </Typography>
              <Box
                sx={{ mt: 2, height: 400, overflowY: "auto", color: "black" }}
                dangerouslySetInnerHTML={{ __html: formik.values.content }}
              />
            </Box>

            <Box>
              <FormControlLabel
                control={<Checkbox />}
                label="Tôi đã hiểu và đồng ý với điều khoản Affiliate"
                sx={{ mt: 2 }}
              />
              <Button
                variant="contained"
                sx={{
                  width: "100%",
                  mt: 2,
                  borderRadius: 30,
                  fontWeight: 500,
                  fontSize: 14,
                  backgroundColor: "primary",
                }}
              >
                Đăng ký Affiliate
              </Button>
            </Box>
          </Box>
        </Box>
      </Box>
      <Box sx={{ width: "100%", display: "flex", justifyContent: "flex-end" }}>
        <Button
          variant="contained"
          type="submit"
          onClick={(e) => {
            e.preventDefault();
            formik.submitForm();
          }}
          sx={{
            mt: 2,
            width: "150px",
            borderRadius: 30,
            fontWeight: 500,
            fontSize: 14,
            backgroundColor: "primary",
          }}
        >
          Xuất bản
        </Button>
      </Box>
    </Container>
  );
};

export default Dashboard;
