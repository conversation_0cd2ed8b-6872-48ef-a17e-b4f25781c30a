import React from "react";
import SettingLayout from "@/src/components/settings/settings-page/SettingLayout";
import { Typography } from "@mui/material";
import Grid from "@mui/system/Grid";
import CombinedInvoice from "@/src/components/settings/settings-page/CombinedInvoice";
import SettingTax from "./tax";

export default function SettingInvoice() {
  return (
    <SettingLayout>
      <Grid sx={{ ml: 1 }}>
        <SettingTax />
      </Grid>
      <Grid>
        <Typography variant="h5" gutterBottom sx={{ ml: 1 }}>
          Thiết lập hóa đơn điện tử
        </Typography>
      </Grid>
      <Grid sx={{ ml: 1 }}>
        <CombinedInvoice />
      </Grid>
    </SettingLayout>
  );
}
