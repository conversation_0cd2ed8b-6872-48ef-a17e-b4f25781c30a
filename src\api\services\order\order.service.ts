import { apiClient } from "@/src/api/config/api-client";
import { API_PATHS } from "@/src/api/constants/api-paths";
import type { ApiError, ErrorConfig } from "@/src/api/types/error.types";
import type { ExtendedRequestConfig } from "@/src/api/types/api.types";
import { GetProductRequest, GetProductRequestBody } from "@/src/api/types/product.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export const orderService = {
  createOrder: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(API_PATHS.ORDER.CREATE_ORDER, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  listOrder: async <T = any>(params, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    const searchTerm = params.data?.search?.trim();
    if (typeof searchTerm === "string" && searchTerm.length === 0 && params?.data?.search) {
      return;
    }

    const queryParams = new URLSearchParams({
      skip: params.skip,
      limit: params.limit,
      ...(params.data?.itemsType && { itemsType: params.data.itemsType }),
      ...(params.data?.shopId && { shopId: params.data.shopId }),
      ...(params.data?.search && { search: params.data.search }),
      ...(params.data?.statusOrder && { statusOrder: params.data.statusOrder }),
      ...(params.data?.statusTransport && { transportStatus: params.data.statusTransport }),
      ...(params.data?.statusPay && { payStatus: params.data.statusPay }),
      ...(params.data?.userId && { userId: params.data.userId }),
      ...(params.data?.orderSource && { orderSource: params.data.orderSource }),
      ...(params.data?.fromDate && { fromDate: params.data.fromDate }),
      ...(params.data?.toDate && { toDate: params.data.toDate }),
    });

    try {
      const response = await apiClient.get(
        `${API_PATHS.ORDER.LIST_ORDER}?${queryParams.toString()}`,

        params.data,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getOrder: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.ORDER.GET_ORDER}?orderId=${data.orderId}&shopId=${data.shopId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  completeShippingItems: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<any, T>(API_PATHS.ORDER.UPDATE_ORDER_INFO, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateNotes: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<any, T>(API_PATHS.ORDER.UPDATE_ORDER_INFO, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  cancelOrder: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<any, T>(API_PATHS.ORDER.UPDATE_ORDER_INFO, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updatePaidOrder: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<any, T>(API_PATHS.ORDER.UPDATE_ORDER_INFO, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  listOrderByUserId: async <T = any>(params, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get(
        `${API_PATHS.ORDER.LIST_ORDER_BY_USER_ID}?skip=${params.skip}&limit=${params.limit}&userId=${params.data?.userId}&search=${params.data?.search}`,
        params.data,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  printTransportOrders: async <T = any>(data, errorConfig?: ErrorConfig) => {
    const config: any = {
      errorHandling: errorConfig,
      responseType: "blob",
    };

    try {
      const response = await apiClient.post(
        `${API_PATHS.ORDER.PRINT_TRANSPORT_ORDERS}`,

        data,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  exportOrderExcel: async <T = any>(params, errorConfig?: ErrorConfig) => {
    const queryParams = new URLSearchParams({
      ...(params?.itemsType && { itemsType: params.itemsType }),
      ...(params?.shopId && { shopId: params.shopId }),
      ...(params?.search && { search: params.search }),
      ...(params?.statusOrder && { statusOrder: params.statusOrder }),
      ...(params?.statusTransport && { transportStatus: params.statusTransport }),
      ...(params?.statusPay && { payStatus: params.statusPay }),
      ...(params?.userId && { userId: params.userId }),
      ...(params?.orderSource && { orderSource: params.orderSource }),
      ...(params?.fromDate && { fromDate: params.fromDate }),
      ...(params?.toDate && { toDate: params.toDate }),
    });
    const config: any = {
      errorHandling: errorConfig,
      responseType: "blob",
    };

    try {
      const response = await apiClient.get(
        `${API_PATHS.ORDER.EXPORT_ORDER_EXCEL}?${queryParams.toString()}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
