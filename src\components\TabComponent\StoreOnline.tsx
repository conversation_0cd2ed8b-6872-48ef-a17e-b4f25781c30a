import React, { useState } from "react";
import { Box, Select, MenuItem, Stack, Typography } from "@mui/material";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider, DatePicker } from "@mui/x-date-pickers-pro";
import { ExpandMore } from "@mui/icons-material";

const StoreOnline = ({
  selectedDay,
  setSelectedDay,
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  dataOrder,
}) => {
  // const [filter, setFilter] = useState('Hôm nay');
  const [open, setOpen] = useState(false);
  const [chartDateRange, setChartDateRange] = useState([null, null]);
  const [filterData, setFilterData] = useState<any>();
  const handleChartStartDateChange = (newValue) => {
    const endDate = chartDateRange[1];
    const startD = newValue.startOf("day").format("YYYY-MM-DD HH:mm:ss");
    let updatedRange;

    if (!endDate || newValue.isAfter(endDate)) {
      updatedRange = [newValue, newValue];
      setEndDate(startD);
      setStartDate(startD);
    } else {
      const endD = endDate.startOf("day").format("YYYY-MM-DD HH:mm:ss");
      updatedRange = [newValue, endDate];
      setEndDate(endD);
      setStartDate(startD);
    }
    setSelectedDay("");
    setChartDateRange(updatedRange);
  };

  const handleChartEndDateChange = (newValue) => {
    const startDate = chartDateRange[0];
    const endD = newValue.startOf("day").format("YYYY-MM-DD HH:mm:ss");
    let updatedRange;

    if (!startDate || newValue.isBefore(startDate, "day")) {
      updatedRange = [newValue, newValue];
      setEndDate(endD);
      setStartDate(endD);
    } else {
      const startD = startDate.startOf("day").format("YYYY-MM-DD HH:mm:ss");
      updatedRange = [startDate, newValue];
      setEndDate(endD);
      setStartDate(startD);
    }
    setSelectedDay("");
    setChartDateRange(updatedRange);
  };

  const logos = [
    "/logo/logo-dashboard/Group 48353.svg",
    "/logo/logo-dashboard/Icon (8).svg",
    "/logo/logo-dashboard/Group 48352.svg",
    "/logo/logo-dashboard/Group 48351.svg",
  ];

  return (
    <Box sx={{ p: 3, borderRadius: "20px", background: "#fff", marginTop: "-15px" }}>
      <Box
        display="flex"
        justifyContent="flex-end"
        gap={2}
        mb={3}
        sx={{
          "@media(max-width: 600px)": {
            flexDirection: "column",
          },
        }}
      >
        <Select
          value={selectedDay}
          onChange={(e) => {
            setStartDate("");
            setEndDate("");
            setChartDateRange([]);
            setSelectedDay(e.target.value);
          }}
          displayEmpty
          defaultValue={selectedDay}
          size="small"
          sx={{ minWidth: 120, height: 36, backgroundColor: "#fff" }}
          IconComponent={(props) => <ExpandMore {...props} sx={{ color: "#000" }} />}
        >
          <MenuItem value="TODAY">Hôm nay</MenuItem>
          <MenuItem value="YESTERDAY">Hôm qua</MenuItem>
          <MenuItem value="LAST_7_DAYS">7 ngày qua</MenuItem>
          <MenuItem value="LAST_30_DAYS">1 tháng qua</MenuItem>
        </Select>
        <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="vi">
          <Box sx={{ display: "flex" }}>
            <DatePicker
              value={chartDateRange[0]}
              format="DD/MM/YYYY"
              onChange={handleChartStartDateChange}
              slotProps={{
                textField: {
                  sx: { height: 36, paddingLeft: 0, width: 180 },
                  InputProps: { sx: { height: 36 } },
                },
              }}
            />
            <Typography sx={{ paddingLeft: 1, paddingRight: 1, marginTop: 0.5 }}>-</Typography>
            <DatePicker
              value={chartDateRange[1]}
              format="DD/MM/YYYY"
              onChange={handleChartEndDateChange}
              shouldDisableDate={(date) => date.isBefore(chartDateRange[1], "day")}
              slotProps={{
                textField: {
                  sx: { height: 36, paddingLeft: 0, width: 180 },
                  InputProps: { sx: { height: 36 } },
                },
              }}
            />
          </Box>
        </LocalizationProvider>
      </Box>
      <Stack
        flexDirection={"row"}
        gap={"50px"}
        sx={{
          "@media(max-width: 980px)": {
            flexWrap: "wrap",
            // gap: '2%',
          },
        }}
      >
        {dataOrder.length > 0 &&
          dataOrder.map((item, index) => (
            <Stack
              key={index}
              flexDirection={"row"}
              alignItems={"start"}
              justifyContent={"space-between"}
              padding={"20px"}
              borderRadius={"20px"}
              bgcolor={"#F4F5F9"}
              minHeight={"150px"}
              width={"25%"}
              boxShadow={"6px 6px 54px 0 #0000000D !important;"}
              sx={{
                "@media(max-width: 980px)": {
                  width: "45%",
                },
                "@media(max-width: 600px)": {
                  width: "100%",
                },
              }}
            >
              <Box>
                <Typography sx={{ color: "#202224", fontSize: "16px", fontWeight: "400" }}>
                  {item.title}
                </Typography>
                <Typography sx={{ color: "#202224", fontSize: "28px", fontWeight: "700" }}>
                  {item.value}
                </Typography>
              </Box>

              <img
                src={logos[index] || "/logo/logo-dashboard/default.svg"} // fallback if index exceeds length
                alt="Logo"
                style={{ width: "60px", height: "60px" }}
              />
            </Stack>
          ))}

        {/* <Stack
          flexDirection={'row'}
          alignItems={'start'}
          justifyContent={'space-between'}
          padding={'20px'}
          borderRadius={'20px'}
          bgcolor={'#F4F5F9'}
          minHeight={'150px'}
          width={'25%'}
          boxShadow={'6px 6px 54px 0 #0000000D !important;'}
          sx={{
            '@media(max-width: 980px)': {
              width: '45%',
            },
            '@media(max-width: 600px)': {
              width: '100%',
            },
          }}
        >
          <Box>
            <Typography sx={{ color: '#202224', fontSize: '16px', fontWeight: '400' }}>
              Đang xử lý
            </Typography>
            <Typography sx={{ color: '#202224', fontSize: '28px', fontWeight: '700' }}>
              50
            </Typography>
          </Box>
          <img
            src="/logo/logo-dashboard/Icon (8).svg"
            alt="Logo"
            style={{ width: '60px', height: '60px' }}
          />
        </Stack>
        <Stack
          flexDirection={'row'}
          alignItems={'start'}
          justifyContent={'space-between'}
          padding={'20px'}
          borderRadius={'20px'}
          bgcolor={'#F4F5F9'}
          minHeight={'150px'}
          width={'25%'}
          boxShadow={'6px 6px 54px 0 #0000000D !important;'}
          sx={{
            '@media(max-width: 980px)': {
              width: '45%',
            },
            '@media(max-width: 600px)': {
              width: '100%',
            },
          }}
        >
          <Box>
            <Typography sx={{ color: '#202224', fontSize: '16px', fontWeight: '400' }}>
              Đang giao hàng
            </Typography>
            <Typography sx={{ color: '#202224', fontSize: '28px', fontWeight: '700' }}>
              50
            </Typography>
          </Box>
          <img
            src="/logo/logo-dashboard/Group 48352.svg"
            alt="Logo"
            style={{ width: '60px', height: '60px' }}
          />
        </Stack>
        <Stack
          flexDirection={'row'}
          alignItems={'start'}
          justifyContent={'space-between'}
          padding={'20px'}
          borderRadius={'20px'}
          bgcolor={'#F4F5F9'}
          minHeight={'150px'}
          width={'25%'}
          boxShadow={'6px 6px 54px 0 #0000000D !important;'}
          sx={{
            '@media(max-width: 980px)': {
              width: '45%',
            },
            '@media(max-width: 600px)': {
              width: '100%',
            },
          }}
        >
          <Box>
            <Typography sx={{ color: '#202224', fontSize: '16px', fontWeight: '400' }}>
              Hoàn hủy
            </Typography>
            <Typography sx={{ color: '#202224', fontSize: '28px', fontWeight: '700' }}>
              50
            </Typography>
          </Box>
          <img
            src="/logo/logo-dashboard/Group 48351.svg"
            alt="Logo"
            style={{ width: '60px', height: '60px' }}
          />
        </Stack> */}
      </Stack>
    </Box>
  );
};

export default StoreOnline;
