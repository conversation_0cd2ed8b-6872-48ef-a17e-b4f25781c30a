import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Paper,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Avatar,
  Typography,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Radio,
  RadioGroup,
  FormControlLabel,
  InputAdornment,
  Select,
  MenuItem,
  TablePagination,
  Tooltip,
} from "@mui/material";
import UserPointDetail from "./PointDetails";
import { useMembershipLevel } from "@/src/api/hooks/membership-level/use-membership-level";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useSnackbar } from "notistack";
import { Search as SearchIcon } from "@mui/icons-material";
import { ExchangeHistoryType } from "src/api/types/membership.types";
import { LocalizationProvider } from "@mui/x-date-pickers";
import _ from "lodash";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import { usePathname } from "next/navigation";
import EditIcon from "@mui/icons-material/Edit";
import { Close, ContentPaste } from "@mui/icons-material";
import ModalChangePoint from "./ModalChangePoint";
import { useDebounce } from "@/src/hooks/use-debounce";
import TruncatedText from "@/src/components/truncated-text/truncated-text";

interface FilterData {
  skip?: number;
  limit?: number;
  shopId?: string;
  search?: string;
}
export interface AdjustPointState {
  adjustType: "add" | "subtract";
  points: string;
  reason: string;
  isSubmitting: boolean;
}

const UserPointsTab = () => {
  const pathname = usePathname();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [adjustPointData, setAdjustPointData] = useState<AdjustPointState>({
    adjustType: "add",
    points: "",
    reason: "",
    isSubmitting: false,
  });
  const [viewDetail, setViewDetail] = useState(false);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const { getUserPoints, updateUserPoints } = useMembershipLevel();
  const storeId = useStoreId();
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("ALL");
  const [totalCount, setTotalCount] = useState(0);
  const [filterData, setFilterData] = useState<FilterData | undefined>();
  const debouncedSearchValue = useDebounce(searchTerm, 500);

  const { enqueueSnackbar } = useSnackbar();

  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  const fetchUserPoints = async (currentPage, pageSize, shopId, searchTerm) => {
    try {
      const skip = currentPage * pageSize;
      const limit = pageSize;
      setLoading(true);
      const data = {
        skip,
        limit,
        shopId,
        search: debouncedSearchValue,
      };
      setFilterData((prev: FilterData | undefined) => ({
        ...(prev || {}),
        skip,
        limit,
        shopId,
      }));
      const response = await getUserPoints(data);

      if (response?.data) {
        const formattedUsers = response.data.data.map((user) => ({
          id: user.userId,
          name: user.fullName,
          phone: user.phone,
          points: user.currentPoint,
          currentPoints: user.totalPoint,
          spentPoints: user.spentPoint,
          totalSpent: user.spentAmount?.toLocaleString(),
          avatar: user.avatar,
          shopId: storeId,
          membershipLevel: user?.membershipLevel,
        }));
        setUsers(formattedUsers);
        setTotalCount(response.data.total || 0);
      }
    } catch (error) {
      console.error("Error fetching user points:", error);
    } finally {
      setLoading(false);
    }
  };
  const handleUpdatePoints = async () => {
    try {
      setAdjustPointData((prev) => ({ ...prev, isSubmitting: true }));

      // Validate
      if (!adjustPointData.points || !adjustPointData.reason) {
        enqueueSnackbar("Vui lòng nhập đầy đủ thông tin", { variant: "error" });
        return;
      }

      const updateData = {
        shopId: storeId,
        userId: selectedUser.id,
        note: adjustPointData.reason,
        point:
          adjustPointData.adjustType === "add" ? adjustPointData.points : -adjustPointData.points,
        isAdd: adjustPointData.adjustType === "add",
        type: "Adjust",
      };

      const response = await updateUserPoints(updateData);
      if (response?.data.status === true) {
        enqueueSnackbar("Điều chỉnh điểm thành công", { variant: "success" });
        handleCloseDialog();
        fetchUserPoints(page, rowsPerPage, storeId, searchTerm);
      }
    } catch (error) {
      console.error("Error updating points:", error);
      enqueueSnackbar(error?.response?.data?.message, { variant: "error" });
    } finally {
      setAdjustPointData((prev) => ({ ...prev, isSubmitting: false }));
    }
  };

  useEffect(() => {
    if (!storeId) return;
    fetchUserPoints(page, rowsPerPage, storeId, debouncedSearchValue);
  }, [storeId]);

  const handleOpenDialog = (user) => {
    setSelectedUser(user);
    setOpenDialog(true);
  };
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setAdjustPointData((prev) => ({
      ...prev,
      points: "",
      reason: "",
    }));
  };
  const handleViewDetail = (user) => {
    setSelectedUser(user);
    setViewDetail(true);
  };
  const handleBack = () => {
    setViewDetail(false);
    setSelectedUser(null);
  };
  const handleChangePage = (_event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const updateUser = async (userId) => {
    const data = {
      skip: 0,
      limit: 10,
      shopId: storeId,
      search: searchTerm,
    };
    const response = await getUserPoints(data);
    const formattedUsers = response.data.data.map((user) => ({
      id: user.userId,
      name: user.fullName,
      phone: user.phone,
      points: user.currentPoint,
      currentPoints: user.totalPoint,
      spentPoints: user.spentPoint,
      totalSpent: user.spentAmount?.toLocaleString(),
      avatar: user.avatar,
      shopId: storeId,
      membershipLevel: user?.membershipLevel,
    }));
    const fUser = formattedUsers.filter((item) => item.id === userId);
    setSelectedUser(fUser[0]);
  };

  useEffect(() => {
    if (!storeId) return;
    debouncedFetchMembershipLevelList(page, rowsPerPage, storeId);
    return () => {
      debouncedFetchMembershipLevelList.cancel();
    };
  }, [storeId, page, rowsPerPage, debouncedSearchValue]);

  const debouncedFetchMembershipLevelList = useCallback(
    _.debounce((currentPage, pageSize, shopId) => {
      fetchUserPoints(currentPage, pageSize, shopId, debouncedSearchValue);
    }, 400),
    [storeId, debouncedSearchValue]
  );
  return (
    <Box>
      {viewDetail ? (
        <UserPointDetail
          user={selectedUser}
          onBack={handleBack}
          setUser={setSelectedUser}
          updateUser={updateUser}
        />
      ) : (
        <>
          <Stack direction={"row"} gap={2} mb={2}>
            <TextField
              fullWidth
              placeholder="Tìm kiếm tên, số điện thoại khách hàng"
              variant="outlined"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ backgroundColor: "white", borderRadius: 1 }}
            />
          </Stack>
          <TableContainer component={Paper} sx={{ mt: 2, borderRadius: 2 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ width: 5 }}>STT</TableCell>
                  <TableCell sx={{ width: 300 }}>Tên khách hàng</TableCell>
                  <TableCell sx={{ width: 100 }}>Số điện thoại</TableCell>
                  <TableCell sx={{ width: 150 }}>Tổng điểm đã tích lũy</TableCell>
                  <TableCell sx={{ width: 100 }}>Điểm hiện có</TableCell>
                  <TableCell sx={{ width: 100 }}>Điểm đã tiêu</TableCell>
                  <TableCell sx={{ width: 100 }}>Tổng tiền chi tiêu</TableCell>
                  <TableCell sx={{ width: 50 }}>Quản lý</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {users.map((user, index) => (
                  <TableRow key={user.id}>
                    <TableCell>{page * rowsPerPage + index + 1}</TableCell>
                    <TableCell>
                      <Stack direction="row" spacing={1} alignItems="center">
                        <Avatar src={user.avatar} alt={user.name} />
                        <Typography
                          sx={{ color: "#2654FE", fontWeight: 500, cursor: "pointer" }}
                          onClick={() => handleViewDetail(user)}
                        >
                          <TruncatedText
                            text={user.name}
                            isLink={true}
                            typographyProps={{ fontWeight: 600 }}
                          />
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>{user.phone}</TableCell>
                    <TableCell>{user.currentPoints}</TableCell>
                    <TableCell>{user.points}</TableCell>
                    <TableCell>{user.spentPoints}</TableCell>
                    <TableCell>{user.totalSpent}</TableCell>
                    <TableCell>
                      {isGranted(pathname, PERMISSION_TYPE_ENUM.Edit) ? (
                        <IconButton
                          onClick={() => handleOpenDialog(user)}
                          size="small"
                          sx={{ color: "primary.main" }}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      ) : (
                        <Tooltip title="Bạn không có quyền sửa">
                          <span>
                            <IconButton size="small" sx={{ color: "primary.main" }} disabled={true}>
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </span>
                        </Tooltip>
                      )}
                      <IconButton onClick={() => handleViewDetail(user)} color="primary">
                        <ContentPaste />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <TablePagination
              rowsPerPageOptions={rowPerPageOptionsDefault}
              component="div"
              count={totalCount}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              sx={{ width: "100%" }}
              labelRowsPerPage="Số dòng mỗi trang"
            />
          </TableContainer>
        </>
      )}

      <ModalChangePoint
        openDialog={openDialog}
        user={selectedUser}
        handleCloseDialog={handleCloseDialog}
        adjustPointData={adjustPointData}
        setAdjustPointData={setAdjustPointData}
        handleUpdatePoints={handleUpdatePoints}
      />
    </Box>
  );
};

export default UserPointsTab;
