import { createContext } from 'react';
import { Issuer } from 'src/utils/auth';


// Initial authentication state
export const initialState = {
  isAuthenticated: false,
  isInitialized: false,
  user: null,
};

// Create AuthContext with default values
export const AuthContext = createContext({
  ...initialState,
  issuer: Issuer.JWT,
  signIn: () => Promise.resolve(),
  signOut: () => Promise.resolve(),
  forgotPassword: () => Promise.resolve(),
  resetPassword: () => Promise.resolve(),
});