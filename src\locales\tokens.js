export const tokens = {
  common: {
    languageChanged: "common.languageChanged",
    cancel: "common.cancel",
    delete: "common.delete",
    save: "common.save",
    none: "common.none",
    validation: {
      maxLength: "common.validation.maxLength",
      maxNumber: "common.validation.maxNumber",
      minValue: "common.validation.minValue",
    },
  },
  nav: {
    academy: "nav.academy",
    account: "nav.account",
    analytics: "nav.analytics",
    auth: "nav.auth",
    blog: "nav.blog",
    browse: "nav.browse",
    calendar: "nav.calendar",
    chat: "nav.chat",
    checkout: "nav.checkout",
    concepts: "nav.concepts",
    contact: "nav.contact",
    course: "nav.course",
    create: "nav.create",
    crypto: "nav.crypto",
    customers: "nav.customers",
    dashboard: "nav.dashboard",
    details: "nav.details",
    ecommerce: "nav.ecommerce",
    edit: "nav.edit",
    error: "nav.error",
    feed: "nav.feed",
    fileManager: "nav.fileManager",
    files: "nav.files",
    finance: "nav.finance",
    fleet: "nav.fleet",
    forgotPassword: "nav.forgotPassword",
    invoiceList: "nav.invoices",
    jobList: "nav.jobList",
    kanban: "nav.kanban",
    list: "nav.list",
    login: "nav.login",
    logistics: "nav.logistics",
    mail: "nav.mail",
    management: "nav.management",
    orderList: "nav.orders",
    overview: "nav.overview",
    pages: "nav.pages",
    postCreate: "nav.postCreate",
    postDetails: "nav.postDetails",
    postList: "nav.postList",
    pricing: "nav.pricing",
    productList: "nav.products",
    profile: "nav.profile",
    register: "nav.register",
    resetPassword: "nav.resetPassword",
    socialMedia: "nav.socialMedia",
    verifyCode: "nav.verifyCode",
  },
  auth: {
    loginWithEmail: "auth.loginWithEmail",
    loginWithPhone: "auth.loginWithPhone",
    emailLabel: "auth.emailLabel",
    phoneLabel: "auth.phoneLabel",
    passwordLabel: "auth.passwordLabel",
    forgotPassword: "auth.forgotPassword",
    loginButton: "auth.loginButton",
    emailButton: "auth.emailButton",
    phoneButton: "auth.phoneButton",
    demoCredentials: "auth.demoCredentials",
    and: "auth.and",
    // Validation messages
    emailRequired: "auth.emailRequired",
    emailInvalid: "auth.emailInvalid",
    phoneRequired: "auth.phoneRequired",
    phoneInvalid: "auth.phoneInvalid",
    phoneMinLength: "auth.phoneMinLength",
    passwordRequired: "auth.passwordRequired",
  },
  forgotPassword: {
    title: "forgotPassword.title",
    description: "forgotPassword.description",
    phoneLabel: "forgotPassword.phoneLabel",
    submitButton: "forgotPassword.submitButton",
    verifyTitle: "forgotPassword.verifyTitle",
    codeLabel: "forgotPassword.codeLabel",
    verifyButton: "forgotPassword.verifyButton",
    resetTitle: "forgotPassword.resetTitle",
    newPasswordLabel: "forgotPassword.newPasswordLabel",
    confirmPasswordLabel: "forgotPassword.confirmPasswordLabel",
    successMessage: "forgotPassword.successMessage",
    loginButton: "forgotPassword.loginButton",
    backButton: "forgotPassword.backButton",
    phoneInvalid: "forgotPassword.phoneInvalid",
    phoneRequired: "forgotPassword.phoneRequired",
    codeInvalid: "forgotPassword.codeInvalid",
    codeRequired: "forgotPassword.codeRequired",
    newPasswordRequired: "forgotPassword.newPasswordRequired",
    confirmPasswordRequired: "forgotPassword.confirmPasswordRequired",
    passwordsMustMatch: "forgotPassword.passwordsMustMatch",
  },
  createStore: {
    title: "createStore.title",
    storeNameLabel: "createStore.storeNameLabel",
    businessTypeLabel: "createStore.businessTypeLabel",
    createButton: "createStore.createButton",
    // Add more tokens as needed
  },
  store: {
    title: "store.title",
    storeNameLabel: "store.storeNameLabel",
    businessTypeLabel: "store.businessTypeLabel",
    addressLabel: "store.addressLabel",
    workingTimeLabel: "store.workingTimeLabel",
    createButton: "store.createButton",
    joinDateLabel: "store.joinDateLabel",
    expiryDateLabel: "store.expiryDateLabel",
    statusLabel: "store.statusLabel",
    detailsButton: "store.detailsButton",
    emptyStateMessage: "store.emptyStateMessage",
    createStoreButton: "store.createStoreButton",
    // Add validation messages
    storeNameRequired: "store.storeNameRequired",
    businessTypeRequired: "store.businessTypeRequired",
    logoRequired: "store.logoRequired",
    createSuccess: "store.createSuccess",
    createError: "store.createError",
    deleteConfirmTitle: "store.deleteConfirmTitle",
    deleteConfirmMessage: "store.deleteConfirmMessage",
    deleteSuccess: "store.deleteSuccess",
    deleteError: "store.deleteError",
    activatedLabel: "store.activatedLabel",
  },
  businessTypes: {
    retail: "businessTypes.retail",
    wholesale: "businessTypes.wholesale",
    service: "businessTypes.service",
  },
  logoUpload: {
    uploadLogo: "logoUpload.uploadLogo",
    description: "logoUpload.description",
    errorProcessing: "logoUpload.errorProcessing",
    invalidFile: "logoUpload.invalidFile",
  },
  settings: {
    title: "settings.title",
    personalInfo: "settings.personalInfo",
    firstName: "settings.firstName",
    lastName: "settings.lastName",
    phone: "settings.phone",
    email: "settings.email",
    notificationEmail: "settings.notificationEmail",
    language: "settings.language",
    languageSection: "settings.languageSection",
    saveButton: "settings.saveButton",
    // Validation messages
    firstNameRequired: "settings.firstNameRequired",
    lastNameRequired: "settings.lastNameRequired",
    phoneRequired: "settings.phoneRequired",
    phoneInvalid: "settings.phoneInvalid",
    emailRequired: "settings.emailRequired",
    updateSuccess: "settings.updateSuccess",
    updateError: "settings.updateError",
  },
  contentManagement: {
    title: "contentManagement.title",
    tabs: {
      popupAds: "contentManagement.tabs.popupAds",
      articleCategories: "contentManagement.tabs.articleCategories",
      articles: "contentManagement.tabs.articles",
      shopOnline: "contentManagement.tabs.shopOnline",
      shopOffline: "contentManagement.tabs.shopOffline",
    },
    popupAds: {
      addNew: "contentManagement.popupAds.addNew",
      search: "contentManagement.popupAds.search",
      emptyStateMessage: "contentManagement.popupAds.emptyStateMessage",
      emptyStateButton: "contentManagement.popupAds.emptyStateButton",
      table: {
        number: "contentManagement.popupAds.table.number",
        name: "contentManagement.popupAds.table.name",
        runTime: "contentManagement.popupAds.table.runTime",
        views: "contentManagement.popupAds.table.views",
        creationDate: "contentManagement.popupAds.table.creationDate",
        status: "contentManagement.popupAds.table.status",
        actions: "contentManagement.popupAds.table.actions",
      },
      dialog: {
        deleteTitle: "contentManagement.popupAds.dialog.deleteTitle",
        deleteMessage: "contentManagement.popupAds.dialog.deleteMessage",
        deleteConfirm: "contentManagement.popupAds.dialog.deleteConfirm",
        deleteCancel: "contentManagement.popupAds.dialog.deleteCancel",
      },
      notification: {
        deleteSuccess: "contentManagement.popupAds.notification.deleteSuccess",
        deleteError: "contentManagement.popupAds.notification.deleteError",
      },
      snackbar: {
        copySuccess: "contentManagement.popupAds.snackbar.copySuccess",
        deleteSuccess: "contentManagement.popupAds.snackbar.deleteSuccess",
        deleteError: "contentManagement.popupAds.snackbar.deleteError",
      },
      emptyState: {
        title: "contentManagement.popupAds.emptyState.title",
        message: "contentManagement.popupAds.emptyState.message",
        subtitle: "contentManagement.popupAds.emptyState.subtitle",
        button: "contentManagement.popupAds.emptyState.button",
      },
      create: {
        title: "contentManagement.popupAds.create.title",
        editTitle: "contentManagement.popupAds.create.editTitle",
        success: "contentManagement.popupAds.create.success",
        form: {
          contentTitle: "contentManagement.popupAds.create.form.contentTitle",
          adContent: "contentManagement.popupAds.create.form.adContent",
          imageTitle: "contentManagement.popupAds.create.form.imageTitle",
          imageCaption: "contentManagement.popupAds.create.form.imageCaption",
          displayPages: "contentManagement.popupAds.create.form.displayPages",
          displayFrequency: "contentManagement.popupAds.create.form.displayFrequency",
          displayTime: "contentManagement.popupAds.create.form.displayTime",
          startDate: "contentManagement.popupAds.create.form.startDate",
          endDate: "contentManagement.popupAds.create.form.endDate",
          link: "contentManagement.popupAds.create.form.link",
          isActive: "contentManagement.popupAds.create.form.isActive",
          saveButton: "contentManagement.popupAds.create.form.saveButton",
          cancelButton: "contentManagement.popupAds.create.form.cancelButton",
          pages: {
            home: "contentManagement.popupAds.create.form.pages.home",
            products: "contentManagement.popupAds.create.form.pages.products",
            account: "contentManagement.popupAds.create.form.pages.account",
            success: "contentManagement.popupAds.create.form.pages.success",
          },
          frequency: {
            once: "contentManagement.popupAds.create.form.frequency.once",
            everyVisit: "contentManagement.popupAds.create.form.frequency.everyVisit",
          },
          nameRequired: "contentManagement.popupAds.create.form.nameRequired",
          nameMaxLength: "contentManagement.popupAds.create.form.nameMaxLength",
          imageRequired: "contentManagement.popupAds.create.form.imageRequired",
          linkRequired: "contentManagement.popupAds.create.form.linkRequired",
          linkInvalid: "contentManagement.popupAds.create.form.linkInvalid",
          linkMaxLength: "contentManagement.popupAds.create.form.linkMaxLength",
          dateRequired: "contentManagement.popupAds.create.form.dateRequired",
          endDateInvalid: "contentManagement.popupAds.create.form.endDateInvalid",
          createSuccess: "contentManagement.popupAds.create.form.createSuccess",
          createError: "contentManagement.popupAds.create.form.createError",
        },
      },
      deleteConfirmTitle: "contentManagement.popupAds.deleteConfirmTitle",
      deleteConfirmMessage: "contentManagement.popupAds.deleteConfirmMessage",
      deleteSuccess: "contentManagement.popupAds.deleteSuccess",
      deleteError: "contentManagement.popupAds.deleteError",
      edit: {
        success: "contentManagement.popupAds.edit.success",
        error: "contentManagement.popupAds.edit.error",
      },
    },
    articleCategory: {
      create: {
        title: "contentManagement.articleCategory.create.title",
        editTitle: "contentManagement.articleCategory.create.editTitle",
        success: "contentManagement.articleCategory.create.success",
        form: {
          contentTitle: "contentManagement.articleCategory.create.form.contentTitle",
          categoryName: "contentManagement.articleCategory.create.form.categoryName",
          isActive: "contentManagement.articleCategory.create.form.isActive",
          saveButton: "contentManagement.articleCategory.create.form.saveButton",
          cancelButton: "contentManagement.articleCategory.create.form.cancelButton",
          nameRequired: "contentManagement.articleCategory.create.form.nameRequired",
          nameMaxLength: "contentManagement.articleCategory.create.form.nameMaxLength",
          createSuccess: "contentManagement.articleCategory.create.form.createSuccess",
          createError: "contentManagement.articleCategory.create.form.createError",
        },
      },
      edit: {
        success: "contentManagement.articleCategory.edit.success",
        error: "contentManagement.articleCategory.edit.error",
      },
      addNew: "contentManagement.articleCategory.addNew",
      search: "contentManagement.articleCategory.search",
      table: {
        number: "contentManagement.articleCategory.table.number",
        name: "contentManagement.articleCategory.table.name",
        creationDate: "contentManagement.articleCategory.table.creationDate",
        status: "contentManagement.articleCategory.table.status",
        actions: "contentManagement.articleCategory.table.actions",
      },
      emptyState: {
        title: "contentManagement.articleCategory.emptyState.title",
        message: "contentManagement.articleCategory.emptyState.message",
        subtitle: "contentManagement.articleCategory.emptyState.subtitle",
        button: "contentManagement.articleCategory.emptyState.button",
      },
      deleteConfirmTitle: "contentManagement.articleCategory.deleteConfirmTitle",
      deleteConfirmMessage: "contentManagement.articleCategory.deleteConfirmMessage",
      deleteSuccess: "contentManagement.articleCategory.deleteSuccess",
      deleteError: "contentManagement.articleCategory.deleteError",
      delete: {
        confirmTitle: "contentManagement.articleCategory.delete.confirmTitle",
        confirmMessage: "contentManagement.articleCategory.delete.confirmMessage",
        successMessage: "contentManagement.articleCategory.delete.successMessage",
        errorMessage: "contentManagement.articleCategory.delete.errorMessage",
      },
    },
    article: {
      create: {
        title: "contentManagement.article.create.title",
        editTitle: "contentManagement.article.create.editTitle",
        success: "contentManagement.article.create.success",
        form: {
          contentTitle: "contentManagement.article.create.form.contentTitle",
          title: "contentManagement.article.create.form.title",
          description: "contentManagement.article.create.form.description",
          category: "contentManagement.article.create.form.category",
          image: "contentManagement.article.create.form.image",
          imageCaption: "contentManagement.article.create.form.imageCaption",
          content: "contentManagement.article.create.form.content",
          isActive: "contentManagement.article.create.form.isActive",
          saveButton: "contentManagement.article.create.form.saveButton",
          cancelButton: "contentManagement.article.create.form.cancelButton",
          titleRequired: "contentManagement.article.create.form.titleRequired",
          titleMaxLength: "contentManagement.article.create.form.titleMaxLength",
          descriptionRequired: "contentManagement.article.create.form.descriptionRequired",
          descriptionMaxLength: "contentManagement.article.create.form.descriptionMaxLength",
          categoryRequired: "contentManagement.article.create.form.categoryRequired",
          imageRequired: "contentManagement.article.create.form.imageRequired",
          contentRequired: "contentManagement.article.create.form.contentRequired",
          imageTitle: "contentManagement.article.create.form.imageTitle",
        },
      },
      edit: {
        success: "contentManagement.article.edit.success",
        error: "contentManagement.article.edit.error",
      },
      addNew: "contentManagement.article.addNew",
      search: "contentManagement.article.search",
      table: {
        number: "contentManagement.article.table.number",
        title: "contentManagement.article.table.title",
        image: "contentManagement.article.table.image",
        creationDate: "contentManagement.article.table.creationDate",
        status: "contentManagement.article.table.status",
        actions: "contentManagement.article.table.actions",
      },
      emptyState: {
        title: "contentManagement.article.emptyState.title",
        subtitle: "contentManagement.article.emptyState.subtitle",
        button: "contentManagement.article.emptyState.button",
      },
      snackbar: {
        copySuccess: "contentManagement.article.snackbar.copySuccess",
        deleteSuccess: "contentManagement.article.snackbar.deleteSuccess",
        deleteError: "contentManagement.article.snackbar.deleteError",
      },
      deleteConfirmTitle: "contentManagement.article.deleteConfirmTitle",
      deleteConfirmMessage: "contentManagement.article.deleteConfirmMessage",
      delete: {
        confirmTitle: "contentManagement.article.delete.confirmTitle",
        confirmMessage: "contentManagement.article.delete.confirmMessage",
        successMessage: "contentManagement.article.delete.successMessage",
        errorMessage: "contentManagement.article.delete.errorMessage",
      },
    },
    category: {
      tabs: {
        product: "contentManagement.category.tabs.product",
        service: "contentManagement.category.tabs.service",
      },
      search: {
        placeholder: "contentManagement.category.search.placeholder",
        filter: "contentManagement.category.search.filter",
      },
      table: {
        image: "contentManagement.category.table.image",
        name: "contentManagement.category.table.name",
        parentCategory: "contentManagement.category.table.parentCategory",
        productCount: "contentManagement.category.table.productCount",
        displayOrder: "contentManagement.category.table.displayOrder",
        homeDisplay: "contentManagement.category.table.homeDisplay",
        status: "contentManagement.category.table.status",
        actions: "contentManagement.category.table.actions",
      },
      status: {
        published: "contentManagement.category.status.published",
        unpublished: "contentManagement.category.status.unpublished",
      },
      actions: {
        create: "contentManagement.category.actions.create",
        edit: "contentManagement.category.actions.edit",
        delete: "contentManagement.category.actions.delete",
        save: "contentManagement.category.actions.save",
      },
      tooltips: {
        edit: "contentManagement.category.tooltips.edit",
        delete: "contentManagement.category.tooltips.delete",
        homeDisplay: "contentManagement.category.tooltips.homeDisplay",
        status: "contentManagement.category.tooltips.status",
      },
      delete: {
        confirmTitle: "contentManagement.category.delete.confirmTitle",
        confirmMessage: "contentManagement.category.delete.confirmMessage",
        successMessage: "contentManagement.category.delete.successMessage",
        errorMessage: "contentManagement.category.delete.errorMessage",
      },
      emptyState: {
        title: "contentManagement.category.emptyState.title",
        subtitle: "contentManagement.category.emptyState.subtitle",
        button: "contentManagement.category.emptyState.button",
      },
      create: {
        title: "contentManagement.category.create.title",
        productTitle: "contentManagement.category.create.productTitle",
        serviceTitle: "contentManagement.category.create.serviceTitle",
        form: {
          contentTitle: "contentManagement.category.create.form.contentTitle",
          categoryName: "contentManagement.category.create.form.categoryName",
          parentCategory: "contentManagement.category.create.form.parentCategory",
          position: "contentManagement.category.create.form.position",
          imageTitle: "contentManagement.category.create.form.imageTitle",
          imageCaption: "contentManagement.category.create.form.imageCaption",
          isActive: "contentManagement.category.create.form.isActive",
          isHome: "contentManagement.category.create.form.isHome",
          nameRequired: "contentManagement.category.create.form.nameRequired",
          positionRequired: "contentManagement.category.create.form.positionRequired",
          positionMin: "contentManagement.category.create.form.positionMin",
          imageRequired: "contentManagement.category.create.form.imageRequired",
        },
        success: "contentManagement.category.create.success",
        error: "contentManagement.category.create.error",
      },
      edit: {
        title: "contentManagement.category.edit.title",
        success: "contentManagement.category.edit.success",
        error: "contentManagement.category.edit.error",
      },
    },
    priceList: {
      delete: {
        confirmTitle: "contentManagement.priceList.delete.confirmTitle",
        confirmMessage: "contentManagement.priceList.delete.confirmMessage",
        successMessage: "contentManagement.priceList.delete.successMessage",
        errorMessage: "contentManagement.priceList.delete.errorMessage",
      },
    },
    product: {
      tabs: {
        product: "contentManagement.product.tabs.product",
        service: "contentManagement.product.tabs.service",
      },
      search: {
        placeholder: "contentManagement.product.search.placeholder",
        filter: "contentManagement.product.search.filter",
        category: "contentManagement.product.search.category",
        searchButton: "contentManagement.product.search.searchButton",
      },
      table: {
        product: "contentManagement.product.table.product",
        service: "contentManagement.product.table.service",
        price: "contentManagement.product.table.price",
        stock: "contentManagement.product.table.stock",
        status: "contentManagement.product.table.status",
        featured: "contentManagement.product.table.featured",
        featuredService: "contentManagement.product.table.featuredService",
        category: "contentManagement.product.table.category",
        actions: "contentManagement.product.table.actions",
      },
      status: {
        active: "contentManagement.product.status.active",
        inactive: "contentManagement.product.status.inactive",
        featured: "contentManagement.product.status.featured",
        notFeatured: "contentManagement.product.status.notFeatured",
      },
      actions: {
        create: "contentManagement.product.actions.create",
        edit: "contentManagement.product.actions.edit",
        delete: "contentManagement.product.actions.delete",
      },
      emptyState: {
        title: "contentManagement.product.emptyState.title",
        subtitle: "contentManagement.product.emptyState.subtitle",
        button: "contentManagement.product.emptyState.button",
      },
      delete: {
        confirmTitle: "contentManagement.product.delete.confirmTitle",
        confirmMessage: "contentManagement.product.delete.confirmMessage",
        successMessage: "contentManagement.product.delete.successMessage",
        errorMessage: "contentManagement.product.delete.errorMessage",
      },
      create: {
        typeLabel: "contentManagement.product.create.typeLabel",
        physicalProduct: "contentManagement.product.create.physicalProduct",
        physicalDescription: "contentManagement.product.create.physicalDescription",
        service: "contentManagement.product.create.service",
        serviceDescription: "contentManagement.product.create.serviceDescription",
        nameLabel: "contentManagement.product.create.nameLabel",
        descriptionLabel: "contentManagement.product.create.descriptionLabel",
        displaySettings: "contentManagement.product.create.displaySettings",
        visibility: "contentManagement.product.create.visibility",
        featured: "contentManagement.product.create.featured",
        displayOrder: "contentManagement.product.create.displayOrder",
        categories: "contentManagement.product.create.categories",
        parentCategory: "contentManagement.product.create.parentCategory",
        childCategory: "contentManagement.product.create.childCategory",
        images: "contentManagement.product.create.images",
        variants: "contentManagement.product.create.variants",
        hasVariants: "contentManagement.product.create.hasVariants",
        editVariants: "contentManagement.product.create.editVariants",
        inventory: {
          title: "contentManagement.product.create.inventory.title",
          maxQuantity: "contentManagement.product.create.inventory.maxQuantity",
          stock: "contentManagement.product.create.inventory.stock",
        },
        pricing: {
          title: "contentManagement.product.create.pricing.title",
          price: "contentManagement.product.create.pricing.price",
          priceCapital: "contentManagement.product.create.pricing.priceCapital",
          priceCapitalHint: "contentManagement.product.create.pricing.priceCapitalHint",
          priceReal: "contentManagement.product.create.pricing.priceReal",
          validation: {
            priceRequired: "contentManagement.product.create.pricing.validation.priceRequired",
            priceMin: "contentManagement.product.create.pricing.validation.priceMin",
            priceCapitalRequired:
              "contentManagement.product.create.pricing.validation.priceCapitalRequired",
            priceCapitalMin: "contentManagement.product.create.pricing.validation.priceCapitalMin",
            priceRealRequired:
              "contentManagement.product.create.pricing.validation.priceRealRequired",
            priceRealMin: "contentManagement.product.create.pricing.validation.priceRealMin",
          },
        },
        shipping: {
          title: "contentManagement.product.create.shipping.title",
          weight: "contentManagement.product.create.shipping.weight",
          weightUnit: "contentManagement.product.create.shipping.weightUnit",
          dimensions: "contentManagement.product.create.shipping.dimensions",
          dimensionsDescription: "contentManagement.product.create.shipping.dimensionsDescription",
          length: "contentManagement.product.create.shipping.length",
          width: "contentManagement.product.create.shipping.width",
          height: "contentManagement.product.create.shipping.height",
          dimensionUnit: "contentManagement.product.create.shipping.dimensionUnit",
          warehouse: "contentManagement.product.create.shipping.warehouse",
          selectWarehouse: "contentManagement.product.create.shipping.selectWarehouse",
        },
        seo: {
          title: "contentManagement.product.create.seo.title",
          description: "contentManagement.product.create.seo.description",
          editButton: "contentManagement.product.create.seo.editButton",
          dialog: {
            title: "contentManagement.product.create.seo.dialog.title",
            pageTitle: "contentManagement.product.create.seo.dialog.pageTitle",
            pageDesc: "contentManagement.product.create.seo.dialog.pageDesc",
            tags: "contentManagement.product.create.seo.dialog.tags",
            url: "contentManagement.product.create.seo.dialog.url",
          },
        },
        soldCount: "contentManagement.product.create.soldCount",
        displayOrderMin: "contentManagement.product.create.displayOrderMin",
        soldCountMin: "contentManagement.product.create.soldCountMin",
        dropToUpload: "contentManagement.product.create.dropToUpload",
        uploadFiles: "contentManagement.product.create.uploadFiles",
        dragAndDrop: "contentManagement.product.create.dragAndDrop",
        addFromUrl: "contentManagement.product.create.addFromUrl",
        addFromLibrary: "contentManagement.product.create.addFromLibrary",
        purchaseQuantity: {
          title: "contentManagement.product.create.purchaseQuantity.title",
          purchaseLimit: "contentManagement.product.create.purchaseQuantity.purchaseLimit",
          purchaseLimitHint: "contentManagement.product.create.purchaseQuantity.purchaseLimitHint",
          stock: "contentManagement.product.create.purchaseQuantity.stock",
          stockHint: "contentManagement.product.create.purchaseQuantity.stockHint",
        },
        variant: {
          title: "contentManagement.product.create.variant.title",
          description: "contentManagement.product.create.variant.description",
          hasVariants: "contentManagement.product.create.variant.hasVariants",
          editButton: "contentManagement.product.create.variant.editButton",
          dialog: {
            title: "contentManagement.product.create.variant.dialog.title",
            addVariant: "contentManagement.product.create.variant.dialog.addVariant",
            variantName: "contentManagement.product.create.variant.dialog.variantName",
            variantValue: "contentManagement.product.create.variant.dialog.variantValue",
            deleteVariant: "contentManagement.product.create.variant.dialog.deleteVariant",
            noVariants: "contentManagement.product.create.variant.dialog.noVariants",
            saveButton: "contentManagement.product.create.variant.dialog.saveButton",
            cancelButton: "contentManagement.product.create.variant.dialog.cancelButton",
            validation: {
              nameRequired:
                "contentManagement.product.create.variant.dialog.validation.nameRequired",
              valuesRequired:
                "contentManagement.product.create.variant.dialog.validation.valuesRequired",
            },
          },
          placeholder: {
            costPrice: "contentManagement.product.variant.placeholder.costPrice",
            listPrice: "contentManagement.product.variant.placeholder.listPrice",
            salePrice: "contentManagement.product.variant.placeholder.salePrice",
          },
          helperText: {
            costPrice: "contentManagement.product.variant.helperText.costPrice",
            listPrice: "contentManagement.product.variant.helperText.listPrice",
            salePrice: "contentManagement.product.variant.helperText.salePrice",
          },
        },
        media: {
          title: "contentManagement.product.create.media.title",
          description: "contentManagement.product.create.media.description",
          dropToUpload: "contentManagement.product.create.media.dropToUpload",
          dragAndDrop: "contentManagement.product.create.media.dragAndDrop",
          addFromUrl: "contentManagement.product.create.media.addFromUrl",
          addFromLibrary: "contentManagement.product.create.media.addFromLibrary",
        },
      },
      variant: {
        dialog: {
          title: "contentManagement.product.variant.dialog.title",
          addSpecification: "contentManagement.product.variant.dialog.addSpecification",
          specificationName: "contentManagement.product.variant.dialog.specificationName",
          specificationValue: "contentManagement.product.variant.dialog.specificationValue",
          specificationNamePlaceholder:
            "contentManagement.product.variant.dialog.specificationNamePlaceholder",
          specificationValuePlaceholder:
            "contentManagement.product.variant.dialog.specificationValuePlaceholder",
          deleteSpecification: "contentManagement.product.variant.dialog.deleteSpecification",
          addValue: "contentManagement.product.variant.dialog.addValue",
          specificationCount: "contentManagement.product.variant.dialog.specificationCount",
          variantsCreated: "contentManagement.product.variant.dialog.variantsCreated",
          validation: {
            nameRequired: "contentManagement.product.variant.dialog.validation.nameRequired",
            valuesRequired: "contentManagement.product.variant.dialog.validation.valuesRequired",
          },
        },
        table: {
          image: "contentManagement.product.variant.table.image",
          specification: "contentManagement.product.variant.table.specification",
          costPrice: "contentManagement.product.variant.table.costPrice",
          listPrice: "contentManagement.product.variant.table.listPrice",
          salePrice: "contentManagement.product.variant.table.salePrice",
          stock: "contentManagement.product.variant.table.stock",
          maxQuantity: "contentManagement.product.variant.table.maxQuantity",
          costPriceInput: "contentManagement.product.variant.table.costPriceInput",
          listPriceInput: "contentManagement.product.variant.table.listPriceInput",
          salePriceInput: "contentManagement.product.variant.table.salePriceInput",
          stockInput: "contentManagement.product.variant.table.stockInput",
          maxQuantityInput: "contentManagement.product.variant.table.maxQuantityInput",
        },
        imageUpload: {
          button: "contentManagement.product.variant.imageUpload.button",
        },
        validation: {
          priceCapitalRequired: "contentManagement.product.variant.validation.priceCapitalRequired",
          priceRequired: "contentManagement.product.variant.validation.priceRequired",
          priceRealRequired: "contentManagement.product.variant.validation.priceRealRequired",
          quantityRequired: "contentManagement.product.variant.validation.quantityRequired",
          quantityPurchaseRequired:
            "contentManagement.product.variant.validation.quantityPurchaseRequired",
          priceCapitalMin: "contentManagement.product.variant.validation.priceCapitalMin",
          priceMin: "contentManagement.product.variant.validation.priceMin",
          priceRealMin: "contentManagement.product.variant.validation.priceRealMin",
          quantityMin: "contentManagement.product.variant.validation.quantityMin",
          quantityPurchaseMin: "contentManagement.product.variant.validation.quantityPurchaseMin",
          priceCapitalLessThanPrice:
            "contentManagement.product.variant.validation.priceCapitalLessThanPrice",
          priceLessThanPriceReal:
            "contentManagement.product.variant.validation.priceLessThanPriceReal",
          priceGreaterThanZero: "contentManagement.product.variant.validation.priceGreaterThanZero",
          priceCapitalLessPrice:
            "contentManagement.product.variant.validation.priceCapitalLessPrice",
          priceGreaterPriceCapital:
            "contentManagement.product.variant.validation.priceGreaterPriceCapital",
          priceLessPriceReal: "contentManagement.product.variant.validation.priceLessPriceReal",
          priceRealGreaterPrice:
            "contentManagement.product.variant.validation.priceRealGreaterPrice",
          priceCapitalGreaterThanZero:
            "contentManagement.product.variant.validation.priceCapitalGreaterThanZero",
          priceRealGreaterThanZero:
            "contentManagement.product.variant.validation.priceRealGreaterThanZero",
          quantityGreaterThanZero:
            "contentManagement.product.variant.validation.quantityGreaterThanZero",
        },
        placeholder: {
          costPrice: "contentManagement.product.variant.placeholder.costPrice",
          listPrice: "contentManagement.product.variant.placeholder.listPrice",
          salePrice: "contentManagement.product.variant.placeholder.salePrice",
        },
        helperText: {
          costPrice: "contentManagement.product.variant.helperText.costPrice",
          listPrice: "contentManagement.product.variant.helperText.listPrice",
          salePrice: "contentManagement.product.variant.helperText.salePrice",
          stock: "contentManagement.product.variant.helperText.stock",
          maxQuantity: "contentManagement.product.variant.helperText.maxQuantity",
        },
      },
    },
    search: {
      filter: "contentManagement.search.filter",
      category: "contentManagement.search.category",
      selectCategory: "contentManagement.search.selectCategory",
      clearFilter: "contentManagement.search.clearFilter",
    },
  },
  datePicker: {
    today: "datePicker.today",
    clear: "datePicker.clear",
    ok: "datePicker.ok",
    cancel: "datePicker.cancel",
    invalidDate: "datePicker.invalidDate",
  },
};
