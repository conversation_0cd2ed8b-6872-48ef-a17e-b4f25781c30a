import React, { useState } from "react";
import { Box, Skeleton } from "@mui/material";
import ImageIcon from "@mui/icons-material/Image";

interface ImageData {
  type: string;
  link: string;
}

interface ArticleImageProps {
  src?: ImageData[] | string;
  alt?: string;
  size?: number;
  circular?: boolean;
  onClick?: () => void;
}

const ShowImage: React.FC<ArticleImageProps> = ({
  src,
  alt,
  size = 60,
  circular = true,
  onClick,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  const getImageUrl = (): string => {
    if (!src) return "";

    if (Array.isArray(src) && src.length > 0) {
      const firstImage = src[0];
      return firstImage?.link || "";
    }

    if (typeof src === "string") {
      return src;
    }

    return "";
  };

  const imageUrl = getImageUrl();

  React.useEffect(() => {
    if (imageUrl) {
      const img = new Image();
      img.src = imageUrl;
      img.onload = handleLoad;
      img.onerror = handleError;
    }
  }, [imageUrl]);

  if (!imageUrl) {
    return (
      <Box
        sx={{
          width: size,
          height: size,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: "grey.100",
          borderRadius: circular ? "50%" : 1,
        }}
      >
        <ImageIcon sx={{ color: "grey.400", fontSize: size * 0.5 }} />
      </Box>
    );
  }

  return (
    <Box sx={{ position: "relative", width: size, height: size }}>
      {isLoading && (
        <Skeleton
          variant="rectangular"
          width={size}
          height={size}
          sx={{
            borderRadius: circular ? "50%" : 1,
            position: "absolute",
            top: 0,
            left: 0,
          }}
        />
      )}
      <Box
        component="img"
        src={imageUrl}
        alt={alt}
        onLoad={handleLoad}
        onError={handleError}
        onClick={onClick}
        sx={{
          width: size,
          height: size,
          objectFit: "cover",
          borderRadius: circular ? "50%" : 1,
          opacity: isLoading ? 0 : 1,
          transition: "opacity 0.2s",
        }}
      />
    </Box>
  );
};

export default ShowImage;
