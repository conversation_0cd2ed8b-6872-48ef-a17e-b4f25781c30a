import { useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import Head from "next/head";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import CardHeader from "@mui/material/CardHeader";
import Divider from "@mui/material/Divider";
import FormHelperText from "@mui/material/FormHelperText";
import Stack from "@mui/material/Stack";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import { useRouter } from "next/router";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import { useAppDispatch } from "@/src/redux/hooks";
import { forgotPassword, resetState } from "@/src/redux/slices/passwordResetSlice";
import { CircularProgress } from "@mui/material";

const ForgotPassword = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const snackbar = useSnackbar();
  const [isEmailSent, setIsEmailSent] = useState(false);

  const formik = useFormik({
    initialValues: {
      email: "",
      submit: null,
    },
    validationSchema: Yup.object({
      email: Yup.string().email("Email không hợp lệ").required("Vui lòng nhập email"),
    }),
    onSubmit: async (values, helpers) => {
      try {
        await dispatch(forgotPassword(values.email)).unwrap();
        setIsEmailSent(true);
        helpers.setSubmitting(false);
        helpers.resetForm();
      } catch (err: any) {
        const errorMessage =
          typeof err === "string" ? err : err.message || "Đã có lỗi xảy ra. Vui lòng thử lại.";

        helpers.setStatus({ success: false });
        helpers.setErrors({ submit: errorMessage });
        helpers.setSubmitting(false);
      }
    },
  });

  const handleBackToLogin = () => {
    dispatch(resetState());
    router.push("/auth/login");
  };

  return (
    <>
      <Head>
        <title>Quên mật khẩu</title>
      </Head>
      <Box
        sx={{
          backgroundColor: "background.default",
          display: "flex",
          flexDirection: "column",
          minHeight: "100vh",
        }}
      >
        <Box
          sx={{
            flex: "1 1 auto",
            alignItems: "center",
            display: "flex",
            justifyContent: "center",
            p: 3,
          }}
        >
          <Card sx={{ maxWidth: 550, width: "100%" }}>
            <CardHeader
              title="Quên mật khẩu"
              subheader={
                !isEmailSent
                  ? "Nhập email đã đăng ký để nhận liên kết đặt lại mật khẩu."
                  : "Yêu cầu đã được gửi"
              }
              subheaderTypographyProps={{ sx: { mt: 1 } }}
              sx={{ pb: 0 }}
            />
            <CardContent>
              {!isEmailSent ? (
                <form noValidate onSubmit={formik.handleSubmit}>
                  <Stack spacing={3}>
                    <TextField
                      autoFocus
                      error={!!(formik.touched.email && formik.errors.email)}
                      fullWidth
                      helperText={formik.touched.email && formik.errors.email}
                      label="Địa chỉ Email"
                      name="email"
                      onBlur={formik.handleBlur}
                      onChange={formik.handleChange}
                      type="email"
                      value={formik.values.email}
                    />
                    {formik.errors.submit && (
                      <FormHelperText error>{String(formik.errors.submit)}</FormHelperText>
                    )}
                    <Button
                      disabled={formik.isSubmitting}
                      fullWidth
                      size="large"
                      type="submit"
                      variant="contained"
                    >
                      {formik.isSubmitting ? (
                        <CircularProgress size={24} color="inherit" />
                      ) : (
                        "Xác nhận"
                      )}
                    </Button>
                    <Divider />
                    <Button fullWidth variant="text" onClick={() => router.push("/auth/login")}>
                      Quay lại đăng nhập
                    </Button>
                  </Stack>
                </form>
              ) : (
                <Stack spacing={3} alignItems="center">
                  <Typography variant="body1" textAlign="center">
                    Nếu email của bạn tồn tại trong hệ thống, một liên kết đặt lại mật khẩu sẽ được
                    gửi đến. Vui lòng kiểm tra hộp thư của bạn.
                  </Typography>
                  <Button onClick={handleBackToLogin} variant="contained">
                    Quay lại đăng nhập
                  </Button>
                </Stack>
              )}
            </CardContent>
          </Card>
        </Box>
      </Box>
    </>
  );
};

export default ForgotPassword;
