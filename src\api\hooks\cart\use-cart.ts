import { useState } from "react";
import { useStoreId } from "@/src/hooks/use-store-id";

import { cartService } from "@/src/api/services/cart/cart.service";
import { ErrorHandlerService } from "@/src/api/services/error-handler.service";
import { StorageService } from "nextjs-api-lib";
import { CartOrigin } from "../../types/cart.types";

export const useCart = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const partnerId = StorageService.get("partnerId") as string | null;

  const searchItems = async (skip: number, limit: number, shopId: string, search: string = "") => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        shopId: shopId,
        search,
        skip,
        limit,
      };

      const response = await cartService.searchItems(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const searchUsers = async (skip: number, limit: number, shopId: string, search: string = "") => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        shopId: shopId,
        search,
        skip,
        limit,
      };

      const response = await cartService.searchUsers(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const createOrUpdateCart = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await cartService.createOrUpdateCart({ ...data, partnerId });
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getCart = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await cartService.getCart(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const listCart = async (params: {
    skip: number;
    limit: number;
    shopId: string;
    origins?: CartOrigin[];
    branchId?: string;
  }) => {
    try {
      setLoading(true);
      setError(null);

      const paramsData = {
        ...params,
        partnerId,
      };

      const response = await cartService.listCart(paramsData);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const deleteCart = async (cartId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await cartService.deleteCart(cartId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const estimatePoint = async (params: { cartId: string }) => {
    try {
      setLoading(true);
      setError(null);

      const response = await cartService.estimatePoint(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  return {
    searchItems,
    searchUsers,
    createOrUpdateCart,
    getCart,
    listCart,
    estimatePoint,
    loading,
    error,
    deleteCart,
  };
};
