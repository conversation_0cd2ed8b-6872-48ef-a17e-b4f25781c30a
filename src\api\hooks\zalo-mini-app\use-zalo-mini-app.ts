import { useState } from "react";
import { ErrorHandlerService } from "../../services/error-handler.service";
import { BodyTaxInvoiceDto, userTaxInvoiceService } from "../../services/tax-invoice/tax-invoice";
import {
  GetListVersionParams,
  zaloMiniAppService,
} from "../../services/zalo-mini-app/zalo-mini-app.service";

export interface TriggerParameterDto {
  name: string;
  value: string;
  dataType: string;
  orderNumber: number;
}

export const useZaloMiniApp = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getListVersion = async (data: GetListVersionParams) => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloMiniAppService.getListVersion(data);
      return response;
    } catch (err: any) {
      // const errorResponse = ErrorHandlerService.handle(err, {
      //   showSnackbar: true,
      //   logError: true,
      // });
      // setError(errorResponse.detail);
    } finally {
      // setLoading(false);
    }
  };

  return {
    getListVersion,
    loading,
    error,
  };
};
