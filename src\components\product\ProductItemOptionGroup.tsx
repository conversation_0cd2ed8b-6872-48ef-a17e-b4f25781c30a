import { <PERSON>, <PERSON><PERSON>, Card, Form<PERSON>ont<PERSON>l, FormHelperText, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import EditIcon from "@mui/icons-material/Edit";
import CustomSwitch from "../custom-switch";
import { useTranslation } from "react-i18next";
import { ProductDialogAddItemGroupOption } from "./ProductDialogAddItemGroupOption";
import TitleDialog from "../dialog/TitleDialog";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useItemOptionGroup } from "@/src/api/hooks/item-option-group/use-item-option-group";
import { useItemOption } from "@/src/api/hooks/item-option/use-item-option";
export const ProductItemOptionGroup = ({ formik, listDefaultItemOptionGroups }) => {
  const { t } = useTranslation();
  const [openDialog, setOpenDialog] = useState(false);
  const storeId = useStoreId();
  const [selectedOptionGroups, setSelectedOptionGroups] = useState([]);
  const { listItemOptionGroup, loading, error } = useItemOptionGroup();
  const [options, setOptions] = useState([]);

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleSaveItemGroupOption = (itemOptionGroups) => {
    const validItemOptionGroups = Array.isArray(itemOptionGroups) ? itemOptionGroups : [];
    setSelectedOptionGroups(itemOptionGroups);
    const formatItemOptionGroups = validItemOptionGroups.map((item) => {
      return {
        itemOptionGroupId: item.itemOptionGroup?.itemOptionGroupId,
        itemOptionIds: item.itemOptions.map((itemOption) => itemOption.itemOptionId),
      };
    });

    setTimeout(() => {
      formik.setValues(
        {
          ...formik.values,
          extraItemOptionGroups: formatItemOptionGroups,
        },
        true
      );
    }, 0);
  };

  const fetchItemOptionGroupData = async (shopId) => {
    try {
      const response = await listItemOptionGroup(0, 999, { shopId: shopId });
      if (response?.data) {
        const { data } = response.data;
        setOptions(data);
      }
    } catch (error) {
      //   setErrors({ general: error });
    }
  };

  useEffect(() => {
    if (storeId) {
      fetchItemOptionGroupData(storeId);
    }
  }, [storeId]);

  useEffect(() => {
    if (listDefaultItemOptionGroups.length > 0) {
      // Lọc itemOptions trong b dựa trên itemOptionIds từ a
      const matchingData = listDefaultItemOptionGroups.map((group) => {
        // Tìm group tương ứng trong a
        const matchingGroup = formik.values.extraItemOptionGroups.find(
          (item) => item.itemOptionGroupId === group.itemOptionGroupId
        );

        // Nếu tìm thấy, lọc itemOptions trong b theo itemOptionIds của a
        if (matchingGroup) {
          return {
            itemOptionGroup: { itemOptionGroupId: group.itemOptionGroupId, name: group.name },
            itemOptions: group.itemOptions.filter((option) =>
              matchingGroup.itemOptionIds.includes(option.itemOptionId)
            ),
            allItemOptions: group.itemOptions,
          };
        }

        // Nếu không tìm thấy nhóm phù hợp, giữ nguyên group
        return group;
      });
      setSelectedOptionGroups(matchingData);
      // setSelectedOptionGroups(

      // );
    }
  }, [listDefaultItemOptionGroups]);

  return (
    <Card sx={{ p: 2.5 }}>
      <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2.5 }}>
        <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
          Tùy chọn sản phẩm
        </Typography>
        {/* {formik.values.extraItemOptionGroups && formik.values.extraItemOptionGroups?.length > 0 && ( */}
        <Button
          startIcon={<EditIcon />}
          onClick={() => setOpenDialog(true)}
          variant="outlined"
          size="small"
          sx={{ color: "#2654FE" }}
        >
          Sửa tùy chọn
        </Button>
        {/* )} */}
      </Box>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
        {/* <FormControlLabel
          control={
            <CustomSwitch
              checked={formik.values.extraItemOptionGroups.length > 0}
              onChange={handleVariantChange}
            />
          }
          label={
            <Typography variant="body2" sx={{ color: 'text.primary', ml: 1.5 }}>
              Sản phẩm này có nhiều tùy chọn
            </Typography>
          }
          sx={{ mx: 0 }}
        /> */}
        <FormHelperText sx={{ mt: -1.5, ml: 0 }}>
          Thêm tùy chọn như độ ngọt, topping,...
        </FormHelperText>

        {/* {formik.values.extraItemOptionGroups && formik.values.extraItemOptionGroups?.length > 0 && ( */}
        <Box sx={{ mt: 1 }}>
          <Typography variant="body2" color="text.secondary">
            Số lượng tùy chọn: {formik.values.extraItemOptionGroups?.length}
          </Typography>
        </Box>
        {/* )} */}
      </Box>

      <TitleDialog
        open={openDialog}
        handleClose={handleCloseDialog}
        title="Tùy chọn sản phẩm"
        showActionDialog={false}
        maxWidth="xl"
      >
        <ProductDialogAddItemGroupOption
          handleClose={handleCloseDialog}
          onSave={handleSaveItemGroupOption}
          selectedOptionGroups={selectedOptionGroups}
          itemGroupOptions={options}
        />
      </TitleDialog>
    </Card>
  );
};
