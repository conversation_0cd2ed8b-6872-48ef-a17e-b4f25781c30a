import { apiClient } from "@/src/api/config/api-client";
import { API_PATHS } from "@/src/api/constants/api-paths";
import type { ApiError, ErrorConfig } from "@/src/api/types/error.types";
import type { ExtendedRequestConfig } from "@/src/api/types/api.types";
import {
  GetProductCategoryRequest,
  GetProductCategoryRequestBody,
} from "@/src/api/types/product-category.types";
import { ParamDashboard } from "@/src/api/types/dashboard.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export const dashboardPartnerService = {
  getDashboardPartner: async <T = any>(shopId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      if (shopId) {
        const response = await apiClient.get<GetProductCategoryRequestBody, T>(
          `${API_PATHS.DASHBOARD.GET_DASHBOARD_PARTNER}?shopId=${shopId}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getDashboardPartnerV2: async <T = any>(data: ParamDashboard, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      if (data.shopId) {
        const params = new URLSearchParams();

        params.append("ShopId", String(data.shopId));
        if (data.paramMonth) params.append("ParamMonth", String(data.paramMonth));
        if (data.paramDate) params.append("ParamDate", String(data.paramDate));
        if (data.startDate) params.append("StartDate", String(data.startDate));
        if (data.endDate) params.append("EndDate", String(data.endDate));
        if (data.branchId) params.append("BranchId", String(data.branchId));
        if (data.typeSortRank) params.append("TypeSortRank", String(data.typeSortRank));

        const response = await apiClient.get<any, T>(
          `${API_PATHS.DASHBOARD.GET_DASHBOARD_PARTNER_V2}?${params.toString()}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
