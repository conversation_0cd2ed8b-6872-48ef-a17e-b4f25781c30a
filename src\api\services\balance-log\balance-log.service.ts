import { apiClient } from "../../config/api-client";
import { API_PATHS } from "../../constants/api-paths";
import type { ApiError, ErrorConfig } from "../../types/error.types";
import type { ExtendedRequestConfig } from "../../types/api.types";
import { logger } from "@/src/utils/logger";
import { InvoiceConfigData, InvoiceProvider } from "../../types/invoice.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }

  throw error;
}

export interface IBalanceLogDto {
  partnerId: string;
  refType: string;
  refId: string;
  type: string;
  balanceLogNo: string;
  amount: number;
  balanceAfterTransaction: number;
  currency: string;
  message: string;
  status: string;
  createdDate: string;
  modifiedDate: string;
  createdBy: string;
  modifiedBy: string;
  isDeleted: boolean;
  deletedAt: string;
  id: string;
}

export interface PagingDto {
  pageSize: number;
  pageIndex: number;
  search: string;
  name: string;
  sort: string;
  nameType: string;
  sortType: string;
}

export interface BalanceLogRequestDto {
  partnerId?: string;
  refType?: string;
  type?: string;
  status?: string;
  paging?: PagingDto;
}

export const balanceLogService = {
  getBalanceHistory: async (data: BalanceLogRequestDto, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post(
        `${API_PATHS.BALANCE_LOG.GET_LIST_BALANCE_LOG}`,
        data,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
