export enum BusinessType {
  Retail = "Retail",
  Wholesale = "Wholesale",
  Service = "Service",
  Other = "Other",
}

export const getBusinessTypeText = (businessType: BusinessType): string => {
  switch (businessType) {
    case BusinessType.Retail:
      return "Bán lẻ";
    case BusinessType.Wholesale:
      return "Bán buôn";
    case BusinessType.Service:
      return "Dịch vụ";
    case BusinessType.Other:
      return "Khác";
    default:
      return "Không xác định";
  }
};

export enum StoreStatus {
  Pending = "Pending",
  Actived = "Actived",
  InActived = "InActived",
}

export const getStatusText = (status: StoreStatus): string => {
  switch (status) {
    case StoreStatus.Pending:
      return "Đang chờ";
    case StoreStatus.Actived:
      return "Đã kích hoạt";
    case StoreStatus.InActived:
      return "Không hoạt động";
    default:
      return "Không xác định";
  }
};

export const rowPerPageOptionsDefault = [10, 25, 50];
