import React, { useEffect } from "react";
import DashboardLayout from "@/src/layouts/dashboard";
import PageTitleWithBackBtn from "@/src/layouts/dashboard/page-title-with-back-btn/page-title";
import { Box } from "@mui/material";
import NewCustomerForm from "@/src/components/customers/NewCustomerForm";
import { useUser } from "@/src/api/hooks/user/use-user";
import { getProvinces } from "@/src/slices/addressSlice";
import { useDispatch } from "react-redux";
import { useRouter } from "next/router";
import { paths } from "@/src/paths";
import { useStoreId } from "@/src/hooks/use-store-id";
import useSnackbar from "@/src/hooks/use-snackbar";
import { ShippingAddressFormData } from "@/src/components/customers/FormShippingAddress";
import { Padding } from "@/src/styles/CommonStyle";

const hasValidShippingAddress = (shippingData: Partial<ShippingAddressFormData>): boolean => {
  const requiredFields: (keyof ShippingAddressFormData)[] = [
    "shippingAddressFullname",
    "shippingAddressPhone",
    "shippingAddressAddress",
    "shippingAddressProvince",
    "shippingAddressDistrict",
    "shippingAddressWard",
  ];

  // Kiểm tra xem có bất kỳ trường nào có giá trị không
  const hasAnyValue = requiredFields.some((field) => !!shippingData[field]);

  // Nếu có ít nhất một trường có giá trị, kiểm tra xem tất cả các trường có giá trị không
  if (hasAnyValue) {
    return requiredFields.every((field) => !!shippingData[field]);
  }

  // Nếu không có trường nào có giá trị, return false
  return false;
};

export default function NewCustomer() {
  const router = useRouter();
  const storeId = useStoreId();
  const snackbar = useSnackbar();
  const { createUser, createUserAddress, loading, error } = useUser();
  const dispatch = useDispatch();

  const handleCreateCustomer = async (data) => {
    const userData = {
      fullName: data.name,
      email: data.email,
      phoneNumber: data.phone,
      tags: data.tags,
      shopId: storeId,
    };

    try {
      const response = await createUser(userData);
      if (response && response.data) {
        const { userId } = response.data;

        // Kiểm tra xem có nên tạo địa chỉ không
        if (hasValidShippingAddress(data)) {
          const userAddressData = {
            fullName: data.shippingAddressFullname,
            address: data.shippingAddressAddress,
            phoneNumber: data.shippingAddressPhone,
            wardId: data.shippingAddressWard,
            districtId: data.shippingAddressDistrict,
            provinceId: data.shippingAddressProvince,
            userId,
          };

          await createUserAddress(userAddressData);
        }

        await router.push(paths.customers.detail.replace(":id", userId));
        snackbar.success("Tạo tài khoản khách hàng thành công");
      }
    } catch (error) {
      snackbar.error("Có lỗi xảy ra khi tạo khách hàng");
    }
  };

  useEffect(() => {
    dispatch(getProvinces());
  }, []);

  return (
    <DashboardLayout>
      <Box sx={{ p: Padding }}>
        <PageTitleWithBackBtn title="Thêm khách hàng" backPath={paths.customers.list} />
        <NewCustomerForm onSubmit={handleCreateCustomer} />
      </Box>
    </DashboardLayout>
  );
}
