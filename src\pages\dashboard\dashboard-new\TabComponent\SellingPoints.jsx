import React, { useState } from 'react';
import { <PERSON>, Select, MenuItem, Button, Stack, Typography, Tabs, Tab } from '@mui/material';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider, DateRangePicker } from '@mui/x-date-pickers-pro';
import { ExpandMore } from '@mui/icons-material';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import MorningSchedule from './TimeComponent/AfternoonSchedule';
import AfternoonSchedule from './TimeComponent/MorningSchedule';

const SellingPoints = () => {
  const [filter, setFilter] = useState('Hôm nay');
  const [filterPlace, setFilterPlace] = useState('Chi nhánh');
  const [open, setOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);

  const data = [
    {
      title: '<PERSON><PERSON>h thu',
      content: '40,000,000đ',
    },
    {
      title: 'Đơn hàng',
      content: '100',
    },
    {
      title: 'Khách hàng',
      content: '50',
    },
  ];

  return (
    <Box
      sx={{
        p: 3,
        borderRadius: '20px',
        marginTop: '-15px',
        background: '#f5f5f5',
      }}
    >
      <Stack
        flexDirection={'row'}
        justifyContent={'space-between'}
        mb={'25px'}
        sx={{
          '@media(max-width: 600px)': {
            flexDirection: 'column',
            gap: '20px',
            alignItems: 'start',
          },
        }}
      >
        <Select
          value={filterPlace}
          onChange={(e) => setFilterPlace(e.target.value)}
          displayEmpty
          size="small"
          sx={{ minWidth: 120, height: 50, backgroundColor: '#fff' }}
          IconComponent={(props) => <ExpandMore {...props} sx={{ color: '#000' }} />}
        >
          <MenuItem value="Chi nhánh">Chi nhánh</MenuItem>
          <MenuItem value="Chi nhánh1">Chi nhánh1</MenuItem>
          <MenuItem value="Chi nhánh2">Chi nhánh2</MenuItem>
        </Select>
        <Box
          display="flex"
          justifyContent="flex-end"
          gap={2}
          sx={{
            '@media(max-width: 480px)': {
              flexDirection: 'column',
            },
          }}
        >
          <Select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            displayEmpty
            size="small"
            sx={{ minWidth: 120, height: 50, backgroundColor: '#fff' }}
            IconComponent={(props) => <ExpandMore {...props} sx={{ color: '#000' }} />}
          >
            <MenuItem value="Hôm nay">Hôm nay</MenuItem>
            <MenuItem value="Hôm qua">Hôm qua</MenuItem>
            <MenuItem value="7 ngày qua">7 ngày qua</MenuItem>
            <MenuItem value="1 tháng qua">1 tháng qua</MenuItem>
          </Select>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DateRangePicker
              open={open}
              onOpen={() => setOpen(true)}
              onClose={() => setOpen(false)}
              format="DD/MM/YYYY"
              localeText={{ start: 'Từ ngày', end: 'Đến ngày' }}
              slotProps={{
                textField: { sx: { display: 'none' } },
                popper: { placement: 'bottom-start' },
              }}
              slots={{
                field: () => (
                  <Button
                    onClick={() => setOpen(true)}
                    sx={{
                      border: '1px solid #C4C4C4',
                      borderRadius: '12px',
                      padding: '10px 16px',
                      backgroundColor: '#F8F9FA',
                      fontSize: '16px',
                      fontWeight: 400,
                      color: '#000',
                      textTransform: 'none',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      minWidth: '260px',
                      justifyContent: 'flex-start',
                      '&:hover': { backgroundColor: '#ECECEC' },
                    }}
                  >
                    <Stack
                      direction="row"
                      alignItems="center"
                      gap="4px"
                      justifyContent="space-between"
                      width="100%"
                    >
                      <Stack direction="row" alignItems="center" gap="4px">
                        {'Bắt đầu'} - {'Kết thúc'}
                      </Stack>
                      <CalendarMonthIcon sx={{ fontSize: 22 }} />
                    </Stack>
                  </Button>
                ),
              }}
            />
          </LocalizationProvider>
        </Box>
      </Stack>
      <Stack
        direction="row"
        gap={2}
        mt={2}
        sx={{
          gap: '50px',
          '@media(max-width: 980px)': {
            flexWrap: 'wrap',
            // gap: '2%',
          },
        }}
      >
        {data.map((slot, index) => (
          <Box
            key={index}
            sx={{
              background: '#F4F5F9',
              borderRadius: '12px',
              padding: '12px',
              minWidth: '150px',
              textAlign: 'center',
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              textAlignLast: 'start',
              boxShadow: '0 0px 20px 0 #00000026 !important',
              padding: '20px',
              width: '25%',
              '@media(max-width: 980px)': {
                width: '45%',
              },
              '@media(max-width: 600px)': {
                width: '100%',
              },
            }}
          >
            <Typography color="#202224" fontSize={'16px'} fontWeight={'400'} mb={'25px'}>
              {slot.title}
            </Typography>
            <Typography
              fontSize={14}
              fontWeight={600}
              sx={{
                fontSize: '28px',
                fontWeight: '700',
                color: '#202224',
                marginBottom: '10px',
              }}
            >
              {slot.content}
            </Typography>
          </Box>
        ))}
      </Stack>
    </Box>
  );
};

export default SellingPoints;
