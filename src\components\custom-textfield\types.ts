

import { TextFieldProps } from '@mui/material/TextField';

export type CustomTextFieldProps = TextFieldProps & {
  /**
   * Giá trị của input
   */
  value?: string | number;

  /**
   * Callback được gọi khi giá trị thay đổi
   */
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;

  /**
   * Callback được gọi khi input mất focus
   */
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;

  /**
   * Props cho input element
   */
  inputProps?: {
    min?: number;
    max?: number;
    step?: number;
    [key: string]: any;
  };

  /**
   * Text hiển thị khi có lỗi
   */
  helperText?: React.ReactNode;

  /**
   * Trạng thái lỗi của input
   */
  error?: boolean;

  /**
   * Tên của field, được sử dụng trong form
   */
  name?: string;

  /**
   * Chiều rộng của input
   */
  fullWidth?: boolean;

  /**
   * Loại input (text, number, etc.)
   */
  type?: string;
} 