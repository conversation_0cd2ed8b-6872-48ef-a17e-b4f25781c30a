import React, { useEffect, useState } from 'react';
import { useItemOption } from "@/src/api/hooks/item-option/use-item-option";

interface DetachableLabelProps {
  item: any;
  index: number;
  cart: any;
  order?: any;
}

const DetachableLabel: React.FC<DetachableLabelProps> = ({ item, index, cart, order }) => {
  const extraOptions = item.extraOptions || [];
  const [optionDetails, setOptionDetails] = useState([]);
  const { listItemOptionByIds } = useItemOption();

  const fetchItemOptions = async (itemOptionIds: any) => {
    const response = await listItemOptionByIds(itemOptionIds);
    if (response?.data) {
      const details = response.data.data.flatMap((group: any) =>
        group.itemOptions.map((option: any) => ({
          groupName: group.name,
          optionName: option.name,
        }))
      );
      setOptionDetails(details);
    }
  };

  useEffect(() => {
    if (extraOptions && extraOptions.length > 0) {
      fetchItemOptions(extraOptions);
    }
  }, [extraOptions]);

  const listItems = cart?.listItems?.length > 0 ? cart.listItems : order?.listItems || [];
  const creatorName = cart?.creator?.fullName || order?.creator?.fullName || '';
  const totalItems = listItems.reduce(
    (sum: number, currentItem: any) => sum + (currentItem.quantity || 1),
    0
  );

  const startingIndex = listItems
    .slice(0, index)
    .reduce((sum: number, currentItem: any) => sum + (currentItem.quantity || 1), 0);

  return (
    // <div style={{ fontFamily: 'Arial, sans-serif' }}>
    //   <div
    //     id={`print-label-content-${index}`}
    //     style={{
    //       maxWidth: '300px',
    //       padding: '10px',
    //       border: '1px solid #000',
    //       borderRadius: '4px',
    //       marginTop: '12px',
    //       fontSize: '14px',
    //       textAlign: 'left',
    //       overflow: 'hidden',
    //       wordBreak: 'break-word',
    //     }}
    //   >
    //     <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '6px' }}>
    //       <p style={{ fontWeight: 700, margin: 0, whiteSpace: "nowrap" }}>Mã đơn hàng:</p>
    //       <p style={{ fontWeight: 500, margin: 0, textAlign: 'right', whiteSpace: "normal", wordBreak: 'break-word' }}>
    //         {orderData.orderNo || ''}
    //       </p>
    //     </div>
    //     <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '6px' }}>
    //       <p style={{ fontWeight: 700, margin: 0, whiteSpace: "nowrap" }}>Tên khách hàng:</p>
    //       <p style={{ fontWeight: 500, margin: 0, textAlign: 'right', whiteSpace: "normal", wordBreak: 'break-word' }}>
    //         {orderData.creator?.fullName || ''}
    //       </p>
    //     </div>
    //     <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '6px', whiteSpace: "nowrap" }}>
    //       <p style={{ fontWeight: 700, margin: 0 }}>Tên sản phẩm:</p>
    //       <div>
    //         <p style={{ fontWeight: 500, margin: 0, textAlign: 'right', whiteSpace: "normal", wordBreak: 'break-word' }}>
    //           {item.itemsName || ''}
    //         </p>
    //         {item.variantNameOne && (
    //           <p style={{ fontWeight: 500, margin: 0, textAlign: 'right', whiteSpace: "normal", wordBreak: 'break-word' }}>
    //             {item.variantNameOne}: {item.variantValueOne}
    //           </p>
    //         )}
    //         {item.variantNameTwo && (
    //           <p style={{ fontWeight: 500, margin: 0, textAlign: 'right', whiteSpace: "normal", wordBreak: 'break-word' }}>
    //             {item.variantNameTwo}: {item.variantValueTwo}
    //           </p>
    //         )}
    //         {item.variantNameThree && (
    //           <p style={{ fontWeight: 500, margin: 0, textAlign: 'right', whiteSpace: "normal", wordBreak: 'break-word' }}>
    //             {item.variantNameThree}: {item.variantValueThree}
    //           </p>
    //         )}
    <>
      {Array.from({ length: item.quantity || 1 }).map((_, quantityIndex) => {
        const currentLabelIndex = startingIndex + quantityIndex + 1;
        return (
          <div
            key={`${index}-${quantityIndex}`}
            style={{ fontFamily: 'Arial, sans-serif' }}
          >
            <div
              id={`print-label-content-${index}-${quantityIndex}`}
              style={{
                maxWidth: '360px',
                padding: '16px',
                border: '1px solid #000',
                borderRadius: '4px',
                marginTop: 8,
                fontSize: '12px',
                textAlign: 'left',
                overflow: 'hidden',
                wordBreak: 'break-word',
              }}
            >
              <div style={{ display: 'flex', justifyContent: 'flex-start', marginBottom: 4 }}>
                <p style={{ fontWeight: 500, textAlign: 'left', margin: 0 }}>
                  <strong>{creatorName}</strong>
                </p>
              </div>
              <div style={{ marginBottom: 4 }}>
                <div style={{ display: 'flex', justifyContent: 'flex-start', marginBottom: 4 }}>
                  <p style={{ fontWeight: 'bold', textAlign: 'left', margin: 0, color: 'black' }}>
                    <strong>{item.itemsName || ''}</strong>
                  </p>
                </div>
                {item.variantNameOne && (
                  <p style={{ fontWeight: 'normal', textAlign: 'left', margin: 0, color: '#333333' }}>
                    <strong>{item.variantNameOne}: {item.variantValueOne}</strong>
                  </p>
                )}
                {item.variantNameTwo && (
                  <p style={{ fontWeight: 'normal', textAlign: 'left', margin: 0, color: '#333333' }}>
                    <strong>{item.variantNameTwo}: {item.variantValueTwo}</strong>
                  </p>
                )}
                {item.variantNameThree && (
                  <p style={{ fontWeight: 'normal', textAlign: 'left', margin: 0, color: '#333333' }}>
                    <strong>{item.variantNameThree}: {item.variantValueThree}</strong>
                  </p>
                )}
              </div>
              {optionDetails.map((detail, index) => (
                <p
                  key={index}
                  style={{
                    fontWeight: 'normal',
                    margin: 0,
                    wordBreak: 'break-word',
                    textAlign: 'left',
                    color: '#333333',
                  }}
                >
                  <strong>{detail.groupName} - {detail.optionName}</strong>
                </p>
              ))}
              {item.note && (
                <p style={{ fontWeight: 'normal', textAlign: 'left', margin: 0, color: '#333333' }}>
                  <strong>{item.note}</strong>
                </p>
              )}
              <div style={{ marginTop: 8, fontWeight: 500, textAlign: 'center' }}>
                <strong>{currentLabelIndex}/{totalItems}</strong>
              </div>
            </div>
          </div>
        );
      })}
    </>
  );
};

export default DetachableLabel;