import { apiClient } from "@/src/api/config/api-client";
import { API_PATHS } from "@/src/api/constants/api-paths";
import type { ApiError, ErrorConfig } from "@/src/api/types/error.types";
import type { ExtendedRequestConfig } from "@/src/api/types/api.types";
import {
  GetServiceRequest,
  GetServiceRequest2,
  GetServiceRequestBody,
} from "@/src/api/types/service.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export interface DataActiveStatePayment {
  paymentId: string;
  isActive: boolean;
  shopId: string;
}

export const paymentService = {
  getListPayment: async <T = any>(
    shopId: string,
    params: GetServiceRequest2,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (shopId) {
        const queryParams = [];
        if (params.skip !== undefined) queryParams.push(`skip=${params.skip}`);
        if (params.limit !== undefined) queryParams.push(`limit=${params.limit}`);

        const queryString = queryParams.join("&");
        const response = await apiClient.get<GetServiceRequestBody, T>(
          `${API_PATHS.PAYMENT_PARTNER.URL_PAYMENT_PARTNER}?ShopId=${shopId}&${queryString}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  activeStatePayment: async <T = any>(data: DataActiveStatePayment, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<any, T>(
        API_PATHS.PAYMENT_PARTNER.URL_PAYMENT_PARTNER,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getDetailPayment: async <T = any>(paymentId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (paymentId) {
        const response = await apiClient.get<any, T>(
          `${API_PATHS.PAYMENT_PARTNER.URL_PAYMENT_PARTNER}/${paymentId}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updatePayment: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<any, T>(
        `${API_PATHS.PAYMENT_PARTNER.URL_PAYMENT_PARTNER}`,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createPayment: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.PAYMENT_PARTNER.URL_PAYMENT_PARTNER,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deletePayment: async <T = any>(paymentId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.delete<any, T>(
        `${API_PATHS.PAYMENT_PARTNER.URL_PAYMENT_PARTNER}/${paymentId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getListBankPartner: async <T = any>(errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<any, T>(`${API_PATHS.BANK.LIST_BANK_PARTNER}`, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
