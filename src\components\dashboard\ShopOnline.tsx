import { Card, CardContent, Typography } from '@mui/material'
import { Box, Grid, Stack } from '@mui/system'
import React from 'react'
import * as Icons from "@mui/icons-material";
import { formatCurrency } from '@/src/utils/format-number';

const ShopOnline = ({ dashboardParter }) => {

  function DynamicIcon({ name }) {
    const IconComponent = Icons[name]; // Lấy icon theo tên
    return IconComponent ? <IconComponent /> : <span>Icon not found</span>;
  }

  return (
    <Stack>
      <Grid container spacing={2} sx={{ display: 'flex', justifyContent: 'space-between', flexWrap: 'nowrap', marginRight: 2.2 }}>
        {dashboardParter?.dataShopOnline?.map((item, index) => (
          <Grid sx={{ width: '25%', height: '100%' }} key={index}>
            <Card
              sx={{
                textAlign: "center",
                padding: '10px',
                height: '140px',
                borderRadius: '20px'
              }}
            >
              <CardContent sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'space-between', height: '100%', borderRadius: '20px' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'nowrap' }}>
                  <Typography sx={{ textAlign: 'left', fontSize: '16px', marginRight: '4px' }}>
                    <DynamicIcon name={item?.iconName} />
                  </Typography>
                  <Typography fontWeight='700' sx={{ textAlign: 'left', fontSize: '14px' }}>
                    {item?.title}
                  </Typography>
                </Box>
                <Typography fontWeight="700" fontSize='20px' sx={{ fontSize: '20px', textAlign: 'center' }}>
                  {item?.value}
                </Typography>

              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Stack>
  )
}

export default ShopOnline
