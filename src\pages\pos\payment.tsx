import { Container, Box, Grid, Button } from "@mui/material";
import LeftPanel from "src/components/Pos/Bill";
import RightPanel from "src/components/Pos/Calculator";
import { useEffect, useState } from "react";
import PaymentMethods from "@/src/components/Pos/paymentbill";
import { useOrder } from "@/src/api/hooks/order/use-order";
import Banking from "src/components/Pos/Banking";
interface PaymentConfirmationProps {
  onClose: () => void;
  orderData: any;
  cart: any;
  clearCart?: any;
}
const PaymentConfirmation = ({ onClose, orderData, cart, clearCart }: PaymentConfirmationProps) => {
  const [changeAmount, setChangeAmount] = useState(0);
  const [order, setOrder] = useState(null);
  const [shouldPrint, setShouldPrint] = useState(false);
  const [cartState, setCart] = useState(cart);
  const [currentPanel, setCurrentPanel] = useState<"RightPanel" | "Banking">("RightPanel");
  useEffect(() => {
    if (!cartState?.paymentSelected) {
      console.warn("paymentSelected is undefined. Check the logic updating cartState.");
    }
    if (cartState?.paymentSelected?.typePay?.toLowerCase() === "cash") {
      setCurrentPanel("RightPanel");
    } else {
      setCurrentPanel("Banking");
    }
  }, [cartState?.paymentSelected]);
  const handleChangeMoney = (money: number) => {
    setChangeAmount(money);
  };
  const { createOrder } = useOrder();
  const handlePrintAllLabels = async () => {
    try {
      const listItems = order ? order.listItems : cart.listItems || [];
      const labelContents = listItems
        .flatMap((item, index) => {
          return Array.from({ length: item.quantity || 1 }).map((_, quantityIndex) => {
            const labelContent = document.getElementById(
              `print-label-content-${index}-${quantityIndex}`
            )?.innerHTML;
            if (!labelContent) {
              console.warn(
                `Không tìm thấy nội dung in cho ID: print-label-content-${index}-${quantityIndex}`
              );
            }
            return labelContent ? `<div class="page">${labelContent}</div>` : null;
          });
        })
        .filter(Boolean);

      if (labelContents.length === 0) {
        console.error("Không tìm thấy nội dung in");
        return;
      }

      if (window.electron && typeof window.electron.invoke === "function") {
        const result = await window.electron.invoke("print-label", labelContents);
      } else {
        console.warn("Electron API không khả dụng, chuyển sang in truyền thống.");
        const printWindow = window.open("", "", "width=600,height=400");
        if (!printWindow) {
          console.error("Không thể mở cửa sổ in. Có thể bị chặn bởi trình duyệt.");
          return;
        }
        printWindow.document.write(`
                    <html>
                        <head>
                            <title>In Tem</title>
                            <style>
                                @page {
                                    margin: 25mm 20mm 20mm 20mm;
                                }
                                body {
                                    font-family: Arial, sans-serif;
                                    font-size: 10px;
                                    margin: 0;
                                    padding: 10px;
                                }
                                .page {
                                    page-break-after: always; 
                                    margin: 0;
                                    padding: 10px;
                                }
                            </style>
                        </head>
                        <body>
                            ${labelContents.join("")}
                        </body>
                    </html>
                `);
        printWindow.document.close();
        printWindow.focus();
        printWindow.print();
        printWindow.close();
      }
    } catch (error) {
      console.error("Error printing labels:", error);
    }
  };

  const handlePrintBill = async () => {
    try {
      if (order) {
        setShouldPrint(true);
        return;
      }
      if (cart.listItems.length === 0) {
        console.error("Thông tin đơn hàng không hợp lệ.");
        return;
      }

      const { paymentSelected, ...cartFinal } = cartState;
      const response = await createOrder({
        ...cartFinal,
        cartOrigin: "Pos",
      });

      if (response && response.data) {
        setOrder(response.data);
        clearCart();
        setShouldPrint(true);
      } else {
        console.error("Tạo đơn hàng thất bại.");
      }
    } catch (error) {
      console.error("Lỗi khi tạo đơn hàng hoặc in hóa đơn:", error);
    }
  };
  useEffect(() => {
    if (shouldPrint && order) {
      const printContent = document.getElementById("print-invoice")?.innerHTML;
      if (!printContent) {
        console.error("Không tìm thấy nội dung hóa đơn để in.");
        return;
      }

      if (window.electron && typeof window.electron.invoke === "function") {
        window.electron
          .invoke("print-invoice", printContent)
          .catch((error) => console.error("Lỗi khi in hóa đơn qua Electron:", error));
      } else {
        console.warn("Electron API không khả dụng, chuyển sang in truyền thống.");
        const printWindow = window.open("", "", "width=900,height=650");
        if (!printWindow) {
          console.error("Không thể mở cửa sổ in. Có thể bị chặn bởi trình duyệt.");
          return;
        }
        printWindow.document.write(`
                    <html>
                        <head>
                            <title>Hóa Đơn</title>
                        </head>
                        <body>
                            ${printContent}
                        </body>
                    </html>
                `);
        printWindow.document.close();
        printWindow.focus();
        printWindow.print();
        printWindow.close();
      }

      setShouldPrint(false);
    }
  }, [shouldPrint, order]);
  return (
    <Container
      maxWidth={false}
      disableGutters
      sx={{ height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
    >
      <Box
        sx={{
          width: "95%",
          height: "85%",
          backgroundColor: "white",
          borderRadius: 1,
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
        }}
      >
        <Box sx={{ flex: 1, overflowY: "auto", padding: 1 }}>
          <Grid container sx={{ height: "100%" }}>
            <Grid
              item
              xs={12}
              md={4}
              sx={{
                height: 400,
                display: "flex",
                flexDirection: "column",
              }}
            >
              <PaymentMethods cart={cartState} setCart={setCart} />
            </Grid>
            <Grid
              item
              xs={12}
              md={4}
              sx={{
                height: 400,
                display: "flex",
                flexDirection: "column",
                overflowY: "auto",
                "&::-webkit-scrollbar": {
                  width: "2px",
                },
                "&::-webkit-scrollbar-thumb": {
                  backgroundColor: "rgba(0, 0, 0, 0.2)",
                  borderRadius: "3px",
                },
                "&::-webkit-scrollbar-thumb:hover": {
                  backgroundColor: "rgba(0, 0, 0, 0.4)",
                },
                "&::-webkit-scrollbar-track": {
                  backgroundColor: "rgba(0, 0, 0, 0.1)",
                },
              }}
            >
              <LeftPanel changeAmount={changeAmount} cart={cartState} order={order} />
            </Grid>
            <Grid
              item
              xs={12}
              md={4}
              sx={{
                height: 400,
                display: "flex",
                flexDirection: "column",
              }}
            >
              {currentPanel === "RightPanel" ? (
                <RightPanel
                  onClose={onClose}
                  initialTotalAmount={cartState?.price}
                  handleChangeMoney={handleChangeMoney}
                />
              ) : (
                <Banking
                  onClose={onClose}
                  bankName={cartState?.bankName || ""}
                  totalAmount={cartState?.price || 0}
                  paymentSelected={cartState?.paymentSelected}
                  cart={cartState}
                />
              )}
            </Grid>
          </Grid>
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            padding: 1,
            position: "sticky",
            bottom: 0,
            backgroundColor: "white",
            zIndex: 10,
            boxShadow: "0 -2px 5px rgba(0, 0, 0, 0.1)",
          }}
        >
          <Button
            variant="contained"
            sx={{
              height: 40,
              width: 100,
              backgroundColor: "#C7C7C7",
              color: "black",
              fontSize: "0.8rem",
            }}
            onClick={onClose}
          >
            Đóng
          </Button>
          <Box sx={{ display: "flex", gap: 1 }}>
            <Button
              variant="contained"
              sx={{
                height: 40,
                width: 100,
                backgroundColor: "#FF0F0F",
                color: "white",
                fontSize: "0.8rem",
              }}
              onClick={handlePrintAllLabels}
            >
              In Tem
            </Button>
            <Button
              variant="contained"
              sx={{
                height: 40,
                width: 100,
                backgroundColor: "#2654FE",
                color: "white",
                fontSize: "0.8rem",
              }}
              onClick={handlePrintBill}
            >
              In Bill
            </Button>
          </Box>
        </Box>
      </Box>
    </Container>
  );
};

export default PaymentConfirmation;
