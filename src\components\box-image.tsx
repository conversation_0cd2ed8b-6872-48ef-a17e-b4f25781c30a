import { Box, Typography } from '@mui/material';
import React from 'react';

export default function BoxImage({ link, alt, children }) {
  return (
    <Box display="flex" gap={1} alignItems="center">
      <Box
        sx={{
          width: 40, // <PERSON><PERSON><PERSON> thước chiều ngang
          height: 40, // <PERSON><PERSON><PERSON> thước chiều dọc (vuông)
          overflow: 'hidden', // Ẩn phần hình ảnh vượt ra ngoài
          borderRadius: 0.5, // Bo góc, nếu cần
          boxShadow: 3,
          flexShrink: 0, // Đổ bóng
        }}
      >
        <img
          src={link} // Thay đường dẫn hình ảnh tại đây
          alt={alt}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover', // Đ<PERSON>m bảo hình ảnh vừa khít khung mà không bị méo
          }}
        />
      </Box>
      {children}
    </Box>
  );
}
