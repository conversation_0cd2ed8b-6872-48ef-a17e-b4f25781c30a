import React, { useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  IconButton,
  Box,
  Typography,
  Select,
  MenuItem,
  Paper,
  TextField,
  InputAdornment,
  Button,
  Switch,
  Modal,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Stack,
  Input,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  CircularProgress,
  Grid,
  Tooltip,
} from "@mui/material";
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  NavigateBefore as NavigateBeforeIcon,
  NavigateNext as NavigateNextIcon,
  Search as SearchIcon,
  FileDownload as FileDownloadIcon,
  Close as CloseIcon,
  Label,
  IosShare,
  DateRange,
  Refresh,
  RefreshOutlined,
} from "@mui/icons-material";
import { on } from "events";
import { DatePicker, DateTimePicker, LocalizationProvider } from "@mui/x-date-pickers";
import Link from "next/link";
import { Calendar, Eye, EyeOff, Plus } from "@untitled-ui/icons-react";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { margin } from "@mui/system";
import ModalAddZns from "./component/modal-add-zns";
import { shopSettingService } from "@/src/api/services/shop-setting/shop-setting.service";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useZaloAutomation } from "@/src/api/hooks/zalo-automation/zalo-automation";
import {
  IBodyTemplateZnsUpdate,
  ZnsTemplateDto,
} from "@/src/api/services/zalo-automation/zalo-automation.service";
import { formatPrice } from "@/src/api/types/membership.types";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useDebounce } from "@/src/hooks/use-debounce";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { usePathname } from "next/navigation";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import TruncatedText from "@/src/components/truncated-text/truncated-text";

const getStatusInfo = (status) => {
  switch (status) {
    case "ENABLE":
      return { title: "Đang hoạt động", color: "#e6f4ea", textColor: "#2e7d32" }; // Xanh lá nhạt
    case "PENDING_REVIEW":
      return { title: "Chờ duyệt", color: "#fff8e1", textColor: "#f9a825" }; // Vàng nhạt
    case "REJECT":
      return { title: "Bị từ chối", color: "#ffebee", textColor: "#c62828" }; // Đỏ nhạt
    case "DISABLE":
      return { title: "Đã vô hiệu hóa", color: "#eceff1", textColor: "#546e7a" }; // Xám nhạt
    default:
      return { title: "Không xác định", color: "#eeeeee", textColor: "#616161" };
  }
};

export function formatTimeout(ms: number): string {
  const totalSeconds = Math.floor(ms / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  const parts = [];
  if (hours > 0) parts.push(`${hours} giờ`);
  if (minutes > 0) parts.push(`${minutes} phút`);
  if (seconds > 0 || parts.length === 0) parts.push(`${seconds} giây`);

  return parts.join(" ");
}
const valueToLabelMap = {
  UNKNOWN: "Không xác định",
  TEXT: "Văn bản",
  OTP: "OTP",
  TABLE: "Bảng",
  RATING: "Đánh giá",
};

function getLabelFromValue(value) {
  return valueToLabelMap[value] || "Không xác định";
}
const ZnsMessageTemplate = () => {
  const { getListTemplateZns, getTemplateZnsFromZalo, getDetailTemplateZns, updateTemplateZns } =
    useZaloAutomation();
  const storeId = useStoreId();
  const [rows, setRows] = useState<ZnsTemplateDto[]>([]);
  const [selected, setSelected] = useState<number[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [isOpenModalCreate, setIsOpenModalCreate] = useState<boolean>(false);
  const [campaignType, setCampaignType] = useState("oneTime");
  const [isDetail, setIsDetail] = useState(false);
  const [isOpenModalDelete, setIsOpenModalDelete] = useState(false);
  const [isOpenModalUpdate, setIsOpenModalUpdate] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedRow, setSelectedRow] = useState<ZnsTemplateDto | null>(null);
  const [isOpenViewModal, setIsOpenViewModal] = useState(false);
  const [isOpenUpdateModal, setIsOpenUpdateModal] = useState(false);
  const [zaloOAName, setZaloOAName] = useState("");
  const [templateName, setTemplateName] = useState("");
  const [templateId, setTemplateId] = useState("");
  const snackbar = useSnackbar();
  const debouncedTemplateName = useDebounce(templateName, 600);
  const debouncedTemplateId = useDebounce(templateId, 600);
  const fetchListTemplateZns = async () => {
    const res = await getListTemplateZns(storeId, debouncedTemplateName, debouncedTemplateId);
    if (res && res?.status === 200) {
      if (Array.isArray(res?.data?.data) && res?.data?.data.length > 0) {
        setRows(res?.data?.data);
      }
    }
  };

  const pathname = usePathname();
  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  useEffect(() => {
    fetchListTemplateZns();
  }, [storeId, debouncedTemplateName, debouncedTemplateId]);

  // const handleSelectAllClick = (event) => {
  //   if (event.target.checked) {
  //     const newSelected = rows.map((n) => n.id);
  //     setSelected(newSelected);
  //     return;
  //   }
  //   setSelected([]);
  // };

  const handleClick = (id: number) => {
    const selectedIndex = selected.indexOf(id);
    let newSelected: number[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      );
    }
    setSelected(newSelected);
  };

  const isSelected = (id: number) => selected.indexOf(id) !== -1;

  const handleChangePage = (event, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  const searchFieldProps = {
    placeholder: "Tìm kiếm",
    variant: "outlined" as "outlined",
    size: "small" as "small",
    sx: {
      width: { xs: "100%", sm: 240 },
      "& .MuiOutlinedInput-root": {
        borderRadius: 30,
        backgroundColor: "#fff",
      },
      marginRight: 1,
    },
    InputProps: {
      startAdornment: (
        <InputAdornment position="start">
          <SearchIcon fontSize="small" />
        </InputAdornment>
      ),
    },
  };

  const importButtonProps = {
    variant: "outlined" as "outlined",
    startIcon: <FileDownloadIcon />,
    sx: {
      textTransform: "none",
      width: { xs: "100%", sm: "auto" },
      border: "none",
      boxShadow: "none",
    },
    children: "Import",
  };

  const addButtonProps = {
    variant: "contained" as "contained",
    children: "Tạo chiến dịch",
    onClick: () => setIsOpenModalCreate(true),
  };

  const tableContainerProps = {
    sx: {
      overflowX: "auto",
      maxWidth: "100%",
    },
  };

  const paginationBoxProps = {
    sx: {
      display: "flex",
      justifyContent: "flex-end",
      alignItems: "center",
      px: 2,
      py: 1.5,
      flexDirection: { xs: "column", sm: "row" },
      gap: 1,
    },
  };

  const handleDeleteCampaign = (campaign) => {};

  const fetchShopSettings = async () => {
    if (!storeId) return;
    try {
      const response = await shopSettingService.getDetailShopSetting(storeId);
      if (response?.data) {
        setZaloOAName(response.data.zaloOAName);
      } else {
      }
    } catch (error) {
      console.error("Error fetching shop settings:", error);
    } finally {
    }
  };
  useEffect(() => {
    fetchShopSettings();
  }, [storeId]);

  const handleUpdateTemplateZns = async () => {
    setIsLoading(true);
    const res = await getTemplateZnsFromZalo(storeId);
    if (res && res?.status === 200) {
      snackbar.success("Cập nhật mẫu tin thành công");
      fetchListTemplateZns();
    } else {
      snackbar.error("Cập nhật mẫu tin thất bại");
    }
    setIsLoading(false);
  };

  const handleSaveTemplateZns = async () => {
    const data: IBodyTemplateZnsUpdate = {
      shopId: selectedRow.shopId,
      znsId: selectedRow.znsId,
      templateType: selectedRow.templateType,
    };
    const res = await updateTemplateZns(data);
    if (res?.status === 200) {
      setIsOpenUpdateModal(false);
      snackbar.success("Cập nhật mẫu tin ZNS thành công");
      fetchListTemplateZns();
    }
  };

  return (
    <>
      <Box sx={{ p: 2 }}>
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", md: "row" },
            alignItems: { xs: "stretch", md: "center" },
            justifyContent: "space-between",
            gap: { xs: 2, md: 3 },
            marginBottom: 3,
          }}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: { xs: "column", sm: "row" },
              alignItems: { xs: "stretch", sm: "center" },
              gap: { xs: 2, sm: 1.5 },
              width: { xs: "100%", md: "auto" },
            }}
          >
            <TextField
              value={zaloOAName}
              disabled
              variant="outlined"
              size="small"
              sx={{
                minWidth: { xs: "100%", sm: 200 },
                "& .MuiInputBase-input.Mui-disabled": {
                  fontWeight: "500",
                  color: "#1e1e1e",
                  WebkitTextFillColor: "#1e1e1e",
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#e0e0e0",
                },
                "& .MuiOutlinedInput-root.Mui-disabled": {
                  backgroundColor: "#fff",
                },
                borderRadius: "8px",
              }}
            />
            <TextField
              placeholder="Tên mẫu"
              value={templateName}
              onChange={(e) => setTemplateName(e.target.value)}
              variant="outlined"
              size="small"
              sx={{
                minWidth: { xs: "100%", sm: 200 },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#e0e0e0",
                },
                "&:hover .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#bdbdbd",
                },
                "& .MuiInputBase-input": {
                  fontWeight: 500,
                  color: "#1e1e1e",
                },
                borderRadius: "8px",
              }}
            />
            <TextField
              placeholder="Template ID"
              value={templateId}
              onChange={(e) => setTemplateId(e.target.value)}
              variant="outlined"
              size="small"
              sx={{
                minWidth: { xs: "100%", sm: 200 },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#e0e0e0",
                },
                "&:hover .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#bdbdbd",
                },
                "& .MuiInputBase-input": {
                  fontWeight: 500,
                  color: "#1e1e1e",
                },
                borderRadius: "8px",
              }}
            />
          </Box>

          <Box
            sx={{
              display: "flex",
              flexDirection: { xs: "column", sm: "row" },
              alignItems: "center",
              gap: { xs: 1, sm: 1.5 },
              width: { xs: "100%", md: "auto" },
            }}
          >
            <Button
              sx={{
                padding: "8px",
                width: { xs: "100%", sm: "auto" },
                maxHeight: "36.5px",
              }}
              variant="contained"
              onClick={handleUpdateTemplateZns}
              // disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <CircularProgress size={18} color="inherit" sx={{ marginRight: 1 }} />
                  <Typography
                    sx={{
                      fontSize: { xs: 16, sm: 14 },
                      fontWeight: 500,
                    }}
                  >
                    Đang cập nhật...
                  </Typography>
                </>
              ) : (
                <>
                  <RefreshOutlined sx={{ width: { xs: 20, sm: 16 } }} />
                  <Typography
                    sx={{
                      fontSize: { xs: 16, sm: 14 },
                      fontWeight: 500,
                      paddingLeft: 0.5,
                    }}
                  >
                    Cập nhật mẫu tin
                  </Typography>
                </>
              )}
            </Button>
            {isGranted(pathname, PERMISSION_TYPE_ENUM.Add) ? (
              <Button
                sx={{
                  padding: "8px",
                  width: { xs: "100%", sm: "auto" },
                  maxHeight: "36.5px",
                }}
                onClick={() => setIsOpenModalCreate(true)}
                variant="contained"
              >
                <Plus fontSize={1} width={18} />
                <Typography
                  sx={{
                    fontSize: { xs: 16, sm: 14 },
                    fontWeight: 500,
                    paddingLeft: 0.5,
                  }}
                >
                  Thêm Template
                </Typography>
              </Button>
            ) : (
              <Tooltip title="Bạn không có quyền thêm">
                <span>
                  <Button
                    sx={{
                      padding: "8px",
                      width: { xs: "100%", sm: "auto" },
                      maxHeight: "36.5px",
                    }}
                    disabled={true}
                    variant="contained"
                  >
                    <Plus fontSize={1} width={18} />
                    <Typography
                      sx={{
                        fontSize: { xs: 16, sm: 14 },
                        fontWeight: 500,
                        paddingLeft: 0.5,
                      }}
                    >
                      Thêm Template
                    </Typography>
                  </Button>
                </span>
              </Tooltip>
            )}
          </Box>
        </Box>
      </Box>
      <Paper
        elevation={3}
        sx={{
          borderRadius: 2,
          overflow: "hidden",
          // Make table container scrollable horizontally on mobile
          "& .MuiTableContainer-root": {
            maxWidth: "100vw",
            overflowX: "auto",
          },
        }}
      >
        <TableContainer>
          <Table
            stickyHeader
            sx={{
              // Adjust table cell padding on mobile
              "& .MuiTableCell-root": {
                px: { xs: 1, sm: 2 },
                py: { xs: 1, sm: 1.5 },
                "&:first-of-type": {
                  pl: { xs: 2, sm: 3 },
                },
                "&:last-of-type": {
                  pr: { xs: 2, sm: 3 },
                },
              },
              // Adjust text size on mobile
              "& .MuiTableCell-body": {
                fontSize: { xs: "0.875rem", sm: "1rem" },
              },
              // Make status badges responsive
              "& .status-badge": {
                minWidth: { xs: "110px", sm: "140px" },
                height: { xs: "28px", sm: "32px" },
                fontSize: { xs: "0.75rem", sm: "0.875rem" },
              },
            }}
          >
            <TableHead>
              <TableRow>
                <TableCell align="center" sx={{ width: 5 }}>
                  STT
                </TableCell>
                <TableCell align="center" sx={{ width: 50 }}>
                  ID
                </TableCell>
                <TableCell sx={{ width: 300 }}>Tên mẫu ZNS</TableCell>
                <TableCell sx={{ width: 150 }}>Loại mẫu</TableCell>
                <TableCell sx={{ width: 150 }}>Ngày tạo</TableCell>
                <TableCell align="center" sx={{ width: 100 }}>
                  Giá (VNĐ)
                </TableCell>
                <TableCell sx={{ width: 100 }}>Trạng thái</TableCell>
                <TableCell sx={{ width: 80 }}>Quản lý</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {" "}
              {rows
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((row, index) => {
                  return (
                    <>
                      <TableRow hover role="checkbox" tabIndex={-1} key={row.znsId}>
                        <TableCell align="center">{page * rowsPerPage + index + 1}</TableCell>
                        <TableCell align="center" component="th" scope="row">
                          {row.templateId}
                        </TableCell>
                        <TableCell>
                          <TruncatedText text={row.templateName} typographyProps={{ width: 350 }} />
                        </TableCell>
                        <TableCell>{getLabelFromValue(row.templateType)}</TableCell>
                        <TableCell>{row.templateCreatedAt}</TableCell>
                        <TableCell align="center">{formatPrice(row.price)}</TableCell>
                        <TableCell>
                          {(() => {
                            const statusInfo = getStatusInfo(row.status);
                            return (
                              <span
                                className="status-badge"
                                style={{
                                  backgroundColor: statusInfo.color,
                                  color: statusInfo.textColor,
                                  padding: "4px 8px",
                                  borderRadius: "12px",
                                  fontWeight: 500,
                                  display: "inline-flex",
                                  alignItems: "center",
                                  justifyContent: "center",
                                  textAlign: "center",
                                }}
                              >
                                {statusInfo.title}
                              </span>
                            );
                          })()}
                        </TableCell>{" "}
                        <TableCell align="center" sx={{ display: "flex" }}>
                          <Tooltip title="Xem chi tiết mẫu tin">
                            <IconButton
                              onClick={() => {
                                setSelectedRow(row);
                                setIsOpenViewModal(true);
                              }}
                              sx={{
                                color: "primary.main",
                                "&:hover": {
                                  backgroundColor: "rgba(0, 0, 0, 0.04)",
                                },
                              }}
                            >
                              <Eye />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Cập nhật mẫu tin">
                            <IconButton
                              onClick={() => {
                                setSelectedRow(row);
                                setIsOpenUpdateModal(true);
                              }}
                              sx={{
                                color: "primary.main",
                                "&:hover": {
                                  backgroundColor: "rgba(0, 0, 0, 0.04)",
                                },
                              }}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    </>
                  );
                })}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
      <Box
        sx={{
          display: "flex",
          flexDirection: { xs: "column", sm: "row" },
          alignItems: "center",
          justifyContent: "end",
          px: { xs: 1, sm: 2 },
          py: { xs: 2, sm: 1.5 },
          gap: { xs: 2, sm: 0 },
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            flexDirection: { xs: "column", sm: "row" },
            gap: { xs: 1, sm: 2 },
            width: { xs: "100%", sm: "auto" },
          }}
        >
          <Typography
            variant="body2"
            sx={{
              fontSize: { xs: "0.875rem", sm: "1rem" },
            }}
          >
            Số dòng mỗi trang
          </Typography>

          <Select
            value={rowsPerPage}
            onChange={handleChangeRowsPerPage}
            size="small"
            sx={{
              minWidth: { xs: "100%", sm: 60 },
              "& .MuiOutlinedInput-notchedOutline": { border: "none" },
              "& .MuiSelect-select": {
                padding: { xs: "8px", sm: "4px 8px" },
                fontSize: { xs: "0.875rem", sm: "1rem" },
              },
            }}
          >
            {" "}
            <MenuItem value={10}>10</MenuItem>
            <MenuItem value={25}>25</MenuItem>
            <MenuItem value={50}>50</MenuItem>
          </Select>

          <Typography
            variant="body2"
            sx={{
              fontSize: { xs: "0.875rem", sm: "1rem" },
            }}
          >
            {`${page * rowsPerPage + 1}–${Math.min((page + 1) * rowsPerPage, rows.length)} của ${
              rows.length
            }`}
          </Typography>
        </Box>

        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "center", sm: "flex-end" },
            width: { xs: "100%", sm: "auto" },
          }}
        >
          <IconButton
            disabled={page === 0}
            onClick={() => handleChangePage(null, page - 1)}
            sx={{
              padding: { xs: "8px", sm: "12px" },
            }}
          >
            <NavigateBeforeIcon sx={{ fontSize: { xs: "1.25rem", sm: "1.5rem" } }} />
          </IconButton>

          <IconButton
            disabled={page >= Math.ceil(rows.length / rowsPerPage) - 1}
            onClick={() => handleChangePage(null, page + 1)}
            sx={{
              padding: { xs: "8px", sm: "12px" },
            }}
          >
            <NavigateNextIcon sx={{ fontSize: { xs: "1.25rem", sm: "1.5rem" } }} />
          </IconButton>
        </Box>
      </Box>{" "}
      {isOpenModalCreate && (
        <ModalAddZns
          isOpenModalCreate={isOpenModalCreate}
          setIsOpenModalCreate={setIsOpenModalCreate}
          fetchListTemplateZns={fetchListTemplateZns}
        />
      )}
      <Dialog
        open={isOpenUpdateModal}
        onClose={() => setIsOpenUpdateModal(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Cập nhật mẫu tin ZNS
          <IconButton
            aria-label="close"
            onClick={() => setIsOpenUpdateModal(false)}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>{" "}
        <DialogContent
          dividers
          sx={{
            padding: 0, // Bỏ padding mặc định
            overflow: "hidden", // Ngăn scroll ngoài
          }}
        >
          {selectedRow && (
            <Box sx={{ display: "flex", height: "55vh" }}>
              <Box
                sx={{
                  p: 2,
                  width: "50%",
                  overflowY: "auto",
                  "&::-webkit-scrollbar": { width: "8px" },
                  "&::-webkit-scrollbar-thumb": { backgroundColor: "#bdbdbd", borderRadius: "4px" },
                }}
              >
                <Grid container spacing={2} sx={{}}>
                  <Grid
                    item
                    xs={12}
                    sm={12}
                    sx={{ display: "flex", justifyContent: "space-between", paddingBottom: 2 }}
                  >
                    <Typography variant="subtitle2" gutterBottom>
                      Trạng thái
                    </Typography>
                    {(() => {
                      const statusInfo = getStatusInfo(selectedRow.status);
                      return (
                        <span
                          className="status-badge"
                          style={{
                            backgroundColor: statusInfo.color,
                            color: statusInfo.textColor,
                            padding: "4px 8px",
                            borderRadius: "12px",
                            fontWeight: 500,
                            display: "inline-flex",
                            alignItems: "center",
                            justifyContent: "center",
                            textAlign: "center",
                          }}
                        >
                          {statusInfo.title}
                        </span>
                      );
                    })()}
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    sm={12}
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      borderTop: "1px solid rgb(240, 240, 240)",
                      paddingLeft: 2,
                      paddingBottom: 2,
                    }}
                  >
                    <Typography variant="subtitle2" gutterBottom>
                      ID mẫu tin
                    </Typography>
                    <Typography>{selectedRow.templateId}</Typography>
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    sm={12}
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      borderTop: "1px solid rgb(240, 240, 240)",
                      paddingBottom: 2,
                    }}
                  >
                    <Typography variant="subtitle2" gutterBottom>
                      Tên mẫu
                    </Typography>
                    <Typography>{selectedRow.templateName}</Typography>
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    sm={12}
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      paddingBottom: 2,
                      borderTop: "1px solid rgb(240, 240, 240)",
                    }}
                  >
                    <Typography variant="subtitle2" gutterBottom>
                      Loại mẫu
                    </Typography>
                    {/* <Typography>{selectedRow.templateType}</Typography> */}
                    <Select
                      fullWidth
                      name="templateType"
                      displayEmpty
                      onChange={(e) =>
                        setSelectedRow((prev) => ({ ...prev, templateType: e.target.value }))
                      }
                      value={getLabelFromValue(selectedRow.templateType)}
                      renderValue={(selected) => {
                        if (!selected) {
                          return <Typography sx={{ color: "#9CA3AF" }}>Chọn loại mẫu</Typography>;
                        }

                        const options = {
                          Unknown: "Không xác định",
                          Text: "Văn bản",
                          OTP: "OTP",
                          Table: "Bảng",
                          Rating: "Đánh giá",
                        };

                        return options[selected] || selected;
                      }}
                      sx={{
                        borderRadius: "8px",
                        height: "40px",
                        width: 200,
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#E5E7EB",
                        },
                        "&:hover .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#D1D5DB",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#6366F1",
                        },
                        "& .MuiSelect-select": {
                          padding: "8px 14px",
                        },
                      }}
                      MenuProps={{
                        PaperProps: {
                          sx: {
                            borderRadius: "8px",
                            boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.1)",
                          },
                        },
                      }}
                    >
                      <MenuItem value="UNKNOWN">Không xác định</MenuItem>
                      <MenuItem value="TEXT">Văn bản</MenuItem>
                      <MenuItem value="OTP">OTP</MenuItem>
                      <MenuItem value="TABLE">Bảng</MenuItem>
                      <MenuItem value="RATING">Đánh giá</MenuItem>
                    </Select>
                  </Grid>

                  <Grid
                    item
                    xs={12}
                    sm={12}
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      paddingBottom: 2,
                      borderTop: "1px solid rgb(240, 240, 240)",
                    }}
                  >
                    <Typography variant="subtitle2" gutterBottom>
                      Đơn giá
                    </Typography>
                    <Typography>{formatPrice(selectedRow.price)} VNĐ</Typography>
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    sm={12}
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      borderTop: "1px solid rgb(240, 240, 240)",
                      paddingBottom: 2,
                    }}
                  >
                    <Typography variant="subtitle2" gutterBottom>
                      Thời gian chờ
                    </Typography>
                    <Typography>{formatTimeout(selectedRow.timeout)}</Typography>
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    sm={12}
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      borderTop: "1px solid rgb(240, 240, 240)",
                    }}
                  >
                    <Typography variant="subtitle2" gutterBottom>
                      Ngày tạo
                    </Typography>
                    <Typography>{selectedRow.templateCreatedAt}</Typography>
                  </Grid>
                </Grid>
              </Box>{" "}
              <Box sx={{ width: "50%", height: "100%", borderLeft: "1px solid #e0e0e0" }}>
                <iframe
                  src={selectedRow.previewUrl}
                  title="ZNS Preview"
                  width="100%"
                  height="100%"
                  style={{ border: "none" }}
                />
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button variant="outlined" onClick={() => setIsOpenUpdateModal(false)}>
            Đóng
          </Button>
          <Button variant="contained" onClick={handleSaveTemplateZns}>
            Lưu
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={isOpenViewModal}
        onClose={() => setIsOpenViewModal(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Chi tiết mẫu tin ZNS
          <IconButton
            aria-label="close"
            onClick={() => setIsOpenViewModal(false)}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>{" "}
        <DialogContent
          dividers
          sx={{
            padding: 0, // Bỏ padding mặc định
            overflow: "hidden", // Ngăn scroll ngoài
          }}
        >
          {selectedRow && (
            <Box sx={{ display: "flex", height: "55vh" }}>
              <Box
                sx={{
                  p: 2,
                  width: "50%",
                  overflowY: "auto",
                  "&::-webkit-scrollbar": { width: "8px" },
                  "&::-webkit-scrollbar-thumb": { backgroundColor: "#bdbdbd", borderRadius: "4px" },
                }}
              >
                <Grid container spacing={2} sx={{}}>
                  <Grid
                    item
                    xs={12}
                    sm={12}
                    sx={{ display: "flex", justifyContent: "space-between", paddingBottom: 2 }}
                  >
                    <Typography variant="subtitle2" gutterBottom>
                      Trạng thái
                    </Typography>
                    {(() => {
                      const statusInfo = getStatusInfo(selectedRow.status);
                      return (
                        <span
                          className="status-badge"
                          style={{
                            backgroundColor: statusInfo.color,
                            color: statusInfo.textColor,
                            padding: "4px 8px",
                            borderRadius: "12px",
                            fontWeight: 500,
                            display: "inline-flex",
                            alignItems: "center",
                            justifyContent: "center",
                            textAlign: "center",
                          }}
                        >
                          {statusInfo.title}
                        </span>
                      );
                    })()}
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    sm={12}
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      borderTop: "1px solid rgb(240, 240, 240)",
                      paddingLeft: 2,
                      paddingBottom: 2,
                    }}
                  >
                    <Typography variant="subtitle2" gutterBottom>
                      ID mẫu tin
                    </Typography>
                    <Typography>{selectedRow.templateId}</Typography>
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    sm={12}
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      borderTop: "1px solid rgb(240, 240, 240)",
                      paddingBottom: 2,
                    }}
                  >
                    <Typography variant="subtitle2" gutterBottom>
                      Tên mẫu
                    </Typography>
                    <Typography>{selectedRow.templateName}</Typography>
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    sm={12}
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      paddingBottom: 2,
                      borderTop: "1px solid rgb(240, 240, 240)",
                    }}
                  >
                    <Typography variant="subtitle2" gutterBottom>
                      Loại mẫu
                    </Typography>
                    <Typography>{selectedRow.templateType}</Typography>
                  </Grid>

                  <Grid
                    item
                    xs={12}
                    sm={12}
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      paddingBottom: 2,
                      borderTop: "1px solid rgb(240, 240, 240)",
                    }}
                  >
                    <Typography variant="subtitle2" gutterBottom>
                      Đơn giá
                    </Typography>
                    <Typography>{formatPrice(selectedRow.price)} VNĐ</Typography>
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    sm={12}
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      borderTop: "1px solid rgb(240, 240, 240)",
                      paddingBottom: 2,
                    }}
                  >
                    <Typography variant="subtitle2" gutterBottom>
                      Thời gian chờ
                    </Typography>
                    <Typography>{formatTimeout(selectedRow.timeout)}</Typography>
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    sm={12}
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      borderTop: "1px solid rgb(240, 240, 240)",
                    }}
                  >
                    <Typography variant="subtitle2" gutterBottom>
                      Ngày tạo
                    </Typography>
                    <Typography>{selectedRow.templateCreatedAt}</Typography>
                  </Grid>
                </Grid>
              </Box>{" "}
              <Box sx={{ width: "50%", height: "100%", borderLeft: "1px solid #e0e0e0" }}>
                <iframe
                  src={selectedRow.previewUrl}
                  title="ZNS Preview"
                  width="100%"
                  height="100%"
                  style={{ border: "none" }}
                />
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsOpenViewModal(false)}>Đóng</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ZnsMessageTemplate;
