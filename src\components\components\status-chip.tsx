import { Chip } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { tokens } from '@/src/locales/tokens';

interface StatusChipProps {
  status: 'Actived' | 'InActived' | 'Publish' | 'UnPublish';
  type?: 'category' | 'product' | 'service';
}

export const StatusChip = ({ status, type = 'category' }: StatusChipProps) => {
  const { t } = useTranslation();
  const isActive = status === 'Actived' || status === 'Publish';

  return (
    <Chip
      label={
        isActive
          ? t(tokens.contentManagement[type].status.published)
          : t(tokens.contentManagement[type].status.unpublished)
      }
      color={isActive ? 'success' : 'default'}
      sx={{
        backgroundColor: isActive ? 'rgba(84, 214, 44, 0.16)' : 'rgba(145, 158, 171, 0.16)',
        color: isActive ? 'rgb(34, 154, 22)' : 'rgb(99, 115, 129)'
      }}
    />
  );
};
