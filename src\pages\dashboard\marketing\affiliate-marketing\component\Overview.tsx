import React, { useC<PERSON>back, useEffect, useState } from "react";
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  ToggleButtonGroup,
  ToggleButton,
  TextField,
  TableRow,
  Stack,
  Button,
  Paper,
  Pagination,
  Select,
  MenuItem,
  FormControl,
  TablePagination,
  InputAdornment,
} from "@mui/material";
import { Line } from "react-chartjs-2";
import "chart.js/auto";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import { format, subDays, differenceInDays, parse, isSameDay } from "date-fns";
import DownloadIcon from "@mui/icons-material/Download";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider, DateRangePicker } from "@mui/x-date-pickers-pro";
import toast from "react-hot-toast";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useAffiliation } from "@/src/api/hooks/affiliation/use-affiliation";
import {
  AffiliationConversionRateType,
  GetOverviewPartnerRequest,
  ReportOverviewInterface,
} from "@/src/api/types/affiliation.type";
import dayjs from "dayjs";
import { formatCurrency } from "@/src/utils/format-number";
import { ChartOptions } from "chart.js";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import _, { filter } from "lodash";
import SearchIcon from "@mui/icons-material/Search";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { useDebounce } from "@/src/hooks/use-debounce";

const stats = [
  { label: "Tổng lượt truy cập", value: "0" },
  { label: "Khách hàng qua liên kết", value: "0" },
  { label: "Tổng số đối tác", value: "0" },
  { label: "Tổng đơn hàng đối tác", value: "0" },
  { label: "Đơn hàng thành công", value: "0" },
  { label: "Doanh thu", value: "0 đ" },
  { label: "Hoa hồng đã duyệt", value: "0 đ" },
  { label: "Hoa hồng chờ duyệt", value: "0 đ" },
];

const initReportOverview: ReportOverviewInterface = {
  totalAccess: 0,
  accessByLink: 0,
  totalPartner: 30,
  totalOrderByPartners: 0,
  successOrders: 0,
  revenue: 0,
  approvedCommission: 0,
  pendingCommission: 0,
};

const toggleOptions = [
  { label: "Hôm nay", value: "day" },
  { label: "7 ngày", value: "7days" },
  { label: "30 ngày", value: "30days" },
];

const commonButtonStyle = (isSelected: boolean) => ({
  // height: "30px",
  padding: "2px 16px",
  fontSize: "14px",
  boxShadow: "none",
  border: "none",
  fontWeight: "400",
  color: isSelected ? "#fff" : "#000",
  backgroundColor: isSelected ? "#2654FE" : "#fff",
  borderRadius: "0",
  textTransform: "none",
  "&.Mui-selected": {
    backgroundColor: "#2654FE",
    color: "#fff",
    fontWeight: "400",
    "&:hover": {
      backgroundColor: "#1E45D9",
    },
  },
});

const generateChartDataV2 = (range, dateRange, numbersAccessViaLink, numbersOrder) => {
  let data = [];

  if (
    dateRange &&
    dateRange[0] &&
    dateRange[1] &&
    isSameDay(dateRange[0].toDate(), dateRange[1].toDate())
  ) {
    // Nếu chọn cùng một ngày => xử lý giống range === "day"
    for (let i = 0; i < 24; i++) {
      data.push(`${i}:00`);
    }
  } else if (dateRange && dateRange[0] && dateRange[1]) {
    const startDate = dateRange[0].toDate();
    const endDate = dateRange[1].toDate();
    const daysDiff = differenceInDays(endDate, startDate) + 1;

    for (let i = 0; i < daysDiff; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);
      data.push(format(currentDate, "dd/MM"));
    }
  } else if (range === "day") {
    for (let i = 0; i < 24; i++) {
      data.push(`${i}:00`);
    }
  } else if (range === "7days" || range === "30days") {
    let days = range === "7days" ? 7 : 30;
    for (let i = days - 1; i >= 0; i--) {
      let date = format(subDays(new Date(), i), "dd/MM");
      data.push(date);
    }
  }

  const finalData = data.map((d, index) => ({
    day: d,
    numbersAccessViaLink: numbersAccessViaLink[index],
    numbersOrder: numbersOrder[index],
  }));
  return finalData;
};

const generateChartData = (range, dateRange, numbersAccessViaLink, numbersOrder) => {
  let labels = [];

  if (
    dateRange &&
    dateRange[0] &&
    dateRange[1] &&
    isSameDay(dateRange[0].toDate(), dateRange[1].toDate())
  ) {
    // Nếu chọn cùng một ngày => xử lý giống range === "day"
    for (let i = 0; i < 24; i++) {
      labels.push(`${i}:00`);
    }
  } else if (dateRange && dateRange[0] && dateRange[1]) {
    const startDate = dateRange[0].toDate();
    const endDate = dateRange[1].toDate();
    const daysDiff = differenceInDays(endDate, startDate) + 1;

    for (let i = 0; i < daysDiff; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);
      labels.push(format(currentDate, "dd/MM"));
    }
  } else if (range === "day") {
    for (let i = 0; i < 24; i++) {
      labels.push(`${i}:00`);
    }
  } else if (range === "7days" || range === "30days") {
    let days = range === "7days" ? 7 : 30;
    for (let i = days - 1; i >= 0; i--) {
      let date = format(subDays(new Date(), i), "dd/MM");
      labels.push(date);
    }
  }

  return {
    labels,
    datasets: [
      {
        label: "Truy cập qua liên kết",
        data: numbersAccessViaLink,
        borderColor: "#FF6B6B",
        backgroundColor: "#FF8F6DCD",
        fill: true,
      },
      {
        label: "Mua hàng",
        data: numbersOrder,
        borderColor: "#6A5ACD",
        backgroundColor: "#8280FFCA",
        fill: true,
      },
    ],
  };
};

const chartOptions: ChartOptions<"line"> = {
  plugins: {
    legend: {
      display: true,
      position: "bottom",
      labels: {
        usePointStyle: true,
        pointStyle: "circle",
        font: { size: 10, weight: "bold" },
        padding: 20,
      },
    },
  },
  maintainAspectRatio: false,
  responsive: true,
};

const Overview = () => {
  const [chartTimeRange, setChartTimeRange] = useState("30days");
  const [partnerFilterTimeRange, setPartnerFilterTimeRange] = useState("30days");
  const [reportOverviewTimeRange, setReportOverviewTimeRange] = useState("30days");

  const [chartDateRange, setChartDateRange] = useState([null, null]);
  const [partnerOverviewDateRange, setPartnerOverviewDateRange] = useState([null, null]);
  const [reportOverviewDateRange, setReportOverviewDateRange] = useState([null, null]);
  const [partnerDate, setPartnerDate] = useState(null);

  const [searchName, setSearchName] = useState("");
  const [searchId, setSearchId] = useState("");
  const [page, setPage] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [filterPartnerOverviewData, setFilterPartnerOverviewData] =
    useState<GetOverviewPartnerRequest>();

  const [statistic, setStatistic] = useState<AffiliationConversionRateType>({
    rateAddToCart: 0.0,
    rateConversion: 0.0,
    newCusViaLink: 0,
    numbersAccessViaLink: Array(30).fill(0),
    numbersOrder: Array(30).fill(0),
  });
  const debouncedSearchValue = useDebounce(searchName, 600);
  const storeId = useStoreId();
  const {
    getReportOverview,
    getDashboardStatistic,
    getPartnerOverview,
    exportExcelPartnerOverview,
  } = useAffiliation();
  const [partners, setPartners] = useState([]);

  const [reportOverviews, setReportOverviews] = useState(stats);
  const conversionInfo = [
    {
      label: "Tỉ lệ thêm vào giỏ hàng",
      value: `${statistic.rateAddToCart}%`,
    },
    {
      label: "Tỉ lệ chuyển đổi",
      value: `${statistic.rateConversion}%`,
    },
    {
      label: "Khách hàng mới qua liên kết",
      value: formatCurrency(statistic.newCusViaLink),
    },
  ];
  useEffect(() => {
    if (storeId) {
      const fromDate = dayjs().subtract(29, "day").startOf("day").format("YYYY-MM-DD HH:mm:ss");
      const toDate = dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss");
      fetchOverview(storeId, fromDate, toDate);
      fetchDashboardStatistic(storeId, fromDate, toDate);
    }
  }, [storeId]);

  const debouncedFetchPartnerOverview = useCallback(
    _.debounce((shopId, skip, limit, fromDate, toDate, search) => {
      fetchPartnerOverview(shopId, skip, limit, fromDate, toDate, search);
    }, 500), // Thời gian debounce là 500ms
    [] // Thực thi lại debounce khi page, rowsPerPage, searchText thay đổi
  );

  useEffect(() => {
    if (!storeId) return;

    const toDate = (partnerOverviewDateRange[1] ?? dayjs())
      .endOf("day")
      .format("YYYY-MM-DD HH:mm:ss");
    let fromDate;

    if (partnerFilterTimeRange) {
      const subtractDays =
        partnerFilterTimeRange === "day"
          ? 0
          : partnerFilterTimeRange === "7days"
          ? 6
          : partnerFilterTimeRange === "30days"
          ? 29
          : 30;
      fromDate = dayjs().subtract(subtractDays, "day").startOf("day").format("YYYY-MM-DD HH:mm:ss");
    } else if (partnerOverviewDateRange[0] && partnerOverviewDateRange[1]) {
      fromDate = partnerOverviewDateRange[0].startOf("day").format("YYYY-MM-DD HH:mm:ss");
    } else {
      fromDate = dayjs().subtract(30, "day").startOf("day").format("YYYY-MM-DD HH:mm:ss");
    }
    setFilterPartnerOverviewData({
      ...filterPartnerOverviewData,
      shopId: storeId,
      searchTerm: debouncedSearchValue,
      fromDate,
      toDate,
    });

    debouncedFetchPartnerOverview(
      storeId,
      page,
      rowsPerPage,
      fromDate,
      toDate,
      debouncedSearchValue
    );
  }, [storeId, page, rowsPerPage, debouncedSearchValue, debouncedFetchPartnerOverview]);

  const fetchOverview = async (shopId, fromDate, toDate) => {
    const response = await getReportOverview({ shopId, fromDate, toDate });
    if (response?.data) {
      const { data }: { data: ReportOverviewInterface } = response?.data;
      const newStats = [
        { label: "Tổng lượt truy cập", value: `${formatCurrency(data.totalAccess)}` },
        { label: "Khách hàng qua liên kết", value: `${formatCurrency(data.accessByLink)}` },
        { label: "Tổng số đối tác", value: `${formatCurrency(data.totalPartner)}` },
        { label: "Tổng đơn hàng đối tác", value: `${formatCurrency(data.totalOrderByPartners)}` },
        { label: "Đơn hàng thành công", value: `${formatCurrency(data.successOrders)}` },
        { label: "Doanh thu", value: `${formatCurrency(data.revenue)} đ` },
        { label: "Hoa hồng đã duyệt", value: `${formatCurrency(data.approvedCommission)} đ` },
        { label: "Hoa hồng chờ duyệt", value: `${formatCurrency(data.pendingCommission)} đ` },
      ];
      setReportOverviews(newStats);
    }
  };

  const fetchDashboardStatistic = async (shopId, fromDate, toDate) => {
    const response = await getDashboardStatistic({ shopId, toDate, fromDate });
    if (response?.data) {
      setStatistic(response.data?.data);
    }
  };

  const fetchPartnerOverview = async (shopId, skip, limit, fromDate, toDate, search) => {
    const newSkip = skip * limit;
    const response = await getPartnerOverview({
      shopId,
      skip: newSkip,
      limit,
      toDate,
      fromDate,
      searchTerm: search,
    });
    if (response?.data) {
      setPartners(response.data.data);
      setTotalCount(response.data.total);
    }
  };

  const handleReportOverviewTimeRangeChange = (newRange) => {
    let fromDate;
    const toDate = dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss");

    switch (newRange) {
      case "day":
        fromDate = dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss");
        break;
      case "7days":
        fromDate = dayjs().subtract(6, "day").startOf("day").format("YYYY-MM-DD HH:mm:ss");
        break;
      case "30days":
        fromDate = dayjs().subtract(29, "day").startOf("day").format("YYYY-MM-DD HH:mm:ss");
        break;
      default:
        fromDate = dayjs().subtract(30, "day").startOf("day").format("YYYY-MM-DD HH:mm:ss");
    }

    if (newRange !== null) {
      fetchOverview(storeId, fromDate, toDate);
      setReportOverviewTimeRange(newRange);
      setReportOverviewDateRange([null, null]);
    }
  };

  const handleChartTimeRangeChange = (event, newRange) => {
    let fromDate;
    const toDate = dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss");

    switch (newRange) {
      case "day":
        fromDate = dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss");
        break;
      case "7days":
        fromDate = dayjs().subtract(6, "day").startOf("day").format("YYYY-MM-DD HH:mm:ss");
        break;
      case "30days":
        fromDate = dayjs().subtract(29, "day").startOf("day").format("YYYY-MM-DD HH:mm:ss");
        break;
      default:
        fromDate = dayjs().subtract(30, "day").startOf("day").format("YYYY-MM-DD HH:mm:ss");
    }

    fetchDashboardStatistic(storeId, fromDate, toDate);

    if (newRange !== null) {
      setChartTimeRange(newRange);
      setChartDateRange([null, null]);
    }
  };

  const handleChartDateChange = (newValue) => {
    setChartDateRange(newValue);
    setChartTimeRange("");
  };

  const handlePartnerTimeRangeChange = (event, newRange) => {
    let fromDate;
    const toDate = dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss");

    switch (newRange) {
      case "day":
        fromDate = dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss");
        break;
      case "7days":
        fromDate = dayjs().subtract(6, "day").startOf("day").format("YYYY-MM-DD HH:mm:ss");
        break;
      case "30days":
        fromDate = dayjs().subtract(29, "day").startOf("day").format("YYYY-MM-DD HH:mm:ss");
        break;
      default:
        fromDate = dayjs().subtract(30, "day").startOf("day").format("YYYY-MM-DD HH:mm:ss");
    }

    if (newRange !== null) {
      setFilterPartnerOverviewData({
        ...filterPartnerOverviewData,
        fromDate,
        toDate,
      });
      fetchPartnerOverview(storeId, page, rowsPerPage, fromDate, toDate, debouncedSearchValue);
      setPartnerFilterTimeRange(newRange);
      setPartnerOverviewDateRange([null, null]);
    }
  };

  const handlePartnerDateChange = (newValue) => {
    setPartnerDate(newValue);
    setPartnerFilterTimeRange("");
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleChartStartDateChange = (newValue) => {
    if (newValue.toString() !== "Invalid Date") {
      const endDate = chartDateRange[1];

      let updatedRange;

      if (!endDate || newValue.isAfter(endDate)) {
        updatedRange = [newValue, newValue];
      } else {
        updatedRange = [newValue, endDate];
      }

      setChartDateRange(updatedRange);
      setChartTimeRange(null);
      // Gọi API nếu đủ cả 2 mốc thời gian
      if (updatedRange[0] && updatedRange[1]) {
        fetchDashboardStatistic(
          storeId,
          updatedRange[0].startOf("day").format("YYYY-MM-DD HH:mm:ss"),
          updatedRange[1].endOf("day").format("YYYY-MM-DD HH:mm:ss")
        );
      }
    }
  };

  const handleChartEndDateChange = (newValue) => {
    const validDayjs = dayjs(newValue);
    if (validDayjs.isValid() && validDayjs.year() >= 1000) {
      const startDate = chartDateRange[0];

      let updatedRange;

      if (!startDate || newValue.isBefore(startDate, "day")) {
        updatedRange = [newValue, newValue];
      } else {
        updatedRange = [startDate, newValue];
      }
      setChartDateRange(updatedRange);
      setChartTimeRange(null);
      // Gọi API nếu đủ cả 2 mốc thời gian
      if (updatedRange[0] && updatedRange[1]) {
        fetchDashboardStatistic(
          storeId,
          updatedRange[0].startOf("day").format("YYYY-MM-DD HH:mm:ss"),
          updatedRange[1].endOf("day").format("YYYY-MM-DD HH:mm:ss")
        );
      }
    }
  };

  const handlePartnerOverviewStartDateChange = (newValue) => {
    if (newValue.toString() !== "Invalid Date") {
      const endDate = partnerOverviewDateRange[1];

      let updatedRange;

      if (!endDate || newValue.isAfter(endDate)) {
        updatedRange = [newValue, newValue];
      } else {
        updatedRange = [newValue, endDate];
      }

      setPartnerOverviewDateRange(updatedRange);
      setPartnerFilterTimeRange(null);
      // Gọi API nếu đủ cả 2 mốc thời gian
      if (updatedRange[0] && updatedRange[1]) {
        setFilterPartnerOverviewData({
          ...filterPartnerOverviewData,
          fromDate: updatedRange[0].startOf("day").format("YYYY-MM-DD HH:mm:ss"),
          toDate: updatedRange[1].endOf("day").format("YYYY-MM-DD HH:mm:ss"),
        });
        fetchPartnerOverview(
          storeId,
          page,
          rowsPerPage,
          updatedRange[0].startOf("day").format("YYYY-MM-DD HH:mm:ss"),
          updatedRange[1].endOf("day").format("YYYY-MM-DD HH:mm:ss"),
          debouncedSearchValue
        );
      }
    }
  };

  const handlePartnerOverviewEndDateChange = (newValue) => {
    const validDayjs = dayjs(newValue);

    if (validDayjs.isValid() && validDayjs.year() >= 1000) {
      const startDate = partnerOverviewDateRange[0];

      let updatedRange;

      if (!startDate || newValue.isBefore(startDate, "day")) {
        updatedRange = [newValue, newValue];
      } else {
        updatedRange = [startDate, newValue];
      }
      setPartnerOverviewDateRange(updatedRange);
      setPartnerFilterTimeRange(null);
      // Gọi API nếu đủ cả 2 mốc thời gian
      if (updatedRange[0] && updatedRange[1]) {
        setFilterPartnerOverviewData({
          ...filterPartnerOverviewData,
          fromDate: updatedRange[0].startOf("day").format("YYYY-MM-DD HH:mm:ss"),
          toDate: updatedRange[1].endOf("day").format("YYYY-MM-DD HH:mm:ss"),
        });
        fetchPartnerOverview(
          storeId,
          page,
          rowsPerPage,
          updatedRange[0].startOf("day").format("YYYY-MM-DD HH:mm:ss"),
          updatedRange[1].endOf("day").format("YYYY-MM-DD HH:mm:ss"),
          debouncedSearchValue
        );
      }
    }
  };

  const handleReportOverviewStartDateChange = (newValue) => {
    if (newValue.toString() !== "Invalid Date") {
      const endDate = reportOverviewDateRange[1];

      let updatedRange;

      if (!endDate || newValue.isAfter(endDate)) {
        updatedRange = [newValue, newValue];
      } else {
        updatedRange = [newValue, endDate];
      }

      setReportOverviewDateRange(updatedRange);
      setReportOverviewTimeRange(null);
      // Gọi API nếu đủ cả 2 mốc thời gian
      if (updatedRange[0] && updatedRange[1]) {
        fetchOverview(
          storeId,
          updatedRange[0].startOf("day").format("YYYY-MM-DD HH:mm:ss"),
          updatedRange[1].endOf("day").format("YYYY-MM-DD HH:mm:ss")
        );
      }
    }
  };

  const handleReportOverviewEndDateChange = (newValue) => {
    const validDayjs = dayjs(newValue);

    if (validDayjs.isValid() && validDayjs.year() >= 1000) {
      const startDate = reportOverviewDateRange[0];

      let updatedRange;

      if (!startDate || newValue.isBefore(startDate, "day")) {
        updatedRange = [newValue, newValue];
      } else {
        updatedRange = [startDate, newValue];
      }
      setReportOverviewDateRange(updatedRange);
      setReportOverviewTimeRange(null);
      // Gọi API nếu đủ cả 2 mốc thời gian
      if (updatedRange[0] && updatedRange[1]) {
        fetchOverview(
          storeId,
          updatedRange[0].startOf("day").format("YYYY-MM-DD HH:mm:ss"),
          updatedRange[1].endOf("day").format("YYYY-MM-DD HH:mm:ss")
        );
      }
    }
  };

  const handleChangePage = (event, newPage) => {
    console.log("🚀 ~ handleChangePage ~ newPage:", newPage);
    setPage(newPage);
  };

  const handleExportExcel = async () => {
    try {
      const response = await exportExcelPartnerOverview(filterPartnerOverviewData);
      if (response) {
        const blob = new Blob([response.data], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", "DanhSachDoiTac.xlsx"); // tên file
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error("Export Excel failed:", error);
    }
  };

  return (
    <Box
      sx={{
        padding: { xs: 1, md: "20px" },
        paddingRight: 0,
        marginBottom: "100px",
        marginLeft: "0px",
      }}
    >
      <Box
        sx={{
          background: "#FFFFFF",
          padding: { xs: 1, md: "20px" },
          borderRadius: "15px",
        }}
      >
        <Stack
          justifyContent="space-between"
          flexDirection={{ xs: "column", md: "row" }}
          alignItems={{ xs: "flex-start", md: "center" }}
          gap={1}
          mb={2}
        >
          <Typography variant="h5" fontWeight="bold" sx={{ fontSize: { xs: 16, md: 20 } }}>
            Báo cáo tổng quan
          </Typography>
          <Stack
            gap={1}
            direction={{ xs: "column", sm: "row" }}
            alignItems={{ xs: "flex-start", sm: "center" }}
            sx={{ width: { xs: "100%", sm: "auto" } }}
          >
            <ToggleButtonGroup
              value={reportOverviewTimeRange}
              exclusive
              onChange={(event: any, newValue) => {
                if (newValue !== null) {
                  handleReportOverviewTimeRangeChange(newValue);
                }
              }}
              sx={{
                margin: "0",
                padding: "0",
                border: "1px solid #ccc",
                borderRadius: "10px",
                overflow: "hidden",
                width: { xs: "100%", sm: "auto" },
              }}
            >
              {toggleOptions.map((option) => (
                <ToggleButton
                  key={option.value}
                  value={option.value}
                  sx={{
                    height: { xs: 40, sm: 30 },
                    width: { xs: "33.33%", sm: "120px" },
                    padding: { xs: "8px 4px", sm: "15px 2px" },
                    fontSize: { xs: 13, sm: 14 },
                    boxShadow: "none",
                    border: "none",
                    fontWeight: "400",
                    color: reportOverviewTimeRange === option.value ? "#fff" : "#000",
                    backgroundColor: reportOverviewTimeRange === option.value ? "#2654FE" : "#fff",
                    borderRadius: "0",
                    textTransform: "none",
                    "&.Mui-selected": {
                      backgroundColor: "#2654FE",
                      color: "#fff",
                      fontWeight: "400",
                      "&:hover": {
                        backgroundColor: "#1E45D9",
                      },
                    },
                  }}
                >
                  {option.label}
                </ToggleButton>
              ))}
            </ToggleButtonGroup>
            <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="vi">
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "row",
                  gap: 1,
                  width: { xs: "100%", sm: 300, md: 300 },
                  mb: { xs: 1, md: 0 },
                }}
              >
                <DatePicker
                  value={reportOverviewDateRange[0]}
                  format="DD/MM/YYYY"
                  onChange={handleReportOverviewStartDateChange}
                  slotProps={{
                    textField: {
                      sx: {
                        width: { xs: "100%", sm: 200 },
                        "& .MuiOutlinedInput-root": {
                          height: { xs: 36, sm: 30 },
                          borderRadius: "8px",
                        },
                      },
                    },
                  }}
                />
                <DatePicker
                  value={reportOverviewDateRange[1]}
                  format="DD/MM/YYYY"
                  onChange={handleReportOverviewEndDateChange}
                  shouldDisableDate={(date) => date.isBefore(reportOverviewDateRange[0], "day")}
                  slotProps={{
                    textField: {
                      sx: {
                        width: { xs: "100%", sm: 200 },
                        "& .MuiOutlinedInput-root": {
                          height: { xs: 36, sm: 30 },
                          borderRadius: "8px",
                        },
                      },
                    },
                  }}
                />
              </Box>
            </LocalizationProvider>
          </Stack>
        </Stack>
        <Grid container spacing={1}>
          {reportOverviews.map((stat, index) => (
            <Grid
              item
              xs={6}
              sm={6}
              md={3}
              key={index}
              sx={{ display: "flex", alignItems: "stretch" }}
            >
              <Card sx={{ flex: 1, boxShadow: "6px 6px 54px 0 #0000000D" }}>
                <CardContent>
                  <Typography
                    sx={{
                      fontSize: { xs: 12, md: 14 },
                      fontWeight: "600",
                      color: "#202224",
                      marginBottom: "10px",
                    }}
                  >
                    {stat.label}
                  </Typography>
                  <Typography
                    sx={{ fontSize: { xs: 16, md: 20 }, fontWeight: "700", color: "#202224" }}
                  >
                    {stat.value}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      <Box
        sx={{
          padding: { xs: 1, md: "20px" },
          background: "#fff",
          borderRadius: "14px",
          marginTop: { xs: 2, md: "25px" },
        }}
      >
        <Stack
          direction={{ xs: "column", md: "row" }}
          justifyContent="space-between"
          alignItems={{ xs: "flex-start", md: "center" }}
          gap={2}
          mb={2}
        >
          <Typography variant="h5" fontWeight="bold" sx={{ fontSize: { xs: 16, md: 20 } }}>
            Tỉ lệ chuyển đổi
          </Typography>
          <Stack
            direction={{ xs: "column", sm: "row" }}
            alignItems={{ xs: "flex-start", sm: "center" }}
            gap={1}
            sx={{ width: { xs: "100%", sm: "auto" } }}
          >
            {/* Tỉ lệ chuyển đổi - chỉ sửa phần ToggleButtonGroup */}
            <ToggleButtonGroup
              value={chartTimeRange}
              exclusive
              onChange={handleChartTimeRangeChange}
              sx={{
                margin: "0",
                padding: "0",
                border: "1px solid #ccc",
                borderRadius: "10px",
                overflow: "hidden",
                width: { xs: "100%", sm: "auto" },
              }}
            >
              {toggleOptions.map((option) => (
                <ToggleButton
                  key={option.value}
                  value={option.value}
                  sx={{
                    height: { xs: 40, sm: 30 },
                    width: { xs: "33.33%", sm: "120px" },
                    padding: { xs: "8px 4px", sm: "15px 2px" },
                    fontSize: { xs: 13, sm: 14 },
                    boxShadow: "none",
                    border: "none",
                    fontWeight: "400",
                    color: chartTimeRange === option.value ? "#fff" : "#000",
                    backgroundColor: chartTimeRange === option.value ? "#2654FE" : "#fff",
                    borderRadius: "0",
                    textTransform: "none",
                    "&.Mui-selected": {
                      backgroundColor: "#2654FE",
                      color: "#fff",
                      fontWeight: "400",
                      "&:hover": {
                        backgroundColor: "#1E45D9",
                      },
                    },
                  }}
                >
                  {option.label}
                </ToggleButton>
              ))}
            </ToggleButtonGroup>
            <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="vi">
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "row",
                  gap: 1,
                  width: { xs: "100%", sm: 300, md: 300 },
                  mb: { xs: 1, md: 0 },
                }}
              >
                <DatePicker
                  value={chartDateRange[0]}
                  format="DD/MM/YYYY"
                  onChange={handleChartStartDateChange}
                  slotProps={{
                    textField: {
                      sx: {
                        width: { xs: "100%", sm: 200 },
                        "& .MuiOutlinedInput-root": {
                          height: { xs: 36, sm: 30 },
                          borderRadius: "8px",
                        },
                      },
                    },
                  }}
                />
                <DatePicker
                  value={chartDateRange[1]}
                  format="DD/MM/YYYY"
                  onChange={handleChartEndDateChange}
                  shouldDisableDate={(date) => date.isBefore(chartDateRange[0], "day")}
                  slotProps={{
                    textField: {
                      sx: {
                        width: { xs: "100%", sm: 200 },
                        "& .MuiOutlinedInput-root": {
                          height: { xs: 36, sm: 30 },
                          borderRadius: "8px",
                        },
                      },
                    },
                  }}
                />
              </Box>
            </LocalizationProvider>
          </Stack>
        </Stack>
        <Box height={{ xs: 220, md: 350 }}>
          <Line
            data={generateChartData(
              chartTimeRange,
              chartDateRange,
              statistic.numbersAccessViaLink,
              statistic.numbersOrder
            )}
            options={chartOptions}
          />
        </Box>
        <Box sx={{ mt: 2 }}>
          <Grid container spacing={1}>
            {conversionInfo.map((stat, index) => (
              <Grid item xs={12} md={4} key={index} sx={{ display: "flex", alignItems: "stretch" }}>
                <Card sx={{ flex: 1, boxShadow: "6px 6px 54px 0 #0000000D" }}>
                  <CardContent>
                    <Typography
                      sx={{
                        fontSize: { xs: 12, md: 14 },
                        fontWeight: "600",
                        color: "#202224",
                        marginBottom: "10px",
                      }}
                    >
                      {stat.label}
                    </Typography>
                    <Typography
                      sx={{ fontSize: { xs: 16, md: 20 }, fontWeight: "700", color: "#202224" }}
                    >
                      {stat.value}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Box>

      <Box
        mt={4}
        sx={{
          background: "#fff",
          padding: { xs: 1, md: "15px" },
          borderRadius: "14px",
          marginTop: { xs: 2, md: "25px" },
        }}
      >
        <Stack direction="column" gap={1} mb={2}>
          <Typography fontWeight="bold" mb={0} sx={{ fontSize: { xs: 16, md: 18 } }}>
            Danh sách đối tác
          </Typography>
          <Stack
            direction={{ xs: "column", xl: "row" }}
            alignItems={{ xs: "flex-start", xl: "center" }}
            gap={1}
            sx={{ width: "100%" }}
          >
            <TextField
              variant="outlined"
              value={searchName}
              onChange={(e) => setSearchName(e.target.value)}
              placeholder="Tìm tên, mã đối tác"
              size="small"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{
                width: { xs: "100%", md: 220 },
                "& .MuiOutlinedInput-root": {
                  borderRadius: "8px",
                  height: 36,
                  fontSize: 14,
                },
              }}
            />

            <Box
              sx={{
                display: "flex",
                flexDirection: "row",
                gap: 1,
                mb: { xs: 1, md: 0 },
              }}
            >
              <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="vi">
                <DatePicker
                  value={partnerOverviewDateRange[0]}
                  format="DD/MM/YYYY"
                  onChange={handlePartnerOverviewStartDateChange}
                  slotProps={{
                    textField: {
                      size: "small",
                      sx: {
                        width: { xs: "50%", sm: "200px" },
                        flex: 1,
                        "& .MuiOutlinedInput-root": {
                          height: 36,
                          borderRadius: "8px",
                          fontSize: 14,
                        },
                      },
                    },
                  }}
                />
                <DatePicker
                  value={partnerOverviewDateRange[1]}
                  format="DD/MM/YYYY"
                  onChange={handlePartnerOverviewEndDateChange}
                  shouldDisableDate={(date) => date.isBefore(partnerOverviewDateRange[0], "day")}
                  slotProps={{
                    textField: {
                      size: "small",
                      sx: {
                        width: { xs: "50%", sm: "200px" },
                        flex: 1,
                        "& .MuiOutlinedInput-root": {
                          height: 36,
                          borderRadius: "8px",
                          fontSize: 14,
                        },
                      },
                    },
                  }}
                />
              </LocalizationProvider>
            </Box>
          </Stack>
          <Box
            sx={{
              width: { xs: "100%", md: "auto" },

              display: "flex",
              justifyContent: { xs: "flex-start", md: "flex-end" },
            }}
          >
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={handleExportExcel}
              size="small"
              sx={{
                textTransform: "none",
                borderRadius: "8px",
                backgroundColor: "#fff",
                color: "#2654FE",
                border: "1.5px solid #2654FE",
                fontSize: "14px",
                fontWeight: 600,
                minWidth: 90,
                height: 36,
                display: "flex",
                width: { xs: "50%", md: 150 },
                boxShadow: "none",
                transition: "all 0.2s",
                "&:hover": {
                  backgroundColor: "#F0F6FF",
                  borderColor: "#2654FE",
                  color: "#2654FE",
                },
                "& .MuiButton-startIcon": {
                  marginRight: 1,
                },
              }}
            >
              Export
            </Button>
          </Box>
        </Stack>
        <Box sx={{ width: "100%", overflowX: "auto" }}>
          <Table sx={{ width: "max-content", minWidth: "100%" }}>
            <TableHead>
              <TableRow>
                <TableCell>ID đối tác</TableCell>
                <TableCell>Tên đối tác</TableCell>
                <TableCell>Tổng khách hàng</TableCell>
                <TableCell>Khách đã mua</TableCell>
                <TableCell>Đơn hàng</TableCell>
                <TableCell>Doanh thu</TableCell>
                <TableCell>Hoa hồng</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {partners.map((partner, index) => (
                <TableRow key={index}>
                  <TableCell>{partner?.referalCode}</TableCell>
                  <TableCell>{partner.fullname}</TableCell>
                  <TableCell>{partner.totalCustomer}</TableCell>
                  <TableCell>{partner.totalPurchasedCus}</TableCell>
                  <TableCell>{partner.totalOrder}</TableCell>
                  <TableCell>{formatCurrency(partner?.revenue || 0)} đ</TableCell>
                  <TableCell>{formatCurrency(partner?.totalCommission || 0)} đ</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Box>
        <Stack
          direction={{ xs: "column", sm: "row" }}
          justifyContent="space-between"
          alignItems={{ xs: "flex-start", sm: "center" }}
          mt={2}
          gap={2}
        >
          <Typography variant="body2" sx={{ fontSize: { xs: 12, md: 14 } }}>
            <span style={{ color: "red" }}>*</span>{" "}
            <span style={{ color: "#A6A6A6" }}>
              Các đối tác không có dữ liệu doanh thu sẽ không được hiển thị trong danh sách
            </span>
          </Typography>
          <TablePagination
            component="div"
            count={totalCount}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={[10, 25, 50]}
            labelRowsPerPage="Số dòng mỗi trang"
            sx={{
              "& .MuiTablePagination-toolbar": {
                flexDirection: "row !important", // Luôn là 1 dòng
                alignItems: "center",
                flexWrap: "nowrap",
                gap: 1,
              },
              "& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows": {
                fontSize: { xs: 12, md: 14 },
              },
            }}
          />
        </Stack>
      </Box>
    </Box>
  );
};

export default Overview;
