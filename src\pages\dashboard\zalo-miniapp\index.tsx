import React, { useState } from "react";
import { <PERSON>, Typo<PERSON>, Box, Grid, Button, Stack, IconButton } from "@mui/material";
import DashboardLayout from "../../../layouts/dashboard";
import ZMASetUp from "./component/ZMASetUp";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useRouter } from "next/router";
import TitleTypography from "../../../components/title-typography/title-typography";
import { Padding } from "@/src/styles/CommonStyle";

const ZaloMiniApp = () => {
  const [isSetup, setIsSetup] = useState(false);
  const handleCancel = () => {
    // router.back();
    window.history.back();
  };
  const router = useRouter();

  return (
    <DashboardLayout>
      <Box marginBottom={"100px"} padding={Padding}>
        {isSetup ? (
          <Box>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                mb: 3,
                paddingBottom: "20px",
                marginBottom: "16px",
                borderBottom: "1px solid #bdbdbd",
              }}
            >
              <IconButton
                onClick={() => setIsSetup(false)}
                sx={{ mr: 2, paddingBottom: 0, mb: "-8px" }}
              >
                <ArrowBackIcon />
              </IconButton>

              <TitleTypography
                sx={{
                  fontSize: "20px !important",
                  fontWeight: "700",
                  lineHeight: "1",
                  marginTop: "20px",
                }}
              >
                Zalo Mini App
              </TitleTypography>
            </Box>

            <ZMASetUp
            // onBack={() => setIsSetup(false)}
            />
          </Box>
        ) : (
          <Box
            sx={{
              mx: "auto",
              mt: 4,
              p: 3,
              borderRadius: 3,
              // boxShadow: '0 0px 20px 0 #00000026!important',
              background: "#fff",
            }}
          >
            <Card
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                mb: 2,
                boxShadow: "none !important",
                "@media(max-width: 480px)": {
                  flexDirection: "column",
                  gap: "25px",
                  alignItems: "start",
                },
              }}
            >
              <Stack flexDirection={"row"} boxShadow={"none"}>
                <img
                  style={{ width: "79px", height: "79px" }}
                  src="/logo/logo-miniapp/icon-zalo.svg"
                  alt="Logo"
                />
                <Stack marginLeft={"15px"}>
                  <Typography
                    sx={{
                      fontSize: "32px",
                      fontWeight: "700",
                      "@media(max-width: 480px)": {
                        fontSize: "25px",
                      },
                    }}
                  >
                    Zalo MiniApp
                  </Typography>
                  <Box
                    sx={{
                      width: "136px",
                      height: "13px",
                      borderRadius: "100px",
                      background: "#F5F6FA",
                      marginBottom: "12px",
                    }}
                  ></Box>
                  <Box
                    sx={{
                      width: "103px",
                      height: "13px",
                      borderRadius: "100px",
                      background: "#F5F6FA",
                    }}
                  ></Box>
                </Stack>
              </Stack>
              <Button
                variant="contained"
                sx={{
                  color: "#FFFFFF",
                  fontSize: "16px",
                  fontWeight: "700",
                  textTransform: "none",
                  background: "#2654FE",
                  borderRadius: "5px",
                }}
                onClick={() => setIsSetup(true)}
              >
                Thiết lập
              </Button>
            </Card>

            <Stack
              flexDirection={"row"}
              gap={"30px"}
              sx={{
                "@media(max-width: 767px)": {
                  display: "block",
                },
              }}
            >
              <Stack
                width={"75%"}
                sx={{
                  "@media(max-width: 767px)": {
                    width: "100%",
                  },
                }}
              >
                <Typography
                  sx={{
                    fontSize: "35px",
                    fontWeight: "700",
                    gap: "8px",
                    "@media(max-width: 480px)": {
                      fontSize: "25px",
                    },
                  }}
                >
                  Zalo Mini App - Nền tảng dịch vụ{" "}
                  <Typography
                    component="span"
                    sx={{
                      fontSize: "35px",
                      fontWeight: "700",
                      color: "#2654FE",
                      "@media(max-width: 480px)": {
                        fontSize: "25px",
                      },
                    }}
                  >
                    Đa năng
                  </Typography>
                </Typography>
                <Grid container spacing={2} marginTop={"25px"}>
                  <Grid item xs={12} display={"flex"} flexWrap={"wrap"} gap={"3%"}>
                    <Card
                      sx={{
                        p: 2,
                        border: "1px solid rgba(185, 185, 185, 0.61)",
                        alignItems: "center",
                        borderRadius: "20px",
                        width: "48%",
                        marginTop: "20px",
                        "&:hover": {
                          background: "#e8f7ff",
                        },
                        "@media(max-width: 480px)": {
                          width: "100%",
                        },
                      }}
                    >
                      <Stack sx={{ display: "flex", alignItems: "start", flexDirection: "row" }}>
                        <Box sx={{ mr: 2 }}>
                          <img
                            style={{ width: "55px", height: "auto" }}
                            src="/logo/logo-miniapp/holding.png"
                            alt="Logo"
                          />
                        </Box>
                        <Typography fontSize={"20px"} fontWeight={"700"}>
                          Dịch vụ hành chính công
                        </Typography>
                      </Stack>
                      <Box>
                        <Typography fontSize={"16px"} fontWeight={"500"} marginTop={"15px"}>
                          Cơ quan hành chính, trường học, bệnh viện và các dịch vụ công
                        </Typography>
                      </Box>
                    </Card>
                    <Card
                      sx={{
                        p: 2,
                        border: "1px solid rgba(185, 185, 185, 0.61)",
                        alignItems: "center",
                        borderRadius: "20px",
                        width: "48%",
                        marginTop: "20px",
                        "&:hover": {
                          background: "#e8f7ff",
                        },
                        "@media(max-width: 480px)": {
                          width: "100%",
                        },
                      }}
                    >
                      <Stack sx={{ display: "flex", alignItems: "start", flexDirection: "row" }}>
                        <Box sx={{ mr: 2 }}>
                          <img
                            style={{ width: "55px", height: "auto" }}
                            src="/logo/logo-miniapp/bus.png"
                            alt="Logo"
                          />
                        </Box>
                        <Typography fontSize={"20px"} fontWeight={"700"}>
                          Dịch vụ hành chính công
                        </Typography>
                      </Stack>
                      <Box>
                        <Typography fontSize={"16px"} fontWeight={"500"} marginTop={"15px"}>
                          Cơ quan hành chính, trường học, bệnh viện và các dịch vụ công
                        </Typography>
                      </Box>
                    </Card>
                    <Card
                      sx={{
                        p: 2,
                        border: "1px solid rgba(185, 185, 185, 0.61)",
                        alignItems: "center",
                        borderRadius: "20px",
                        width: "48%",
                        marginTop: "20px",
                        "&:hover": {
                          background: "#e8f7ff",
                        },
                        "@media(max-width: 480px)": {
                          width: "100%",
                        },
                      }}
                    >
                      <Stack sx={{ display: "flex", alignItems: "start", flexDirection: "row" }}>
                        <Box sx={{ mr: 2 }}>
                          <img
                            style={{ width: "55px", height: "auto" }}
                            src="/logo/logo-miniapp/cart.png"
                            alt="Logo"
                          />
                        </Box>
                        <Typography fontSize={"20px"} fontWeight={"700"}>
                          Dịch vụ hành chính công
                        </Typography>
                      </Stack>
                      <Box>
                        <Typography fontSize={"16px"} fontWeight={"500"} marginTop={"15px"}>
                          Cơ quan hành chính, trường học, bệnh viện và các dịch vụ công
                        </Typography>
                      </Box>
                    </Card>
                    <Card
                      sx={{
                        p: 2,
                        border: "1px solid rgba(185, 185, 185, 0.61)",
                        alignItems: "center",
                        borderRadius: "20px",
                        width: "48%",
                        marginTop: "20px",
                        "&:hover": {
                          background: "#e8f7ff",
                        },
                        "@media(max-width: 480px)": {
                          width: "100%",
                        },
                      }}
                    >
                      <Stack sx={{ display: "flex", alignItems: "start", flexDirection: "row" }}>
                        <Box sx={{ mr: 2 }}>
                          <img
                            style={{ width: "55px", height: "auto" }}
                            src="/logo/logo-miniapp/people.png"
                            alt="Logo"
                          />
                        </Box>
                        <Typography fontSize={"20px"} fontWeight={"700"}>
                          Dịch vụ hành chính công
                        </Typography>
                      </Stack>
                      <Box>
                        <Typography fontSize={"16px"} fontWeight={"500"} marginTop={"15px"}>
                          Cơ quan hành chính, trường học, bệnh viện và các dịch vụ công
                        </Typography>
                      </Box>
                    </Card>
                  </Grid>
                </Grid>
              </Stack>
              <Stack
                width={"25%"}
                justifyContent={"end"}
                sx={{
                  "@media(max-width: 767px)": {
                    width: "50%",
                    margin: " 0 auto",
                    marginTop: "35px",
                  },
                  "@media(max-width: 480px)": {
                    width: "100%",
                  },
                }}
              >
                <img
                  style={{ width: "100%", height: "auto", borderRadius: "50px" }}
                  src="/logo/logo-miniapp/screen-mb.jpg"
                  alt="Logo"
                />
              </Stack>
            </Stack>
          </Box>
        )}
      </Box>
    </DashboardLayout>
  );
};
export default ZaloMiniApp;
