import React, { useState } from 'react';
import { Box, Select, MenuItem, Button, Stack, Typography } from '@mui/material';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider, DateRangePicker } from '@mui/x-date-pickers-pro';
import { ExpandMore } from '@mui/icons-material';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';

const StoreOnline = () => {
  const [filter, setFilter] = useState('Hôm nay');
  const [open, setOpen] = useState(false);

  return (
    <Box sx={{ p: 3, borderRadius: '20px', background: '#f5f5f5', marginTop: '-15px' }}>
      <Box
        display="flex"
        justifyContent="flex-end"
        gap={2}
        mb={3}
        sx={{
          '@media(max-width: 600px)': {
            flexDirection: 'column',
          },
        }}
      >
        <Select
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          displayEmpty
          size="small"
          sx={{ minWidth: 120, height: 50, backgroundColor: '#fff' }}
          IconComponent={(props) => <ExpandMore {...props} sx={{ color: '#000' }} />}
        >
          <MenuItem value="Hôm nay">Hôm nay</MenuItem>
          <MenuItem value="Hôm qua">Hôm qua</MenuItem>
          <MenuItem value="7 ngày qua">7 ngày qua</MenuItem>
          <MenuItem value="1 tháng qua">1 tháng qua</MenuItem>
        </Select>

        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <DateRangePicker
            open={open}
            onOpen={() => setOpen(true)}
            onClose={() => setOpen(false)}
            format="DD/MM/YYYY"
            localeText={{ start: 'Từ ngày', end: 'Đến ngày' }}
            slotProps={{
              textField: { sx: { display: 'none' } },
              popper: { placement: 'bottom-start' },
            }}
            slots={{
              field: () => (
                <Button
                  onClick={() => setOpen(true)}
                  sx={{
                    border: '1px solid #C4C4C4',
                    borderRadius: '12px',
                    padding: '10px 16px',
                    backgroundColor: '#F8F9FA',
                    fontSize: '16px',
                    fontWeight: 400,
                    color: '#000',
                    textTransform: 'none',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    minWidth: '260px',
                    justifyContent: 'flex-start',
                    '&:hover': { backgroundColor: '#ECECEC' },
                  }}
                >
                  <Stack
                    direction="row"
                    alignItems="center"
                    gap="4px"
                    justifyContent="space-between"
                    width="100%"
                  >
                    <Stack direction="row" alignItems="center" gap="4px">
                      {'Bắt đầu'} - {'Kết thúc'}
                    </Stack>
                    <CalendarMonthIcon sx={{ fontSize: 22 }} />
                  </Stack>
                </Button>
              ),
            }}
          />
        </LocalizationProvider>
      </Box>
      <Stack
        flexDirection={'row'}
        gap={'50px'}
        sx={{
          '@media(max-width: 980px)': {
            flexWrap: 'wrap',
            // gap: '2%',
          },
        }}
      >
        <Stack
          flexDirection={'row'}
          alignItems={'start'}
          justifyContent={'space-between'}
          padding={'20px'}
          borderRadius={'20px'}
          bgcolor={'#F4F5F9'}
          minHeight={'150px'}
          width={'25%'}
          boxShadow={'0 0px 20px 0 #00000026 !important;'}
          sx={{
            '@media(max-width: 980px)': {
              width: '45%',
            },
            '@media(max-width: 600px)': {
              width: '100%',
            },
          }}
        >
          <Box>
            <Typography sx={{ color: '#202224', fontSize: '16px', fontWeight: '400' }}>
              Hoàn thành
            </Typography>
            <Typography sx={{ color: '#202224', fontSize: '28px', fontWeight: '700' }}>
              50
            </Typography>
          </Box>
          <img
            src="/logo/logo-dashboard/Group 48353.svg"
            alt="Logo"
            style={{ width: '60px', height: '60px' }}
          />
        </Stack>
        <Stack
          flexDirection={'row'}
          alignItems={'start'}
          justifyContent={'space-between'}
          padding={'20px'}
          borderRadius={'20px'}
          bgcolor={'#F4F5F9'}
          minHeight={'150px'}
          width={'25%'}
          boxShadow={'0 0px 20px 0 #00000026 !important;'}
          sx={{
            '@media(max-width: 980px)': {
              width: '45%',
            },
            '@media(max-width: 600px)': {
              width: '100%',
            },
          }}
        >
          <Box>
            <Typography sx={{ color: '#202224', fontSize: '16px', fontWeight: '400' }}>
              Đang xử lý
            </Typography>
            <Typography sx={{ color: '#202224', fontSize: '28px', fontWeight: '700' }}>
              50
            </Typography>
          </Box>
          <img
            src="/logo/logo-dashboard/Icon (8).svg"
            alt="Logo"
            style={{ width: '60px', height: '60px' }}
          />
        </Stack>
        <Stack
          flexDirection={'row'}
          alignItems={'start'}
          justifyContent={'space-between'}
          padding={'20px'}
          borderRadius={'20px'}
          bgcolor={'#F4F5F9'}
          minHeight={'150px'}
          width={'25%'}
          boxShadow={'0 0px 20px 0 #00000026 !important;'}
          sx={{
            '@media(max-width: 980px)': {
              width: '45%',
            },
            '@media(max-width: 600px)': {
              width: '100%',
            },
          }}
        >
          <Box>
            <Typography sx={{ color: '#202224', fontSize: '16px', fontWeight: '400' }}>
              Đang giao hàng
            </Typography>
            <Typography sx={{ color: '#202224', fontSize: '28px', fontWeight: '700' }}>
              50
            </Typography>
          </Box>
          <img
            src="/logo/logo-dashboard/Group 48352.svg"
            alt="Logo"
            style={{ width: '60px', height: '60px' }}
          />
        </Stack>
        <Stack
          flexDirection={'row'}
          alignItems={'start'}
          justifyContent={'space-between'}
          padding={'20px'}
          borderRadius={'20px'}
          bgcolor={'#F4F5F9'}
          minHeight={'150px'}
          width={'25%'}
          boxShadow={'0 0px 20px 0 #00000026 !important;'}
          sx={{
            '@media(max-width: 980px)': {
              width: '45%',
            },
            '@media(max-width: 600px)': {
              width: '100%',
            },
          }}
        >
          <Box>
            <Typography sx={{ color: '#202224', fontSize: '16px', fontWeight: '400' }}>
              Hoàn hủy
            </Typography>
            <Typography sx={{ color: '#202224', fontSize: '28px', fontWeight: '700' }}>
              50
            </Typography>
          </Box>
          <img
            src="/logo/logo-dashboard/Group 48351.svg"
            alt="Logo"
            style={{ width: '60px', height: '60px' }}
          />
        </Stack>
      </Stack>
    </Box>
  );
};

export default StoreOnline;
