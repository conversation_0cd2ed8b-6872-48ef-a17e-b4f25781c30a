import React, { useEffect, useState } from "react";
import { Box, Card, Typography, IconButton, Divider, Paper, Button, Tooltip } from "@mui/material";
import ToggleOffIcon from "@mui/icons-material/ToggleOff";
import ToggleOnIcon from "@mui/icons-material/ToggleOn";
import ConfigureInvoiceModal from "./ConfigureInvoiceModal";
import { useStoreId } from "@/src/hooks/use-store-id";
import useSnackbar from "@/src/hooks/use-snackbar";
import { InvoiceConfigData, InvoiceProvider } from "@/src/api/types/invoice.types";
import { useInvoice } from "@/src/api/hooks/invoice/invoice";
import SettingsIcon from "@mui/icons-material/Settings";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { usePathname } from "next/navigation";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";

const initialProviders: Array<{
  name: string;
  description: string;
  provider: InvoiceProvider;
  data?: InvoiceConfigData;
}> = [
  {
    name: "Viettel S-Invoice",
    description: "Cấu hình thông tin kết nối hóa đơn điện tử Viettel S-Invoice",
    provider: InvoiceProvider.Viettel,
  },
  // {
  //   name: "VNPT-Invoice",
  //   description: "Cấu hình thông tin kết nối hóa đơn điện tử VNPT-Invoice",
  //   provider: InvoiceProvider.VNPT,
  // },
  // {
  //   name: "MISA meInvoice",
  //   description: "Cấu hình thông tin kết nối hóa đơn điện tử MISA meInvoice",
  //   provider: InvoiceProvider.Misa,
  // },
];

export default function InvoiceConfig() {
  const pathname = usePathname();
  const [configs, setConfigs] = React.useState(initialProviders);
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<InvoiceProvider | null>(null);
  const storeId = useStoreId();
  const snackbar = useSnackbar();
  const {
    getInvoiceConfig,
    activeInvoiceConfig,
    createInvoiceConfig,
    updateInvoiceConfig,
    loading,
  } = useInvoice();

  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  useEffect(() => {
    if (storeId) {
      fetchInvoiceConfigs();
    }
  }, [storeId]);

  const fetchInvoiceConfigs = async () => {
    try {
      // const providers = [InvoiceProvider.Viettel, InvoiceProvider.VNPT, InvoiceProvider.Misa];
      const providers = [InvoiceProvider.Viettel];
      const configsData = await Promise.all(
        providers.map(async (provider) => {
          const res = await getInvoiceConfig(storeId, provider);
          return {
            provider,
            name: providers.find((p) => p === provider),
            description: initialProviders.find((p) => p.provider === provider).description,
            data: res?.data?.data,
          };
        })
      );
      setConfigs(configsData);
    } catch (error) {
      snackbar.error(error.detail);
    }
  };
  const handleOpenModal = (provider: InvoiceProvider) => {
    setSelectedProvider(provider);
    setModalOpen(true);
  };

  const handleToggleActive = async (
    provider: InvoiceProvider,
    currentState: boolean,
    configId: string
  ) => {
    try {
      const response = await activeInvoiceConfig(configId);

      if (response.data.data != null) snackbar.success(`Kích hoạt cấu hình ${provider} thành công`);
      else snackbar.error(`${response.data.message}`);

      await fetchInvoiceConfigs();
    } catch (error: any) {
      snackbar.error(error.detail || "Không thể cập nhật trạng thái");
    }
  };

  const handleSaveConfig = async (formData: Partial<InvoiceConfigData>) => {
    try {
      if (!formData.username || !formData.password || !formData.sellerTaxCode) {
        snackbar.error("Vui lòng điền đầy đủ các trường bắt buộc");
        return;
      }
      formData.shopId = storeId;
      const existingConfig = configs.find((c) => c.provider === selectedProvider)?.data;

      if (existingConfig) {
        const updateData = {
          ...formData,
          invoiceConfigId: existingConfig.invoiceConfigId,
        };
        await updateInvoiceConfig(updateData);
        snackbar.success(`Cấu hình ${formData.provider} đã được cập nhật`);
      } else {
        await createInvoiceConfig(formData);
        snackbar.success(`Cấu hình ${formData.provider} đã được tạo`);
      }

      await fetchInvoiceConfigs();
      setModalOpen(false);
      setSelectedProvider(null);
    } catch (error: any) {}
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      {configs.map((provider, index) => (
        <React.Fragment key={provider.name}>
          <Card sx={{ width: "40%", px: 2, pt: 2, backgroundColor: "white" }}>
            <Typography variant="h6" gutterBottom>
              {provider.name}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              {provider.description}
            </Typography>
            {provider.data ? (
              <Paper
                variant="outlined"
                sx={{
                  p: 2,
                  bgcolor: "primary.50",
                  borderColor: "primary.100",
                }}
              >
                <Box sx={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    Tên đăng nhập:
                  </Typography>
                  <Typography variant="body2" fontWeight={500}>
                    {provider.data.username}
                  </Typography>

                  <Typography variant="body2" color="text.secondary">
                    Mã số thuế:
                  </Typography>
                  <Typography variant="body2" fontWeight={500}>
                    {provider.data.sellerTaxCode}
                  </Typography>

                  <Typography variant="body2" color="text.secondary">
                    Loại hình:
                  </Typography>
                  <Typography variant="body2" fontWeight={500}>
                    {provider.data.isDraft === true ? "Nháp" : "Phát hành"}
                  </Typography>
                </Box>
              </Paper>
            ) : (
              <Box sx={{ textAlign: "center", py: 3, mb: 2, color: "text.secondary" }}>
                Chưa có cấu hình nào được thiết lập
              </Box>
            )}
            <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
              <Tooltip
                title={
                  !isGranted(pathname, PERMISSION_TYPE_ENUM.Edit) ? "Bạn không có quyền sửa" : ""
                }
              >
                <span>
                  <Button
                    variant="contained"
                    size="small"
                    startIcon={<SettingsIcon fontSize="small" />}
                    onClick={() => handleOpenModal(provider.provider)}
                    sx={{ py: 1 }}
                    disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                  >
                    {provider.data ? "Chỉnh sửa cấu hình" : "Thêm cấu hình mới"}
                  </Button>
                </span>
              </Tooltip>
              {provider.data ? (
                <>
                  <Tooltip
                    title={
                      !isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)
                        ? "Bạn không có quyền sửa"
                        : ""
                    }
                  >
                    <span>
                      <IconButton
                        onClick={() =>
                          handleToggleActive(
                            provider.provider,
                            provider.data?.isActive,
                            provider.data.invoiceConfigId
                          )
                        }
                        size="large"
                        disabled={loading || !isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                        color={provider.data?.isActive ? "primary" : "default"}
                      >
                        {provider.data?.isActive ? (
                          <ToggleOnIcon sx={{ fontSize: 35 }} />
                        ) : (
                          <ToggleOffIcon sx={{ fontSize: 35 }} />
                        )}
                      </IconButton>
                    </span>
                  </Tooltip>
                </>
              ) : (
                <Box height={50}></Box>
              )}
            </Box>
          </Card>
          <Divider sx={{ marginBottom: 2, marginTop: 2 }} />
        </React.Fragment>
      ))}
      <ConfigureInvoiceModal
        open={modalOpen}
        onClose={() => {
          setModalOpen(false);
          setSelectedProvider(null);
        }}
        provider={selectedProvider}
        onSave={handleSaveConfig}
        existingConfig={configs.find((c) => c.provider === selectedProvider)?.data}
      />
    </Box>
  );
}
