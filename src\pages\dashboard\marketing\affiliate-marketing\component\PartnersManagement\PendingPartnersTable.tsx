import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Box,
  Button,
  Typography,
} from "@mui/material";
import { TablePagination } from "@mui/material";
import TitleDialog from "@/src/components/dialog/TitleDialog";
import { useAffiliation } from "@/src/api/hooks/affiliation/use-affiliation";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useStoreId } from "@/src/hooks/use-store-id";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";

const partnersData = [
  {
    id: "**********",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "**********",
    joinDate: "10/10/2025",
    refId: "-",
    refName: "-",
    idCard: "***********",
    taxCode: "***********",
    paymentMethod: "Bank",
    paymentInfo: "MBBANK",
    accountNumber: "*********",
    status: "active",
  },
  {
    id: "**********",
    name: "Nguyễn Văn A",
    email: "<EMAIL>",
    phone: "**********",
    joinDate: "05/09/2025",
    refId: "**********",
    refName: "Ngô Văn B",
    idCard: "***********",
    taxCode: "***********",
    paymentMethod: "Zalo pay",
    paymentInfo: "MBBANK",
    accountNumber: "*********",
    status: "inactive",
  },
];

const ActivePartnersTable = ({
  searchQuery,
  page,
  setPage,
  rowsPerPage,
  setRowsPerPage,
  affiliationPartner,
  totalCount,
  fetchAffiliationPartner,
}) => {
  const { approvalAffiliationPartner } = useAffiliation();
  const snackbar = useSnackbar();
  const storeId = useStoreId();

  const [isOpenModalApproval, setIsOpenModalApproval] = useState(false);
  const [selectedPartner, setSelectedPartner] = useState<any>();
  const filteredData = partnersData.filter(
    (partner) =>
      partner.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      partner.id.includes(searchQuery)
  );

  const paginatedData = filteredData.slice(page * rowsPerPage, (page + 1) * rowsPerPage);

  const handleApproval = async () => {
    try {
      if (selectedPartner && selectedPartner?.userId) {
        const res = await approvalAffiliationPartner(selectedPartner.userId);
        setIsOpenModalApproval(false);
        if (res.status === 400) {
          snackbar.warning(res.detail);
        } else {
          snackbar.success("Phê duyệt thành công");
          fetchAffiliationPartner(page, rowsPerPage, "pending", { ShopId: storeId });
        }
      }
    } catch (error) {
      console.log({ error });
    }
  };

  return (
    <TableContainer sx={{ maxWidth: "100%", mt: 2, overflow: "auto" }}>
      <Table size="small">
        <TableHead>
          <TableRow>
            {[
              { header: "STT", width: "50px" },
              { header: "ID Đối tác", width: "100px" },
              { header: "Tên đối tác", width: "150px" },
              { header: "Email", width: "180px" },
              { header: "Số điện thoại", width: "120px" },
              { header: "Ngày tham gia", width: "120px" },
              { header: "ID Người giới thiệu", width: "150px" },
              { header: "Tên Người giới thiệu", width: "150px" },
              { header: "CMND/CCCD", width: "120px" },
              { header: "Mã số thuế", width: "120px" },
              { header: "Phương thức thanh toán", width: "180px" },
              { header: "Thông tin thanh toán", width: "180px" },
              { header: "Tài khoản thanh toán", width: "180px" },
              { header: "Trạng thái", width: "100px" },
              { header: "Quản lý", width: "150px", isLastColumn: true },
            ].map((item) => (
              <TableCell
                key={item.header}
                sx={{
                  fontWeight: "bold",
                  minWidth: item.width,
                  ...(item.isLastColumn && {
                    position: "sticky",
                    right: 0,
                    bottom: 0,
                    backgroundColor: "#fff",
                    zIndex: 3,
                    boxShadow: "-2px 0 5px rgba(0,0,0,0.1)",
                    padding: "20px 16px",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "start",
                  }),
                }}
              >
                {item.header}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {affiliationPartner.length > 0 &&
            affiliationPartner.map((partner, index) => (
              <React.Fragment key={partner.userId}>
                <TableRow>
                  <TableCell>{page * rowsPerPage + index + 1}</TableCell>
                  <TableCell>{partner.referralCode}</TableCell>
                  <TableCell>{partner.fullname}</TableCell>
                  <TableCell>{partner.email}</TableCell>
                  <TableCell>{partner.phoneNumber}</TableCell>
                  <TableCell>{partner.joinDate}</TableCell>
                  <TableCell>{partner.refId}</TableCell>
                  <TableCell>{partner.refName}</TableCell>
                  <TableCell>{partner.identityCardNumber}</TableCell>
                  <TableCell>{partner.taxCode}</TableCell>
                  <TableCell>{partner.paymentType}</TableCell>
                  <TableCell>{partner.bankAccountName}</TableCell>
                  <TableCell>{partner.bankAccountNumber}</TableCell>
                  <TableCell>
                    <Chip
                      label={
                        partner.affiliationStatus === "Expired" ? "Đối tác hết hạn" : "Chờ duyệt"
                      }
                      sx={{
                        backgroundColor: partner.status === "active" ? "#B3FFB3" : "#FFD6D6",
                        color: partner.status === "active" ? "#008000" : "#FF0000",
                        fontWeight: "500",
                        borderRadius: "8px",
                        padding: "5px 10px",
                        fontSize: "14px",
                      }}
                    />
                  </TableCell>
                  <TableCell
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "start",
                      position: "sticky",
                      right: 0,
                      backgroundColor: "#fff",
                      zIndex: 2,
                      boxShadow: "-2px 0 5px rgba(0,0,0,0.1)",
                    }}
                  >
                    <Button
                      onClick={() => {
                        setIsOpenModalApproval(true);
                        setSelectedPartner(partner);
                      }}
                    >
                      Phê duyệt
                    </Button>
                  </TableCell>
                </TableRow>
                <TitleDialog
                  title={`Phê duyệt đối tác`}
                  open={isOpenModalApproval}
                  handleClose={() => {
                    setIsOpenModalApproval(false);
                    setSelectedPartner(null);
                  }}
                  handleSubmit={() => handleApproval()}
                  submitBtnTitle="Xác nhận"
                >
                  <Box>
                    <Typography>Bạn có chắc muốn phê duyệt đối tác này?</Typography>
                  </Box>
                </TitleDialog>
              </React.Fragment>
            ))}
        </TableBody>
      </Table>

      <Box display="flex" justifyContent="flex-end" mt={2} pr={2}>
        <TablePagination
          labelRowsPerPage="Số hàng mỗi trang"
          rowsPerPageOptions={rowPerPageOptionsDefault}
          component="div"
          count={totalCount}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={(event, newPage) => setPage(newPage)}
          onRowsPerPageChange={(event) => {
            setRowsPerPage(parseInt(event.target.value, 10));
            setPage(0);
          }}
        />
      </Box>
    </TableContainer>
  );
};

export default ActivePartnersTable;
