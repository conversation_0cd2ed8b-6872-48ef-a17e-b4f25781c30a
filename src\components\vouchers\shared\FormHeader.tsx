import React from "react";
import {
  <PERSON>,
  Typography,
  Icon<PERSON>utton,
  Breadcrum<PERSON>,
  <PERSON>,
  Chip,
  useTheme,
  alpha,
  Stack,
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";

interface BreadcrumbItem {
  label: string;
  href?: string;
  onClick?: () => void;
}

interface FormHeaderProps {
  title: string;
  subtitle?: string;
  onBack: () => void;
  breadcrumbs?: BreadcrumbItem[];
  status?: {
    label: string;
    color: "primary" | "secondary" | "success" | "error" | "warning" | "info";
  };
  actions?: React.ReactNode;
  sx?: any;
}

const FormHeader: React.FC<FormHeaderProps> = ({
  title,
  subtitle,
  onBack,
  breadcrumbs = [],
  status,
  actions,
  sx = {},
}) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        p: 3,
        ...sx,
      }}
    >
      {/* Breadcrumbs */}
      {breadcrumbs.length > 0 && (
        <Breadcrumbs
          separator={<NavigateNextIcon fontSize="small" />}
          sx={{
            mb: 2,
            "& .MuiBreadcrumbs-separator": {
              color: alpha(theme.palette.text.secondary, 0.6),
            },
          }}
        >
          {breadcrumbs.map((item, index) => (
            <Link
              key={index}
              color={index === breadcrumbs.length - 1 ? "text.primary" : "text.secondary"}
              href={item.href}
              onClick={item.onClick}
              sx={{
                textDecoration: "none",
                fontSize: "0.875rem",
                fontWeight: index === breadcrumbs.length - 1 ? 500 : 400,
                cursor: item.href || item.onClick ? "pointer" : "default",
                "&:hover": {
                  textDecoration: item.href || item.onClick ? "underline" : "none",
                },
              }}
            >
              {item.label}
            </Link>
          ))}
        </Breadcrumbs>
      )}

      {/* Header Content */}
      <Box
        sx={{
          display: "flex",
          alignItems: "flex-start",
          justifyContent: "space-between",
          gap: 2,
        }}
      >
        <Box sx={{ display: "flex", alignItems: "flex-start", gap: 2, flex: 1 }}>
          {/* Back Button */}
          <IconButton
            onClick={onBack}
            sx={{
              mt: 0.5,
              p: 1.5,
              borderRadius: 2,
              backgroundColor: alpha(theme.palette.action.hover, 0.5),
              border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
              color: "white",
              "&:hover": {
                backgroundColor: alpha(theme.palette.primary.main, 0.08),
                borderColor: alpha(theme.palette.primary.main, 0.3),
                color: theme.palette.primary.main,
              },
              transition: "all 0.2s ease-in-out",
            }}
          >
            <ArrowBackIcon />
          </IconButton>

          {/* Title and Subtitle */}
          <Box sx={{ flex: 1 }}>
            <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: subtitle ? 1 : 0 }}>
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 700,
                  color: theme.palette.text.primary,
                  fontSize: { xs: "16px", md: "20px" },
                  lineHeight: 1.2,
                }}
              >
                {title}
              </Typography>
              {status && (
                <Chip
                  label={status.label}
                  color={status.color}
                  size="small"
                  sx={{
                    fontWeight: 500,
                    fontSize: "0.75rem",
                    height: 28,
                  }}
                />
              )}
            </Stack>
            {subtitle && (
              <Typography
                variant="body1"
                sx={{
                  color: theme.palette.text.secondary,
                  fontSize: "1rem",
                  lineHeight: 1.5,
                }}
              >
                {subtitle}
              </Typography>
            )}
          </Box>
        </Box>

        {/* Actions */}
        {actions && <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>{actions}</Box>}
      </Box>
    </Box>
  );
};

export default FormHeader;
