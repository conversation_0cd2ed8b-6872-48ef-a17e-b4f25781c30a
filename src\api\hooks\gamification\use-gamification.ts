import { EarnPointConfig } from "../../types/membership.types";
import { useState } from "react";
import { ErrorHandlerService } from "../../services/error-handler.service";
import useSnackbar from "@/src/hooks/use-snackbar";
import { gamificationService } from "../../services/gamification/gamification.service";

export const useGamification = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const snackbar = useSnackbar();

  const createGameBrand = async (shopId: string) => {
    try {
      setLoading(true);
      setError(null);

      const response = await gamificationService.createGameBrand(shopId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const listCampaign = async (shopId: string) => {
    try {
      setLoading(true);
      setError(null);

      const response = await gamificationService.listCampaign(shopId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const createCampaign = async (data: any) => {
    try {
      setLoading(true);
      setError(null);

      const response = await gamificationService.createCampaign(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getCampaign = async (campaignId: string) => {
    try {
      setLoading(true);
      setError(null);

      const response = await gamificationService.getCampaign(campaignId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const activeCampaign = async (shopId: string, campaignId: string) => {
    try {
      // setLoading(true);
      setError(null);

      const response = await gamificationService.activeCampaign(shopId, campaignId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      // setLoading(false);
    }
  };

  const getPrizes = async (campaignId: string) => {
    try {
      setLoading(true);
      setError(null);

      const response = await gamificationService.getPrizes(campaignId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updatePrizes = async (data: any) => {
    try {
      setLoading(true);
      setError(null);

      const response = await gamificationService.updatePrizes(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const createPrize = async (data: { shopId: string; [key: string]: any }) => {
    try {
      setLoading(true);
      setError(null);

      const formData = new FormData();
      formData.append("ShopId", data.ShopId);
      for (const key in data) {
        if (data.hasOwnProperty(key) && key !== "ShopId") {
          if (key === "avatar" && data[key] instanceof File) {
            formData.append(key, data[key], data[key].name);
          } else if (data[key] !== null && data[key] !== undefined) {
            formData.append(key, data[key]);
          }
        }
      }

      const response = await gamificationService.createPrize(formData);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getGames = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await gamificationService.getGames();
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  return {
    createGameBrand,
    listCampaign,
    getCampaign,
    createCampaign,
    activeCampaign,
    getPrizes,
    updatePrizes,
    createPrize,
    getGames,
    loading,
    error,
  };
};
