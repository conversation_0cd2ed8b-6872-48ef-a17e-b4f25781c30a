import React, { useC<PERSON>back, useContext, useEffect, useState } from "react";
import {
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Button,
  TextField,
  InputAdornment,
  IconButton,
  TablePagination,
  Box,
  Modal,
  ListItem,
  Checkbox,
  ListItemText,
  List,
  Stack,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import _ from "lodash";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useUser } from "@/src/api/hooks/user/use-user";
import CloseIcon from "@mui/icons-material/Close";
import SearchIcon from "@mui/icons-material/Search";
import { UserGroupCommissionConfig } from "@/src/api/types/affiliation.type";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { AffiliatePolicyContext } from "../../CommissionPolicy";

const TableCommissionGroups = ({ groups, handleSaveGroup, onBack }) => {
  const [localGroups, setLocalGroups] = useState(groups);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [selectedGroup, setSelectedGroup] = useState<UserGroupCommissionConfig>();
  const [errors, setErrors] = useState({});
  const { commissionsConfigData, setCommissionsConfigData } = useContext(AffiliatePolicyContext);
  // Hàm kiểm tra lỗi
  const validateField = (name, value, groupId) => {
    let errorMessage = "";

    if (name === "name" && value.trim() === "") {
      errorMessage = "Tên nhóm không được để trống.";
    } else if (
      (name === "levelOneCommissionPercentage" || name === "levelTwoCommissionPercentage") &&
      (isNaN(value) || value < 0 || value > 100)
    ) {
      errorMessage = "Tỷ lệ hoa hồng phải từ 0% đến 100%.";
    }

    setErrors((prevErrors) => {
      const updatedErrors = { ...prevErrors };

      if (errorMessage) {
        // Nếu có lỗi, cập nhật lỗi vào state
        updatedErrors[groupId] = { ...updatedErrors[groupId], [name]: errorMessage };
      } else {
        // Nếu không có lỗi, xóa field error
        if (updatedErrors[groupId]) {
          delete updatedErrors[groupId][name];

          // Nếu không còn lỗi nào cho groupId, xóa luôn groupId khỏi errors
          if (Object.keys(updatedErrors[groupId]).length === 0) {
            delete updatedErrors[groupId];
          }
        }
      }

      return updatedErrors;
    });
  };

  const validateAllFields = () => {
    let newErrors: { [key: string]: { [key: string]: string } } = {}; // Định nghĩa kiểu dữ liệu của error

    localGroups.forEach((group) => {
      let groupErrors: { [key: string]: string } = {}; // Object chứa lỗi của từng group

      if (!group.name?.trim()) {
        groupErrors["name"] = "Tên nhóm không được để trống.";
      }

      if (
        isNaN(group.levelOneCommissionPercentage) ||
        group.levelOneCommissionPercentage < 0 ||
        group.levelOneCommissionPercentage > 100
      ) {
        groupErrors["levelOneCommissionPercentage"] = "Tỷ lệ hoa hồng phải từ 0% đến 100%.";
      }

      if (
        isNaN(group.levelTwoCommissionPercentage) ||
        group.levelTwoCommissionPercentage < 0 ||
        group.levelTwoCommissionPercentage > 100
      ) {
        groupErrors["levelTwoCommissionPercentage"] = "Tỷ lệ hoa hồng phải từ 0% đến 100%.";
      }

      if (Object.keys(groupErrors).length > 0) {
        newErrors[group.id] = groupErrors; // Lưu lỗi theo group ID
      }
    });

    setErrors(newErrors);

    return Object.keys(newErrors).length === 0; // Trả về true nếu không có lỗi
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const paginatedGroups = localGroups.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  const [openModal, setOpenModal] = useState(false);

  const handleOpenModal = (group) => {
    setOpenModal(true);
    setSelectedGroup(group);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setSelectedGroup(null);
  };

  const handleSubmitUserGroup = (members) => {
    if (!selectedGroup) return; // Kiểm tra nếu chưa chọn nhóm nào
    const userIds = members.map((item) => item.userId);

    setLocalGroups((prevGroups) =>
      prevGroups.map((group) => (group.id === selectedGroup.id ? { ...group, userIds } : group))
    );

    handleCloseModal();
  };

  useEffect(() => {
    if (groups && groups.length > 0) {
      const groupsWithId = groups.map((group, index) => ({
        ...group,
        id: `group-${index}`,
      }));
      setLocalGroups(groupsWithId);
    }
  }, [groups]);

  const handleInputChange = (e, id: string) => {
    const { name, value } = e.target;
    let updatedValue = value;
    if (name === "levelOneCommissionPercentage" || name === "levelTwoCommissionPercentage") {
      updatedValue = Math.max(0, Math.min(100, Number(value))).toString();
    }

    validateField(name, updatedValue, id);
    setLocalGroups((prev) =>
      prev.map((group) => (group.id === id ? { ...group, [name]: updatedValue } : group))
    );
  };

  const handleAddGroup = () => {
    setLocalGroups((prev) => [
      ...prev,
      {
        id: `group-${Date.now()}`,
        name: "",
        userIds: [],
        levelOneCommissionPercentage: "0",
        levelTwoCommissionPercentage: "",
      },
    ]);
  };
  // setIsAddingGroup(true);

  const handleDeleteGroup = (groupId) => {
    setLocalGroups((prev) => prev.filter((group) => group.id !== groupId));
    validateAllFields();
  };

  useEffect(() => {
    validateAllFields();
  }, [localGroups]); // Gọi validate khi localGroups thay đổi

  return (
    <Box>
      <Stack flexDirection={"row"} justifyContent={"space-between"} alignItems={"center"}>
        <Typography
          component="span"
          onClick={onBack}
          sx={{
            fontSize: "20px",
            textDecoration: "none",
            fontWeight: "700",
            color: "#000",
            display: "flex",
            alignItems: "center",
            gap: "8px",
            paddingBottom: "30px",
            cursor: "pointer",
          }}
        >
          <ArrowBackIcon sx={{ fontSize: "24px" }} />
          Hoa hồng theo nhóm đối tác
        </Typography>
        <Button
          sx={{
            color: "#FFFFFF",
            fontSize: "18px",
            fontWeight: "700",
            background: "#2654FE",
            width: "fit-content",
            padding: "5px 15px",
            borderRadius: "10px",
            textTransform: "none",
          }}
          onClick={() => {
            if (!validateAllFields()) {
              return;
            }

            handleSaveGroup(localGroups.map(({ id, ...rest }) => rest));
          }}
        >
          Lưu
        </Button>
      </Stack>
      {Object.keys(errors).length > 0 && (
        <Typography sx={{ color: "red", fontSize: 15, mt: 1 }}>
          Vui lòng kiểm tra lại thông tin nhập vào. Các trường có lỗi sẽ được đánh dấu đỏ.
        </Typography>
      )}

      <TableContainer sx={{ marginTop: 2, border: "1px solid #e0e0e0", borderRadius: "8px" }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ minWidth: "50px" }}>STT</TableCell>
              <TableCell sx={{ minWidth: "200px" }}>Tên nhóm</TableCell>
              <TableCell sx={{ minWidth: "200px" }}>Thành viên</TableCell>
              <TableCell sx={{ minWidth: "150px" }}>Hoa hồng bậc 1</TableCell>
              {commissionsConfigData.basicCommissionsConfig.isActiveLevelTwo && (
                <TableCell sx={{ minWidth: "150px" }}>Hoa hồng bậc 2</TableCell>
              )}

              <TableCell sx={{ minWidth: "100px" }}>Quản lý</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {localGroups.length === 0 && (
              <TableRow>
                <TableCell colSpan={6} align="center" sx={{ padding: 4 }}>
                  <Typography sx={{ marginTop: 1, color: "#757575" }}>Không có nhóm nào</Typography>
                  <Button
                    variant="contained"
                    sx={{ marginTop: 2, backgroundColor: "#2654FE" }}
                    onClick={handleAddGroup}
                  >
                    Thêm nhóm mới
                  </Button>
                </TableCell>
              </TableRow>
            )}

            {selectedGroup && (
              <ModalSelectUsers
                openModal={openModal}
                handleCloseModal={handleCloseModal}
                selectedGroup={selectedGroup}
                submitUserGroup={handleSubmitUserGroup}
                localGroups={localGroups}
              />
            )}

            {paginatedGroups.map((group, index) => (
              <TableRow key={group.id}>
                <TableCell>{page * rowsPerPage + index + 1}</TableCell>
                <TableCell>
                  <TextField
                    name="name"
                    value={group?.name}
                    onChange={(e) => handleInputChange(e, group.id)}
                    size="small"
                    variant="outlined"
                    placeholder="Nhập tên nhóm"
                    error={!!errors[group.id]?.name}
                    helperText={errors[group.id]?.name}
                    sx={{
                      border: "1px solid #D9D9D9",
                      borderRadius: "8px",
                      "& fieldset": { border: "none" },
                      "& input::placeholder": {
                        color: "#D9D9D9",
                        fontSize: "16px",
                        fontWeight: "400",
                        opacity: "1",
                      },
                    }}
                  />
                </TableCell>
                <TableCell>
                  <Typography
                    onClick={() => handleOpenModal(group)}
                    sx={{
                      color: "#2654FE",
                      cursor: "pointer",
                      "&:hover": { textDecoration: "underline" },
                    }}
                  >
                    {group?.userIds.length > 0
                      ? `Đã thêm (${group?.userIds.length})`
                      : "Thêm thành viên"}
                  </Typography>
                </TableCell>
                <TableCell>
                  <TextField
                    name="levelOneCommissionPercentage"
                    value={group.levelOneCommissionPercentage}
                    onChange={(e) => handleInputChange(e, group.id)}
                    size="small"
                    variant="outlined"
                    type="number"
                    placeholder="Nhập tỷ lệ hoa hồng"
                    error={!!errors[group.id]?.levelOneCommissionPercentage}
                    helperText={errors[group.id]?.levelOneCommissionPercentage}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <Typography sx={{ color: "#000", fontSize: "16px" }}>%</Typography>
                        </InputAdornment>
                      ),
                      inputProps: {
                        min: 0, // Giới hạn giá trị tối thiểu
                        max: 100, // Giới hạn giá trị tối đa
                      },
                    }}
                    sx={{
                      "& fieldset": { border: "1px solid #E0E0E0", borderRadius: "8px" },
                    }}
                  />
                </TableCell>
                {commissionsConfigData.basicCommissionsConfig.isActiveLevelTwo && (
                  <TableCell>
                    <TextField
                      name="levelTwoCommissionPercentage"
                      value={group?.levelTwoCommissionPercentage}
                      onChange={(e) => handleInputChange(e, group.id)}
                      size="small"
                      variant="outlined"
                      type="number"
                      placeholder="Nhập tỷ lệ hoa hồng"
                      error={!!errors[group.id]?.levelTwoCommissionPercentage}
                      helperText={errors[group.id]?.levelTwoCommissionPercentage}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <Typography sx={{ color: "#000", fontSize: "16px" }}>%</Typography>
                          </InputAdornment>
                        ),
                        inputProps: {
                          min: 0,
                          max: 100,
                        },
                      }}
                      sx={{
                        "& fieldset": { border: "1px solid #E0E0E0", borderRadius: "8px" },
                      }}
                    />
                  </TableCell>
                )}

                <TableCell>
                  <IconButton onClick={(e) => handleDeleteGroup(group.id)}>
                    <DeleteIcon sx={{ color: "#FF0000" }} />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {localGroups.length > 0 && (
        <Button
          variant="contained"
          sx={{
            mt: 2,
            backgroundColor: "#fff",
            color: "#2654FE",
            fontSize: "14px",
            fontWeight: "700",
            padding: 0,
            boxShadow: "none",
            textTransform: "none !important",
            "&:hover": {
              boxShadow: "none",
            },
          }}
          onClick={handleAddGroup}
        >
          Thêm nhóm mới
        </Button>
      )}

      {localGroups.length > 0 && (
        <Box sx={{ display: "flex", justifyContent: "end", alignItems: "center", mt: 2 }}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Typography variant="body2" color="textSecondary">
              Rows per page:
            </Typography>
            <TextField
              select
              size="small"
              value={rowsPerPage}
              onChange={handleChangeRowsPerPage}
              SelectProps={{ native: true }}
              sx={{ width: "60px" }}
            >
              {[5, 10, 15].map((option) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </TextField>
          </Box>
          <TablePagination
            component="div"
            count={localGroups.length}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            rowsPerPageOptions={[]}
            labelDisplayedRows={({ from, to, count }) => `${from}-${to} of ${count}`}
            sx={{
              "& .MuiTablePagination-actions": {
                display: "flex",
                gap: 1,
              },
              "& .MuiTablePagination-selectLabel": {
                display: "none",
              },
              "& .MuiTablePagination-displayedRows": {
                margin: 0,
              },
            }}
          />
        </Box>
      )}
    </Box>
  );
};

const ModalSelectUsers = ({
  openModal,
  handleCloseModal,
  selectedGroup,
  submitUserGroup,
  localGroups,
}) => {
  const [selectedMembers, setSelectedMembers] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");

  const [users, setUsers] = useState([]);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const storeId = useStoreId();

  const { listUser, listUserByUserIds, loading } = useUser();

  const fetchUserList = async (currentPage, pageSize, searchQuery, shopId) => {
    const skip = searchQuery ? 0 : currentPage * pageSize; // Always search from the beginning if there's a search query
    const limit = pageSize;

    const response = await listUser(`?skip=${skip}&limit=${limit}`, {
      search: searchQuery,
      shopId: shopId,
      affiliationStatus: "Actived",
    });

    if (response && response.data) {
      setUsers(response.data.data || []);
      setTotalCount(response.data.total || 0);
    }
  };

  // Wrap fetchUserList with debounce
  const debouncedFetchUserList = useCallback(
    _.debounce((currentPage, pageSize, searchQuery, shopId) => {
      fetchUserList(currentPage, pageSize, searchQuery, shopId);
    }, 400), // Delay 1s
    []
  );

  const handleSelectMember = (member) => {
    setSelectedMembers(
      (prev) =>
        prev.some((selectedMember) => selectedMember.userId === member.userId)
          ? prev.filter((m) => m.userId !== member.userId) // Loại bỏ member nếu đã có trong selectedMembers
          : [...prev, member] // Thêm member vào selectedMembers nếu chưa có
    );
  };

  const handleSelectAll = (event) => {
    if (event.target.checked) {
      // Nếu checkbox được chọn, thêm tất cả các thành viên vào selectedMembers mà chưa có và chưa thuộc nhóm nào (hoặc thuộc selectedGroup)
      setSelectedMembers((prevSelectedMembers) => {
        const selectedUserIds = prevSelectedMembers.map((user) => user.userId); // Lấy các userIds đã được chọn
        const usersCanBeSelected = users.filter((user) => {
          // Kiểm tra xem user có thuộc selectedGroup hoặc không thuộc nhóm nào trong localGroups
          const isInSelectedGroup = selectedGroup.userIds.includes(user.userId); // Kiểm tra nếu user thuộc selectedGroup
          const isInAnyOtherGroup = localGroups.some(
            (group) => group.id !== selectedGroup.id && group.userIds.includes(user.userId)
          ); // Kiểm tra xem user có thuộc nhóm nào khác không

          // Nếu user thuộc selectedGroup hoặc chưa thuộc nhóm nào khác, cho phép chọn
          return (
            (isInSelectedGroup || !isInAnyOtherGroup) && !selectedUserIds.includes(user.userId)
          );
        });

        return [...prevSelectedMembers, ...usersCanBeSelected]; // Cập nhật selectedMembers với các thành viên còn thiếu
      });
    } else {
      // Nếu checkbox không được chọn, bỏ các thành viên hiện tại trong selectedMembers
      setSelectedMembers((prevSelectedMembers) => {
        const allUserIds = users.map((user) => user.userId); // Lấy danh sách tất cả userIds
        return prevSelectedMembers.filter((member) => !allUserIds.includes(member.userId)); // Bỏ những userId hiện có trong selectedMembers
      });
    }
  };

  const handleConfirmSelection = () => {
    submitUserGroup(selectedMembers);
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleRemoveMember = (member) => {
    setSelectedMembers((prev) => prev.filter((m) => m !== member));
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const fetchUserListByUserId = async (shopId, userIds) => {
    const response = await listUserByUserIds({
      userIds,
      shopId,
    });
    if (response?.data) {
      setSelectedMembers(response.data.data || []);
    }
  };
  const isDisableCheckbox = (user) => {
    const remainingGroups = localGroups.filter((group) => group.id !== selectedGroup.id);

    return remainingGroups.some((group) => group.userIds.includes(user.userId));
  };

  const userGroupName = (user) => {
    // Tìm nhóm đầu tiên chứa userId
    const group = localGroups.find((group) => group.userIds.includes(user.userId));
    return group?.name;
  };

  useEffect(() => {
    if (storeId && selectedGroup?.userIds?.length) {
      fetchUserListByUserId(storeId, selectedGroup.userIds);
    }
  }, [selectedGroup, storeId]);

  const isAllChecked = () => {
    if (users.length === 0) return false;
    const selectedUserIds = selectedMembers.map((user) => user.userId); // Lấy các userIds đã được chọn

    const userIdsCanBeSelected = users
      .filter((user) => {
        // Kiểm tra xem user có thuộc selectedGroup hoặc không thuộc nhóm nào trong localGroups
        const isInSelectedGroup = selectedGroup.userIds.includes(user.userId); // Kiểm tra nếu user thuộc selectedGroup
        const isInAnyOtherGroup = localGroups.some(
          (group) => group.id !== selectedGroup.id && group.userIds.includes(user.userId)
        ); // Kiểm tra xem user có thuộc nhóm nào khác không

        // Nếu user thuộc selectedGroup hoặc chưa thuộc nhóm nào khác, cho phép chọn
        return isInSelectedGroup || !isInAnyOtherGroup;
      })
      .map((user) => user.userId);
    // Kiểm tra xem tất cả userIds trong usersCanBeSelected có trong selectedUserIds không
    const allChecked = userIdsCanBeSelected.every((userId) => selectedUserIds.includes(userId));

    return allChecked;
  };

  useEffect(() => {
    if (!storeId) return;
    debouncedFetchUserList(page, rowsPerPage, searchTerm, storeId);
    return () => {
      debouncedFetchUserList.cancel(); // Cancel debounce if component unmounts or searchText changes
    };
  }, [storeId, page, rowsPerPage, searchTerm]);

  return (
    <Modal open={openModal} onClose={handleCloseModal}>
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: "80%",
          bgcolor: "background.paper",
          borderRadius: "8px",
          boxShadow: 24,
          p: 3,
        }}
      >
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Typography sx={{ fontSize: "16px", fontWeight: "400", color: "#000" }}>
            Chọn thành viên
          </Typography>
          <IconButton onClick={handleCloseModal}>
            <CloseIcon />
          </IconButton>
        </Box>
        <Box
          sx={{
            display: "flex",
            alignItems: "start",
            "@media (max-width: 600px)": { flexDirection: "column" },
          }}
        >
          <Box
            sx={{
              width: "50%",
              paddingRight: "20px",
              borderRight: "1px solid #E0E0E0",
              "@media (max-width: 600px)": { width: "100%" },
            }}
          >
            <TextField
              fullWidth
              placeholder="Tìm thành viên"
              value={searchTerm}
              onChange={handleSearchChange}
              size="small"
              variant="outlined"
              sx={{ mt: 2 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon sx={{ color: "#757575" }} />
                  </InputAdornment>
                ),
              }}
            />
            <Box sx={{ mt: 2 }}>
              <ListItem>
                <Checkbox checked={isAllChecked()} onChange={handleSelectAll} />
                <ListItemText primary="Chọn tất cả" />
              </ListItem>
              <Box sx={{ maxHeight: 200, overflow: "auto" }}>
                <List dense>
                  {users.map((member, index) => (
                    <ListItem key={index}>
                      <Checkbox
                        checked={selectedMembers.some(
                          (memberItem) => memberItem.userId === member.userId
                        )}
                        onChange={() => handleSelectMember(member)}
                        disabled={isDisableCheckbox(member)}
                      />
                      <ListItemText
                        primary={
                          <>
                            <Typography>{`${member.fullname} (${member.phoneNumber})`}</Typography>
                            {isDisableCheckbox(member) && (
                              <Typography
                                variant="body2"
                                color="textSecondary"
                                sx={{ marginTop: 1 }}
                              >
                                Thuộc nhóm {userGroupName(member)}
                              </Typography>
                            )}
                          </>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                rowsPerPageOptions={[]}
                labelDisplayedRows={({ from, to, count }) => `${from}-${to} of ${count}`}
                sx={{
                  "& .MuiTablePagination-actions": {
                    display: "flex",
                    gap: 1,
                  },
                  "& .MuiTablePagination-selectLabel": {
                    display: "none",
                  },
                  "& .MuiTablePagination-displayedRows": {
                    margin: 0,
                  },
                }}
              />
            </Box>
          </Box>
          {selectedMembers.length > 0 && (
            <Box
              sx={{
                mt: 2,
                width: "50%",
                paddingLeft: "20px",
                "@media (max-width: 600px)": { width: "100%" },
              }}
            >
              <Typography variant="body2" color="textSecondary">
                Đã chọn {selectedMembers.length} người
              </Typography>
              <List dense sx={{ overflow: "auto", mt: 1, maxHeight: 400 }}>
                {selectedMembers.map((member, index) => (
                  <ListItem
                    key={index}
                    secondaryAction={
                      <IconButton edge="end" onClick={() => handleRemoveMember(member)}>
                        <Box
                          sx={{
                            backgroundColor: "#D9D9D9",
                            borderRadius: "50%",
                            width: 20,
                            height: 20,
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >
                          <CloseIcon sx={{ fontSize: 14, color: "#FFFFFF" }} />
                        </Box>
                      </IconButton>
                    }
                  >
                    <ListItemText primary={`${member.fullname} (${member.phoneNumber})`} />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
        </Box>
        <Box sx={{ mt: 3, display: "flex", justifyContent: "flex-end", gap: 2 }}>
          <Button
            variant="outlined"
            onClick={handleCloseModal}
            sx={{ borderColor: "#E0E0E0", color: "#757575", textTransform: "none" }}
          >
            Hủy bỏ
          </Button>
          <Button
            variant="contained"
            onClick={handleConfirmSelection}
            sx={{ backgroundColor: "#2654FE", textTransform: "none" }}
          >
            Xác nhận
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default TableCommissionGroups;
