import React, { useEffect, useState } from "react";
import DashboardLayout from "@/src/layouts/dashboard";
import {
  Box,
  Button,
  Card,
  Divider,
  FormControl,
  MenuItem,
  Select,
  Typography,
  IconButton,
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import Grid from "@mui/material/Grid2";
import OrderItemsBox from "@/src/components/orders/draft/OrderItemsBox";
import CustomerOrderBox from "@/src/components/orders/draft/CustomerOrderBox";
import OrderTotalPaymentSummary from "@/src/components/orders/draft/OrderTotalPaymentSummary";
import DeliveryMethodOrder from "@/src/components/orders/draft/DeliveryMethodOrder";
import PaymentMethodOrder from "@/src/components/orders/draft/PaymentMethodOrder";
import ShippingAddressOrder from "@/src/components/orders/draft/ShippingAddressOrder";
import { formatMoney } from "@/src/utils/format-money";
import {
  CartStatusDelivery,
  CartTypePay,
  CreateOrUpdateCartData,
  OrderDeliveryMethod,
} from "@/src/api/types/cart.types";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useCart } from "@/src/api/hooks/cart/use-cart";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useUser } from "@/src/api/hooks/user/use-user";
import { paths } from "@/src/paths";
import { useRouter } from "next/router";
import { useOrder } from "@/src/api/hooks/order/use-order";
import { useBranch } from "@/src/api/hooks/branch/use-branch";
import { LoadingState } from "../../common/loading-state";
import { LoadingBackdrop } from "../../common/loading-backdrop";
import { Padding } from "@/src/styles/CommonStyle";
import { useShop } from "@/src/api/hooks/shop/use-shop";
import { Edit, LocationOn, Receipt } from "@mui/icons-material";
import ModalInvoice from "./ModalInvoice";

const LeftColumn = ({ children }) => {
  return (
    <Grid size={{ xs: 12, md: 9 }}>
      <Box>{children}</Box>
    </Grid>
  );
};

const RightColumn = ({ children }) => {
  return (
    <Grid size={{ xs: 12, md: 3 }}>
      <Box>{children}</Box>
    </Grid>
  );
};

export interface TaxInvoiceDto {
  id?: string;
  createdDate?: string;
  modifiedDate?: string;
  createdBy?: string;
  modifiedBy?: string;
  isDeleted?: boolean;
  deletedAt?: string;
  taxInvoiceId?: string;
  shopId: string;
  userId: string;
  taxPayerType: string;
  taxCode: string;
  name: string;
  address: string;
  email: string;
  phoneNumber?: string;
  isDefault?: boolean;
}

const initCartData = (): CreateOrUpdateCartData => ({
  cartNo: "",
  cartId: "",
  transactionId: "",
  partnerId: "",
  userId: "",
  addressId: "",
  shopId: "",
  listItems: [], // Mặc định là một mảng rỗng
  voucherPromotion: [], // Mặc định là một mảng rỗng
  voucherTransport: [], // Mặc định là một mảng rỗng
  price: 0,
  exchangePoints: 0,
  pointPrice: 0,
  voucherPromotionPrice: 0,
  voucherTransportPrice: 0,
  transportPrice: 0,
  transportService: "LCOD", // Giá trị mặc định
  statusDelivery: "InShop", // Giá trị mặc định
  typePay: "COD", // Giá trị mặc định,
  branchId: "",
  cartOrigin: "WebPartner",
  created: new Date().toISOString(), // Mặc định là ngày giờ hiện tại
  updated: new Date().toISOString(), // Mặc định là ngày giờ hiện tại
  taxInvoice: {
    shopId: "",
    userId: "",
    taxPayerType: "",
    taxCode: "",
    name: "",
    address: "",
    email: "",
    phoneNumber: "",
    isDefault: false,
  },
});

export default function OrderDraftForm({ cart }) {
  const isEdit = !!cart;
  const snackbar = useSnackbar();
  const { detailShop } = useShop();
  const [selectedCustomer, setSelectedCustomer] = useState<any>({});
  const [selectedShippingAddress, setSelectedShippingAddress] = useState<any>({});
  const [cartData, setCartData] = useState<CreateOrUpdateCartData>(initCartData);
  const [selectCustomerFromList, setSelectCustomerFromList] = useState<any>({});
  const [isDisablePayment, setIsDisablePayment] = useState(false);
  const router = useRouter();
  const [taxRateShop, setTaxRateShop] = useState<number>(0);
  const { createOrUpdateCart, loading } = useCart();
  const { createOrder, loading: orderLoading } = useOrder();
  const [isOpenModalEditInvoice, setIsOpenModalEditInvoice] = useState<boolean>(false);
  const storeId = useStoreId();
  const [businessForm, setBusinessForm] = useState({
    taxCode: "",
    companyName: "",
    address: "",
    email: "",
    taxPayerType: "",
  });

  // Form states for individual
  const [individualForm, setIndividualForm] = useState({
    name: "",
    taxCode: "",
    address: "",
    email: "",
    taxPayerType: "",
  });
  const [tabValue, setTabValue] = useState(0);

  const { listAddress, detailUser } = useUser();
  // Tính tổng số tiền giỏ hàng
  const calculateTotal = (listItems) => {
    return listItems.reduce((total, item) => total + item.price * item.quantity, 0);
  };

  useEffect(() => {
    const fetchShop = async () => {
      const res = await detailShop(storeId);
      if (res && res.status === 200) {
        setTaxRateShop(res.data.defaultTaxRate);
      }
    };
    fetchShop();
  }, [storeId]);

  // Cập nhật số lượng sản phẩm
  const updateQuantity = (itemsId, newQuantity) => {
    // setCartItems(cartItems.map((item) => (item.itemsId === itemsId ? { ...item, quantity: newQuantity } : item)));
    setCartData((prevState) => {
      const listItems = prevState.listItems.map((item) => {
        if (item.itemsId === itemsId) {
          const taxRateCheck =
            item.customTaxRate === 0 || item.customTaxRate
              ? item.customTaxRate
              : item.taxRate === 0 || item.taxRate
              ? item.taxRate
              : taxRateShop;
          const taxAmount = item.price * newQuantity * (taxRateCheck / 100);
          return {
            ...item,
            quantity: newQuantity,
            taxAmount: taxAmount,
            taxRate: taxRateCheck,
          };
        } else {
          const taxRateCheck =
            item.customTaxRate === 0 || item.customTaxRate
              ? item.customTaxRate
              : item.taxRate === 0 || item.taxRate
              ? item.taxRate
              : taxRateShop;
          return {
            ...item,
            taxRate: taxRateCheck,
            quantity: item.quantity,
            customTaxRate: item.customTaxRate,
            taxAmount: item.price * item.quantity * (taxRateCheck / 100),
          };
        }
      });
      return {
        ...prevState,
        listItems: listItems,
        price: calculateTotal(listItems),
      };
    });
  };

  // Xóa sản phẩm khỏi giỏ hàng
  const removeItem = (itemsId) => {
    // setCartItems(cartItems.filter((item) => item.itemsId !== itemsId));

    setCartData((prevState) => {
      const listItems = prevState.listItems.filter((item) => item.itemsId !== itemsId);
      return {
        ...prevState,
        listItems,
        price: calculateTotal(listItems),
      };
    });
  };

  const addItemToCart = (items) => {
    let cartListItems = cartData.listItems;
    cartListItems = cartData.listItems.map((item) => {
      return {
        ...item,
        customTaxRate: item.customTaxRate ? item.customTaxRate : taxRateShop,
        taxRate: item.customTaxRate ? item.customTaxRate : taxRateShop,
        taxAmount:
          item.price *
          item.quantity *
          (item.customTaxRate ? item.customTaxRate : taxRateShop / 100),
      };
    });
    // Lọc ra các sản phẩm chưa có trong giỏ hàng
    const filteredItems = items.filter(
      (newItem) => !cartListItems.some((cartItem) => cartItem.itemsId === newItem.itemsId)
    );

    if (filteredItems.length > 0) {
      // Thêm các sản phẩm chưa có vào giỏ hàng
      // setCartItems((prevCartItems) => [...prevCartItems, ...filteredItems]);

      setCartData((prevState) => {
        const newCartListItems = [...prevState.listItems, ...filteredItems];
        return {
          ...prevState,
          listItems: newCartListItems,
          price: calculateTotal(newCartListItems),
        };
      });
    } else {
    }
  };

  const handleSelectCustomer = async (item) => {
    setSelectedCustomer(item);
    setSelectCustomerFromList(item);
    setCartData((prevState) => {
      const listItems = prevState.listItems;
      return {
        ...prevState,
        userId: item.userId,
        price: calculateTotal(listItems),
      };
    });
  };

  const handleClickRemoveUser = () => {
    setSelectedCustomer({});
    setSelectCustomerFromList({});
    setCartData((prevState) => {
      const listItems = prevState.listItems;
      return {
        ...prevState,
        price: calculateTotal(listItems),
        userId: null,
      };
    });
  };

  const handleDeliveryMethod = (e) => {
    // setSelectedDeliveryMethod(e.target.value);
    setCartData((prevState) => {
      return {
        ...prevState,
        statusDelivery: e.target.value,
      };
    });
  };

  const handlePaymentMethod = (e) => {
    // setSelectedPaymentMethod(e.target.value);
    setCartData((prevState) => {
      return {
        ...prevState,
        typePay: e.target.value,
      };
    });
  };

  const handleSelectedShippingAddress = (item) => {
    setSelectedShippingAddress(item);
    setCartData((prevState) => {
      return {
        ...prevState,
        addressId: item && item.shippingAddressId ? item?.shippingAddressId : "",
      };
    });
  };

  const handleSubmitForm = async () => {
    if (!cartData.userId || cartData.userId === "") {
      snackbar.error("Khách hàng không được để trống");
      return;
    }

    if (cartData.listItems.length === 0) {
      snackbar.error("Sản phẩm không được để trống");
      return;
    }
    if (businessForm.taxCode || individualForm.taxCode) {
      cartData.taxInvoice = {
        address: tabValue === 0 ? businessForm.address : individualForm.address,
        email: tabValue === 0 ? businessForm.email : individualForm.email,
        name: tabValue === 0 ? businessForm.companyName : individualForm.name,
        shopId: storeId,
        taxCode: tabValue === 0 ? businessForm.taxCode : individualForm.taxCode,
        taxPayerType: tabValue === 0 ? businessForm.taxPayerType : individualForm.taxPayerType,
        userId: cartData.userId,
      };
    } else {
      cartData.taxInvoice = null;
    }
    cartData.listItems = cartData.listItems.map((item) => {
      const taxRateCheck =
        item.customTaxRate === 0 || item.customTaxRate
          ? item.customTaxRate
          : item.taxRate === 0 || item.taxRate
          ? item.taxRate
          : taxRateShop;
      const taxRate = taxRateCheck / 100;
      const taxAmount = item.price * item.quantity * taxRate;
      return {
        ...item,
        taxRate: taxRateCheck,
        taxAmount,
      };
    });

    const response = await createOrUpdateCart({ ...cartData, cartOrigin: "WebPartner" });
    if (response && response.data) {
      if (cart) {
        setIsDisablePayment(false);
        snackbar.success("Cập nhật đơn hàng thành công");
      } else {
        await router.push(`${paths.orders.draft.detail}?id=${response?.data.cartId}`);
        snackbar.success("Tạo đơn hàng thành công");
      }
    }
  };

  const handleClickPayment = async () => {
    if (isEdit) {
      if (!cartData.userId || cartData.userId === "") {
        snackbar.error("Khách hàng không được để trống");
        return;
      }

      if (cartData.listItems.length === 0) {
        snackbar.error("Sản phẩm không được để trống");
        return;
      }

      if (!cartData.branchId) {
        snackbar.error("Điểm bán không được để trống");
        return;
      }

      const updateCartResponse = await createOrUpdateCart({
        ...cartData,
        cartOrigin: "WebPartner",
      });
      if (updateCartResponse && updateCartResponse.data) {
        const createOrderResponse = await createOrder({ ...updateCartResponse.data });
        if (createOrderResponse && createOrderResponse.data) {
          await router.push(paths.orders.draft.listdraftorder);
          snackbar.success("Tạo đơn hàng thành công");
        }
      }
    }
  };

  useEffect(() => {
    if (selectCustomerFromList && selectCustomerFromList.userId) {
      fetchShippingAddress(selectCustomerFromList.userId);
    } else {
      setSelectedShippingAddress({});
    }
  }, [selectCustomerFromList]);

  const fetchShippingAddress = async (userId) => {
    const response = await listAddress("?skip=0&limit=999", { userId: userId });
    if (response && response.data) {
      const listFetchAddress = response.data.data;

      const defaultAddress = listFetchAddress.find((address) => address.isDefault);
      setSelectedShippingAddress(defaultAddress || {}); // Cập nhật địa chỉ mặc định
      setCartData((prevState) => {
        return {
          ...prevState,
          addressId: defaultAddress?.shippingAddressId || "",
        };
      });
    }
  };

  const fetchUser = async (userId) => {
    try {
      const response = await detailUser(userId);
      if (response.data) {
        const { data } = response;
        setSelectedCustomer(data);
      }
    } catch (error) {}
  };

  const fetchInitShippingAddress = async (userId, addressId) => {
    const response = await listAddress("?skip=0&limit=999", { userId: userId });
    if (response?.data) {
      const listFetchAddress = response.data.data;
      const selectedAddress = listFetchAddress.find(
        (address) => address.shippingAddressId === addressId
      );
      setSelectedShippingAddress(selectedAddress || {});
    }
  };

  const handleSelectBranch = (branchId) => {
    setCartData((prevState) => {
      return {
        ...prevState,
        branchId,
      };
    });
  };

  useEffect(() => {
    if (cart) {
      // Khởi tạo dữ liệu ban đầu
      const initialize = async () => {
        if (cart.userId) {
          await fetchUser(cart.userId);
        }

        if (cart.addressId) {
          await fetchInitShippingAddress(cart.userId, cart.addressId);
        }

        setCartData(cart); // Cập nhật cartData từ cart
      };

      initialize();
    }
  }, [cart]);

  // useEffect theo dõi sự thay đổi trong cartData
  useEffect(() => {
    if (isEdit) {
      // Kiểm tra xem dữ liệu hiện tại có khác với dữ liệu ban đầu không
      const hasChanges = JSON.stringify(cartData) !== JSON.stringify(cart);

      setIsDisablePayment(hasChanges);
    }
  }, [cartData]);

  useEffect(() => {
    if (!isEdit && storeId) {
      setCartData((prevState) => {
        return {
          ...prevState,
          shopId: storeId,
        };
      });
    }
  }, [storeId]);

  const handleCancel = () => {
    router.push(paths.orders.draft.listdraftorder);
  };

  return (
    <DashboardLayout>
      {(loading || orderLoading) && <LoadingBackdrop />}
      <Box sx={{ p: Padding }}>
        <Box>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              mb: 3,
              paddingBottom: "20px",
              marginBottom: "20px",
              borderBottom: "1px solid #bdbdbd",
            }}
          >
            <IconButton onClick={handleCancel} sx={{ mr: 2 }}>
              <ArrowBackIcon />
            </IconButton>
            <Typography sx={{ fontSize: "20px !important", fontWeight: "500", lineHeight: "1" }}>
              Tạo đơn hàng
            </Typography>
          </Box>
          <Grid container spacing={2}>
            <LeftColumn>
              <Card sx={{ p: 2 }}>
                <OrderItemsBox
                  cartItems={cartData.listItems}
                  updateQuantity={updateQuantity}
                  removeItem={removeItem}
                  addItemToCart={addItemToCart}
                  taxRateShop={taxRateShop}
                />
              </Card>

              <Card sx={{ p: 2, marginTop: 2 }}>
                <OrderTotalPaymentSummary
                  cartData={cartData}
                  isEdit={isEdit}
                  handleClickPayment={handleClickPayment}
                  isDisablePayment={isDisablePayment}
                />
              </Card>
            </LeftColumn>

            <RightColumn>
              <Card sx={{ p: 2 }}>
                <Box display="flex" gap={0.5}>
                  <CustomerOrderBox
                    handleClickRemoveUser={handleClickRemoveUser}
                    handleSelectCustomer={handleSelectCustomer}
                    selectedCustomer={selectedCustomer}
                  />
                </Box>
                {/* <Divider /> */}
              </Card>

              <Card sx={{ p: 2, marginTop: 2 }}>
                <BoxSelectBranch
                  branchId={cartData?.branchId}
                  handleSelectBranch={handleSelectBranch}
                />
              </Card>

              <Card sx={{ p: 2, marginTop: 2 }}>
                <DeliveryMethodOrder
                  selectedMethod={cartData.statusDelivery}
                  handleDeliveryMethod={handleDeliveryMethod}
                />
              </Card>

              <Card sx={{ p: 2, marginTop: 2 }}>
                <PaymentMethodOrder
                  selectedMethod={cartData.typePay}
                  handlePaymentMethod={handlePaymentMethod}
                />
              </Card>

              <Card sx={{ p: 2, marginTop: 2 }}>
                <ShippingAddressOrder
                  selectedAddress={selectedShippingAddress}
                  handleSelectAddress={handleSelectedShippingAddress}
                  selectedCustomer={selectedCustomer}
                  selectCustomerFromList={selectCustomerFromList}
                />
              </Card>
              <Card
                sx={{
                  borderRadius: 1,
                  boxShadow: "0 4px 12px rgba(0,0,0,0.05)",
                  transition: "box-shadow 0.3s ease-in-out",
                  marginTop: 2,
                }}
              >
                <Box sx={{ p: 2, display: "flex", justifyContent: "space-between" }}>
                  <Box sx={{ display: "flex", alignItems: "flex-start" }}>
                    <Receipt sx={{ color: "#2e7d32", mr: 1, fontSize: 22 }} />
                    <Box>
                      <Box
                        sx={{
                          width: "100%",
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "start",
                        }}
                      >
                        <Typography
                          variant="h6"
                          sx={{
                            fontWeight: 700,
                            mb: 2,
                            pb: 1,
                            // borderBottom: "1px solid #f0f0f0",
                          }}
                        >
                          Xuất hoá đơn GTGT
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                  <Box
                    onClick={() => {
                      setIsOpenModalEditInvoice(true);
                    }}
                  >
                    <IconButton
                      size="small"
                      sx={{
                        mr: 0.5,
                        "&:hover": {
                          color: "primary.main",
                          bgcolor: "primary.lighter",
                        },
                      }}
                    >
                      <Edit fontSize="small" />
                    </IconButton>
                  </Box>
                </Box>
                {isEdit === true ? (
                  <>
                    {cart?.taxInvoice && (
                      <Grid
                        container
                        spacing={1}
                        sx={{
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "flex-start",
                          padding: 2,
                          paddingTop: 0,
                        }}
                      >
                        <Grid>
                          <Typography sx={{ color: "#757575", fontSize: "0.875rem" }}>
                            {cart?.taxInvoice?.taxPayerType !== "Business"
                              ? "Họ tên:"
                              : "Tên công ty:"}
                          </Typography>
                          <Typography sx={{ fontWeight: 500, mt: 0, fontSize: 14 }}>
                            {cart?.taxInvoice?.name}
                          </Typography>
                        </Grid>

                        <Grid>
                          <Typography sx={{ color: "#757575", fontSize: "0.875rem" }}>
                            Mã số thuế:
                          </Typography>
                          <Typography sx={{ fontWeight: 500, mt: 0, fontSize: 14 }}>
                            {cart?.taxInvoice?.taxCode}
                          </Typography>
                        </Grid>

                        <Grid>
                          <Typography sx={{ color: "#757575", fontSize: "0.875rem" }}>
                            Email:
                          </Typography>
                          <Typography sx={{ fontWeight: 500, mt: 0, fontSize: 14 }}>
                            {cart?.taxInvoice?.email}
                          </Typography>
                        </Grid>
                        <Grid>
                          <Typography sx={{ color: "#757575", fontSize: "0.875rem" }}>
                            Địa chỉ:
                          </Typography>
                          <Typography sx={{ fontWeight: 500, mt: 0, fontSize: 14 }}>
                            {cart?.taxInvoice?.address}
                          </Typography>
                        </Grid>
                      </Grid>
                    )}
                  </>
                ) : (
                  <>
                    {(businessForm.taxCode && businessForm.companyName) ||
                    (individualForm.taxCode && individualForm.name) ? (
                      <Grid
                        container
                        spacing={1}
                        sx={{
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "flex-start",
                          padding: 2,
                          paddingTop: 0,
                        }}
                      >
                        <Grid>
                          <Typography sx={{ color: "#757575", fontSize: "0.875rem" }}>
                            {tabValue === 1 ? "Họ tên:" : "Tên công ty:"}
                          </Typography>
                          <Typography sx={{ fontWeight: 500, mt: 0, fontSize: 14 }}>
                            {tabValue === 0 ? businessForm.companyName : individualForm.name}
                          </Typography>
                        </Grid>

                        <Grid>
                          <Typography sx={{ color: "#757575", fontSize: "0.875rem" }}>
                            Mã số thuế:
                          </Typography>
                          <Typography sx={{ fontWeight: 500, mt: 0, fontSize: 14 }}>
                            {tabValue === 0 ? businessForm.taxCode : individualForm.taxCode}
                          </Typography>
                        </Grid>

                        <Grid>
                          <Typography sx={{ color: "#757575", fontSize: "0.875rem" }}>
                            Email:
                          </Typography>
                          <Typography sx={{ fontWeight: 500, mt: 0, fontSize: 14 }}>
                            {tabValue === 0 ? businessForm.email : individualForm.email}
                          </Typography>
                        </Grid>
                        <Grid>
                          <Typography sx={{ color: "#757575", fontSize: "0.875rem" }}>
                            Địa chỉ:
                          </Typography>
                          <Typography sx={{ fontWeight: 500, mt: 0, fontSize: 14 }}>
                            {tabValue === 0 ? businessForm.address : individualForm.address}
                          </Typography>
                        </Grid>
                      </Grid>
                    ) : (
                      <Typography
                        sx={{
                          textAlign: "center",
                          marginTop: 0,
                          marginBottom: 2,
                          color: "#6C737F",
                          fontSize: "15px",
                        }}
                      >
                        Không có dữ liệu
                      </Typography>
                    )}
                  </>
                )}
                {/* {(businessForm.taxCode && businessForm.companyName) ||
                (individualForm.taxCode && individualForm.name) ? (
                  <Grid
                    container
                    spacing={1}
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "flex-start",
                      padding: 2,
                      paddingTop: 0,
                    }}
                  >
                    <Grid>
                      <Typography sx={{ color: "#757575", fontSize: "0.875rem" }}>
                        {tabValue === 1 ? "Họ tên:" : "Tên công ty:"}
                      </Typography>
                      <Typography sx={{ fontWeight: 500, mt: 0, fontSize: 14 }}>
                        {tabValue === 0 ? businessForm.companyName : individualForm.name}
                      </Typography>
                    </Grid>

                    <Grid>
                      <Typography sx={{ color: "#757575", fontSize: "0.875rem" }}>
                        Mã số thuế:
                      </Typography>
                      <Typography sx={{ fontWeight: 500, mt: 0, fontSize: 14 }}>
                        {tabValue === 0 ? businessForm.taxCode : individualForm.taxCode}
                      </Typography>
                    </Grid>

                    <Grid>
                      <Typography sx={{ color: "#757575", fontSize: "0.875rem" }}>
                        Email:
                      </Typography>
                      <Typography sx={{ fontWeight: 500, mt: 0, fontSize: 14 }}>
                        {tabValue === 0 ? businessForm.email : individualForm.email}
                      </Typography>
                    </Grid>
                    <Grid>
                      <Typography sx={{ color: "#757575", fontSize: "0.875rem" }}>
                        Địa chỉ:
                      </Typography>
                      <Typography sx={{ fontWeight: 500, mt: 0, fontSize: 14 }}>
                        {tabValue === 0 ? businessForm.address : individualForm.address}
                      </Typography>
                    </Grid>
                  </Grid>
                ) : (
                  <Typography
                    sx={{
                      textAlign: "center",
                      marginTop: 0,
                      marginBottom: 2,
                      color: "#6C737F",
                      fontSize: "15px",
                    }}
                  >
                    Không có dữ liệu
                  </Typography>
                )} */}

                {/* {!order?.taxInvoice && (
                  <>
                    <Typography
                      sx={{
                        textAlign: "center",
                        marginTop: 0,
                        marginBottom: 2,
                        color: "#6C737F",
                        fontSize: "15px",
                      }}
                    >
                      Không có dữ liệu
                    </Typography>
                  </>
                )} */}
              </Card>
            </RightColumn>
          </Grid>

          <Divider sx={{ marginTop: 2, marginBottom: 2 }} />
          <Box sx={{ display: "flex", justifyContent: "end" }}>
            <Button
              sx={{
                marginRight: 1,
                textTransform: "none",
                color: "#2654FE",
                borderColor: "#2654FE",
              }}
              variant="outlined"
            >
              Hủy
            </Button>
            <Button sx={{ background: "#2654FE" }} variant="contained" onClick={handleSubmitForm}>
              Lưu
            </Button>
          </Box>
        </Box>
      </Box>

      <>
        <ModalInvoice
          open={isOpenModalEditInvoice}
          setOpen={setIsOpenModalEditInvoice}
          businessForm={businessForm}
          setBusinessForm={setBusinessForm}
          individualForm={individualForm}
          setIndividualForm={setIndividualForm}
          tabValue={tabValue}
          setTabValue={setTabValue}
        />
      </>
    </DashboardLayout>
  );
}

const BoxSelectBranch = ({ branchId, handleSelectBranch }) => {
  const [currentBranch, setCurrentBranch] = useState(branchId);
  const [listBranches, setListBranches] = useState([]);
  const { getBranches } = useBranch();
  const storeId = useStoreId();
  const fetchBranches = async (shopId) => {
    const response = await getBranches(0, 9999, shopId);
    if (response?.data) {
      const listFetchBranches = response.data.data;
      setListBranches(listFetchBranches);
    }
  };

  const onChooseBranch = (branchId) => {
    setCurrentBranch(branchId);
    handleSelectBranch(branchId);
  };
  useEffect(() => {
    if (storeId) {
      fetchBranches(storeId);
    }
  }, [storeId]);

  useEffect(() => {
    if (branchId) {
      setCurrentBranch(branchId);
    }
  }, [branchId]);

  return (
    <Box>
      <Typography variant="h6" sx={{ marginBottom: 1, fontWeight: 600 }}>
        <LocationOn sx={{ fontSize: 22, marginRight: 0.5, color: "#000081" }} /> Điểm bán
      </Typography>
      <Box sx={{ minWidth: 120 }}>
        <FormControl fullWidth>
          <Select
            labelId="select-branch"
            id="branch-select"
            value={currentBranch || ""}
            onChange={(e) => onChooseBranch(e.target.value)}
          >
            {listBranches.map((branch) => (
              <MenuItem key={branch.branchId} value={branch.branchId}>
                {branch.branchName}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>
    </Box>
  );
};
