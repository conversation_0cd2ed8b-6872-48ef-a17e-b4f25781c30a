import React from "react";
import SettingLayout from "@/src/components/settings/settings-page/SettingLayout";
import TaxSetting from "@/src/components/settings/settings-page/tax-setting";
import Grid from "@mui/system/Grid/Grid";
import { Typography, IconButton, Box } from "@mui/material";

const SettingTax = () => {
  return (
    // <SettingLayout>
    <>
      <Grid>
        <Typography variant="h5" sx={{ ml: 1 }}>
          Thuế
        </Typography>
      </Grid>
      <Grid sx={{ mt: 1 }}>
        <TaxSetting />
      </Grid>
    </>
    // </SettingLayout>
  );
};

export default SettingTax;
