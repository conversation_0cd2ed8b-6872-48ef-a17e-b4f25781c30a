import PropTypes from "prop-types";
import { Box, Collapse, Divider } from "@mui/material";
import Stack from "@mui/material/Stack";
import React, { useState } from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { SideNavItem } from "./side-nav-item";

const renderItems = ({ depth = 0, items, pathname, isCollapsed = false }) =>
  items.reduce(
    (acc, item) =>
      reduceChildRoutes({
        acc,
        depth,
        item,
        pathname,
        isCollapsed,
      }),
    []
  );

const reduceChildRoutes = ({ acc, depth, item, pathname, isCollapsed = false }) => {
  const checkPath = !!(item.path && pathname);
  const partialMatch = checkPath ? pathname.includes(item.path) : false;
  const exactMatch = checkPath ? pathname.replace(/\/$/, "") === item.path : false;

  if (item.items) {
    acc.push(
      <SideNavItem
        active={partialMatch}
        depth={depth}
        disabled={item.disabled}
        icon={item.icon}
        key={item.title}
        label={item.label}
        open={partialMatch}
        title={item.title}
        isCollapsed={isCollapsed}
      >
        <Stack
          component="ul"
          spacing={0.5}
          sx={{
            listStyle: "none",
            m: 0,
            p: 0,
          }}
        >
          {renderItems({
            depth: depth + 1,
            items: item.items,
            pathname,
            isCollapsed,
          })}
        </Stack>
      </SideNavItem>
    );
  } else {
    acc.push(
      <SideNavItem
        active={exactMatch}
        depth={depth}
        disabled={item.disabled}
        external={item.external}
        icon={item.icon}
        key={item.title}
        label={item.label}
        path={item.path}
        title={item.title}
        isCollapsed={isCollapsed}
      />
    );
  }

  return acc;
};

export const SideNavSection = (props) => {
  const { items = [], pathname, subheader, icon, isCollapsed = false, ...other } = props;
  const [isOpen, setIsOpen] = useState(true);
  let startIcon;

  return (
    <Stack
      component="ul"
      className="list-item-sidebar"
      spacing={0.5}
      sx={{
        listStyle: "none",
        m: 0,
        p: 0,
        paddingLeft: isCollapsed ? "4px" : "20px",
        paddingRight: isCollapsed ? "4px" : "20px",
        "& .item-sidebar": {
          marginBottom: isCollapsed ? "4px" : "4px",
        },
      }}
      {...other}
    >
      <>
        {subheader && !isCollapsed && (
          <Box
            onClick={() => setIsOpen((prev) => !prev)}
            component="li"
            sx={{
              color: "#000",
              fontWeight: "700",
              fontSize: 14,
              lineHeight: 1.66,
              mb: 1,
              ml: 1,
              marginBottom: "5px !important",
              textTransform: "none",
              cursor: "pointer",
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              gap: "15px",
            }}
          >
            {subheader}
            <ExpandMoreIcon
              sx={{
                transition: "transform 0.3s",
                transform: isOpen ? "rotate(180deg)" : "rotate(0deg)",
                fontSize: 18,
              }}
            />
          </Box>
        )}
        <Collapse in={isCollapsed || isOpen} timeout="auto" unmountOnExit>
          {renderItems({ items, pathname, isCollapsed })}
        </Collapse>

        {/* Divider ở cuối section cho collapsed mode */}
        {isCollapsed && subheader && (
          <Divider
            sx={{
              mx: 2,
              my: 1,
              borderColor: "#E0E0E0",
            }}
          />
        )}
      </>
    </Stack>
  );
};

SideNavSection.propTypes = {
  items: PropTypes.array,
  pathname: PropTypes.string,
  subheader: PropTypes.node,
  icon: PropTypes.node,
};
