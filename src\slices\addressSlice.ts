import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { provinces } from '@/src/utils/address/provinces';
interface IAddress {
  id: number;
  address: string;
  note: string;
  province: string;
  district: string;
  ward: string;
  createdAt: string;
  updatedAt: string;
  userName: number;
  phone: number;
  addressObj: {
    wardText: string;
    districtText: String;
    provinceText: string;
  };
}

interface AddressState {
  list: IAddress[] | null;
  isLoading: boolean;
  provinces: ProvinceType[];
  districts: [];
  wards: [];
}

interface ProvinceType {
  provinceID: any;
  provinceName: string;
}

const initialState: AddressState = {
  list: null,
  isLoading: true,
  provinces: [],
  districts: [],
  wards: []
};

const addressSlice = createSlice({
  name: 'address',
  initialState,
  reducers: {
    getProvinces: (state, action: PayloadAction) => {
      state.provinces = provinces;
      // state.currentAddress = action.payload;
    }
  }
});

export const { getProvinces } = addressSlice.actions;
export default addressSlice.reducer;
