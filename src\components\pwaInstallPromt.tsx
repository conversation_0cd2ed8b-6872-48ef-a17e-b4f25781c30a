import { useEffect, useState } from "react";
import { Alert, Box, Button, IconButton, Snackbar, Typography, useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import CloseIcon from "@mui/icons-material/Close";

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: "accepted" | "dismissed";
    platform: string;
  }>;
  prompt(): Promise<void>;
}

const PWAInstallPrompt = () => {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [isStandalone, setIsStandalone] = useState(false);
  const [isDismissed, setIsDismissed] = useState(true);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  useEffect(() => {
    const isDeviceIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isAppStandalone = window.matchMedia("(display-mode: standalone)").matches;

    setIsIOS(isDeviceIOS);
    setIsStandalone(isAppStandalone);

    const dismissedTime = localStorage.getItem("pwa-install-dismissed");
    if (
      !dismissedTime ||
      (dismissedTime && Date.now() - parseInt(dismissedTime) > 24 * 60 * 60 * 1000)
    ) {
      setIsDismissed(false);
    }

    const handler = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      if (!isAppStandalone) {
        setShowInstallPrompt(true);
      }
    };
    window.addEventListener("beforeinstallprompt", handler);

    if (isDeviceIOS && !isAppStandalone) {
      setShowInstallPrompt(true);
    }

    return () => {
      window.removeEventListener("beforeinstallprompt", handler);
    };
  }, []);

  const handleInstallClick = async () => {
    if (deferredPrompt) {
      setShowInstallPrompt(false);
      await deferredPrompt.prompt();

      const choiceResult = await deferredPrompt.userChoice;
      if (choiceResult.outcome === "accepted") {
        console.log("Người dùng đã chấp nhận cài đặt PWA");
      }
      setDeferredPrompt(null);
    }
  };

  const handleClose = () => {
    setShowInstallPrompt(false);
    localStorage.setItem("pwa-install-dismissed", Date.now().toString());
  };

  if (isStandalone || isDismissed || !showInstallPrompt) return null;

  return (
    <Snackbar
      open={showInstallPrompt}
      anchorOrigin={{ vertical: "bottom", horizontal: isMobile ? "center" : "right" }}
      sx={{
        bottom: { xs: 16, sm: 24 },
        left: { xs: 16, sm: "auto" },
        right: { xs: 16, sm: 24 },
      }}
    >
      <Alert
        severity="info"
        icon={false}
        action={
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: { xs: 1, sm: 1 },
              pl: { xs: 1, sm: 2 },
            }}
          >
            {!isIOS && deferredPrompt && (
              <Button
                variant="contained"
                size="small"
                onClick={handleInstallClick}
                sx={{
                  textTransform: "none",
                  bgcolor: "#2654FE",
                  whiteSpace: "nowrap",
                  mt: { xs: 1, sm: 0 },
                  px: { xs: 1.5, sm: 2 },
                  py: { xs: 1, sm: 1 },
                }}
              >
                Cài đặt
              </Button>
            )}
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={handleClose}
              sx={{ color: "#666" }}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          </Box>
        }
        sx={{
          width: "100%",
          maxWidth: { sm: 420 },
          bgcolor: "white",
          p: 2,
          pr: 0,
          borderRadius: 2,
          boxShadow: "0 4px 20px rgba(0,0,0,0.15)",
          "& .MuiAlert-message": {
            flex: 1,
            display: "flex",
            alignItems: "center",
          },
          "& .MuiAlert-action": {
            marginRight: 1,
            padding: 0,
          },
        }}
      >
        <Box>
          <Typography
            variant="subtitle2"
            sx={{
              fontWeight: 600,
              color: "#333",
            }}
          >
            Cài đặt ứng dụng
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: "#666",
              mt: 0.5,
            }}
          >
            {isIOS
              ? 'Nhấn nút Chia sẻ và chọn "Thêm vào Màn hình chính"'
              : "Cài đặt để truy cập nhanh hơn"}
          </Typography>
        </Box>
      </Alert>
    </Snackbar>
  );
};

export default PWAInstallPrompt;
