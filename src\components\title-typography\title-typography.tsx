import React from 'react';
import { StyledTypography } from './styles';
import { TypographyProps } from '@mui/material';

interface TitleTypographyProps extends TypographyProps {
  children: React.ReactNode;
}

const TitleTypography: React.FC<TitleTypographyProps> = ({ children, sx, ...props }) => {
  return (
    <StyledTypography variant="h5" sx={sx} {...props}>
      {children}
    </StyledTypography>
  );
};

export default TitleTypography;
