import { useState } from "react";
import { useStoreId } from "@/src/hooks/use-store-id";
import { GetProductCategoryRequest, CategoryType } from "@/src/api/types/product-category.types";
import {
  CategoryDto,
  ExportProductCategoryParams,
  productCategoryService,
} from "@/src/api/services/dashboard/product/category.service";
import { ErrorHandlerService } from "@/src/api/services/error-handler.service";
import { StorageService } from "nextjs-api-lib";

export const useProductCategory = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const storeId = useStoreId();
  const partnerId = StorageService.get("partnerId") as string | null;

  const getProductCategoryTree = async (data: GetProductCategoryRequest) => {
    try {
      setLoading(true);
      setError(null);

      const response = await productCategoryService.getProductCategoryTree(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const getProductCategory = async (params: GetProductCategoryRequest) => {
    try {
      setLoading(true);
      setError(null);

      const response = await productCategoryService.getProductCategory(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const createProductCategory = async (data: CategoryDto) => {
    try {
      setLoading(true);
      setError(null);
      const response = await productCategoryService.createProductCategory(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateProductCategory = async (data: CategoryDto) => {
    try {
      setLoading(true);
      setError(null);
      const response = await productCategoryService.updateProductCategory(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateProductCategoryImage = async (productCategoryId: string, imageFile: File) => {
    try {
      setLoading(true);
      setError(null);
      const response = await productCategoryService.updateImageProductCategory(
        productCategoryId,
        imageFile
      );
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const deleteProductCategory = async (productCategoryId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await productCategoryService.deleteProductCategory(productCategoryId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const getProductCategoryDetail = async (productCategoryId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await productCategoryService.getProductCategoryDetail(productCategoryId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const exportTemplateProductCategory = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await productCategoryService.exportTemplateProductCategory(
        data.shopId,
        data.categoryType
      );
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };
  const importProductCategory = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await productCategoryService.importProductCategory(
        data.shopId,
        data.categoryType,
        data.file
      );
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };
  const exportListProductCategory = async (data: ExportProductCategoryParams) => {
    try {
      setLoading(true);
      setError(null);
      const response = await productCategoryService.exportListProductCategory(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  return {
    getProductCategory,
    getProductCategoryTree,
    createProductCategory,
    getProductCategoryDetail,
    updateProductCategory,
    updateProductCategoryImage,
    deleteProductCategory,
    exportTemplateProductCategory,
    importProductCategory,
    exportListProductCategory,
    loading,
    error,
  };
};
