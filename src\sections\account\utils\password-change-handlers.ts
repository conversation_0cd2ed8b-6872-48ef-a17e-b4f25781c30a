import { FormikHelpers } from "formik";
import { AppDispatch } from "@/src/redux/store";
import { changePassword } from "@/src/redux/slices/profileSlice";
import { PasswordFormValues } from "../validations/password-validations";

export const handleChangePasswordSuccess = (
  helpers: FormikHelpers<PasswordFormValues>,
  onSuccess: () => void,
  showSuccessMessage: (message: string) => void,
  message: string
) => {
  showSuccessMessage(message);
  helpers.resetForm();
  onSuccess();
};

export const performPasswordChange = async (
  values: PasswordFormValues,
  helpers: FormikHelpers<PasswordFormValues>,
  dispatch: AppDispatch,
  onSuccess: () => void,
  showSuccessMessage: (message: string) => void,
  showErrorMessage: (message: string) => void,
  translate: (key: string) => string,
  successMessage: string
) => {
  try {
    await dispatch(
      changePassword({
        oldPassword: values.oldPassword,
        newPassword: values.newPassword,
      })
    ).unwrap();

    handleChangePasswordSuccess(helpers, onSuccess, showSuccessMessage, successMessage);
  } catch (err: any) {
    helpers.setErrors({ submit: err });
  }
};
