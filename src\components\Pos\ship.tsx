import { Paper, Button, Box, Typography } from "@mui/material"
import { LocalShipping } from "@mui/icons-material"
import { Grid } from "@mui/system"

interface Order {
    id: string
    location: string
    customer: string
    phone: string
    status: string
}

const orders: Order[] = [
    {
        id: "ABC123",
        location: "HeyU",
        customer: "Nguyễn Trần Trung Hoa",
        phone: "0349587999",
        status: "Đang làm",
    },
    {
        id: "ABC123",
        location: "GHN",
        customer: "Nguyễn Trần Trung Hoa",
        phone: "0349587999",
        status: "Đang làm",
    },
    {
        id: "ABC123",
        location: "GHTK",
        customer: "Nguyễn Trần Trung Hoa",
        phone: "0349587999",
        status: "Đang làm",
    },
    {
        id: "ABC123",
        location: "SPF",
        customer: "Nguyễn Trần Trung Hoa",
        phone: "0349587999",
        status: "Đang làm",
    },
]

const renderOrderIcon = (order: Order) => (
    <Box
        sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            width: 80,
            height: "100%",
            borderRadius: 1,
            bgcolor: "#1DD1A1",
            position: "absolute",
            left: -30,
            top: 0,
            bottom: 0,
        }}
    >
        <LocalShipping sx={{ color: "#fff", fontSize: 24 }} />
    </Box>
)

export default function Ship() {
    return (
        <Box>
            {orders.map((order, index) => (
                <Paper
                    key={index}
                    sx={{ p: 2, mb: 2, position: "relative", border: "1px solid #E8E8E8", borderRadius: 1, boxShadow: "none" }}
                >
                    {renderOrderIcon(order)}
                    <Grid container alignItems="center" spacing={2} sx={{ pl: 6 }}>
                        <Grid size={{ xs: 2 }}>
                            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                                <Typography fontWeight="bold">{order.id}</Typography>
                                <Typography variant="body1">{order.location}</Typography>
                            </Box>
                        </Grid>

                        <Grid size={{ xs: 3 }} sx={{ display: "flex", flexDirection: "column", alignItems: 'center' }}>
                            <Typography fontWeight="bold">{order.customer}</Typography>
                            <Typography textAlign="center">{order.phone}</Typography>
                        </Grid>

                        <Grid size={{ xs: 3 }}>
                            <Typography variant="body1" color="text.secondary">
                                {order.status}
                            </Typography>
                        </Grid>

                        <Grid size={{ xs: 2 }}>
                            <Button
                                variant="contained"
                                sx={{
                                    width: 160,
                                    height: 50,
                                    bgcolor: "#4C8BF5",
                                    color: "#fff",
                                    fontWeight: "bold",
                                    whiteSpace: "nowrap",
                                }}
                            >
                                Gọi Ship
                            </Button>
                        </Grid>

                        <Grid size={{ xs: 2 }}>
                            <Button
                                variant="contained"
                                sx={{
                                    width: 160,
                                    height: 50,
                                    bgcolor: "#D1D1D1",
                                    color: "#2E2E2E",
                                    fontWeight: "bold",
                                    boxShadow: 'none',
                                }}
                            >
                                Hoàn thành
                            </Button>
                        </Grid>
                    </Grid>
                </Paper>
            ))}
        </Box>
    )
}