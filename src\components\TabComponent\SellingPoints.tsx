import React, { useEffect, useState } from 'react';
import { Box, Select, MenuItem, Button, Stack, Typography, Tabs, Tab } from '@mui/material';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider, DateRangePicker, DatePicker } from '@mui/x-date-pickers-pro';
import { ExpandMore } from '@mui/icons-material';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import MorningSchedule from './TimeComponent/AfternoonSchedule';
import AfternoonSchedule from './TimeComponent/MorningSchedule';
import { branchService } from '@/src/api/services/branch/branch.service';
import { useStoreId } from '@/src/hooks/use-store-id';
import { formatPrice } from '@/src/api/types/membership.types';

const SellingPoints = ({ selectedDay,
  setSelectedDay,
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  dataOrder, selectedBranch,
  setSelectedBranch, listBranch,
  setListBranch }) => {
  const [filter, setFilter] = useState('Hôm nay');
  const [open, setOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [chartDateRange, setChartDateRange] = useState([null, null]);
  const storeId = useStoreId();
  const handleChartStartDateChange = (newValue) => {
    const endDate = chartDateRange[1];
    const startD = newValue.startOf("day").format("YYYY-MM-DD HH:mm:ss")
    let updatedRange;

    if (!endDate || newValue.isAfter(endDate)) {
      updatedRange = [newValue, newValue];
      setEndDate(newValue.endOf("day").format("YYYY-MM-DD HH:mm:ss"))
      setStartDate(startD)
    } else {
      const endD = endDate.endOf("day").format("YYYY-MM-DD HH:mm:ss")
      updatedRange = [newValue, endDate];
      setEndDate(endD)
      setStartDate(startD)
    }
    setSelectedDay("")
    setChartDateRange(updatedRange);
  };

  const handleChartEndDateChange = (newValue) => {
    const startDate = chartDateRange[0];
    const endD = newValue.endOf("day").format("YYYY-MM-DD HH:mm:ss")
    let updatedRange;

    if (!startDate || newValue.isBefore(startDate, "day")) {
      updatedRange = [newValue, newValue];
      setEndDate(endD)
      setStartDate(newValue.startOf("day").format("YYYY-MM-DD HH:mm:ss"))
    } else {
      const startD = startDate.startOf("day").format("YYYY-MM-DD HH:mm:ss")
      updatedRange = [startDate, newValue];
      setEndDate(endD)
      setStartDate(startD)
    }
    setSelectedDay("")
    setChartDateRange(updatedRange);
  };

  return (
    <Box
      sx={{
        p: 3,
        borderRadius: '20px',
        marginTop: '-15px',
        background: '#fff',
      }}
    >
      <Stack
        flexDirection={'row'}
        justifyContent={'space-between'}
        mb={'25px'}
        sx={{
          '@media(max-width: 600px)': {
            flexDirection: 'column',
            gap: '20px',
            alignItems: 'start',
          },
        }}
      >
        <Select
          value={selectedBranch}
          onChange={(e) => setSelectedBranch(e.target.value)}
          displayEmpty
          size="small"
          sx={{ minWidth: 120, height: 50, backgroundColor: '#fff' }}
          IconComponent={(props) => <ExpandMore {...props} sx={{ color: '#000' }} />}
        >
          {listBranch.length > 0 && listBranch.map((branch) => (
            <MenuItem key={branch.branchId} value={branch.branchId}>
              {branch.branchName}
            </MenuItem>
          ))}
        </Select>
        <Box
          display="flex"
          justifyContent="flex-end"
          gap={2}
          mb={3}
          sx={{
            '@media(max-width: 600px)': {
              flexDirection: 'column',
            },
          }}
        >
          <Select
            value={selectedDay}
            onChange={(e) => {
              setStartDate("")
              setEndDate("")
              setChartDateRange([])
              setSelectedDay(e.target.value)
            }}
            displayEmpty
            defaultValue={selectedDay}
            size="small"
            sx={{ minWidth: 120, height: 36, backgroundColor: '#fff' }}
            IconComponent={(props) => <ExpandMore {...props} sx={{ color: '#000' }} />}
          >
            <MenuItem value="TODAY">Hôm nay</MenuItem>
            <MenuItem value="YESTERDAY">Hôm qua</MenuItem>
            <MenuItem value="LAST_7_DAYS">7 ngày qua</MenuItem>
            <MenuItem value="LAST_30_DAYS">1 tháng qua</MenuItem>
          </Select>
          <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="vi">
            <Box sx={{ display: "flex" }}>
              <DatePicker
                value={chartDateRange[0]}
                format="DD/MM/YYYY"
                onChange={handleChartStartDateChange}
                slotProps={{
                  textField: {
                    sx: { height: 36, paddingLeft: 0, width: 180 },
                    InputProps: { sx: { height: 36 } },
                  },
                }}
              />
              <Typography sx={{ paddingLeft: 1, paddingRight: 1, marginTop: 0.5 }}>-</Typography>
              <DatePicker
                value={chartDateRange[1]}
                format="DD/MM/YYYY"
                onChange={handleChartEndDateChange}
                shouldDisableDate={(date) => date.isBefore(chartDateRange[1], "day")}
                slotProps={{
                  textField: {
                    sx: { height: 36, paddingLeft: 0, width: 180 },
                    InputProps: { sx: { height: 36 } },
                  },
                }}
              />
            </Box>
          </LocalizationProvider>
        </Box>
        {/* <Box
          display="flex"
          justifyContent="flex-end"
          gap={2}
          sx={{
            '@media(max-width: 480px)': {
              flexDirection: 'column',
            },
          }}
        >
          <Select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            displayEmpty
            size="small"
            sx={{ minWidth: 120, height: 50, backgroundColor: '#fff' }}
            IconComponent={(props) => <ExpandMore {...props} sx={{ color: '#000' }} />}
          >
            <MenuItem value="Hôm nay">Hôm nay</MenuItem>
            <MenuItem value="Hôm qua">Hôm qua</MenuItem>
            <MenuItem value="7 ngày qua">7 ngày qua</MenuItem>
            <MenuItem value="1 tháng qua">1 tháng qua</MenuItem>
          </Select>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DateRangePicker
              open={open}
              onOpen={() => setOpen(true)}
              onClose={() => setOpen(false)}
              format="DD/MM/YYYY"
              localeText={{ start: 'Từ ngày', end: 'Đến ngày' }}
              slotProps={{
                textField: { sx: { display: 'none' } },
                popper: { placement: 'bottom-start' },
              }}
              slots={{
                field: () => (
                  <Button
                    onClick={() => setOpen(true)}
                    sx={{
                      border: '1px solid #C4C4C4',
                      borderRadius: '12px',
                      padding: '10px 16px',
                      backgroundColor: '#F8F9FA',
                      fontSize: '16px',
                      fontWeight: 400,
                      color: '#000',
                      textTransform: 'none',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      minWidth: '260px',
                      justifyContent: 'flex-start',
                      '&:hover': { backgroundColor: '#ECECEC' },
                    }}
                  >
                    <Stack
                      direction="row"
                      alignItems="center"
                      gap="4px"
                      justifyContent="space-between"
                      width="100%"
                    >
                      <Stack direction="row" alignItems="center" gap="4px">
                        {'Bắt đầu'} - {'Kết thúc'}
                      </Stack>
                      <CalendarMonthIcon sx={{ fontSize: 22 }} />
                    </Stack>
                  </Button>
                ),
              }}
            />
          </LocalizationProvider>
        </Box> */}
      </Stack>
      <Stack
        direction="row"
        gap={2}
        mt={2}
        sx={{
          gap: '50px',
          '@media(max-width: 980px)': {
            flexWrap: 'wrap',
            // gap: '2%',
          },
        }}
      >
        {dataOrder.length > 0 && dataOrder.map((slot, index) => (
          <Box
            key={index}
            sx={{
              background: '#F4F5F9',
              borderRadius: '12px',
              minWidth: '150px',
              textAlign: 'center',
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              textAlignLast: 'start',
              boxShadow: '6px 6px 54px 0 #0000000D !important;',
              padding: '20px',
              width: '25%',
              '@media(max-width: 980px)': {
                width: '45%',
              },
              '@media(max-width: 600px)': {
                width: '100%',
              },
            }}
          >
            <Typography color="#202224" fontSize={'16px'} fontWeight={'400'} mb={'25px'}>
              {slot.title}
            </Typography>
            <Typography
              fontSize={14}
              fontWeight={600}
              sx={{
                fontSize: '28px',
                fontWeight: '700',
                color: '#202224',
                marginBottom: '10px',
              }}
            >
              {index === 0 ? `${formatPrice(slot.value)} VNĐ` : slot.value}
            </Typography>
          </Box>
        ))}
      </Stack>
    </Box>
  );
};

export default SellingPoints;
