import React, { useState, useEffect, useRef } from "react";
import {
  EditorState,
  ContentState,
  CompositeDecorator,
  KeyBindingUtil,
  getDefaultKeyBinding,
  Modifier,
  SelectionState,
  ContentBlock,
} from "draft-js";
import { Editor, blockStyleFn, toggleTextAlign } from "contenido";
import "draft-js/dist/Draft.css";
import "contenido/dist/styles.css";
import { Box } from "@mui/material";

const findTemplateVariables = (
  contentBlock: ContentBlock,
  callback: (start: number, end: number) => void
) => {
  const text = contentBlock.getText();
  const regex = /\{\{([^}]+)\}\}/g;
  let matchArr;
  while ((matchArr = regex.exec(text)) !== null) {
    const start = matchArr.index;
    const end = start + matchArr[0].length;
    callback(start, end);
  }
};

const TemplateVariableSpan = (props: any) => {
  return (
    <span
      style={{
        background: "rgb(48, 114, 255)",
        borderRadius: "4px",
        color: "rgb(255, 255, 255)",
        padding: "0px 4px",
        display: "inline-block",
        margin: "0px",
      }}
    >
      {props.children}
    </span>
  );
};

// Draft.js TextField replacement
export const EnhancedTextField = ({
  fullWidth,
  variant,
  size,
  value,
  onChange,
  inputProps,
  maxLength = 100,
  multiline = true,
  rows = 1,
  showCharCount = true,
  ...otherProps
}: any) => {
  const decorator = new CompositeDecorator([
    {
      strategy: findTemplateVariables,
      component: TemplateVariableSpan,
    },
  ]);

  // Initialize editor state
  const [editorState, setEditorState] = useState(() => {
    const contentState = ContentState.createFromText(value || "");
    return EditorState.createWithContent(contentState, decorator);
  });

  const containerRef = useRef<HTMLDivElement>(null);

  // Update editor content when value prop changes from outside
  useEffect(() => {
    const currentContent = editorState.getCurrentContent().getPlainText();
    if (value !== currentContent) {
      const newContentState = ContentState.createFromText(value || "");
      const newEditorState = EditorState.createWithContent(newContentState, decorator);
      setEditorState(newEditorState);
    }
  }, [value]);

  useEffect(() => {
    if (inputProps?.style?.textAlign) {
      toggleTextAlign(editorState, setEditorState, `text-align-${inputProps.style.textAlign}`);
    }
  }, [inputProps?.style?.textAlign]);

  const handleBeforeInput = (chars: string) => {
    const currentContent = editorState.getCurrentContent();
    const currentContentLength = currentContent.getPlainText("").length;

    if (currentContentLength >= maxLength) {
      return "handled";
    }

    if (currentContentLength + chars.length > maxLength) {
      const charsToInsert = chars.substring(0, maxLength - currentContentLength);

      if (charsToInsert.length > 0) {
        const selection = editorState.getSelection();
        const contentStateWithInsert = Modifier.replaceText(
          currentContent,
          selection,
          charsToInsert
        );

        const newEditorState = EditorState.push(
          editorState,
          contentStateWithInsert,
          "insert-characters"
        );

        handleEditorChange(newEditorState);
      }

      return "handled";
    }

    return "not-handled";
  };

  const handlePastedText = (text: string, html: string | undefined, editorState: EditorState) => {
    const currentContent = editorState.getCurrentContent();
    const currentContentLength = currentContent.getPlainText("").length;

    if (currentContentLength >= maxLength) {
      return "handled";
    }

    if (currentContentLength + text.length > maxLength) {
      const textToInsert = text.substring(0, maxLength - currentContentLength);

      if (textToInsert.length > 0) {
        const selection = editorState.getSelection();
        const contentStateWithInsert = Modifier.replaceText(
          currentContent,
          selection,
          textToInsert
        );

        const newEditorState = EditorState.push(
          editorState,
          contentStateWithInsert,
          "insert-characters"
        );

        handleEditorChange(newEditorState);
      }

      return "handled";
    }

    return "not-handled";
  };

  const handleEditorChange = (newState: EditorState) => {
    const content = newState.getCurrentContent().getPlainText();

    if (content.length <= maxLength) {
      setEditorState(newState);
      if (onChange) {
        const syntheticEvent = {
          target: {
            value: content,
          },
        };
        onChange(syntheticEvent);
      }
    }
  };

  const handleKeyCommand = (command: string, editorState: EditorState) => {
    if (command === "split-block" && !multiline) {
      return "handled";
    }
    return "not-handled";
  };

  const keyBindingFn = (e: React.KeyboardEvent) => {
    if (!multiline && e.key === "Enter") {
      return "split-block";
    }
    return getDefaultKeyBinding(e);
  };

  const handleContainerClick = () => {
    if (containerRef.current) {
      const editorElement = containerRef.current.querySelector(".DraftEditor-root");
      if (editorElement) {
        editorElement.dispatchEvent(new Event("focus", { bubbles: true }));
        const contentState = editorState.getCurrentContent();
        const blockMap = contentState.getBlockMap();
        const lastBlock = blockMap.last();
        const blockKey = lastBlock.getKey();
        const blockLength = lastBlock.getLength();

        const newSelection = SelectionState.createEmpty(blockKey).merge({
          anchorOffset: blockLength,
          focusOffset: blockLength,
        });

        const newEditorState = EditorState.forceSelection(editorState, newSelection);
        setEditorState(newEditorState);
      }
    }
  };

  const sizeStyles = {
    small: {
      padding: "8.5px 14px",
      fontSize: "0.875rem",
    },
    medium: {
      padding: "16.5px 14px",
      fontSize: "1rem",
    },
  }[size || "medium"];

  const widthStyle = fullWidth ? { width: "100%" } : {};

  const getBorderStyle = () => {
    switch (variant) {
      case "outlined":
        return "1px solid rgba(0, 0, 0, 0.23)";
      case "filled":
        return "1px solid rgba(0, 0, 0, 0.12)";
      default:
        return "none";
    }
  };

  const getBackground = () => {
    switch (variant) {
      case "filled":
        return "rgba(0, 0, 0, 0.06)";
      default:
        return "transparent";
    }
  };

  const currentCharacterCount = editorState.getCurrentContent().getPlainText("").length;

  return (
    <Box sx={{ width: fullWidth ? "100%" : "auto" }}>
      <Box
        onClick={handleContainerClick}
        sx={{
          ...widthStyle,
          border: getBorderStyle(),
          borderRadius: "8px",
          background: getBackground(),
          minHeight: size === "small" ? "40px" : "56px",
          maxHeight: !multiline ? (size === "small" ? "40px" : "56px") : "none",
          position: "relative",
          cursor: "text",
          borderColor: "rgba(151, 151, 151, 1)",
          overflow: !multiline ? "hidden" : "auto",
        }}
      >
        <div
          ref={containerRef}
          tabIndex={0}
          style={{
            ...sizeStyles,
            fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
            lineHeight: "1.4375em",
            height: multiline && rows > 1 ? `${rows * 1.4375}em` : "auto",
            outline: "none",
          }}
        >
          <Editor
            editorState={editorState}
            onChange={handleEditorChange}
            blockStyleFn={blockStyleFn}
            placeholder=""
            handleKeyCommand={handleKeyCommand}
            keyBindingFn={keyBindingFn}
            stripPastedStyles={!multiline}
            handleBeforeInput={handleBeforeInput}
            handlePastedText={handlePastedText}
            {...otherProps}
          />
        </div>
      </Box>
    </Box>
  );
};

export default EnhancedTextField;
