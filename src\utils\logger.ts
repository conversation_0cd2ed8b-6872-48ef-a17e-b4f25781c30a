type LogLevel = 'info' | 'warn' | 'error' | 'debug';

interface LogOptions {
  level?: LogLevel;
  context?: string;
}

/**
 * Logger utility for development environment
 * All logs will be disabled in production
 */
class Logger {
  private isDev = process.env.NODE_ENV !== 'production';

  constructor() {
    console.log('Logger initialized with NODE_ENV:', process.env.NODE_ENV);
    console.log('isDev:', this.isDev);
  }

  /**
   * Generic log method with level support
   */
  private logWithLevel(level: LogLevel, message: any, ...args: any[]) {
    if (!this.isDev) return;

    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${level.toUpperCase()}]`;

    switch (level) {
      case 'error':
        console.error(prefix, message, ...args);
        break;
      case 'warn':
        console.warn(prefix, message, ...args);
        break;
      case 'info':
        console.info(prefix, message, ...args);
        break;
      case 'debug':
        console.debug(prefix, message, ...args);
        break;
      default:
        console.log(prefix, message, ...args);
    }
  }

  /**
   * Log information messages
   */
  info(message: any, ...args: any[]) {
    this.logWithLevel('info', message, ...args);
  }

  /**
   * Log warning messages
   */
  warn(message: any, ...args: any[]) {
    this.logWithLevel('warn', message, ...args);
  }

  /**
   * Log error messages
   */
  error(message: any, ...args: any[]) {
    this.logWithLevel('error', message, ...args);
  }

  /**
   * Log debug messages
   */
  debug(message: any, ...args: any[]) {
    this.logWithLevel('debug', message, ...args);
  }

  /**
   * Log with context
   */
  withContext(context: string) {
    return {
      info: (message: any, ...args: any[]) => 
        this.logWithLevel('info', `[${context}]`, message, ...args),
      warn: (message: any, ...args: any[]) => 
        this.logWithLevel('warn', `[${context}]`, message, ...args),
      error: (message: any, ...args: any[]) => 
        this.logWithLevel('error', `[${context}]`, message, ...args),
      debug: (message: any, ...args: any[]) => 
        this.logWithLevel('debug', `[${context}]`, message, ...args),
    };
  }
}

export const logger = new Logger(); 