import React, { useState } from "react";
import {
  AppBar,
  Toolbar,
  Typography,
  Box,
  List,
  ListItem,
  ListItemText,
  Paper,
  Drawer,
  IconButton,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import MenuIcon from "@mui/icons-material/Menu";
import DashboardLayout from "@/src/layouts/dashboard";
import Campaign from "./campaign/campaign";
import Report from "./report";
// import TransactionNotification from "@/src/components/zalo-automation/autoUID";
import ZnsMessageTemplate from "./zns";
import PushNotification from "@/src/components/zalo-automation/push-notification/push-notification";
import { borderRadius } from "@mui/system";
import { Padding } from "@/src/styles/CommonStyle";

const menuItems = [
  { title: "Thông báo đẩy", key: "pushNotification" },
  // { title: "Tin tự động UID", key: "uid" },
  { title: "Tin nhắn ZNS", key: "zns" },
  { title: "Chiến dịch", key: "campaign" },
  { title: "<PERSON><PERSON>ch sử thông báo", key: "notiHistory" },
];

const appBarStyles = {
  backgroundColor: "#fff",
  boxShadow: "none",
  borderBottom: 1,
  borderColor: "divider",
};

const toolbarStyles = {
  fontWeight: "bold",
  color: "#000",
  fontSize: { xs: "18px", md: "24px" },
};

const sidebarStyles = (isMobile: boolean) => ({
  width: { xs: "100%", lg: 240 },
  minWidth: { xs: "auto", lg: 240 },
  maxWidth: { xs: "none", lg: 240 },
  flexShrink: 0,
  borderColor: "divider",
  backgroundColor: "#fff",
  borderRadius: { xs: 0, lg: "16px" },
  overflow: "hidden",
});

const drawerStyles = {
  "& .MuiDrawer-paper": {
    width: { xs: "280px", sm: "320px" },
    boxSizing: "border-box",
  },
};

const paperStyles = {
  width: "100%",
  borderRadius: { xs: 1, md: 2 },
  overflow: "hidden",
  minHeight: { xs: "calc(100vh - 120px)", md: "auto" },
};

export default function ZaloAutomationPage() {
  const [selectedMenu, setSelectedMenu] = useState<string>("pushNotification");
  const [openDrawer, setOpenDrawer] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("lg"));

  const handleMenuClick = (key: string) => {
    setSelectedMenu(key);
    if (isMobile) setOpenDrawer(false);
  };

  const toggleDrawer = () => {
    setOpenDrawer(!openDrawer);
  };

  const SidebarContent = (
    <Box sx={sidebarStyles(isMobile)}>
      <List sx={{ padding: { xs: 1, lg: 0 } }}>
        {menuItems.map((item, index) => (
          <ListItem
            key={index}
            onClick={() => handleMenuClick(item.key)}
            sx={{
              color: selectedMenu === item.key ? "#2654FE" : "inherit",
              textDecoration: "none",
              cursor: "pointer",
              borderRadius: { xs: 1, lg: 0 },
              margin: { xs: "4px 0", lg: 0 },
              padding: { xs: "12px 16px", lg: "8px 16px" },
              fontSize: { xs: "14px", lg: "16px" },
              backgroundColor: selectedMenu === item.key ? "#f0f6ff" : "transparent",
              "&:hover": {
                backgroundColor: selectedMenu === item.key ? "#f0f6ff" : "#f5f6fa",
              },
            }}
          >
            <ListItemText
              primary={item.title}
              sx={{
                "& .MuiListItemText-primary": {
                  fontSize: { xs: "14px", lg: "16px" },
                  fontWeight: selectedMenu === item.key ? 600 : 400,
                },
              }}
            />
          </ListItem>
        ))}
      </List>
    </Box>
  );

  const renderContent = () => {
    switch (selectedMenu) {
      case "pushNotification":
        return <PushNotification />;
      // case "uid":
      //   return <TransactionNotification />;
      case "zns":
        return <ZnsMessageTemplate />;
      case "campaign":
        return <Campaign />;
      case "notiHistory":
        return <Report />;
      default:
        return <PushNotification />;
    }
  };

  return (
    <DashboardLayout>
      <Box
        sx={{
          minHeight: "100vh",
          padding: { xs: "8px", md: Padding },
        }}
      >
        <AppBar position="static" elevation={0} sx={appBarStyles}>
          <Toolbar sx={{ minHeight: { xs: 56, md: 64 } }}>
            {isMobile && (
              <IconButton
                edge="start"
                color="default"
                aria-label="menu"
                onClick={toggleDrawer}
                sx={{
                  mr: 2,
                  padding: { xs: "8px", md: "12px" },
                }}
              >
                <MenuIcon />
              </IconButton>
            )}
            <Typography variant="h5" sx={toolbarStyles} component="h1">
              Marketing Automation
            </Typography>
          </Toolbar>
        </AppBar>

        <Box
          sx={{
            display: "flex",
            gap: { xs: 0, lg: "10px" },
            marginTop: { xs: "8px", lg: "10px" },
            flexDirection: { xs: "column", lg: "row" },
          }}
        >
          {isMobile ? (
            <Drawer open={openDrawer} onClose={toggleDrawer} sx={drawerStyles} anchor="left">
              {SidebarContent}
            </Drawer>
          ) : (
            SidebarContent
          )}

          <Box
            sx={{
              flexGrow: 1,
              paddingRight: { xs: 0, lg: 0 },
              paddingLeft: { xs: 0, lg: 0 },
              width: { xs: "100%", lg: "auto" },
            }}
          >
            <Paper sx={paperStyles}>
              <Box
                sx={{
                  padding: { xs: 0, md: 0 },
                  minHeight: { xs: "400px", md: "auto" },
                }}
              >
                {renderContent()}
              </Box>
            </Paper>
          </Box>
        </Box>
      </Box>
    </DashboardLayout>
  );
}
