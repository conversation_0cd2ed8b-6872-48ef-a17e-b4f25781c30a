import { useState } from "react";
import { ErrorHandlerService } from "../../services/error-handler.service";
import {
  CreatePriceListRequest,
  GetListProductParam,
  GetPriceListParams,
  priceListService,
} from "../../services/price-list/price-list.service";

export interface TriggerParameterDto {
  name: string;
  value: string;
  dataType: string;
  orderNumber: number;
}

export const usePriceList = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getListPriceList = async (data: GetPriceListParams) => {
    try {
      setLoading(true);
      setError(null);
      const response = await priceListService.getListPriceList(data);
      return response;
    } catch (err: any) {
      // const errorResponse = ErrorHandlerService.handle(err, {
      //   showSnackbar: true,
      //   logError: true,
      // });
      // setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const createPriceList = async (data: CreatePriceListRequest) => {
    try {
      setLoading(true);
      setError(null);
      const response = await priceListService.createPriceList(data);
      return response;
    } catch (err: any) {
      return err;
      // const errorResponse = ErrorHandlerService.handle(err, {
      //   showSnackbar: true,
      //   logError: true,
      // });
      // setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updatePriceList = async (data: CreatePriceListRequest) => {
    try {
      setLoading(true);
      setError(null);
      const response = await priceListService.updatePriceList(data);
      return response;
    } catch (err: any) {
      return err;
      // const errorResponse = ErrorHandlerService.handle(err, {
      //   showSnackbar: true,
      //   logError: true,
      // });
      // setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getDetailPriceList = async (shopId: string, priceListId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await priceListService.getDetailPriceList(shopId, priceListId);
      return response;
    } catch (err: any) {
      // const errorResponse = ErrorHandlerService.handle(err, {
      //   showSnackbar: true,
      //   logError: true,
      // });
      // setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const deletePriceListById = async (shopId: string, priceListId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await priceListService.deletePriceListById(shopId, priceListId);
      return response;
    } catch (err: any) {
      // const errorResponse = ErrorHandlerService.handle(err, {
      //   showSnackbar: true,
      //   logError: true,
      // });
      // setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const deleteManyPriceList = async (shopId: string, listId: string[]) => {
    try {
      setLoading(true);
      setError(null);
      const response = await priceListService.deleteManyPriceList(shopId, listId);
      return response;
    } catch (err: any) {
      // const errorResponse = ErrorHandlerService.handle(err, {
      //   showSnackbar: true,
      //   logError: true,
      // });
      // setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const addProductsForPriceList = async (
    shopId: string,
    priceListId: string,
    ProductPriceListDto: string[]
  ) => {
    try {
      setLoading(true);
      setError(null);
      const response = await priceListService.addProductsForPriceList(
        shopId,
        priceListId,
        ProductPriceListDto
      );
      return response;
    } catch (err: any) {
      // const errorResponse = ErrorHandlerService.handle(err, {
      //   showSnackbar: true,
      //   logError: true,
      // });
      // setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const deleteProductsForPriceList = async (
    shopId: string,
    priceListId: string,
    ProductPriceListDto: string[]
  ) => {
    try {
      setLoading(true);
      setError(null);
      const response = await priceListService.deleteProductsForPriceList(
        shopId,
        priceListId,
        ProductPriceListDto
      );
      return response;
    } catch (err: any) {
      // const errorResponse = ErrorHandlerService.handle(err, {
      //   showSnackbar: true,
      //   logError: true,
      // });
      // setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const updateRanksForPriceList = async (
    shopId: string,
    priceListId: string,
    listRankId: string[]
  ) => {
    try {
      setLoading(true);
      setError(null);
      const response = await priceListService.updateRanksForPriceList(
        shopId,
        priceListId,
        listRankId
      );
      return response;
    } catch (err: any) {
      // const errorResponse = ErrorHandlerService.handle(err, {
      //   showSnackbar: true,
      //   logError: true,
      // });
      // setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const setActiveStatusForPriceList = async (
    shopId: string,
    priceListId: string,
    isActive: boolean
  ) => {
    try {
      setLoading(true);
      setError(null);
      const response = await priceListService.setActiveStatusForPriceList(
        shopId,
        priceListId,
        isActive
      );
      return response;
    } catch (err: any) {
      // const errorResponse = ErrorHandlerService.handle(err, {
      //   showSnackbar: true,
      //   logError: true,
      // });
      // setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getListProductByPriceListId = async (params: GetListProductParam) => {
    try {
      setLoading(true);
      setError(null);
      const response = await priceListService.getListProductByPriceListId(params);
      return response;
    } catch (err: any) {
      // const errorResponse = ErrorHandlerService.handle(err, {
      //   showSnackbar: true,
      //   logError: true,
      // });
      // setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const getListProductNotInPriceList = async (params: GetListProductParam) => {
    try {
      setLoading(true);
      setError(null);
      const response = await priceListService.getListProductNotInPriceList(params);
      return response;
    } catch (err: any) {
      // const errorResponse = ErrorHandlerService.handle(err, {
      //   showSnackbar: true,
      //   logError: true,
      // });
      // setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getListProductByTiers = async (params: GetListProductParam) => {
    try {
      setLoading(true);
      setError(null);
      const response = await priceListService.getListProductByTiers(params);
      return response;
    } catch (err: any) {
      // const errorResponse = ErrorHandlerService.handle(err, {
      //   showSnackbar: true,
      //   logError: true,
      // });
      // setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  return {
    getListPriceList,
    createPriceList,
    updatePriceList,
    getDetailPriceList,
    deletePriceListById,
    deleteManyPriceList,
    addProductsForPriceList,
    deleteProductsForPriceList,
    updateRanksForPriceList,
    setActiveStatusForPriceList,
    getListProductByPriceListId,
    getListProductNotInPriceList,
    getListProductByTiers,
    loading,
    error,
  };
};
