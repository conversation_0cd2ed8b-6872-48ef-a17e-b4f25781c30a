import React, { useCallback, useRef, useState, useEffect } from "react";
import { Box, Typography } from "@mui/material";
import { Editor } from "@hugerte/hugerte-react";

interface HugerteEditorProps {
  value: string;
  onChange: (content: string) => void;
  height?: number;
  placeholder?: string;
  disabled?: boolean;
  toolbar?: string;
  plugins?: string;
  showLoadingIndicator?: boolean;
  sx?: any;
  // Additional Hugerte configuration
  editorConfig?: any;
}

/**
 * Hugerte Editor Component
 * Reusable editor component with proper content synchronization
 */
const HugerteEditor: React.FC<HugerteEditorProps> = ({
  value,
  onChange,
  height = 500,
  placeholder = "Nhập nội dung...",
  disabled = false,
  toolbar = 'undo redo | bold italic forecolor | alignleft aligncenter ' +
    'alignright alignjustify | bullist numlist outdent indent | code fullscreen preview |' +
    'link image | table media | lineheight | forecolor backcolor removeformat | charmap emoticons | save print | pagebreak anchor codesample | ltr rtl | blocks',
  plugins = "preview importcss searchreplace autolink autosave save directionality code visualblocks visualchars fullscreen image link media template codesample table charmap pagebreak nonbreaking anchor insertdatetime advlist lists wordcount help charmap quickbars emoticons accordion",
  showLoadingIndicator = true,
  sx = {},
  editorConfig = {}
}) => {
  // Reference to the editor instance
  const editorRef = useRef<any>(null);
  
  // State to track if editor is ready
  const [editorReady, setEditorReady] = useState(false);
  
  // Local state for content
  const [localContent, setLocalContent] = useState(value);

  // Handle editor initialization
  const handleEditorInit = useCallback((evt: any, editor: any) => {
    editorRef.current = editor;
    
    // Set initial content when editor is ready
    const initialContent = value || '';
    if (initialContent) {
      editor.setContent(initialContent);
    }
    
    setEditorReady(true);
  }, []);

  // Handle content change
  const handleContentChange = useCallback(
    (content: string) => {
      setLocalContent(content);
      onChange(content);
    },
    [onChange]
  );

  // Sync editor content when value changes externally and editor is ready
  useEffect(() => {
    if (editorReady && editorRef.current) {
      const currentContent = editorRef.current.getContent();
      if (currentContent !== value) {
        editorRef.current.setContent(value || '');
      }
    }
    setLocalContent(value);
  }, [value, editorReady]);

  // Handle disabled state
  useEffect(() => {
    if (editorReady && editorRef.current) {
      editorRef.current.mode.set(disabled ? 'readonly' : 'design');
    }
  }, [disabled, editorReady]);

  // Default editor configuration
  const defaultConfig = {
    height: height,
    menubar: false,
    plugins: plugins,
    toolbar: toolbar,
    content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
    
    // Additional settings for better content loading
    placeholder: placeholder,
    branding: false,
    resize: true,
    elementpath: false,
    statusbar: true,
    
    // Vietnamese language support
    language: 'vi',
    
    // Paste configuration
    paste_data_images: true,
    images_reuse_filename: true,
    
    // Performance settings
    convert_urls: false,
    relative_urls: false,
    
    // Setup function to handle editor events
    setup: function(editor: any) {
      // Handle editor ready event
      editor.on('init', function() {
        console.log('Hugerte Editor initialized');
      });
      
      // Handle content change events
      editor.on('change keyup paste', function() {
        const content = editor.getContent();
        if (content !== localContent) {
          handleContentChange(content);
        }
      });
      
      // Handle focus/blur events
      editor.on('focus', function() {
        console.log('Editor focused');
      });
      
      editor.on('blur', function() {
        console.log('Editor blurred');
      });
    }
  };

  // Merge default config with custom config
  const finalConfig = { ...defaultConfig, ...editorConfig };

  return (
    <Box sx={{ position: 'relative', ...sx }}>
      <Editor
        onInit={handleEditorInit}
        initialValue="" // Leave empty, we'll set content in onInit
        onEditorChange={handleContentChange}
        disabled={disabled}
        init={finalConfig}
      />
      
      {/* Loading indicator */}
      {showLoadingIndicator && !editorReady && (
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 1000,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            padding: 2,
            borderRadius: 1,
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            boxShadow: 1
          }}
        >
          <Typography variant="body2" color="text.secondary">
            Đang tải editor...
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default HugerteEditor; 