export interface ErrorConfig {
  showSnackbar?: boolean;
  logError?: boolean;
}

export interface ApiError {
  status: number;
  title: string;
  detail: string;
  type: string;
  errors?: Record<string, string[]>;
  extensions?: Record<string, any>;
  instance?: string | null;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
}
