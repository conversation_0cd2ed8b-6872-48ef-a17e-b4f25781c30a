import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ield,
  Stack,
  IconButton,
  Box,
  InputAdornment,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import ToggleOnIcon from "@mui/icons-material/ToggleOn";
import ToggleOffIcon from "@mui/icons-material/ToggleOff";
import Grid from "@mui/system/Grid";
import { useShop } from "@/src/api/hooks/shop/use-shop";
import { useStoreId } from "@/src/hooks/use-store-id";
import { IBodyUpdateShopTaxRate } from "@/src/api/services/shop/shop.service";
import useSnackbar from "@/src/hooks/use-snackbar";

const TaxSetting = () => {
  const { updateShopTaxRate, detailShop } = useShop();
  const shopId = useStoreId();
  const [isToggled, setIsToggled] = useState<boolean>(false);
  const [taxRate, setTaxRate] = useState<string>("");
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const snackbar = useSnackbar();
  const [error, setError] = useState<string>("");
  const handleToggle = () => {
    setIsToggled(!isToggled);
  };

  const handleSetupTax = () => {
    if (!taxRate || taxRate.trim() === "") {
      setError("Vui lòng nhập tỷ lệ thuế");
      return;
    }
    setError("");
    setOpenDialog(true);
  };

  const handleValidateAndSetup = () => {
    if (!taxRate || taxRate.trim() === "") {
      setError("Vui lòng nhập tỷ lệ thuế");
      return;
    }

    setError("");
    handleSetupTax();
  };
  const getDetailShop = async () => {
    const res = await detailShop(shopId);
    if (res && res?.status === 200) {
      const tax = res?.data?.defaultTaxRate;
      setTaxRate(tax?.toString());
    }
  };
  useEffect(() => {
    getDetailShop();
  }, [shopId]);

  const handleConfirmSetup = async () => {
    const data: IBodyUpdateShopTaxRate = {
      shopId: shopId,
      taxRate: Number(taxRate),
    };
    const res = await updateShopTaxRate(data);
    if (res && res?.status === 200) {
      setOpenDialog(false);
      snackbar.success("Thiết lập thuế cho cửa hàng thành công");
    } else {
      setOpenDialog(false);
      snackbar.success("Thiết lập thuế cho cửa hàng thất bại");
    }
  };

  return (
    <Grid container spacing={2} sx={{ px: { xs: 2, md: 3 }, py: { xs: 2, md: 3 } }}>
      <Description />
      <TaxRateSetting
        isToggled={isToggled}
        handleToggle={handleToggle}
        handleConfirmSetup={handleConfirmSetup}
        taxRate={taxRate}
        setTaxRate={setTaxRate}
        openDialog={openDialog}
        setOpenDialog={setOpenDialog}
        handleSetupTax={handleSetupTax}
        error={error}
        setError={setError}
        handleValidateAndSetup={handleValidateAndSetup}
      />
    </Grid>
  );
};

const Description = () => (
  <Grid size={{ xs: 12, md: 4 }} sx={{ mt: 2 }}>
    <Typography variant="body2">
      Tính năng này sẽ giúp danh sách sản phẩm của bạn tự động tính thêm thuế cho người tiêu dùng
      theo quy định của pháp luật hiện hành hoặc tỷ lệ thuế quy định tại địa phương bạn kinh doanh
    </Typography>
  </Grid>
);

const TaxRateSetting = ({
  isToggled,
  handleToggle,
  taxRate,
  setTaxRate,
  handleSetupTax,
  openDialog,
  setOpenDialog,
  handleConfirmSetup,
  error,
  setError,
  handleValidateAndSetup,
}) => (
  <Grid
    size={{ xs: 12, md: 8 }}
    sx={{ background: "white", boxShadow: 3, borderRadius: 2, p: { xs: 2, md: 3 } }}
  >
    <Typography variant="h6" sx={{ mb: 1 }}>
      Cài đặt tỷ lệ thuế
    </Typography>
    <Typography variant="body2" sx={{ mb: 2 }}>
      Nếu sản phẩm bạn kinh doanh không thuộc danh mục chịu thuế theo quy định của pháp luật Việt
      Nam hoặc giá bán lẻ của bạn đã bao gồm thuế vui lòng không bật tính năng này
    </Typography>

    <Stack
      direction={{ xs: "column", sm: "row" }}
      alignItems="start"
      justifyContent="flex-start"
      spacing={1}
    >
      <TextField
        type="text"
        value={taxRate}
        onChange={(e) => {
          setTaxRate(e.target.value.replace(/[^\d]/g, ""));
          setError("");
        }}
        placeholder="Nhập tỷ lệ thuế"
        error={!!error}
        helperText={error}
        sx={{ width: { xs: "100%", sm: "50%" } }}
        InputProps={{
          endAdornment: <InputAdornment position="end">%</InputAdornment>,
          sx: {
            height: "40px",
            display: "flex",
            alignItems: "center",
            padding: "0 14px",
          },
        }}
        inputProps={{
          style: { padding: "10px" },
        }}
      />
      <Box
        sx={{
          flexGrow: 1,
          display: "flex",
          justifyContent: "flex-start",
          mt: { xs: 2, sm: 0 },
          height: "40px",
        }}
      >
        <Button
          variant="contained"
          onClick={handleValidateAndSetup}
          sx={{
            bgcolor: "primary.main",
            "&:hover": { bgcolor: "primary.dark" },
            height: "40px",
          }}
        >
          Thiết lập
        </Button>
      </Box>
    </Stack>
    <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
      <DialogTitle>Xác nhận thiết lập thuế</DialogTitle>
      <DialogContent>
        <Typography>Bạn có chắc chắn muốn thiết lập tỷ lệ thuế {taxRate}%?</Typography>
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setOpenDialog(false)}>Hủy</Button>
        <Button onClick={handleConfirmSetup} variant="contained">
          Xác nhận
        </Button>
      </DialogActions>
    </Dialog>
  </Grid>
);

export default TaxSetting;
