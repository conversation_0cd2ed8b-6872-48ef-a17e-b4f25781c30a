import { useState } from "react";
import DashboardLayout from "../../../layouts/dashboard";
import { Box, Card, Tab, Tabs } from "@mui/material";

import TableOrder from "@/src/components/orders/draft/TableOrder";
import TitleTypography from "@/src/components/title-typography/title-typography";
import { Padding } from "@/src/styles/CommonStyle";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
export default function OrderList() {
  const [tabIndex, setTabIndex] = useState(0);
  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };
  const handleTabChange = (event, newValue) => {
    setTabIndex(newValue);
  };

  return (
    <DashboardLayout>
      <Box sx={{ p: Padding, paddingTop: "20px" }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            borderBottom: "1px solid #bdbdbd",
            marginBottom: "20px",
            paddingBottom: "20px",
          }}
          flexDirection={{
            xs: "column", // Điện thoại: cột
            sm: "row", // Tablet trở lên: hàng
          }}
        >
          <TitleTypography
            sx={{
              textTransform: "none",
              color: " #000 !important",
              fontSize: "20px !important",
              fontWeight: "700",
              lineHeight: "20px",
            }}
          >
            Danh sách đơn hàng
          </TitleTypography>
          {/* <Box sx={{ display: 'flex', flexDirection: 'row' }}>
            <Button sx={{ marginRight: '8px' }} startIcon={<FileUploadOutlinedIcon />}>
              Xuất
            </Button>
            <Button variant="contained" onClick={handleAddNew} size="small">
              Thêm khách hàng
            </Button>
          </Box> */}
        </Box>

        <Card sx={{ p: 2, mt: 2 }}>
          <Tabs
            value={tabIndex}
            onChange={handleTabChange}
            sx={{
              mb: 2,
              ".MuiTabs-indicator": {
                backgroundColor: "#2654FE",
              },
            }}
          >
            <Tab
              sx={{
                textTransform: "none !important",
                "&.Mui-selected": {
                  color: "#2654FE",
                },
              }}
              label="Sản phẩm"
            />
          </Tabs>
          {tabIndex === 0 && <TableOrder tabName="Product" isGranted={isGranted} />}
        </Card>
      </Box>
    </DashboardLayout>
  );
}
