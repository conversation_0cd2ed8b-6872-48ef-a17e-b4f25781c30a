import React, { useState } from 'react';
import {
  Box,
  Button,
  Paper,
  TextField,
  Typography,
  Checkbox,
  FormControlLabel,
  IconButton,
} from '@mui/material';
import { Sidebar } from '../index';
import DashboardLayout from '../../../../../layouts/dashboard';
import { Container } from '@mui/system';
import BatteryFullIcon from '@mui/icons-material/BatteryFull';
import SignalCellularAltIcon from '@mui/icons-material/SignalCellularAlt';
import WifiIcon from '@mui/icons-material/Wifi';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import CloseIcon from '@mui/icons-material/Close';

const Dashboard = () => {
  const [image, setImage] = useState(null);
  const [content, setContent] = useState('');
  const [footerText, setFooterText] = useState('');
  const [checked, setChecked] = useState(false);

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      setImage(URL.createObjectURL(file));
    }
  };

  return (
    <DashboardLayout>
      <Container
        maxWidth={false}
        sx={{
          display: 'flex',
          gap: { xs: '10px', md: '15px' },
          p: { xs: 1, md: 0 },
          m: 0,
          flexDirection: { xs: 'column', lg: 'row' },
        }}
      >
        <Sidebar />
        <Box sx={{ flexGrow: 1, padding: { xs: '15px' }, width: { xs: '100%', lg: '70%' } }}>
          <Box
            sx={{
              display: 'flex',
              gap: '2%',
              '@media (max-width: 600px)': { flexDirection: 'column' },
            }}
          >
            <Paper
              sx={{
                width: '60%',
                flex: 1,
                boxShadow: 'none',
                backgroundColor: '#fff !important',
                '@media (max-width: 600px)': { width: '100%', marginBottom: '35px' },
              }}
            >
              <Typography sx={{ fontSize: '16px', fontWeight: '700', color: '#000' }}>
                Thiết lập trang tuyển dụng
              </Typography>
              <Box sx={{ textAlign: 'center', my: 2, background: '#F5F5F5', padding: '20px 30px' }}>
                <input
                  accept="image/*"
                  style={{ display: 'none' }}
                  id="upload-button"
                  type="file"
                  onChange={handleImageUpload}
                />
                <label htmlFor="upload-button">
                  <Button
                    variant="contained"
                    component="span"
                    sx={{
                      backgroundColor: '#2654FE',
                      color: '#fff',
                      fontWeight: '700',
                      fontSize: '16px',
                      textTransform: 'none',
                      padding: '15px 35px',
                    }}
                  >
                    Tải ảnh lên
                  </Button>
                </label>
                {image && (
                  <img src={image} alt="Uploaded" style={{ width: '100%', marginTop: 10 }} />
                )}
              </Box>
              <Typography
                variant="body2"
                sx={{ color: '#000000', fontSize: '16px', fontWeight: '400' }}
              >
                Bạn có thể tải lên hình ảnh JPG, PNG hoặc GIF. Chúng tôi khuyên bạn nên đặt chiều
                rộng là 460px và kiểm soát kích thước tối đa 20M.
              </Typography>
              <Box sx={{ marginTop: '40px' }}>
                <Typography
                  sx={{ fontSize: '16px', fontWeight: '700', color: '#000', marginBottom: '10px' }}
                >
                  Nội dung
                </Typography>
                <TextField
                  multiline
                  rows={8}
                  fullWidth
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  sx={{ borderRadius: '0px', padding: 0 }}
                />
              </Box>
              <Box sx={{ marginTop: '25px' }}>
                <Typography
                  sx={{ fontSize: '16px', fontWeight: '700', color: '#000', marginBottom: '10px' }}
                >
                  Nội dung thanh điều hướng
                </Typography>
                <TextField
                  fullWidth
                  variant="outlined"
                  value={footerText}
                  onChange={(e) => setFooterText(e.target.value)}
                />
              </Box>
            </Paper>

            <Paper
              sx={{
                width: '40%',
                bgcolor: '#FAFAFA',
                '@media (max-width: 600px)': { width: '100%' },
              }}
            >
              <Box
                sx={{
                  bgcolor: '#F46E00',
                  display: 'flex',
                  alignItems: 'center',
                  px: 2,
                  flexDirection: 'column',
                  paddingBottom: '10px',
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    backgroundColor: '#f26c00',
                    color: 'white',
                    width: '100%',
                    paddingTop: '20px',
                  }}
                >
                  <Typography sx={{ fontSize: '14px', fontWeight: '600', color: 'white' }}>
                    09:41
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <SignalCellularAltIcon />
                    <WifiIcon />
                    <BatteryFullIcon sx={{ color: 'white' }} />
                  </Box>
                </Box>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    backgroundColor: '#F46E00',
                    justifyContent: 'space-between',
                    width: '100%',
                    marginTop: '10px',
                  }}
                >
                  <IconButton sx={{ color: 'white', padding: 0 }}>
                    <ArrowBackIcon />
                  </IconButton>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      color: 'white',
                      flex: 1,
                      fontWeight: '600',
                      fontSize: '14px',
                      marginLeft: '10px',
                    }}
                  >
                    Đăng ký đối tác
                  </Typography>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      backgroundColor: '#EAEAEA',
                      borderRadius: '20px',
                      padding: '4px 8px',
                    }}
                  >
                    <MoreHorizIcon sx={{ color: 'gray' }} />
                    <Box sx={{ width: '1px', height: '12px', backgroundColor: 'gray', mx: 1 }} />
                    <IconButton size="small" sx={{ color: 'gray' }}>
                      <CloseIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </Box>
              </Box>
              {image && <img src={image} alt="Banner" style={{ width: '100%' }} />}
              <Box sx={{ p: 2, background: '#fff', borderRadius: '10px' }}>
                <Typography
                  sx={{
                    fontSize: '18px',
                    fontWeight: '700',
                    color: '#000',
                    textAlign: 'center',
                    paddingBottom: '10px',
                    borderBottom: '1px solid #DDDDDD',
                  }}
                >
                  Chào mừng bạn đến với chương trình Affiliate Loftia
                </Typography>
                <Typography
                  sx={{
                    fontSize: '14px',
                    fontWeight: '400',
                    color: '#000',
                    mt: 2,
                    height: '400px',
                    overflow: 'auto',
                  }}
                >
                  {content || 'Chi tiết chương trình tích điểm, text tự viết'}
                </Typography>
                <FormControlLabel
                  control={<Checkbox checked={checked} onChange={() => setChecked(!checked)} />}
                  aria-placeholder="Chi tiết chương trình tích điểm, text tự viết"
                  label="Tôi đã hiểu và đồng ý với điều khoản Affiliate"
                  sx={{ mt: 2 }}
                />
                <Button
                  sx={{
                    color: '#494949',
                    fontWeight: '700',
                    fontSize: '14px',
                    marginTop: '10px',
                    background: '#FEF6E9',
                    textTransform: 'none',
                    padding: '10px 30px',
                    borderRadius: '30px',
                  }}
                  variant="contained"
                  fullWidth
                  disabled={!checked}
                >
                  Đăng ký Affiliate
                </Button>
              </Box>
            </Paper>
          </Box>
        </Box>
      </Container>
    </DashboardLayout>
  );
};

export default Dashboard;
