import * as React from 'react';
import {
  Box,
  Button,
  Checkbox,
  Divider,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Paper,
  Tab,
  Tabs,
  Typography,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormLabel,
  Select,
  MenuItem,
  TextField
} from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Link as LinkIcon } from '@mui/icons-material';
import Grid from '@mui/material/Grid';
import FormDialog from '@/src/components/dialog/TitleDialog';

const groups = [
  { name: 'Không nhóm', count: 3 },
  { name: 'Ảnh sản phẩm', count: 1 },
  { name: 'Ảnh dịch vụ', count: 0 }
];

const files = [
  {
    id: '969377207eee3a13d93e13ebeb166ee2e84944627d-425',
    name: '969377207eee3a13d93e13ebeb166ee2e84944627d-425.jpg',
    size: '213KB',
    thumbnail: '/placeholder.svg?height=40&width=40'
  },
  {
    id: '979393069eb892957df504f1540394367beb196b-502',
    name: '979393069eb892957df504f1540394367beb196b-502.jpg',
    size: '119KB',
    thumbnail: '/placeholder.svg?height=40&width=40'
  },
  {
    id: '77985080288912e71fcc84615231e7a55fc2aee4-3971',
    name: '77985080288912e71fcc84615231e7a55fc2aee4-3971.jpg',
    size: '197KB',
    thumbnail: '/placeholder.svg?height=40&width=40'
  }
];

export default function ImageManagement() {
  const [tab, setTab] = React.useState(0);
  const [selectedGroup, setSelectedGroup] = React.useState<string>('Không nhóm');
  const [selectedFiles, setSelectedFiles] = React.useState<string[]>([]);
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [uploadMethod, setUploadMethod] = React.useState('upload');

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => setTab(newValue);

  const handleSelectAllFiles = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedFiles(event.target.checked ? files.map((file) => file.id) : []);
  };

  const handleSelectFile = (fileId: string) => {
    setSelectedFiles((prev) => (prev.includes(fileId) ? prev.filter((id) => id !== fileId) : [...prev, fileId]));
  };

  const handleDialogOpen = () => setDialogOpen(true);
  const handleDialogClose = () => setDialogOpen(false);
  const handleDialogSubmit = () => console.log('Form data:', {}); // Define the data variable
  const handleUploadMethodChange = (event: React.ChangeEvent<HTMLInputElement>) => setUploadMethod(event.target.value);

  const renderGroupList = () => (
    <List sx={{ p: 0 }}>
      {groups.map((group) => (
        <ListItemButton
          key={group.name}
          selected={selectedGroup === group.name}
          onClick={() => setSelectedGroup(group.name)}
          sx={{
            borderRadius: 1,
            mx: 1,
            '&.Mui-selected': { bgcolor: 'action.selected' }
          }}
        >
          <ListItemText
            primary={
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2">{group.name}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {group.count}
                </Typography>
              </Box>
            }
          />
        </ListItemButton>
      ))}
    </List>
  );

  const renderFileList = () => (
    <List>
      {files.map((file, index) => (
        <React.Fragment key={file.id}>
          <ListItem sx={{ '&:hover': { bgcolor: '#f5f5f5' } }}>
            <Grid container alignItems="center">
              <Grid item xs={1} md={1}>
                <Checkbox checked={selectedFiles.includes(file.id)} onChange={() => handleSelectFile(file.id)} />
              </Grid>
              <Grid item xs={5} md={5} sx={{ display: 'flex', alignItems: 'center' }}>
                <Box component="img" src={file.thumbnail} sx={{ width: 40, height: 40, mr: 2, objectFit: 'cover' }} />
                <Typography variant="body2" noWrap>
                  {file.name}
                </Typography>
              </Grid>
              <Grid item xs={3} md={3}>
                <Typography variant="body2" color="text.secondary">
                  {file.size}
                </Typography>
              </Grid>
              <Grid item xs={3} md={3}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', gap: 1 }}>
                  <IconButton size="small">
                    <EditIcon fontSize="small" />
                  </IconButton>
                  <IconButton size="small">
                    <LinkIcon fontSize="small" sx={{ transform: 'rotate(-45deg)' }} />
                  </IconButton>
                  <IconButton size="small">
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Box>
              </Grid>
            </Grid>
          </ListItem>
          {index < files.length - 1 && <Divider />}
        </React.Fragment>
      ))}
    </List>
  );

  const renderDialogContent = () => (
    <>
      <Typography variant="h6" sx={{ mb: 2 }}>
        Tải ảnh lên
      </Typography>
      <FormControl component="fieldset" sx={{ mb: 2 }}>
        <FormLabel component="legend">Chọn phương pháp tải lên</FormLabel>
        <RadioGroup row aria-label="method" name="method" value={uploadMethod} onChange={handleUploadMethodChange}>
          <FormControlLabel value="upload" control={<Radio />} label="Tải lên tập tin và cục bộ" />
          <FormControlLabel value="link" control={<Radio />} label="Thêm url" />
        </RadioGroup>
      </FormControl>
      <FormControl fullWidth sx={{ mb: 2 }}>
        <FormLabel>Chọn nhóm</FormLabel>
        <Select defaultValue="">
          {groups.map((group) => (
            <MenuItem key={group.name} value={group.name}>
              {group.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      {uploadMethod === 'upload' ? (
        <Box>
          <Typography variant="body2" sx={{ mb: 1 }}>
            Tải lên các tệp tin cục bộ
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Bạn có thể tải lên tối đa 15 ảnh. Chỉ hỗ trợ JPG, GIF, PNG và BMP và mỗi hình ảnh không được vượt quá 10 MB.
          </Typography>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              width: '100%',
              height: 150,
              border: '2px dashed #ccc',
              borderRadius: 1,
              cursor: 'pointer',
              mb: 2
            }}
            component="label"
          >
            <AddIcon sx={{ fontSize: 40, color: '#ccc' }} />
            <input type="file" hidden />
          </Box>
        </Box>
      ) : (
        <TextField fullWidth label="URL ảnh" variant="outlined" />
      )}
    </>
  );

  return (
    <Box
      sx={{ display: 'flex', flexDirection: 'column', height: '100vh', bgcolor: '#ffffff', p: 3, overflow: 'hidden' }}
    >
      <Paper
        sx={{
          p: 1,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          bgcolor: '#ffffff',
          boxShadow: 'none',
          borderBottom: '1px solid #e0e0e0'
        }}
      >
        <Tabs value={tab} onChange={handleTabChange}>
          <Tab label="Hình Ảnh" />
          <Tab label="Video" />
        </Tabs>
        <Button variant="contained" startIcon={<AddIcon />} onClick={handleDialogOpen}>
          Tải ảnh lên
        </Button>
      </Paper>

      <Box sx={{ display: 'flex', gap: 2, mt: 2, flexDirection: { xs: 'column', md: 'row' }, overflow: 'hidden' }}>
        <Paper sx={{ width: { xs: '100%', md: 240 }, bgcolor: '#f5f5f5', boxShadow: 'none' }}>
          {renderGroupList()}
          <Box sx={{ p: 2 }}>
            <Button fullWidth variant="contained" startIcon={<AddIcon />}>
              Thêm nhóm
            </Button>
          </Box>
        </Paper>
        <Paper sx={{ flex: 1, boxShadow: 'none', bgcolor: '#ffffff', overflow: 'hidden' }}>
          <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
            <Typography variant="h6">{selectedGroup}</Typography>
            <Grid container alignItems="center">
              <Grid item xs={1} md={1}>
                <Checkbox
                  checked={selectedFiles.length === files.length}
                  indeterminate={selectedFiles.length > 0 && selectedFiles.length < files.length}
                  onChange={handleSelectAllFiles}
                />
              </Grid>
              <Grid item xs={5} md={5}>
                <Typography variant="subtitle2">Tài liệu</Typography>
              </Grid>
              <Grid item xs={3} md={3}>
                <Typography variant="subtitle2">Kích cỡ</Typography>
              </Grid>
              <Grid item xs={3} md={3} sx={{ textAlign: 'right' }}>
                <Typography variant="subtitle2">Hoạt động</Typography>
              </Grid>
            </Grid>
          </Box>
          <Box sx={{ overflowY: 'auto', height: 'calc(100vh - 200px)' }}>{renderFileList()}</Box>
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 2, gap: 1 }}>
            <Typography variant="body2" color="text.secondary">
              1
            </Typography>
            <Typography variant="body2" color="text.secondary">
              /
            </Typography>
            <Typography variant="body2" color="text.secondary">
              1
            </Typography>
          </Box>
        </Paper>
      </Box>

      <FormDialog
        open={dialogOpen}
        handleClose={handleDialogClose}
        handleSubmit={handleDialogSubmit}
        title="Tải ảnh lên"
        closeBtnTitle="Hủy"
        submitBtnTitle="Lưu"
      >
        {renderDialogContent()}
      </FormDialog>
    </Box>
  );
}
