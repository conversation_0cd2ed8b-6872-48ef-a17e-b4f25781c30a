import { OrderPaymentMethod } from "@/src/api/types/cart.types";
import { CreditCard } from "@mui/icons-material";
import { Box, FormControl, InputLabel, MenuItem, Select, Typography } from "@mui/material";
import React from "react";
// Define the available payment methods
const paymentMethods: OrderPaymentMethod[] = [
  { value: "Transfer", label: "Chuyển khoản" },
  { value: "COD", label: "Thanh toán khi nhận hàng" },
  { value: "Vnpay", label: "VNPAY" },
  { value: "Momo", label: "MOMO" },
  { value: "<PERSON>alo", label: "<PERSON>alo" },
  { value: "Other", label: "<PERSON>há<PERSON>" },
];
export default function PaymentMethodOrder({ selectedMethod, handlePaymentMethod }) {
  return (
    <Box>
      <Box>
        <Typography variant="h6" sx={{ marginBottom: 1, fontWeight: 600 }}>
          <CreditCard sx={{ color: "#2e7d32", fontSize: 22, marginRight: 0.5 }} /> <PERSON><PERSON><PERSON><PERSON> thức thanh
          toán
        </Typography>
        <Box sx={{ minWidth: 120 }}>
          <FormControl fullWidth>
            <Select
              labelId="payment-method-select-label"
              id="payment-method-select"
              value={selectedMethod}
              onChange={handlePaymentMethod}
            >
              {paymentMethods.map((method) => (
                <MenuItem key={method.value} value={method.value}>
                  {method.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </Box>
    </Box>
  );
}
