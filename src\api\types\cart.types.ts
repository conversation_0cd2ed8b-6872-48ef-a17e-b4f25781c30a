import { TaxInvoiceDto } from "@/src/components/orders/draft/OrderDraftForm";

// Define the delivery method interface
export interface OrderDeliveryMethod {
  value: string;
  label: string;
}

export interface OrderPaymentMethod {
  value: string;
  label: string;
}

export type CartStatusDelivery = "InShop" | "ExpressDelivery" | "";
export type CartTransportService =
  | "LCOD"
  | "NCOD"
  | "VHT"
  | "PHS"
  | "PTN"
  | "VCN"
  | "VTK"
  | "AHAMOVE"
  | "JTEXPRESS"
  | "EMS"
  | "NINJAVAN"
  | "BESTEXPRESS";
export type CartTypePay = "COD" | "Momo" | "Zalo" | "Other" | "Transfer";

export interface CreateOrUpdateCartData {
  cartNo: string;
  cartId: string;
  transactionId: string;
  partnerId: string;
  userId: string;
  addressId: string;
  shopId: string;
  listItems: any[]; // Cần thay thế `any` bằng một interface khác nếu có chi tiết về cấu trúc của các item
  voucherPromotion: any[]; // Tương tự, thay `any` bằng interface cụ thể nếu cần
  voucherTransport: any[];
  price: number;
  exchangePoints: number;
  pointPrice: number;
  voucherPromotionPrice: number;
  voucherTransportPrice: number;
  transportPrice: number;
  transportService: CartTransportService; // Nên sử dụng Union type hoặc Enum để mô tả cụ thể hơn
  statusDelivery: CartStatusDelivery; // Tương tự, sử dụng Union type hoặc Enum
  typePay: CartTypePay; // Tương tự, sử dụng Union type hoặc Enum
  created: string; // ISO Date string
  updated: string; // ISO Date string;
  branchId?: string;
  cartOrigin?: CartOrigin;
  taxInvoice?: TaxInvoiceDto;
}

export type CartOrigin = "WebPartner" | "Pos" | "ZaloMiniApp" | "Other";
