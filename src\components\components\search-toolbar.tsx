import {
  Box,
  TextField,
  InputAdornment,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import { useTranslation } from "react-i18next";
import { tokens } from "@/src/locales/tokens";
import CustomSelect from "../custom-select";
import { useMemo, useState } from "react";
import { Download, FileUpload, Add, DeleteOutline } from "@mui/icons-material";
import ModalImportExcelItems from "./modal-import-excel-items";
import { useProduct } from "@/src/api/hooks/dashboard/product/use-product";
import { useStoreId } from "@/src/hooks/use-store-id";
import {
  ExportProductParams,
  IBodyDeleteListItem,
} from "@/src/api/services/dashboard/product/product.service";
import ModalImportExcelCategory from "./modal-import-excel-category";
import { useProductCategory } from "@/src/api/hooks/dashboard/product/use-category";
import { ExportProductCategoryParams } from "@/src/api/services/dashboard/product/category.service";
import { StorageService } from "nextjs-api-lib";
import useSnackbar from "@/src/hooks/use-snackbar";
import { usePathname } from "next/navigation";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import ActionButton from "@/src/components/common/ActionButton";

interface Category {
  categoryId: string;
  name: string;
  categoryName: string;
  listSubCategory?: Category[];
}

interface SearchToolbarProps {
  isGranted: any;
  searchTerm: string;
  onSearchChange: (value: string) => void;
  onAddClick: () => void;
  onDeleteClick?: () => void;
  selectedCount?: number;
  placeholder: string;
  addButtonText: string;
  categories?: Category[];
  categoryId?: string;
  subCategoryId?: string;
  onCategoryChange?: (value: string) => void;
  onSubCategoryChange?: (value: string) => void;
  tabValue?: number;
  pageType?: string;
  listItem?: any[];
  fetchData?: any;
  selectedIds?: string[];
  setSelectedIds?: any;
}

export const SearchToolbar = ({
  isGranted,
  searchTerm,
  onSearchChange,
  onAddClick,
  onDeleteClick,
  selectedCount = 0,
  placeholder,
  addButtonText,
  categories = [],
  categoryId = "",
  subCategoryId = "",
  onCategoryChange,
  onSubCategoryChange,
  tabValue,
  pageType,
  listItem,
  fetchData,
  selectedIds,
  setSelectedIds,
}: SearchToolbarProps) => {
  const [isOpenModalImport, setIsOpenModalImport] = useState<boolean>(false);
  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);

  const { t } = useTranslation();
  const pathname = usePathname();
  const { exportListProduct, deleteListItem } = useProduct();
  const { exportListProductCategory } = useProductCategory();
  const storeId = useStoreId();
  const partnerId = StorageService.get("partnerId") as string | null;
  const snackbar = useSnackbar();

  const handleClearFilters = () => {
    onSearchChange("");
    if (onCategoryChange) {
      onCategoryChange("");
    }
    if (onSubCategoryChange) {
      onSubCategoryChange("");
    }
  };

  const hasCategories = Array.isArray(categories) && categories.length > 0;
  const categoryOptions = useMemo(() => {
    if (!hasCategories) return [];
    return categories.map((cat) => ({
      value: cat.categoryId,
      label: cat.name || cat.categoryName || "Unnamed Category",
    }));
  }, [categories]);

  const subCategoryOptions = useMemo(() => {
    if (!categoryId) return [];
    const selectedCat = categories.find((cat) => cat.categoryId === categoryId);
    return (
      selectedCat?.listSubCategory?.map((subCat) => ({
        value: subCat.categoryId,
        label: subCat.name || subCat.categoryName || "Unnamed Sub-category",
      })) || []
    );
  }, [categories, categoryId]);

  const handleExportExcelListProduct = async () => {
    if (!Array.isArray(listItem) || listItem.length === 0) {
      return snackbar.error("Chưa có dữ liệu để xuất file");
    }

    try {
      let response;

      if (pageType === "category") {
        const data = {
          shopId: storeId,
          partnerId: partnerId,
          categoryType: tabValue === 0 ? "Product" : "Service",
          search: searchTerm,
        };
        response = await exportListProductCategory(data);
      } else {
        const data = {
          shopId: storeId,
          type: tabValue === 0 ? "Product" : "Service",
          categoryId: categoryId,
          subCategoryId: subCategoryId,
          search: searchTerm,
        };
        response = await exportListProduct(data);
      }

      await processExcelResponse(response.data);
      snackbar.success("Tải file thành công");
      fetchData();
    } catch (error) {
      console.error("Lỗi khi xuất file:", error);
      snackbar.error("Xuất file thất bại");
    }
  };

  const processExcelResponse = async (responseData): Promise<void> => {
    return new Promise<void>((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = async function () {
        try {
          if (typeof reader.result !== "string") {
            throw new Error("reader.result không phải là string");
          }
          const jsonResponse = JSON.parse(reader.result);

          if (!jsonResponse.data?.link) {
            throw new Error("Không tìm thấy link file trong response JSON");
          }
          await downloadFile(jsonResponse.data.link);
          resolve();
        } catch (error) {
          console.error("Lỗi khi xử lý JSON:", error);
          reject(error);
        }
      };

      reader.onerror = reject;
      reader.readAsText(responseData);
    });
  };

  const downloadFile = async (fileUrl): Promise<void> => {
    const fileName = fileUrl.split("/").pop();
    const fileResponse = await fetch(fileUrl);
    const fileBlob = await fileResponse.blob();

    const url = window.URL.createObjectURL(fileBlob);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", fileName);
    document.body.appendChild(link);
    link.click();

    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  const confirmDeleteMultiple = async () => {
    const body: IBodyDeleteListItem = {
      shopId: storeId,
      itemsType: tabValue === 0 ? "Product" : "Service",
      data: selectedIds,
    };
    const res = await deleteListItem(body);
    console.log({ res });
    if (res?.status === 200) {
      setIsOpenModalDelete(false);
      snackbar.success(`Xoá ${tabValue === 0 ? " sản phẩm " : " dịch vụ "}thành công`);
      fetchData();
      setSelectedIds([]);
    }
  };

  return (
    <Box
      sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        mb: 3,
        gap: 2,
        flexWrap: "wrap",
      }}
    >
      <Box
        sx={{
          display: "flex",
          gap: 2,
          flexDirection: { xs: "column", sm: "row" },
          flex: 1,
        }}
      >
        <TextField
          placeholder={placeholder}
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          variant="outlined"
          size="small"
          sx={{
            flex: "0 0 auto",
            width: { xs: "100%", sm: "300px" },
            "& .MuiOutlinedInput-root": {
              borderRadius: "8px",
              height: "40px",
            },
          }}
          slotProps={{
            input: {
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: "text.disabled" }} />
                </InputAdornment>
              ),
            },
          }}
        />

        {categories.length > 0 && onCategoryChange && (
          <CustomSelect
            // label={t(tokens.contentManagement.search.category)}
            value={categoryId}
            onChange={(e) => {
              onCategoryChange(e.target.value);
              if (onSubCategoryChange) onSubCategoryChange("");
            }}
            options={categoryOptions}
            sx={{
              width: { xs: "100%", sm: "300px" },
              flex: "0 0 auto",
              "& .MuiOutlinedInput-root": {
                height: "40px",
              },
            }}
          />
        )}

        {categoryId && subCategoryOptions.length > 0 && (
          <CustomSelect
            // label={"Danh mục con"}
            value={subCategoryId}
            onChange={(e) => onSubCategoryChange && onSubCategoryChange(e.target.value)}
            options={subCategoryOptions}
            sx={{
              minWidth: 250,
              maxWidth: 250,
              flex: "0 0 auto",
              "& .MuiOutlinedInput-root": {
                height: "40px",
              },
            }}
          />
        )}

        {(categoryId || searchTerm) && (
          <IconButton onClick={handleClearFilters}>
            <ClearIcon />
          </IconButton>
        )}
      </Box>

      <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap", width: { xs: "100%", sm: "auto" } }}>
        <Box
          sx={{
            display: "flex",
            gap: 1,
            width: { xs: "100%", sm: "auto" },
            justifyContent: "space-between",
          }}
        >
          <ActionButton
            permission={PERMISSION_TYPE_ENUM.Import}
            tooltip="Bạn không có quyền import"
            startIcon={<FileUpload />}
            onClick={() => setIsOpenModalImport(true)}
            isGranted={isGranted}
            pathname={pathname}
            size="medium"
            color="primary"
            variant="outlined"
            customSx={{
              width: { xs: "calc(50% - 4px)", sm: "auto" },
              height: "40px",
              px: { xs: 1, sm: 2 },
            }}
          >
            Import
          </ActionButton>

          <ActionButton
            permission={PERMISSION_TYPE_ENUM.Export}
            tooltip="Bạn không có quyền export"
            startIcon={<Download />}
            onClick={handleExportExcelListProduct}
            isGranted={isGranted}
            pathname={pathname}
            size="medium"
            color="primary"
            variant="outlined"
            customSx={{
              width: { xs: "calc(50% - 4px)", sm: "auto" },
              height: "40px",
              px: { xs: 1, sm: 2 },
            }}
          >
            Export
          </ActionButton>
        </Box>

        {Array.isArray(selectedIds) && selectedIds.length > 0 && (
          <ActionButton
            permission={PERMISSION_TYPE_ENUM.Delete}
            tooltip="Bạn không có quyền xóa"
            startIcon={<DeleteOutline />}
            onClick={() => setIsOpenModalDelete(true)}
            isGranted={isGranted}
            pathname={pathname}
            size="medium"
            color="error"
            variant="outlined"
            customSx={{
              height: "40px",
              minWidth: "auto",
              px: 2,
            }}
          >
            Xoá ({selectedIds?.length})
          </ActionButton>
        )}

        {selectedCount > 0 && onDeleteClick && (
          <ActionButton
            permission={PERMISSION_TYPE_ENUM.Delete}
            tooltip="Bạn không có quyền xoá"
            startIcon={<DeleteOutline />}
            onClick={onDeleteClick}
            isGranted={isGranted}
            pathname={pathname}
            size="medium"
            color="error"
            variant="outlined"
            customSx={{
              height: "40px",
            }}
          >
            Xóa ({selectedCount})
          </ActionButton>
        )}

        <ActionButton
          permission={PERMISSION_TYPE_ENUM.Add}
          tooltip="Bạn không có quyền tạo mới"
          startIcon={<Add />}
          onClick={onAddClick}
          isGranted={isGranted}
          pathname={pathname}
          size="medium"
          color="primary"
          variant="contained"
          customSx={{
            width: { xs: "calc(50% - 4px)", sm: "auto" },
            height: "40px",
            minWidth: "auto",
            px: 2,
          }}
        >
          {addButtonText}
        </ActionButton>
      </Box>

      <ModalImportExcelItems
        open={isOpenModalImport}
        setOpen={setIsOpenModalImport}
        tabValue={tabValue}
        fetchData={fetchData}
      />
      {pageType === "category" && (
        <ModalImportExcelCategory
          open={isOpenModalImport}
          setOpen={setIsOpenModalImport}
          tabValue={tabValue}
          fetchData={fetchData}
        />
      )}

      <Dialog
        open={isOpenModalDelete}
        onClose={() => setIsOpenModalDelete(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Xác nhận xóa nhiều {tabValue === 0 ? " sản phẩm" : " dịch vụ"}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {`Bạn có chắc chắn muốn xóa ${selectedIds?.length} ${
              tabValue === 0 ? " sản phẩm" : " dịch vụ"
            } đã chọn không?`}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setIsOpenModalDelete(false)}
            variant="outlined"
            sx={{ minWidth: 100 }}
          >
            Hủy
          </Button>
          <Button
            onClick={confirmDeleteMultiple}
            color="error"
            variant="contained"
            autoFocus
            sx={{ minWidth: 100 }}
          >
            Xóa
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};
