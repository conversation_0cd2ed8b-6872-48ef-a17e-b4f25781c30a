import React, { useCallback } from 'react';
import { TextField, TextFieldProps, InputAdornment } from '@mui/material';
import { formatCurrency, parseCurrency } from '@/src/utils/format-number';

interface PriceTextFieldProps extends Omit<TextFieldProps, 'onChange'> {
  value: number;
  onChange: (value: number) => void;
}

/**
 * PriceTextField Component
 * A custom TextField for handling price inputs with currency formatting
 */
const PriceTextField: React.FC<PriceTextFieldProps> = ({ value, onChange, ...props }) => {
  // Handle price change with formatting
  const handleChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = parseCurrency(event.target.value);
      onChange(newValue);
    },
    [onChange]
  );

  return (
    <TextField
      {...props}
      value={formatCurrency(value)}
      onChange={handleChange}
      InputProps={{
        ...props.InputProps,
        endAdornment: <InputAdornment position="end">đ</InputAdornment>
      }}
    />
  );
};

export default PriceTextField;
