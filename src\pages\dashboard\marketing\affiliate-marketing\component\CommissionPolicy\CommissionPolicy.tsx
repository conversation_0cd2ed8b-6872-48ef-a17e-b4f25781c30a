import React, { createContext, useState } from "react";
import { Box, Paper, Tabs, Tab } from "@mui/material";
import Policy from "./Policy/Policy";
import AdvancedSettings from "./AdvancedSettings/AdvancedSettings";
import { CommissionsConfigData } from "@/src/api/types/affiliation.type";
// Khởi tạo PolicyContext với giá trị mặc định

export const initConfigPolicyData: CommissionsConfigData = {
  basicCommissionsConfig: {
    levelOneCommissionPercentage: 0,
    levelTwoCommissionPercentage: null,
    isActiveLevelTwo: false,
    userGroupCommissionsConfigs: [],
    itemGroupCommissionsConfigs: [],
  },
  advancedCommissionsConfig: {
    isSelfReferralCommission: false,
    selfReferralCommissionPercentage: "0",
    partnerCommExpiry: 99,
    isAutoApproved: false,
    minSpendToApproved: null,
    paymentDue: 1,
  },
  isActive: false,
};

interface AffiliatePolicyContextType {
  commissionsConfigData: CommissionsConfigData;
  setCommissionsConfigData: React.Dispatch<React.SetStateAction<CommissionsConfigData>>;
}
export const AffiliatePolicyContext = createContext<AffiliatePolicyContextType>({
  commissionsConfigData: initConfigPolicyData,
  setCommissionsConfigData: () => {}, // default no-op
});
const CommissionPolicy = () => {
  const [tabValue, setTabValue] = useState(0);
  const [commissionsConfigData, setCommissionsConfigData] =
    useState<CommissionsConfigData>(initConfigPolicyData);
  return (
    <AffiliatePolicyContext.Provider value={{ commissionsConfigData, setCommissionsConfigData }}>
      <Box
        sx={{
          mx: "auto",
          mt: 3,
          background: "#fff",
          borderRadius: "20px",
          overflow: "hidden",
          "@media (max-width: 1200px)": { width: "100%" },
        }}
      >
        <Paper
          sx={{
            borderRadius: 0,
            overflow: "hidden",
            boxShadow: "none",
            borderBottom: "0 !important",
            marginBottom: "25px",
            padding: "0 16px",
          }}
        >
          <Tabs
            value={tabValue}
            onChange={(e, newValue) => setTabValue(newValue)}
            indicatorColor="primary"
          >
            <Tab sx={{ textTransform: "none", paddingBottom: "0" }} label="Chính sách hoa hồng" />
            <Tab sx={{ textTransform: "none", paddingBottom: "0" }} label="Cài đặt nâng cao" />
          </Tabs>
        </Paper>

        {tabValue === 0 && <Policy />}
        {tabValue === 1 && <AdvancedSettings />}
      </Box>
    </AffiliatePolicyContext.Provider>
  );
};

export default CommissionPolicy;
