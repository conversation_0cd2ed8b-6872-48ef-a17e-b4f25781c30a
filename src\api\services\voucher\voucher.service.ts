import { apiClient } from "@/src/api/config/api-client";
import { API_PATHS } from "@/src/api/constants/api-paths";
import type { ApiError, ErrorConfig } from "@/src/api/types/error.types";
import type { ExtendedRequestConfig } from "@/src/api/types/api.types";
import { GetProductCategoryRequestBody } from "@/src/api/types/product-category.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export const voucherService = {
  getProductCategory: async <T = any>(params, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<GetProductCategoryRequestBody, T>(
        `${API_PATHS.PRODUCT_CATEGORY.GET_PRODUCT_CATEGORY}?skip=${params.skip}&limit=${params.limit}&shopId=${params.shopId}&partnerId=${params.partnerId}&categoryType=${params.categoryType}&search=${params.search}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createVoucher: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(API_PATHS.VOUCHER.VOUCHER, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getVoucher: async <T = any>(id: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<any, T>(`${API_PATHS.VOUCHER.VOUCHER}/${id}`, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  listVoucher: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    const queryParams = new URLSearchParams();

    queryParams.set("paging.pageSize", data.limit?.toString() ?? "10");
    queryParams.set("paging.pageIndex", data.skip?.toString() ?? "0");
    if (data.search) queryParams.set("paging.search", data.search.toString());

    if (data.shopId) queryParams.set("shopId", data.shopId);
    if (data.codeType) queryParams.set("codeType", data.codeType);
    if (data.distributionType) queryParams.set("distributionType", data.distributionType);
    if (data.statusVoucher) queryParams.set("statusVoucher", data.statusVoucher);
    if (data.type && data.type.length > 0) {
      data.type.forEach((type) => {
        queryParams.append("type", type);
      });
    }
    try {
      const response = await apiClient.get(
        `${API_PATHS.VOUCHER.VOUCHER}?${queryParams.toString()}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  listVoucherDetail: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    const queryParams = new URLSearchParams({
      pageSize: data.limit.toString(),
      pageIndex: data.skip.toString(),
    });
    try {
      const response = await apiClient.get(
        `${API_PATHS.VOUCHER.VOUCHER_DETAIL}/${data.voucherId}?${queryParams.toString()}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  exportPdf: async <T = any>(voucherId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.get(`${API_PATHS.VOUCHER.EXPORT_PDF}/${voucherId}`, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteVoucher: async <T = any>(voucherIds: string[], errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete<T>(`${API_PATHS.VOUCHER.VOUCHER}`, {
        ...config,
        data: voucherIds,
      });
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  changeActiveVoucher: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        `${API_PATHS.VOUCHER.CHANGE_ACTIVE_VOUCHER}?active=${data.active}`,
        data.voucherIds,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  updateVoucher: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<any, T>(API_PATHS.VOUCHER.VOUCHER, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  listItemsByItemsIds: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.VOUCHER.LIST_ITEMS_BY_ITEMS_ID,
        { shopId: data.shopId, itemsIds: data.itemsIds },
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
