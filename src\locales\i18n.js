/* eslint-disable import/no-named-as-default-member */
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import { de } from './translations/de';
import { en } from './translations/en';
import { es } from './translations/es';
import { vi } from './translations/vi';

const defaultLanguage = 'vi';
const language = (typeof window !== 'undefined' && localStorage.getItem('i18nextLng')) || defaultLanguage;

i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: { translation: en },
      de: { translation: de },
      es: { translation: es },
      vi: { translation: vi }
    },
    lng: language,
    fallbackLng: defaultLanguage,
    interpolation: {
      escapeValue: false
    }
  });

i18n.on('languageChanged', (lng) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('i18nextLng', lng);
  }
});
