import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Typography,
  TextField,
  Button,
  RadioGroup,
  FormControlLabel,
  Radio,
  CircularProgress,
  Box,
  InputBase,
  Paper,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import { useShop } from "@/src/api/hooks/shop/use-shop";
import { useStoreId } from "@/src/hooks/use-store-id";
import { StorageService } from "nextjs-api-lib";
import { useZaloAuth } from "@/src/api/hooks/zalo/use-zalo-auth";
import { shopSettingService } from "@/src/api/services/shop-setting/shop-setting.service";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";

const schema = yup.object().shape({
  zaloOaName: yup.string().required("Tên Zalo OA là bắt buộc"),
  zaloAppId: yup.string().required("ID của ứng dụng là bắt buộc"),
  zaloSecretKey: yup.string().required("Khóa bí mật là bắt buộc"),
});

const ZaloIntegrationPopup = ({ open, onClose, initialData, onSuccess }) => {
  const snackbar = useSnackbar();
  const storeId = useStoreId();
  const [isLoading, setIsLoading] = useState(false);
  const [showSecretKey, setShowSecretKey] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const {
    control,
    handleSubmit,
    setValue,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      zaloOaName: "",
      zaloAppId: "",
      zaloSecretKey: "",
    },
    mode: "onChange",
  });

  const fetchShopSettings = async () => {
    if (!storeId) return;
    try {
      setIsLoading(true);
      const response = await shopSettingService.getDetailShopSetting(storeId);
      if (response?.data) {
        setValue("zaloAppId", response.data.zaloAppId || "");
        setValue("zaloSecretKey", response.data.zaloSecretKey || "");
        setValue("zaloOaName", response.data.zaloOAName || "");
      } else {
        reset();
      }
    } catch (error) {
      console.error("Error fetching shop settings:", error);
      snackbar.error("Lỗi khi tải thông tin cấu hình");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (open && storeId) {
      fetchShopSettings();
    } else if (!open) {
      reset();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, storeId]);

  const handleToggleShowSecretKey = () => {
    setShowSecretKey((prev) => !prev);
  };

  const onSubmit = async (data) => {
    try {
      setIsSaving(true);
      const payload = {
        shopId: storeId,
        zaloAppId: data.zaloAppId,
        zaloSecretKey: data.zaloSecretKey,
        zaloOAName: data.zaloOaName,
      };
      const response = await shopSettingService.createAndUpdateShopSetting(payload);
      if (response?.status === 200) {
        snackbar.success("Cập nhật thông tin thành công");
        if (onSuccess) onSuccess();

        onClose(true);
        fetchShopSettings();
      }
    } catch (error) {
      snackbar.error(`${error?.detail || error?.message || "Lỗi khi lưu thông tin"}`);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={open} onClose={() => onClose(false)} maxWidth="sm" fullWidth>
      <DialogTitle sx={{ m: 0, p: 2, fontWeight: "bold" }}>
        Liên kết Official Account
        <IconButton
          aria-label="close"
          onClick={() => onClose(false)}
          sx={{ position: "absolute", right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogContent dividers>
          <Typography variant="subtitle1" gutterBottom>
            Chọn hình thức liên kết
          </Typography>
          <RadioGroup defaultValue="app" name="link-type-group">
            <FormControlLabel
              value="app"
              control={
                <Radio
                  sx={{
                    color: "#2654FE",
                    "&.Mui-checked": { color: "#2654FE" },
                  }}
                />
              }
              label="Ứng dụng của bạn"
            />
          </RadioGroup>
          <Box>
            <Typography
              sx={{
                display: "flex",
                mb: 0.5,
                fontWeight: 500,
                fontSize: "15px",
                marginTop: 2,
              }}
            >
              Tên Zalo OA{" "}
              <Typography sx={{ color: "red", marginLeft: 0.5, fontSize: "16px", fontWeight: 500 }}>
                *
              </Typography>
            </Typography>
            <Controller
              name="zaloOaName"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  required
                  fullWidth
                  variant="outlined"
                  margin="normal"
                  sx={{ margin: 0 }}
                  // label="Tên Zalo OA"
                  error={!!errors.zaloOaName}
                  helperText={errors.zaloOaName?.message}
                  disabled={isLoading}
                />
              )}
            />
          </Box>
          <Box>
            <Typography
              sx={{
                display: "flex",
                mb: 0.5,
                fontWeight: 500,
                fontSize: "15px",
                marginTop: 2,
              }}
            >
              ID ứng dụng gốc{" "}
              <Typography sx={{ color: "red", marginLeft: 0.5, fontSize: "16px", fontWeight: 500 }}>
                *
              </Typography>
            </Typography>
            <Controller
              name="zaloAppId"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  required
                  sx={{ margin: 0 }}
                  fullWidth
                  variant="outlined"
                  margin="normal"
                  // label="ID của ứng dụng"
                  error={!!errors.zaloAppId}
                  helperText={errors.zaloAppId?.message}
                  disabled={isLoading}
                />
              )}
            />
          </Box>
          <Box>
            <Typography
              sx={{
                display: "flex",
                mb: 0.5,
                fontWeight: 500,
                fontSize: "15px",
                marginTop: 2,
              }}
            >
              Khóa bí mật của ứng dụng{" "}
              <Typography sx={{ color: "red", marginLeft: 0.5, fontSize: "16px", fontWeight: 500 }}>
                *
              </Typography>
            </Typography>
            <Controller
              name="zaloSecretKey"
              control={control}
              render={({ field }) => (
                <Paper
                  variant="outlined"
                  component="div"
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    width: "100%",
                    borderRadius: 1,
                    border: "1px solid rgba(0, 0, 0, 0.23)",
                    "&:hover": {
                      borderColor: "rgba(0, 0, 0, 0.87)",
                    },
                    height: 56,
                    overflow: "hidden",
                  }}
                >
                  <InputBase
                    {...field}
                    sx={{
                      ml: 1.5,
                      flex: 1,
                      height: "100%",
                      fontSize: "1rem",
                    }}
                    type={showSecretKey ? "text" : "password"}
                    placeholder="Nhập khóa bí mật của ứng dụng"
                    fullWidth
                    disabled={isLoading}
                  />
                  <IconButton
                    sx={{ p: "10px", mr: 0.5 }}
                    aria-label={showSecretKey ? "Ẩn khóa bí mật" : "Hiện khóa bí mật"}
                    onClick={handleToggleShowSecretKey}
                    tabIndex={0}
                  >
                    {showSecretKey ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </Paper>
              )}
            />
            {errors.zaloSecretKey && (
              <Typography color="error" variant="caption">
                {errors.zaloSecretKey.message}
              </Typography>
            )}
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button
            type="submit"
            variant="contained"
            disabled={isSaving || isLoading}
            sx={{
              backgroundColor: "#2654FE",
              "&:hover": { backgroundColor: "#1A3FAB" },
              textTransform: "none",
              minWidth: 100,
            }}
          >
            {isSaving ? <CircularProgress size={24} color="inherit" /> : "Liên kết"}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default ZaloIntegrationPopup;
