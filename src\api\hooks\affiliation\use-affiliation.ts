import { EarnPointConfig } from "./../../types/membership.types";
import { useState } from "react";
import { ErrorHandlerService } from "../../services/error-handler.service";
import { affiliationService } from "../../services/affiliation/affiliation.service";
import {
  GetAffiliationPartnerRequest,
  GetCommissionOrderRequest,
  UpdateAffiliationPartnerRequest,
} from "../../types/affiliation.type";
import useSnackbar from "@/src/hooks/use-snackbar";

export const useAffiliation = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const snackbar = useSnackbar();

  const getReportOverview = async (data: any) => {
    try {
      setLoading(true);
      setError(null);

      const response = await affiliationService.getReportOverview(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getDashboardStatistic = async (data: any) => {
    try {
      setLoading(true);
      setError(null);

      const response = await affiliationService.getDashboardStatistic(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getCommissionsConfig = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await affiliationService.getCommissionsConfig(data);

      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updateLevelTwoStatus = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await affiliationService.updateLevelTwoStatus(data);

      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updateCommissionActiveStatus = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await affiliationService.updateCommissionActiveStatus(data);

      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getAffiliationPartner = async (data: GetAffiliationPartnerRequest) => {
    try {
      setLoading(true);
      setError(null);

      const response = await affiliationService.getAffiliationPartner(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const updateAffiliationPartner = async (
    userId: string,
    data: UpdateAffiliationPartnerRequest
  ) => {
    try {
      setLoading(true);
      setError(null);

      const response = await affiliationService.updateAffiliationPartner(userId, data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const approvalAffiliationPartner = async (userId: string) => {
    try {
      setLoading(true);
      setError(null);

      const response = await affiliationService.approvalAffiliationPartner(userId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getQuickReportAffiliationPartner = async (userId: string) => {
    try {
      setLoading(true);
      setError(null);

      const response = await affiliationService.getQuickReportAffiliationPartner(userId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const exportExcelAffiliationPartner = async (data: GetAffiliationPartnerRequest) => {
    try {
      setLoading(true);
      setError(null);
      const response = await affiliationService.exportExcelAffiliationPartner(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updateCommissionsConfig = async (params: any, data: any) => {
    try {
      setLoading(true);
      setError(null);

      const response = await affiliationService.updateCommissionsConfig(params, data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const getCommissionOrder = async (data: GetCommissionOrderRequest) => {
    try {
      setLoading(true);
      setError(null);
      const response = await affiliationService.getCommissionOrder(data);
      return response;
    } catch (err: any) {
      // const errorResponse = ErrorHandlerService.handle(err, {
      //   showSnackbar: true,
      //   logError: true,
      // });
      // setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const exportExcelCommissionOrder = async (data: GetCommissionOrderRequest) => {
    try {
      setLoading(true);
      setError(null);
      const response = await affiliationService.exportExcelCommissionOrder(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const getPartnerOverview = async (data) => {
    try {
      setLoading(true);
      setError(null);
      const response = await affiliationService.getPartnerOverview(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const exportExcelPartnerOverview = async (data: any) => {
    try {
      setLoading(true);
      setError(null);

      const response = await affiliationService.exportExcelPartnerOverview(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  ///////
  const getCommissionReport = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await affiliationService.getCommissionReport(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const exportExcelCommissionReport = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await affiliationService.exportExcelCommissionReport(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updateRecruitmentPage = async (data: any) => {
    try {
      setLoading(true);
      setError(null);

      const response = await affiliationService.updateRecruitmentPage(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getRecruitmentPage = async (data: any) => {
    try {
      setLoading(true);
      setError(null);

      const response = await affiliationService.getRecruitmentPage(data);
      return response;
    } catch (err: any) {
      console.log({ err });
      if (err.status === 404) {
        snackbar.info("Trang tuyển dụng chưa có");
      } else {
        const errorResponse = ErrorHandlerService.handle(err, {
          showSnackbar: true,
          logError: true,
        });
        setError(errorResponse.detail);
      }
    } finally {
      setLoading(false);
    }
  };

  const getListBank = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await affiliationService.getListBank();
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  return {
    getReportOverview,
    getDashboardStatistic,
    getCommissionsConfig,
    updateCommissionsConfig,
    getAffiliationPartner,
    updateAffiliationPartner,
    exportExcelAffiliationPartner,
    approvalAffiliationPartner,
    getQuickReportAffiliationPartner,
    getCommissionOrder,
    exportExcelCommissionOrder,
    updateLevelTwoStatus,
    updateCommissionActiveStatus,
    getCommissionReport,
    exportExcelCommissionReport,
    getPartnerOverview,
    exportExcelPartnerOverview,
    updateRecruitmentPage,
    getRecruitmentPage,
    getListBank,
    loading,
    error,
  };
};
