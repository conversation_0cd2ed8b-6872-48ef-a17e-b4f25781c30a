import { useState, useEffect, memo, useRef } from "react";
import PropTypes from "prop-types";
import Menu01Icon from "@untitled-ui/icons-react/build/esm/Menu01";
import HistoryIcon from "@mui/icons-material/History";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { alpha } from "@mui/system/colorManipulator";
import Box from "@mui/material/Box";
import IconButton from "@mui/material/IconButton";
import Stack from "@mui/material/Stack";
import SvgIcon from "@mui/material/SvgIcon";
import useMediaQuery from "@mui/material/useMediaQuery";
import { AccountButton } from "../account-button";
import { LanguageSwitch } from "../language-switch";
import { Button, Typography, Menu, MenuItem } from "@mui/material";
import toast from "react-hot-toast";
import { useAppDispatch, useAppSelector } from "@/src/redux/hooks";
import { getProfile } from "@/src/redux/slices/profileSlice";
import { useRouter } from "next/router";
import { useSidebar } from "src/contexts/sidebar-context";
import { SIDE_NAV_WIDTH, SIDE_NAV_COLLAPSED_WIDTH } from "src/components/main-layout";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useDomainName } from "@/src/api/hooks/domain-name/use-domain-name";
const TOP_NAV_HEIGHT = 64;

const TopNavComponent = (props) => {
  const dispatch = useAppDispatch();
  const { profile } = useAppSelector((state) => state.profile);
  const { onMobileNavOpen, ...other } = props;
  const lgUp = useMediaQuery((theme) => theme.breakpoints.up("lg"));
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("sm"));
  const [Notification, setNotification] = useState(0);
  const profileFetchedRef = useRef(false);
  const router = useRouter();
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const isMenuOpen = Boolean(menuAnchorEl);
  const { isCollapsed } = useSidebar();
  const [domainNameList, setDomainNameList] = useState([]);
  const { listShopDomainName } = useDomainName();
  const storeId = useStoreId();
  const fetchDomainNames = async (shopId) => {
    try {
      const response = await listShopDomainName(storeId);
      if (response && response.data) {
        setDomainNameList(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching domain names:", error);
    }
  };

  useEffect(() => {
    if (storeId) {
      fetchDomainNames(storeId);
    }
  }, [storeId]);
  useEffect(() => {
    if (!profileFetchedRef.current) {
      dispatch(getProfile());
      profileFetchedRef.current = true;
    }
    const fakeNotification = "2";
    setNotification(fakeNotification);
  }, [dispatch]);

  const handleMenuOpen = (event) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const navActions = [
    {
      id: "history",
      label: "Lịch sử giao dịch",
      icon: <HistoryIcon fontSize="small" />,
      action: () => router.push("/dashboard/account/balance-history"),
    },
    {
      id: "feedback",
      label: "Góp ý",
      icon: <img src="/assets/icon-2-smile.png" alt="Góp ý" />,
      action: () => toast.error("Chức năng này đang được phát triển"),
    },
    {
      id: "contact",
      label: "Liên hệ",
      icon: <img src="/assets/phone-header.svg" alt="Liên hệ" />,
      action: () => {
        const domain = domainNameList?.[0]?.domain;

        if (domain && domainNameList.length > 0) {
          const url = `https://${domain}`;
          window.open(url, "_blank");
        } else {
          toast.error("Vui lòng cài đặt tên miền trước");
        }
      },
    },
    {
      id: "notification",
      label: "Thông báo",
      icon: (
        <Box sx={{ position: "relative" }}>
          <img src="/assets/notification-header.svg" alt="Thông báo" />
          <Typography
            color="#FFFFFF"
            fontSize={"8px"}
            sx={{
              background: "#2654FE",
              borderRadius: "50%",
              border: "2px solid #CED8FF",
              position: "absolute",
              width: "15px",
              height: "15px",
              top: "-6px",
              right: "-6px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              p: 0,
            }}
          >
            {Notification.toLocaleString()}
          </Typography>
        </Box>
      ),
      action: () => toast.error("Chức năng này đang được phát triển"),
    },
  ];

  const desktopIconButtonStyle = {
    padding: "0 12px",
    width: "fit-content",
    minWidth: "unset",
    borderRadius: 0,
    borderRight: "1px solid #DCDCDC",
    height: "37px",
    "@media(max-width: 440px)": {
      padding: "0 8px",
    },
  };

  const balanceButton = (
    <Button
      sx={{
        textTransform: "none",
        background: "#CED8FF",
        color: "#2654FE",
        height: "37px",
        "@media(max-width: 440px)": { fontSize: "10px" },
      }}
      onClick={() => router.push("/dashboard/account/balance-history")}
    >
      Số dư: {Number(profile?.balance || 0).toLocaleString()}đ
    </Button>
  );

  return (
    <Box
      component="header"
      sx={{
        // backdropFilter: "blur(6px)",
        backgroundColor: (theme) => alpha(theme.palette.background.default, 1),
        position: "sticky",
        left: {
          lg: 0,
        },
        paddingLeft: {
          lg: `${isCollapsed ? SIDE_NAV_COLLAPSED_WIDTH : SIDE_NAV_WIDTH}px`,
        },
        transition: "padding-left 0.3s ease",
        top: 0,
        width: {
          lg: `calc(100%)`,
        },
        zIndex: (theme) => theme.zIndex.appBar,
      }}
      {...other}
    >
      <Stack
        alignItems="center"
        direction="row"
        justifyContent="space-between"
        spacing={2}
        sx={{
          minHeight: TOP_NAV_HEIGHT,
          px: 2,
        }}
      >
        <Stack alignItems="center" direction="row" spacing={2}>
          {!lgUp && (
            <IconButton onClick={onMobileNavOpen}>
              <SvgIcon>
                <Menu01Icon />
              </SvgIcon>
            </IconButton>
          )}
        </Stack>
        <Stack alignItems="center" direction="row" spacing={2}>
          {isMobile ? (
            <>
              {balanceButton}
              <IconButton id="mobile-menu-button" onClick={handleMenuOpen}>
                <SvgIcon>
                  <MoreVertIcon />
                </SvgIcon>
              </IconButton>
              <Menu
                id="basic-menu"
                anchorEl={menuAnchorEl}
                open={isMenuOpen}
                onClose={handleMenuClose}
                MenuListProps={{ "aria-labelledby": "mobile-menu-button" }}
                disableScrollLock={true}
                getContentAnchorEl={null}
                anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
                transformOrigin={{ vertical: "top", horizontal: "right" }}
              >
                {navActions.map((item) => (
                  <MenuItem
                    key={item.id}
                    onClick={() => {
                      handleMenuClose();
                      item.action();
                    }}
                  >
                    <Stack direction="row" alignItems="center" spacing={1.5}>
                      {item.icon}
                      <span>{item.label}</span>
                    </Stack>
                  </MenuItem>
                ))}
              </Menu>
            </>
          ) : (
            <Stack direction="row">
              {balanceButton}
              {navActions
                .filter((item) => item.id !== "history")
                .map((item) => (
                  <Button key={item.id} sx={desktopIconButtonStyle} onClick={item.action}>
                    {item.icon}
                  </Button>
                ))}
            </Stack>
          )}
          <LanguageSwitch />
          <AccountButton />
        </Stack>
      </Stack>
    </Box>
  );
};

TopNavComponent.propTypes = {
  onMobileNavOpen: PropTypes.func,
};

export const TopNav = memo(TopNavComponent);
