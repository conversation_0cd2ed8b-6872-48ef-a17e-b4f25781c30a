import {
  Card,
  CardContent,
  Ava<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import { Box } from "@mui/system";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { tokens } from "../locales/tokens";
import { getBusinessTypeText, BusinessType } from "../types/store/enum-type";
import { formatDateDisplay } from "../utils/date-utils";
import TruncatedText from "./truncated-text/truncated-text";
import EventIcon from "@mui/icons-material/Event";
import BusinessCenterIcon from "@mui/icons-material/BusinessCenter";
import DeleteIcon from "@mui/icons-material/Delete";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";

interface StoreCardProps {
  store: any;
  onDetailsClick: (id: string) => void;
  onDelete: (id: string) => void;
}

export const StoreCard: React.FC<StoreCardProps> = ({ store, onDetailsClick, onDelete }) => {
  const { t } = useTranslation();
  const [openDialog, setOpenDialog] = useState(false);

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setOpenDialog(true);
  };

  const handleConfirmDelete = () => {
    onDelete(store.shopId);
    setOpenDialog(false);
  };

  // Badge trạng thái nhỏ nhất
  const getStatusBadge = () => (
    <Box
      sx={{
        position: "absolute",
        top: 0,
        right: 0,
        background: "#D1FADF",
        color: "#039855",
        borderRadius: "999px",
        px: 1.3,
        mt: 2,
        mr: 2,
        fontWeight: 500,
        fontSize: 11,
        boxShadow: "0 1px 2px 0 rgba(31, 41, 55, 0.06)",
        letterSpacing: 0.2,
        display: "inline-block",
      }}
    >
      {t(tokens.store.activatedLabel)}
    </Box>
  );

  return (
    <>
      <Card
        sx={{
          borderRadius: "12px",
          background: "#fff",
          boxShadow: "0 2px 8px 0 rgba(31, 41, 55, 0.08)",
          position: "relative",
          minHeight: "unset",
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
        }}
      >
        {getStatusBadge()}
        <CardContent sx={{ pb: 1 }}>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Box
              sx={{
                width: 64,
                height: 64,
                mr: 1,
              }}
            >
              {store.shopLogo?.link ? (
                <Avatar
                  src={store.shopLogo?.link}
                  sx={{ width: 64, height: 64, bgcolor: "transparent" }}
                />
              ) : (
                <TruncatedText
                  typographyProps={{ fontWeight: 700, color: "#2563EB", fontSize: 20, width: 200 }}
                  text={
                    store.shopName
                      ? store.shopName
                          .trim()
                          .split(" ")
                          .map((w) => w[0])
                          .join("")
                          .substring(0, 2)
                          .toUpperCase()
                      : ""
                  }
                />
              )}
            </Box>

            <TruncatedText
              typographyProps={{ fontWeight: 700, color: "#1E293B", fontSize: 20 }}
              text={store.shopName}
            />
          </Box>
          <Box sx={{ display: "flex", alignItems: "center", mt: 2 }}>
            <EventIcon sx={{ color: "#94A3B8", fontSize: 12, mr: 0.5, verticalAlign: "middle" }} />
            <Typography fontWeight={500} sx={{ minWidth: 80, color: "#1E293B", fontSize: 14 }}>
              {t(tokens.store.joinDateLabel) || "Ngày tham gia"}:
            </Typography>
            <Typography
              sx={{
                ml: 1,
                fontSize: 14,
                color: "#334155",
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              {store.startDate ? formatDateDisplay(store.startDate) : "-"}
            </Typography>
          </Box>
          <Box sx={{ display: "flex", alignItems: "center", mb: 0.1, mt: 0.5 }}>
            <BusinessCenterIcon
              sx={{ color: "#94A3B8", fontSize: 14, mr: 0.5, verticalAlign: "middle" }}
            />
            <Typography fontWeight={500} sx={{ minWidth: 80, color: "#1E293B", fontSize: 14 }}>
              {t(tokens.store.businessTypeLabel) || "Loại hình kinh doanh"}:
            </Typography>
            <Typography
              sx={{
                ml: 1,
                fontSize: 14,
                color: "#334155",
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              {store.businessType
                ? getBusinessTypeText(store.businessType as BusinessType)
                : "Khác"}
            </Typography>
          </Box>
          {store.address && (
            <Box sx={{ display: "flex", alignItems: "center", mb: 0.1, mt: 0.5 }}>
              <LocationOnIcon
                sx={{ color: "#94A3B8", fontSize: 14, mr: 0.5, verticalAlign: "middle" }}
              />
              <Typography
                fontWeight={500}
                color="#334155"
                sx={{
                  fontSize: 14,
                  verticalAlign: "middle",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                }}
              >
                {store.addressLabel ? t(tokens.store.addressLabel) : "Địa chỉ"}:{" "}
                {store.address || "-"}
              </Typography>
            </Box>
          )}
          <Box sx={{ display: "flex", alignItems: "center", mb: 0.1, mt: 0.5 }}>
            <AccessTimeIcon
              sx={{ color: "#94A3B8", fontSize: 14, mr: 0.5, verticalAlign: "middle" }}
            />
            <Typography
              fontWeight={500}
              color="#334155"
              sx={{
                fontSize: 14,
                verticalAlign: "middle",
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              {store.workingTimeLabel ? t(tokens.store.workingTimeLabel) : "Giờ hoạt động"}:{" "}
              {store.openTime} - {store.closeTime}
            </Typography>
          </Box>
        </CardContent>
        <Box sx={{ px: 2, pb: 2, display: "flex", justifyContent: "flex-end" }}>
          <Button
            variant="contained"
            endIcon={<ArrowForwardIcon sx={{ fontSize: 13 }} />}
            sx={{
              background: "linear-gradient(90deg, #2563EB 0%, #8B5CF6 100%)",
              color: "#fff",
              borderRadius: "10px",
              fontWeight: 700,
              fontSize: 14,
              px: 2,
              py: 0.5,
              minWidth: 80,
              textTransform: "uppercase",
              boxShadow: "0 1px 4px 0 rgba(31, 41, 55, 0.10)",
              "&:hover": {
                background: "linear-gradient(90deg, #1D4ED8 0%, #7C3AED 100%)",
              },
            }}
            onClick={() => onDetailsClick(store.shopId)}
          >
            {t(tokens.store.detailsButton) || "Chi tiết"}
          </Button>
        </Box>
      </Card>

      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="xs" fullWidth>
        <DialogTitle sx={{ pb: 1 }}>{t(tokens.store.deleteConfirmTitle)}</DialogTitle>
        <DialogContent sx={{ pt: 1 }}>
          <Typography>
            {t(tokens.store.deleteConfirmMessage, { storeName: store.shopName })}
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button onClick={() => setOpenDialog(false)} variant="outlined">
            {t(tokens.common.cancel)}
          </Button>
          <Button
            onClick={handleConfirmDelete}
            color="error"
            variant="contained"
            startIcon={<DeleteIcon />}
          >
            {t(tokens.common.delete)}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};
