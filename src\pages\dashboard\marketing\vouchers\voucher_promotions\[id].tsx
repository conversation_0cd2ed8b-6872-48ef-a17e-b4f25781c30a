import React, { useEffect, useState } from "react";
import DashboardLayout from "@/src/layouts/dashboard";
import { useRouter } from "next/router";

import { getProvinces } from "@/src/slices/addressSlice";
import { useDispatch } from "react-redux";

import { AppDispatch } from "@/src/store";
import { useSnackbar } from "@/src/hooks/use-snackbar";

import PromotionForm from "@/src/components/vouchers/PromotionForm";
import { useVoucher } from "@/src/api/hooks/voucher/use-voucher";
import { LoadingBackdrop } from "@/src/components/common/loading-backdrop";

export default function VoucherPromotionDetail() {
  const { getVoucher, loading } = useVoucher();
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();
  const { id } = router.query;
  const [voucher, setVoucher] = useState(null);
  const snackbar = useSnackbar();
  const fetchVoucher = async () => {
    try {
      const response = await getVoucher(id);
      if (response.data.result) {
        setVoucher(response.data.result);
      } else {
        snackbar.error(response.data.message);
      }
    } catch (error) {
    } finally {
    }
  };
  useEffect(() => {
    if (!id) return; // Đảm bảo `id` đã sẵn sàng

    fetchVoucher();
    dispatch(getProvinces());
  }, [id]);

  return (
    <DashboardLayout>
      {loading && <LoadingBackdrop />}
      <PromotionForm voucher={voucher} />
    </DashboardLayout>
  );
}
