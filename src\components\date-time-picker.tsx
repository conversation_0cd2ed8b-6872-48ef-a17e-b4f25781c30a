import React from "react";
import { DateTimePicker as MuiDateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import "dayjs/locale/vi";
import { useTranslation } from "react-i18next";

interface DateTimePickerProps {
  label: string;
  value: Date | null;
  onChange: (date: Date | null) => void;
  onBlur?: () => void;
  minDate?: Date;
  maxDate?: Date;
  error?: boolean;
  helperText?: any;
  sx?: any;
  disabled?: boolean; // TODO: Implement disabled prop and validation for it.
}

export const DateTimePicker: React.FC<DateTimePickerProps> = ({
  label,
  value,
  onChange,
  onBlur,
  minDate,
  maxDate,
  error,
  helperText,
  sx,
  disabled = false, // TODO: Implement disabled prop and validation for it.
}) => {
  const { i18n } = useTranslation();
  const locale = i18n.language === "vi" ? "vi" : "en";
  const dayjsValue = value ? dayjs(value) : null;

  return (
    <div>
      <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale={locale}>
        <MuiDateTimePicker
          label={label}
          value={dayjsValue}
          onChange={(newValue) => onChange(newValue ? newValue.toDate() : null)}
          minDateTime={minDate ? dayjs(minDate) : undefined}
          maxDateTime={maxDate ? dayjs(maxDate) : undefined}
          sx={sx}
          disabled={disabled}
          slotProps={{
            textField: {
              error,
              onBlur,
              fullWidth: true,
              helperText: helperText,
            },
          }}
        />
      </LocalizationProvider>
    </div>
  );
};
