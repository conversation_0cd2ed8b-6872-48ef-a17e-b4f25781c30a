import { Typography, But<PERSON> } from "@mui/material";
import { Box } from "@mui/system";
import { useTranslation } from "react-i18next";
import { tokens } from "../locales/tokens";
import StorefrontIcon from "@mui/icons-material/Storefront";

export const EmptyShop = ({ onCreateStoreClick }) => {
  const { t } = useTranslation();

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        height: "100vh",
        textAlign: "center",
      }}
    >
      <img
        src="/assets/image_empty.png"
        alt="No stores"
        style={{
          width: "100%",
          maxWidth: "200px",
          height: "auto",
          margin: "0 auto",
        }}
      />
      <Typography variant="body1" color="text.secondary" sx={{ mt: 2 }}>
        {t(tokens.store.emptyStateMessage)}
      </Typography>
      <Button
        variant="contained"
        startIcon={<StorefrontIcon />}
        onClick={onCreateStoreClick}
        sx={{ mt: 2 }}
      >
        {t(tokens.store.createStoreButton)}
      </Button>
    </Box>
  );
};
