import React from 'react';
import { Box, Stack, Typography } from '@mui/material';

const MorningSchedule = () => {
  const slots = [
    {
      time: '8:00',
      name: '<PERSON><PERSON><PERSON>',
      description: 'Massage cổ vai gáy nâng cao với liệu trình chuyên sâu',
    },
    {
      time: '8:30',
      name: '<PERSON><PERSON><PERSON>',
      description: 'Massage cổ vai gáy nâng cao với liệu trình chuyên sâu',
    },
    {
      time: '9:00',
      name: '<PERSON><PERSON><PERSON>',
      description: 'Massage cổ vai gáy nâng cao với liệu trình chuyên sâu',
    },
    {
      time: '10:00',
      name: '<PERSON><PERSON><PERSON>',
      description: 'Massage cổ vai gáy nâng cao với liệu trình chuyên sâu',
    },
  ];

  return (
    <Stack
      direction="row"
      gap={2}
      mt={2}
      sx={{
        gap: '50px',
        '@media(max-width: 980px)': {
          flexWrap: 'wrap',
          // gap: '2%',
        },
      }}
    >
      {slots.map((slot, index) => (
        <Box
          key={index}
          sx={{
            background: '#F4F5F9',
            borderRadius: '12px',
            padding: '12px',
            minWidth: '150px',
            textAlign: 'center',
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textAlignLast: 'start',
            boxShadow: '0 0px 20px 0 #00000026 !important',
            padding: '20px',
            '@media(max-width: 980px)': {
              width: '45%',
            },
            '@media(max-width: 600px)': {
              width: '100%',
            },
          }}
        >
          <Typography color="#202224" fontSize={'16px'} fontWeight={'400'} mb={'25px'}>
            {slot.time}
          </Typography>
          <Typography
            fontSize={14}
            fontWeight={600}
            sx={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              fontSize: '28px',
              fontWeight: '700',
              color: '#202224',
              marginBottom: '10px',
            }}
          >
            {slot.name}
          </Typography>
          <Typography
            fontSize={12}
            color="#9CA3AF"
            sx={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              maxWidth: '100%',
              color: '#787878',
              fontSize: '16px',
              fontWeight: '500',
            }}
          >
            {slot.description}
          </Typography>
        </Box>
      ))}
    </Stack>
  );
};

export default MorningSchedule;
