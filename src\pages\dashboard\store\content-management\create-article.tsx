import React, { useState, use<PERSON>ffect, use<PERSON><PERSON><PERSON>, ChangeEventHandler } from "react";
import {
  Box,
  Button,
  Card,
  TextField,
  Typography,
  IconButton,
  CircularProgress,
  FormControlLabel,
  debounce,
} from "@mui/material";
import DashboardLayout from "@/src/layouts/dashboard";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useRouter } from "next/router";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useTranslation } from "react-i18next";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import { useStoreId } from "@/src/hooks/use-store-id";
import CustomSwitch from "@/src/components/custom-switch";
import CustomSelect from "@/src/components/custom-select";
import { paths } from "@/src/paths";
import { tokens } from "@/src/locales/tokens";
import TitleTypography from "@/src/components/title-typography/title-typography";
import dynamic from "next/dynamic";
import { useArticle } from "@/src/api/hooks/dashboard/store/use-article";
import { useArticleCategory } from "@/src/api/hooks/dashboard/store/use-article-category";
import { Padding } from "@/src/styles/CommonStyle";
import { FILE_SIZE_5MB, MAX_FILE_IMAGE } from "@/src/constants/constant";
import { CommonMediaUpload } from "@/src/components/common-media-upload";
import { ExistingMediaFile, scrollToTop } from "../../product/product-management/create/create";
import { CreateFileGroupRequest, GetGroupFileRequest, RefType } from "@/src/api/types/media.types";
import { useMedia } from "@/src/api/hooks/media/use-media";
import { ImageProcessor } from "@/src/utils/image-processor";
import { FileType } from "@/src/constants/file-types";
import { ArticleDto } from "@/src/api/services/dashboard/store/article.service";
import { stripHtmlAndSpaces } from "@/src/components/react-quill-editor";
import DOMPurify from "dompurify";

const ReactQuillEditor = dynamic(() => import("@/src/components/react-quill-editor"), {
  ssr: false,
  loading: () => <p>Loading editor...</p>,
});

interface ObjectImage {
  type: string;
  link: string;
}

interface FormValues {
  title: string;
  desc: string;
  content: string;
  categoryId: string;
  image: File[];
  typePublish: "Publish" | "UnPublish";
  status: "Actived" | "InActived";
  typePosition:
    | "BannerHome1"
    | "BannerHome2"
    | "BannerProduct"
    | "BannerAffiliate"
    | "BannerAccount"
    | "Other";
  existingImages?: string[];
  submit: null;
  images?: ObjectImage[];
}

const CreateArticle: React.FC = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const snackbar = useSnackbar();
  const storeId = useStoreId();
  const { articleId } = router.query;
  const { createArticle, updateArticle, getArticleDetail, updateImage } = useArticle();
  const { getArticleCategory } = useArticleCategory();
  const [categories, setCategories] = useState<
    Array<{ articleCategoryId: string; categoryName: string }>
  >([]);
  const [isEdit, setIsEdit] = useState(false);
  const [isPublished, setIsPublished] = useState(false);
  const [imagePreview, setImagePreview] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const [displayPosition, setDisplayPosition] = useState<FormValues["typePosition"]>("Other");
  const [previewUrlsImg, setPreviewUrlsImg] = useState<string[]>([]);
  const [existingFiles, setExistingFiles] = useState<ExistingMediaFile[]>([]);
  const [localFiles, setLocalFiles] = useState<File[]>([]);
  const [defaultGroupId, setDefaultGroupId] = useState<string>("");
  const [errorMsg, setErrorMsg] = useState<string>("");
  const { uploadFile, getGroups } = useMedia();

  useEffect(() => {
    const fetchDefaultGroup = async () => {
      try {
        const data: GetGroupFileRequest = {
          ShopId: storeId,
          Skip: 0,
          Limit: 1,
        };
        const response = await getGroups(data);
        if (response.data?.data?.length > 0) {
          setDefaultGroupId(response.data.data[0].groupFileId);
        }
      } catch (error) {}
    };
    fetchDefaultGroup();
  }, [storeId]);

  // Validation schemas
  const createValidationSchema = Yup.object({
    title: Yup.string()
      .required(t(tokens.contentManagement.article.create.form.titleRequired))
      .max(255, t(tokens.contentManagement.article.create.form.titleMaxLength)),
    desc: Yup.string().max(255, t(tokens.contentManagement.article.create.form.titleMaxLength)),
    categoryId: Yup.string().required(
      t(tokens.contentManagement.article.create.form.categoryRequired)
    ),
    content: Yup.string()
      .required("Nội dung bài viết là bắt buộc")
      .test(
        "max-length-without-html-and-spaces",
        "Nội dung bài viết không được vượt quá 4000 ký tự",
        function (value) {
          const textOnly = stripHtmlAndSpaces(value);
          return textOnly.length <= 4000;
        }
      ),

    image: Yup.mixed().required(t(tokens.contentManagement.article.create.form.imageRequired)),
  });

  const editValidationSchema = Yup.object({
    title: Yup.string().required(t(tokens.contentManagement.article.create.form.titleRequired)),
    desc: Yup.string().max(255, t(tokens.contentManagement.article.create.form.titleMaxLength)),
    categoryId: Yup.string().required(
      t(tokens.contentManagement.article.create.form.categoryRequired)
    ),
    content: Yup.string()
      .required("Nội dung bài viết là bắt buộc")
      .test(
        "max-length-without-html-and-spaces",
        "Nội dung bài viết không được vượt quá 4000 ký tự",
        function (value) {
          const textOnly = stripHtmlAndSpaces(value);
          return textOnly.length <= 4000;
        }
      ),
  });

  const formik = useFormik<FormValues>({
    initialValues: {
      title: "",
      desc: "",
      content: "",
      categoryId: "",
      image: [],
      typePublish: "UnPublish",
      status: "InActived",
      typePosition: "Other",
      existingImages: [],
      submit: null,
      images: [],
    },
    validationSchema: isEdit ? editValidationSchema : createValidationSchema,
    onSubmit: handleSubmit,
  });

  useEffect(() => {
    setIsEdit(Boolean(articleId));
  }, [articleId]);

  useEffect(() => {
    if (storeId) {
      fetchCategories();
    }
  }, [storeId]);

  useEffect(() => {
    const fetchArticleDetail = async () => {
      if (isEdit && articleId) {
        try {
          setIsLoading(true);
          const response = await getArticleDetail(articleId as string);
          const detail = response.data as any;
          formik.setValues({
            title: detail.title || "",
            desc: detail.desc || "",
            content: detail.content || "",
            categoryId: detail.articleCategoryId || "",
            image: [],
            images: detail.images,
            existingImages: detail.images?.map((img) => img.link) || [],
            typePublish: detail.typePublish || "UnPublish",
            status: detail.status || "InActived",
            typePosition: detail.typePosition || "Other",
            submit: null,
          });
          setExistingFiles(detail.images || []);

          setDisplayPosition(detail.typePosition || "Other");

          if (detail?.images?.length > 0) {
            setPreviewUrls(detail.images.map((img) => img.link));
          }

          setIsPublished(detail.typePublish === "Publish");
        } catch (error) {
          console.error("Error fetching article detail:", error);
          snackbar.error(t(tokens.contentManagement.article.edit.error));
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchArticleDetail();
  }, [isEdit, articleId]);

  const fetchCategories = async () => {
    try {
      const response = await getArticleCategory(0, 100);
      if (response?.data?.data) {
        setCategories(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  const handleSelectChange: ChangeEventHandler<HTMLInputElement | HTMLTextAreaElement> = (
    event
  ) => {
    formik.setFieldValue("categoryId", event.target.value);
  };

  const handleCancel = () => {
    router.back();
  };

  const handlePublishChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsPublished(event.target.checked);
  };

  const handlePositionChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDisplayPosition(event.target.value as FormValues["typePosition"]);
  };

  async function handleSubmit(values: FormValues) {
    console.log({ values });
    try {
      if (localFiles.length + existingFiles.length === 0) {
        setErrorMsg("Hãy chọn ít nhất 1 ảnh");
        scrollToTop();
        return;
      }
      let uploadedFiles = [];
      if (localFiles.length > 0) {
        for (const file of localFiles) {
          const processedFile = await ImageProcessor.processImage(file);
          const data: CreateFileGroupRequest = {
            FileUpload: processedFile,
            ShopId: storeId,
            GroupFileId: defaultGroupId,
            RefType: RefType.Article,
            RefId: "",
          };
          const response = await uploadFile(data);
          uploadedFiles.push(response.data);
        }
      }
      // Tạo mảng images mới
      const newImages = uploadedFiles.map((file) => ({
        type: file.type === "IMAGE" ? FileType.IMAGE : FileType.VIDEO,
        link: file.url || file.link,
        mediaFileId: file.mediaFileId,
      }));
      setIsLoading(true);
      const articleData: ArticleDto = {
        shopId: storeId,
        articleCategoryId: values.categoryId,
        title: values.title,
        content: DOMPurify.sanitize(localContent),
        desc: values.desc,
        typePublish: isPublished ? "Publish" : "UnPublish",
        status: isPublished ? "Actived" : "InActived",
        typePosition: displayPosition,
        images:
          newImages.length > 0
            ? newImages
            : existingFiles
                .filter((file) => !!file.mediaFileId)
                .map((file) => ({
                  type: file.type,
                  link: file.link,
                  mediaFileId: file.mediaFileId!,
                })),
      };

      if (isEdit && articleId) {
        await updateArticle({
          articleId: articleId as string,
          ...articleData,
        });

        if (Array.isArray(values?.image)) {
          for (const item of values?.image) {
            await updateImage(articleId as string, item);
          }
        }
        snackbar.success(t(tokens.contentManagement.article.edit.success));
      } else {
        const response = await createArticle({
          articleId: "",
          ...articleData,
        });
        if (response?.data?.articleId && Array.isArray(values?.image)) {
          for (const item of values?.image) {
            await updateImage(response.data.articleId, item);
          }
        }
        snackbar.success(t(tokens.contentManagement.article.create.success));
      }
      router.push(paths.dashboard.store.contentManagement);
    } catch (error) {
      console.error("Error:", error);
      snackbar.error(
        isEdit
          ? t(tokens.contentManagement.article.edit.error)
          : t(tokens.contentManagement.article.create.error)
      );
    } finally {
      setIsLoading(false);
    }
  }

  const selectOptions = categories.map((category) => ({
    value: category.articleCategoryId,
    label: category.categoryName,
  }));

  // Local state for optimizing input performance
  const [localContent, setLocalContent] = useState("");

  // Debounced function to update formik state
  const debouncedUpdateFormik = useCallback(
    debounce((content: string) => {
      formik.setFieldValue("content", content);
    }, 500),
    []
  );

  // Update local state while typing
  const handleContentChange = useCallback(
    (content: string) => {
      debouncedUpdateFormik(content);
    },
    [debouncedUpdateFormik]
  );

  useEffect(() => {
    setLocalContent(formik.values.content);
  }, [formik.values.content]);

  const handleFilesChange = (files: File[]) => {
    setExistingFiles([]);
    setLocalFiles([]);
    if (files.length > MAX_FILE_IMAGE) {
      snackbar.error(`Bạn chỉ có thể tải lên tối đa ${MAX_FILE_IMAGE} tệp hình ảnh.`);
      return;
    }
    // if (files.length > 0 && !isValidImageFile(files[0])) {
    //   snackbar.error(
    //     "Định dạng file không hợp lệ. Hãy chọn file định dạng PNG, JPG, JPEG hoặc JFIF."
    //   );
    //   return;
    // }
    formik.setFieldValue("image", files[0] || null);
    // setShowPopup(true);
    // Cập nhật localFiles và imagePreview

    if (files.length > 0) {
      setLocalFiles(files);
      setImagePreview(URL.createObjectURL(files[0]));
    } else {
      setImagePreview(null);
    }
  };

  const handleRemove = (index: number, isExisting: boolean) => {
    if (isExisting) {
      const updated = [...existingFiles];
      updated.splice(index, 1);
      setExistingFiles(updated);
    } else {
      const localIdx = index - existingFiles.length;
      setLocalFiles((prev) => prev.filter((_, i) => i !== localIdx));
    }
  };

  return (
    <DashboardLayout>
      <Box sx={{ p: Padding }}>
        <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
          <IconButton onClick={handleCancel} sx={{ mr: 2 }}>
            <ArrowBackIcon />
          </IconButton>
          <TitleTypography>
            {"Quản lý nội dung/Popup/" +
              t(
                isEdit
                  ? tokens.contentManagement.article.create.editTitle
                  : tokens.contentManagement.article.create.title
              )}
          </TitleTypography>
          <FormControlLabel
            control={<CustomSwitch checked={isPublished} onChange={handlePublishChange} />}
            label={t(tokens.contentManagement.article.create.form.isActive)}
            labelPlacement="end"
            sx={{ mr: 0, ml: "auto", "& .MuiFormControlLabel-label": { ml: 2 } }}
          />
        </Box>

        <Card sx={{ p: 3 }}>
          <Typography sx={{ display: "flex", fontSize: "16px", fontWeight: 500, mb: 0.5 }}>
            {t(tokens.contentManagement.article.create.form.title)}
            <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
          </Typography>
          <TextField
            fullWidth
            name="title"
            value={formik.values.title}
            onChange={formik.handleChange}
            error={!!(formik.touched.title && formik.errors.title)}
            helperText={formik.touched.title && formik.errors.title}
            sx={{ mb: 2 }}
          />

          {/* <TextField
            fullWidth
            label={t(tokens.contentManagement.article.create.form.description)}
            name="desc"
            value={formik.values.desc}
            onChange={formik.handleChange}
            error={!!(formik.touched.desc && formik.errors.desc)}
            helperText={formik.touched.desc && formik.errors.desc}
            sx={{ mb: 2 }}
          /> */}

          <Typography sx={{ display: "flex", fontSize: "16px", fontWeight: 500, mb: 0.5 }}>
            {t(tokens.contentManagement.article.create.form.category)}
            <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
          </Typography>
          <CustomSelect
            value={formik.values.categoryId}
            onChange={handleSelectChange}
            onBlur={() => formik.setFieldTouched("categoryId", true)}
            options={selectOptions}
            error={!!(formik.touched.categoryId && formik.errors.categoryId)}
            helperText={formik.touched.categoryId && formik.errors.categoryId}
            sx={{ mb: 2 }}
          />

          <Box sx={{ mb: 4 }}>
            <Typography variant="subtitle1" sx={{ mb: 1 }}>
              {t(tokens.contentManagement.article.create.form.imageTitle)}
            </Typography>

            <CommonMediaUpload
              caption="Thêm ảnh"
              maxFiles={1}
              accept={{
                "image/*": [".png", ".jpg", ".jpeg"],
              }}
              maxSize={FILE_SIZE_5MB} // 5MB
              existingFiles={existingFiles}
              localFiles={localFiles}
              setLocalFiles={setLocalFiles}
              onFilesChange={handleFilesChange}
              onRemove={handleRemove}
              defaultGroupId={defaultGroupId}
              setExistingFiles={setExistingFiles}
              isShowPreviewImage={true}
              errorMsg={errorMsg}
              setErrorMsg={setErrorMsg}
            />
          </Box>

          {/* <Box sx={{ mb: 4 }}>
            <Typography variant="subtitle1" sx={{ mb: 2 }}>
              {t("Vị trí hiển thị")}
            </Typography>
            <RadioGroup
              value={displayPosition}
              onChange={handlePositionChange}
              sx={{ display: "flex", flexDirection: "row", gap: 2, flexWrap: "wrap" }}
            >
              <FormControlLabel
                value="BannerHome1"
                control={<Radio />}
                label="Banner trang chủ 1"
              />
              <FormControlLabel
                value="BannerHome2"
                control={<Radio />}
                label="Banner trang chủ 2"
              />
              <FormControlLabel value="BannerProduct" control={<Radio />} label="Banner sản phẩm" />
              <FormControlLabel
                value="BannerAffiliate"
                control={<Radio />}
                label="Banner tiếp thị liên kết"
              />
              <FormControlLabel
                value="BannerAccount"
                control={<Radio />}
                label="Banner tài khoản"
              />
              <FormControlLabel value="Other" control={<Radio />} label="Khác" />
            </RadioGroup>
          </Box> */}

          <Typography variant="subtitle1" sx={{ mt: 2, mb: 1, display: "flex" }}>
            {t(tokens.contentManagement.article.create.form.content)}{" "}
            <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
          </Typography>
          <ReactQuillEditor
            value={formik.values.content || ""}
            onChange={handleContentChange}
            shopId={storeId}
            defaultGroupId={defaultGroupId}
            error={formik.errors.content}
          />

          <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end", marginTop: "30px" }}>
            <Button
              onClick={handleCancel}
              variant="outlined"
              sx={{ minWidth: 120, color: "#2654FE", borderColor: "#2654FE" }}
            >
              {t(tokens.contentManagement.article.create.form.cancelButton)}
            </Button>
            <Button
              variant="contained"
              onClick={() => formik.handleSubmit()}
              disabled={formik.isSubmitting || isLoading}
              sx={{ minWidth: 120, background: "#2654FE" }}
            >
              {formik.isSubmitting || isLoading ? (
                <CircularProgress size={24} />
              ) : (
                t(tokens.contentManagement.article.create.form.saveButton)
              )}
            </Button>
          </Box>
        </Card>
      </Box>
    </DashboardLayout>
  );
};

export default CreateArticle;
