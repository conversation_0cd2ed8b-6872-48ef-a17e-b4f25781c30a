import React from "react";
import { Box } from "@mui/material";

const ColorPicker = ({ selectedTheme, onThemeChange }) => {
  const colors = [
    ["#FF6B6B", "#FFD6D6"],
    ["#FFA726", "#FFCC80"],
    ["#FF8A80", "#FFE0E0"],
    ["#FF5252", "#B0BEC5"],
    ["#FFD740", "#FFECB3"],
    ["#26A69A", "#B2DFDB"],
    ["#66BB6A", "#9CCC65"],
    ["#81C784", "#C8E6C9"],
    ["#42A5F5", "#90CAF9"],
    ["#D4E157", "#FFF9C4"],
    ["#546E7A", "#ECEFF1"],
    ["#7E57C2", "#D1C4E9"],
    ["#F44336", "#FF8A65"],
  ];

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
      <Box sx={{ display: "flex", gap: 1 }}>
        {colors.slice(0, 7).map(([main, sub], index) => (
          <Box
            key={index}
            display="flex"
            flexDirection="row"
            border={`2px solid ${
              selectedTheme === index ? "blue" : "transparent"
            }`}
            borderRadius={1}
            overflow="hidden"
            sx={{ cursor: "pointer", width: "30px", height: "30px" }}
            onClick={() => onThemeChange(index)}
          >
            <Box sx={{ backgroundColor: main, width: "50%", height: "100%" }} />
            <Box sx={{ backgroundColor: sub, width: "50%", height: "100%" }} />
          </Box>
        ))}
      </Box>
      <Box sx={{ display: "flex", gap: 1 }}>
        {colors.slice(7).map(([main, sub], index) => (
          <Box
            key={index + 7}
            display="flex"
            flexDirection="row"
            border={`2px solid ${
              selectedTheme === index + 7 ? "blue" : "transparent"
            }`}
            borderRadius={1}
            overflow="hidden"
            sx={{ cursor: "pointer", width: "30px", height: "30px" }}
            onClick={() => onThemeChange(index + 7)}
          >
            <Box sx={{ backgroundColor: main, width: "50%", height: "100%" }} />
            <Box sx={{ backgroundColor: sub, width: "50%", height: "100%" }} />
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default ColorPicker;
