import React, { useState } from 'react';
import {
  Container,
  Typography,
  Paper,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Box,
  List,
  ListItem,
  Link,
  Button,
  MenuItem,
  TextField,
  InputAdornment,
  useMediaQuery,
} from '@mui/material';
import DashboardLayout from '../../../../layouts/dashboard';
import { useRouter } from 'next/router';
import SearchIcon from '@mui/icons-material/Search';
import UploadOutlined from '@mui/icons-material/UploadOutlined';
import { CalendarToday } from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  Tooltip,
  CartesianGrid,
  ResponsiveContainer,
} from 'recharts';

const data = {
  overview: {
    'tổng lượt truy cập': 200,
    'Khách hàng đã truy cập qua liên kết': 100,
    '<PERSON><PERSON><PERSON> hàng thành công': 20,
    '<PERSON><PERSON><PERSON> thu': '20.000.000đ',
  },
  chartData: [
    { time: '0:00', value: 0 },
    { time: '2:00', value: 0 },
    { time: '4:00', value: 0 },
    { time: '6:00', value: 0 },
    { time: '8:00', value: 0 },
    { time: '10:00', value: 0 },
    { time: '12:00', value: 0 },
    { time: '14:00', value: 0 },
    { time: '16:00', value: 0 },
    { time: '18:00', value: 0 },
    { time: '20:00', value: 0 },
    { time: '22:00', value: 0 },
  ],
  metrics: {
    "Tỷ lệ 'Thêm vào giỏ hàng'": '0,000%',
    'Tỷ lệ chuyển đổi': '0,000%',
    'Đối tác thêm mới': 0,
  },
  rankings: [
    {
      id: 1,
      code: 'DT001',
      name: 'Ngô Tuấn',
      totalCustomers: 50,
      successfulOrders: 10,
      revenue: '5.000.000đ',
      averageOrderValue: '500.000đ',
      totalCommission: '500.000đ',
    },
    {
      id: 2,
      code: 'DT002',
      name: 'Tâm Văn',
      totalCustomers: 40,
      successfulOrders: 8,
      revenue: '4.000.000đ',
      averageOrderValue: '500.000đ',
      totalCommission: '400.000đ',
    },
    {
      id: 3,
      code: 'DT003',
      name: 'A',
      totalCustomers: 30,
      successfulOrders: 5,
      revenue: '3.000.000đ',
      averageOrderValue: '600.000đ',
      totalCommission: '300.000đ',
    },
    {
      id: 4,
      code: 'DT004',
      name: 'B',
      totalCustomers: 20,
      successfulOrders: 4,
      revenue: '2.000.000đ',
      averageOrderValue: '500.000đ',
      totalCommission: '200.000đ',
    },
    {
      id: 5,
      code: 'DT005',
      name: 'C',
      totalCustomers: 10,
      successfulOrders: 2,
      revenue: '1.000.000đ',
      averageOrderValue: '500.000đ',
      totalCommission: '100.000đ',
    },
  ],
};

export const Sidebar = () => {
  const router = useRouter();
  const menuItems = [
    { id: 'overview', label: 'Tổng quan', url: '/dashboard/marketing/affiliate' },
    {
      id: 'object-list',
      label: 'Danh sách đối tác',
      url: '/dashboard/marketing/affiliate/list-customer',
    },
    {
      id: 'partner-orders',
      label: 'Đơn hàng đối tác',
      url: '/dashboard/marketing/affiliate/order-customer',
    },
    {
      id: 'commission',
      label: 'Báo cáo hoa hồng',
      url: '/dashboard/marketing/affiliate/commission-report',
    },
  ];
  const menuItems2 = [
    {
      id: 'recruitment',
      label: 'Trang tuyển dụng',
      url: '/dashboard/marketing/affiliate/setup/recruitment-page',
    },
    {
      id: 'commission-policy',
      label: 'Chính sách hoa hồng',
      url: '/dashboard/marketing/affiliate/setup/commission-policy/commission-policy',
    },
  ];

  const isActive = (url) => {
    const currentPath = router.pathname;
    if (url === '/dashboard/marketing/affiliate') {
      return currentPath === url || currentPath === url + '/';
    }
    return currentPath.includes(url);
  };

  return (
    <Paper
      sx={{
        padding: { xs: '10px', md: '15px 25px' },
        width: { xs: '100%', lg: '30%' },
        boxShadow: 'none',
        mb: { xs: 2, lg: 0 },
        borderRight: '1px solid #E0E0E0',
        borderRadius: '0px',
      }}
    >
      <Typography
        variant="h3"
        sx={{
          color: '#000',
          fontSize: { xs: '18px', md: '20px' },
          fontWeight: '700',
          mb: 2,
          textAlign: { xs: 'center', md: 'left' },
        }}
      >
        Đối tác kinh doanh
      </Typography>
      <List sx={{ pl: 2, listStyleType: 'disc', paddingLeft: { xs: '0px', md: '0px' } }}>
        <Typography
          sx={{ fontSize: '20px', fontWeight: '400', color: '#000000', marginBottom: '16px' }}
        >
          Quản lý
        </Typography>
        {menuItems.map((item) => {
          const active = isActive(item.url);
          return (
            <ListItem
              key={item.id}
              component="li"
              sx={{
                padding: { xs: '0 0 10px 0', md: '0 0 20px 0' },
                cursor: 'pointer',
                transition: 'color 0.2s ease-in-out',
              }}
            >
              <Link
                href={item.url}
                style={{
                  textDecoration: 'none',
                  color: active ? '#2654FE' : '#000000',
                  fontFamily: 'Inter',
                  fontWeight: 400,
                  fontSize: '16px',
                  lineHeight: '19.36px',
                }}
              >
                {item.label}
              </Link>
            </ListItem>
          );
        })}
      </List>
      <List sx={{ pl: 2, listStyleType: 'disc', paddingLeft: { xs: '0px', md: '0px' } }}>
        <Typography
          sx={{ fontSize: '20px', fontWeight: '400', color: '#000000', marginBottom: '16px' }}
        >
          Thiết lập
        </Typography>
        {menuItems2.map((item) => {
          const active = isActive(item.url);
          return (
            <ListItem
              key={item.id}
              component="li"
              sx={{
                padding: { xs: '0 0 10px 0', md: '0 0 20px 0' },
                cursor: 'pointer',
                transition: 'color 0.2s ease-in-out',
              }}
            >
              <Link
                href={item.url}
                style={{
                  textDecoration: 'none',
                  color: active ? '#2654FE' : '#000000',
                  fontFamily: 'Inter',
                  fontWeight: 400,
                  fontSize: '16px',
                  lineHeight: '19.36px',
                }}
              >
                {item.label}
              </Link>
            </ListItem>
          );
        })}
      </List>
    </Paper>
  );
};

const Dashboard = () => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(2);
  const [anchorEl, setAnchorEl] = useState(null);
  const [dateFilter, setDateFilter] = useState('Hôm nay');

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const visibleRows = data.rankings.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  const isMobile = useMediaQuery('(max-width:767px)');

  const handleDateFilterClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  return (
    <DashboardLayout>
      <Container
        maxWidth={false}
        sx={{
          display: 'flex',
          gap: { xs: '10px', md: '20px' },
          p: { xs: 1, md: 0 },
          m: 0,
          flexDirection: { xs: 'column', lg: 'row' },
        }}
      >
        <Sidebar />
        <Box
          sx={{
            flexGrow: 1,
            padding: { xs: '15px' },
            width: { xs: '100%', lg: '70%' },
            marginBottom: '80px',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              gap: 1,
              mb: 3,
              '@media (max-width: 767px)': { flexDirection: 'column', gap: 2 },
            }}
          >
            <Typography
              variant="h5"
              sx={{ fontWeight: 700, fontSize: '20px', fontFamily: 'Inter' }}
            >
              Tổng quan dữ liệu
            </Typography>
            <Box
              sx={{
                display: 'flex',
                gap: 1,
                '@media (max-width: 767px)': { width: '100%', gap: 3 },
              }}
            >
              <Button
                variant={dateFilter === 'Hôm nay' ? 'contained' : 'outlined'}
                onClick={() => handleDateFilterClick('Hôm nay')}
                sx={{
                  backgroundColor: dateFilter === 'Hôm nay' ? '#2654FE' : 'transparent',
                  color: dateFilter === 'Hôm nay' ? '#fff' : '#787878',
                  borderColor: '#D9D9D9',
                  fontSize: '14px',
                  textTransform: 'none',
                  fontFamily: 'Inter',
                  borderRadius: '0px',
                  fontWeight: 400,
                }}
              >
                Hôm nay
              </Button>
              <Button
                variant={dateFilter === '7 ngày' ? 'contained' : 'outlined'}
                onClick={() => handleDateFilterClick('7 ngày')}
                sx={{
                  backgroundColor: dateFilter === '7 ngày' ? '#2654FE' : 'transparent',
                  color: dateFilter === '7 ngày' ? '#fff' : '#787878',
                  borderColor: '#D9D9D9',
                  fontSize: '14px',
                  textTransform: 'none',
                  fontFamily: 'Inter',
                  borderRadius: '0px',
                  fontWeight: 400,
                }}
              >
                7 ngày
              </Button>
              <Button
                variant={dateFilter === '30 ngày' ? 'contained' : 'outlined'}
                onClick={() => handleDateFilterClick('30 ngày')}
                sx={{
                  backgroundColor: dateFilter === '30 ngày' ? '#2654FE' : 'transparent',
                  color: dateFilter === '30 ngày' ? '#fff' : '#787878',
                  borderColor: '#D9D9D9',
                  fontSize: '14px',
                  textTransform: 'none',
                  fontFamily: 'Inter',
                  borderRadius: '0px',
                  fontWeight: 400,
                }}
              >
                30 ngày
              </Button>
            </Box>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Box>
                <TextField
                  select
                  value={dateFilter}
                  onChange={(event) => handleDateFilterClick(event.target.value)}
                  variant="outlined"
                  sx={{ width: 140, fontSize: '14px', fontFamily: 'Inter' }}
                >
                  <MenuItem value="Hôm nay">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CalendarToday sx={{ fontSize: '16px' }} />
                      Hôm nay
                    </Box>
                  </MenuItem>
                  <MenuItem value="7 ngày">7 ngày</MenuItem>
                  <MenuItem value="30 ngày">30 ngày</MenuItem>
                </TextField>
              </Box>
            </Box>
          </Box>

          <Grid
            container
            spacing={2}
            sx={{
              mb: 4,
              display: 'flex',
              justifyContent: 'space-between',
              marginTop: '20px',
              '@media (max-width: 767px)': { padding: '20px' },
            }}
          >
            {Object.entries(data.overview).map(([key, value], index, array) => (
              <Grid
                item
                key={key}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '24%',
                  padding: ' 0px  !important',
                  '@media (max-width: 767px)': { width: '50%' },
                }}
              >
                {!isMobile && index !== 0 && (
                  <Box sx={{ height: '100%', width: '1px', backgroundColor: '#E0E0E0', mx: 2 }} />
                )}
                <Paper
                  elevation={0}
                  sx={{
                    p: 2,
                    textAlign: 'left',
                    borderRadius: '8px',
                    boxShadow: 'none',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'flex-start',
                    padding: '15px 0px !important',
                  }}
                >
                  <Typography
                    variant="subtitle2"
                    sx={{
                      fontSize: '14px',
                      color: '#000000',
                      mb: 1,
                      fontFamily: 'Inter',
                      fontWeight: 400,
                    }}
                  >
                    {key}
                  </Typography>
                  <Typography
                    variant="h6"
                    sx={{
                      fontSize: '24px',
                      fontWeight: 700,
                      fontFamily: 'Inter',
                      color: '#000000',
                    }}
                  >
                    {value}
                  </Typography>
                </Paper>
              </Grid>
            ))}
          </Grid>

          <Box
            sx={{
              width: '100%',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              padding: '20px',
            }}
          >
            <Paper sx={{ marginBottom: '20px', boxShadow: 'none' }}>
              <ResponsiveContainer width="100%" height={200}>
                <LineChart
                  data={data.chartData}
                  margin={{ top: 5, right: 30, left: -30, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
                  <XAxis dataKey="time" stroke="#666" fontSize={12} />
                  <YAxis
                    domain={[0, 1]}
                    ticks={[0, 0.2, 0.4, 0.6, 0.8, 1]}
                    stroke="#666"
                    fontSize={12}
                  />
                  <Tooltip
                    formatter={(value, name, props) => [
                      `${props.payload.date || '2024-12-20'}: ${value}`,
                    ]}
                  />
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke="#2654FE"
                    strokeWidth={2}
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </Paper>

            <Grid
              container
              spacing={0}
              sx={{ mb: 4, border: '1px solid #E0E0E0', borderRadius: '8px', overflow: 'hidden' }}
            >
              {Object.entries(data.metrics).map(([key, value], index, array) => (
                <Grid
                  item
                  xs={12}
                  sm={4}
                  key={key}
                  sx={{
                    borderRight: index !== array.length - 1 ? '1px solid #E0E0E0' : 'none',
                    borderBottom: 'none',
                  }}
                >
                  <Paper
                    sx={{
                      p: 2,
                      textAlign: 'center',
                      boxShadow: 'none',
                      border: 'none',
                    }}
                  >
                    <Typography
                      variant="subtitle2"
                      sx={{ fontSize: '14px', color: '#666', mb: 1, fontFamily: 'Inter' }}
                    >
                      {key}
                    </Typography>
                    <Typography
                      variant="h6"
                      sx={{ fontSize: '20px', fontWeight: 700, fontFamily: 'Inter' }}
                    >
                      {value}
                    </Typography>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </Box>

          <Box
            sx={{
              width: '100%',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              padding: '20px',
              marginTop: '20px',
            }}
          >
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: 2,
                '@media (max-width: 767px)': { flexDirection: 'column', gap: 2 },
              }}
            >
              <Typography
                variant="h6"
                sx={{ fontWeight: 700, fontSize: '18px', fontFamily: 'Inter' }}
              >
                Danh sách đối tác
              </Typography>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  '@media (max-width: 767px)': { width: '100%', flexDirection: 'column', gap: 2 },
                }}
              >
                <TextField
                  variant="outlined"
                  placeholder="Tìm kiếm theo tên hoặc"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon sx={{ color: '#BFBFBF' }} />
                      </InputAdornment>
                    ),
                    sx: {
                      borderRadius: '10px',
                      backgroundColor: '#fff',
                      border: '1px solid #D9D9D9',
                      fontFamily: 'Inter',
                      padding: '0px 10px',
                      '& fieldset': { border: 'none' },
                      '& input': { padding: '0px', height: '56px' },
                      '@media (max-width: 767px)': { width: '100%' },
                    },
                  }}
                  sx={{ width: '100%' }}
                />
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    '@media (max-width: 767px)': { width: '100%' },
                  }}
                >
                  <TextField
                    select
                    value={dateFilter}
                    onChange={(event) => handleDateFilterClick(event.target.value)}
                    variant="outlined"
                    sx={{
                      width: 170,
                      fontSize: '14px',
                      fontFamily: 'Inter',
                      padding: '0px !important',
                      '@media (max-width: 767px)': { width: '50%' },
                    }}
                  >
                    <MenuItem
                      value="Hôm nay"
                      sx={{
                        padding: '10px !important',
                        '& .MuiTypography-root': { padding: '10px !important' },
                      }}
                    >
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          padding: '0px !important',
                          '& .MuiTypography-root': { padding: '0px !important' },
                        }}
                      >
                        <CalendarToday sx={{ fontSize: '16px' }} />
                        Hôm nay
                      </Box>
                    </MenuItem>
                    <MenuItem value="7 ngày">7 ngày</MenuItem>
                    <MenuItem value="30 ngày">30 ngày</MenuItem>
                  </TextField>
                  <Button
                    variant="contained"
                    startIcon={<UploadOutlined />}
                    sx={{
                      backgroundColor: '#fff',
                      textTransform: 'none',
                      fontFamily: 'Inter',
                      height: '56px',
                      color: '#000',
                      fontWeight: 400,
                      fontSize: '16px',
                      border: '1px solid #D9D9D9',
                      boxShadow: 'none',
                      '@media (max-width: 767px)': { width: '50%' },
                    }}
                  >
                    Xuất Excel
                  </Button>
                </Box>
              </Box>
            </Box>
            <TableContainer component={Paper} sx={{}}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell
                      sx={{
                        fontWeight: 700,
                        fontSize: '14px',
                        fontFamily: 'Inter',
                        minWidth: '100px',
                      }}
                    >
                      Mã đối tác
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 700,
                        fontSize: '14px',
                        fontFamily: 'Inter',
                        minWidth: '100px',
                      }}
                    >
                      Tên đối tác
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 700,
                        fontSize: '14px',
                        fontFamily: 'Inter',
                        minWidth: '100px',
                      }}
                    >
                      Tổng khách hàng
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 700,
                        fontSize: '14px',
                        fontFamily: 'Inter',
                        minWidth: '100px',
                      }}
                    >
                      Khách hàng đã mua
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 700,
                        fontSize: '14px',
                        fontFamily: 'Inter',
                        minWidth: '100px',
                      }}
                    >
                      Đơn hàng thành công
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 700,
                        fontSize: '14px',
                        fontFamily: 'Inter',
                        minWidth: '100px',
                      }}
                    >
                      Doanh thu
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 700,
                        fontSize: '14px',
                        fontFamily: 'Inter',
                        minWidth: '100px',
                      }}
                    >
                      Giá trị đơn trung bình
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 700,
                        fontSize: '14px',
                        fontFamily: 'Inter',
                        minWidth: '100px',
                      }}
                    >
                      Tổng hoa hồng
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {visibleRows.map((partner) => (
                    <TableRow key={partner.id}>
                      <TableCell sx={{ fontSize: '14px', fontFamily: 'Inter' }}>
                        {partner.code}
                      </TableCell>
                      <TableCell sx={{ fontSize: '14px', fontFamily: 'Inter' }}>
                        {partner.name}
                      </TableCell>
                      <TableCell sx={{ fontSize: '14px', fontFamily: 'Inter' }}>
                        {partner.totalCustomers}
                      </TableCell>
                      <TableCell sx={{ fontSize: '14px', fontFamily: 'Inter' }}>
                        {partner.totalCustomers}
                      </TableCell>
                      <TableCell sx={{ fontSize: '14px', fontFamily: 'Inter' }}>
                        {partner.successfulOrders}
                      </TableCell>
                      <TableCell sx={{ fontSize: '14px', fontFamily: 'Inter' }}>
                        {partner.revenue}
                      </TableCell>
                      <TableCell sx={{ fontSize: '14px', fontFamily: 'Inter' }}>
                        {partner.averageOrderValue}
                      </TableCell>
                      <TableCell sx={{ fontSize: '14px', fontFamily: 'Inter' }}>
                        {partner.totalCommission}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              <TablePagination
                rowsPerPageOptions={[2, 4, 6]}
                component="div"
                count={data.rankings.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                labelRowsPerPage="Số hàng mỗi trang:"
                labelDisplayedRows={({ from, to, count }) => `${from}-${to} trên ${count}`}
                sx={{
                  '.MuiTablePagination-displayedRows': {
                    margin: 0,
                    fontSize: { xs: '12px', md: '14px' },
                    fontFamily: 'Inter',
                  },
                  '.MuiTablePagination-selectLabel': {
                    margin: 0,
                    fontSize: { xs: '12px', md: '14px' },
                    fontFamily: 'Inter',
                  },
                  '.MuiTablePagination-select': {
                    padding: '4px',
                    fontSize: { xs: '12px', md: '14px' },
                    fontFamily: 'Inter',
                  },

                  'p.MuiTablePagination-displayedRows': {
                    display: 'none',
                  },
                }}
              />
            </TableContainer>
            <Typography sx={{ fontSize: '12px', color: '#666', mt: 1, fontFamily: 'Inter' }}>
              Đối tác không có dữ liệu sẽ không được hiển thị trong danh sách
            </Typography>
          </Box>
        </Box>
      </Container>
    </DashboardLayout>
  );
};

export default Dashboard;
