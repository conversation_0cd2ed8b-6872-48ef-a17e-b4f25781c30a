import { Box, <PERSON><PERSON>, Card, Container, Divider, Paper, Typography, Link } from '@mui/material';
import { useRouter } from 'next/router';
import { paths } from '@/src/paths';
import Grid from '@mui/system/Grid';

const PaymentMethod = (
  <Grid size={{ xs: 12, md: 4 }}>
    <Box>
      <Typography variant="h6" sx={{ fontSize: '1.125rem', mb: 1, mt: 1 }}>
        <PERSON><PERSON><PERSON>ng thức thanh toán
      </Typography>
      <Typography variant="body2" color="text.secondary">
        Quản lý cách bạn thanh toán hoá đơn trong Evotech. Hoá đơn bao gồm phí dịch vụ và các khoản chi phát sinh như
        SMS, Email marketing, ZNS...
      </Typography>
    </Box>
  </Grid>
);
const PaymentDetails = (
  <Grid size={{ xs: 12, md: 8 }}>
    <Paper sx={{ p: 1, borderRadius: 2, boxShadow: 3 }}>
      <Grid container spacing={1}>
        <Grid size={{ xs: 12, md: 6 }}>
          <Typography variant="h6" sx={{ mb: 1 }}>
            Phư<PERSON>ng thức thanh toán
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: { xs: 'center', md: 'flex-start' } }}>
            <Button
              variant="contained"
              sx={{
                mt: 2,
                bgcolor: '#3366FF',
                width: { xs: '100%', md: '50%' },
                '&:hover': {
                  bgcolor: '#2952CC'
                }
              }}
            >
              Nạp tiền
            </Button>
          </Box>
        </Grid>
        <Grid size={{ xs: 12, md: 6 }}>
          <Card
            sx={{
              bgcolor: '#3366FF',
              color: 'white',
              borderRadius: 2,
              p: 3
            }}
          >
            <Typography variant="h5">Evo Credit</Typography>
            <Typography variant="h4">10.000.000đ</Typography>
          </Card>
        </Grid>
      </Grid>
    </Paper>
  </Grid>
);
const SpendingHistory = (router) => (
  <Grid size={{ xs: 12 }}>
    <Grid container spacing={1}>
      <Grid size={{ xs: 12, md: 4 }}>
        <Typography variant="h6" sx={{ mb: 1, mt: 2 }}>
          Lịch sử chi tiêu
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Các hoá đơn bạn đã thanh toán và nạp tiền
        </Typography>
      </Grid>
      <Grid size={{ xs: 12, md: 8 }}>
        <Paper sx={{ p: 3, borderRadius: 2, boxShadow: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" sx={{ fontSize: '1.125rem', fontWeight: 500 }}>
              Danh sách chi tiêu
            </Typography>
            <Link
              href="#"
              onClick={() => router.push(paths.settings.billHistory)}
              sx={{
                color: '#3366FF',
                textDecoration: 'none',
                '&:hover': {
                  textDecoration: 'underline'
                }
              }}
            >
              Chi tiết lịch sử
            </Link>
          </Box>
          <Paper
            sx={{
              p: 2,
              bgcolor: '#F8F9FA',
              borderRadius: 2,
              textAlign: 'center'
            }}
          >
            <Typography color="text.secondary">Không có dữ liệu có sẵn</Typography>
          </Paper>
        </Paper>
      </Grid>
    </Grid>
  </Grid>
);
const ServiceOverview = (
  <Grid size={{ xs: 12 }}>
    <Grid container spacing={3}>
      <Grid size={{ xs: 12, md: 4 }}>
        <Typography variant="h6" sx={{ fontSize: '1.125rem', mb: 2, mt: 2 }}>
          Tổng quan dịch vụ của bạn
        </Typography>
      </Grid>
      <Grid size={{ xs: 12, md: 8 }}>
        <Paper sx={{ p: 3, borderRadius: 2, boxShadow: 3 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Chu kỳ thanh toán: thanh toán vào ngày 27 tháng 11 năm 2024
          </Typography>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Bạn đang sử dụng gói Evo nâng cao, dịch vụ email mkt, zalo automation và zalo mini app
          </Typography>
          <Divider />
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">Tổng hóa đơn</Typography>
            <Typography variant="h6">28.888.888đ</Typography>
          </Box>
        </Paper>
      </Grid>
    </Grid>
  </Grid>
);
export default function PaymentInterface() {
  const router = useRouter();
  return (
    <Container sx={{ mt: 7 }}>
      <Grid container spacing={3}>
        {PaymentMethod}
        {PaymentDetails}
        <Divider sx={{ width: '100%', bgcolor: 'rgba(0, 0, 0, 0.1)' }} />
        {SpendingHistory(router)}
        <Divider sx={{ width: '100%', bgcolor: 'rgba(0, 0, 0, 0.1)' }} />
        {ServiceOverview}
      </Grid>
    </Container>
  );
}
