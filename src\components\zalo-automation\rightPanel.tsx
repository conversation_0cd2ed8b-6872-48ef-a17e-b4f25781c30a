import { Box, FormControl, Grid, MenuItem, Select, TextField, Typography } from "@mui/material";
import ZaloLayoutNewText from "./zaloLayoutNewText";
import DeliveryStatusPage from "./zaloLayoutTransactionNews";
import { useState } from "react";
import { TEMPLATE_TYPE } from "@/src/constants/constant";

const RightPanel = ({
  triggerEvents,
  sendTypes,
  title,
  content,
  subContent,
  buttons,
  setTitle,
  setContent,
  setSubContent,
  bannerImage,
  templateName,
  setTemplateName,
  templateType,
  setTemplateType,
  messageType,
  setMessageType,
  selectedMessageType,
  setSelectedMessageType,
  selectedSendTypes,
  setSelectedSendTypes,
  isEdit,
  newParameter,
  selectedMsgTypeTrans,
}) => {
  return (
    <Grid
      item
      xs={12}
      md={4}
      sx={{
        width: "45%",
        overflowY: "auto",
        maxHeight: "900px",
        display: "flex",
        flexDirection: "column",
        padding: 2,
        "&::-webkit-scrollbar": {
          width: "8px",
        },
        "&::-webkit-scrollbar-track": {
          background: "#f1f1f1",
        },
        "&::-webkit-scrollbar-thumb": {
          background: "#888",
          borderRadius: "4px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          background: "#555",
        },
      }}
    >
      {/* <Grid container spacing={2}>
        
        {templateType === TEMPLATE_TYPE[0].value && <Grid item xs={12} />}
      </Grid> */}
      <Box sx={{ textAlign: "center", height: "0px" }}>
        <Box
          sx={{
            width: "100%",
            maxHeight: "100%",
            marginLeft: "auto",
            transform: "scale(0.8)",
            transformOrigin: "center",
          }}
        >
          {(!templateType ||
            templateType === TEMPLATE_TYPE[0].value ||
            templateType === TEMPLATE_TYPE[1].value) && (
            <DeliveryStatusPage
              transactionType={templateType}
              title={title}
              content={content}
              subContent={subContent}
              // parameters={parameters}
              bannerImage={bannerImage}
              buttons={buttons}
              newParameter={newParameter}
              selectedMessageType={selectedMessageType}
            />
          )}
          {templateType === TEMPLATE_TYPE[2].value && (
            <ZaloLayoutNewText content={content} bannerImage={bannerImage} />
          )}
        </Box>
      </Box>
    </Grid>
  );
};

export default RightPanel;
