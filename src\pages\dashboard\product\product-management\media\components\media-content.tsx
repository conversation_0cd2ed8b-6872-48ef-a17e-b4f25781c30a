import React, { useState, useCallback } from "react";
import {
  Box,
  Grid,
  Card,
  Checkbox,
  Typography,
  IconButton,
  Tooltip,
  Button,
  Stack,
  Select,
  MenuItem,
  TablePagination,
} from "@mui/material";
import {
  Delete as DeleteIcon,
  ContentCopy as ContentCopyIcon,
  Upload as UploadIcon,
} from "@mui/icons-material";
import { MediaFile, MediaGroup, RefType } from "@/src/api/types/media.types";
import DialogUploadMedia from "./dialog-upload-media";
import { useSnackbar } from "notistack";
import { useMedia } from "@/src/api/hooks/media/use-media";
import { ExistingMediaFile } from "../../create/create";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";

interface MediaContentProps {
  selectedGroup: MediaGroup | null;
  selectedItems: ExistingMediaFile[];
  onSelect: (item: ExistingMediaFile) => void;
  // page: number;
  // onPageChange: (page: number) => void;
  // totalPages: number;
  page: number;
  onPageChange: (event: unknown, newPage: number) => void;
  totalPages: number;
  totalCount: number;
  rowsPerPage: number;
  onRowsPerPageChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  items: ExistingMediaFile[];
  type: "image" | "video";
  onRefresh: () => void;
  onSearchChange: (value: string) => void;
  selectedRefType?: string;
  setSelectedRefType?: React.Dispatch<React.SetStateAction<string>>;
}

interface MediaCardProps {
  item: ExistingMediaFile;
  isSelected: boolean;
  onSelect: () => void;
  onCopyLink: (link: string) => void;
  onDelete: (item: ExistingMediaFile) => void;
}

const MediaCard: React.FC<MediaCardProps> = ({
  item,
  isSelected,
  onSelect,
  onCopyLink,
  onDelete,
}) => {
  return (
    <Card
      sx={{
        position: "relative",
        cursor: "pointer",
        transition: "all 0.2s",
        bgcolor: isSelected ? "primary.lighter" : "background.paper",
        "&:hover": {
          boxShadow: 3,
        },
      }}
    >
      <Box sx={{ position: "relative", paddingTop: "100%" }}>
        {/* Image */}
        <Box
          component="img"
          src={item.link}
          alt={item.mediaFileId}
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            objectFit: "cover",
          }}
        />

        {/* Actions Overlay */}
        <Box
          sx={{
            position: "absolute",
            top: 8,
            right: 8,
            display: "flex",
            gap: 0.5,
          }}
        >
          <Tooltip title="Sao chép link">
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                onCopyLink(item.link);
              }}
              sx={{
                color: "white",
                "&:hover": {
                  // bgcolor: "primary.main",
                },
              }}
            >
              <ContentCopyIcon fontSize="small" />
            </IconButton>
          </Tooltip>

          <Tooltip title="Xóa">
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                onDelete(item);
              }}
              sx={{
                color: "white",
                "&:hover": {
                  // bgcolor: "error.main",
                },
              }}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>

          <Checkbox
            checked={isSelected}
            onChange={(e) => {
              e.stopPropagation();
              onSelect();
            }}
            sx={{
              color: "white",
              "&.Mui-checked": {
                color: "primary.main",
              },
            }}
          />
        </Box>
      </Box>
    </Card>
  );
};

export function getRefTypeLabelVi(refType: string) {
  switch (refType) {
    case "Shop":
      return "Cửa hàng";
    case "TriggerEvent":
      return "Sự kiện kích hoạt";
    case "Campaign":
      return "Chiến dịch";
    case "User":
      return "Người dùng";
    case "CategoryProduct":
      return "Danh mục sản phẩm";
    case "CategoryService":
      return "Danh mục dịch vụ";
    case "Product":
      return "Sản phẩm";
    case "Service":
      return "Dịch vụ";
    case "ZaloUID":
      return "Zalo UID";
    case "Voucher":
      return "Voucher";
    case "ArticleCategory":
      return "Danh mục bài viết";
    case "Article":
      return "Bài viết";
    case "MembershipPoint":
      return "Điểm thành viên";
    case "PopupAds":
      return "Quảng cáo popup";
    case "ALL":
      return "Tất cả";
    default:
      return refType;
  }
}

const MediaContent: React.FC<MediaContentProps> = ({
  selectedGroup,
  selectedItems,
  onSelect,
  items,
  onRefresh,
  type,
  page,
  onPageChange,
  totalPages,
  selectedRefType,
  totalCount,
  onRowsPerPageChange,
  onSearchChange,
  rowsPerPage,
  setSelectedRefType,
}) => {
  const [openUploadDialog, setOpenUploadDialog] = useState(false);
  const { enqueueSnackbar } = useSnackbar();
  const { deleteFile } = useMedia();
  const [isDeleting, setIsDeleting] = useState(false);
  const [processingItems, setProcessingItems] = useState<Set<string>>(new Set());

  const refTypeOptions = [
    { value: "ALL", label: "Tất cả" },
    ...Object.entries(RefType).map(([key, value]) => ({
      value,
      label: value,
    })),
  ];
  // Handle single item delete
  const handleDeleteSingle = async (item: ExistingMediaFile) => {
    if (processingItems.has(item.mediaFileId)) {
      return; // Prevent duplicate deletion
    }

    try {
      setProcessingItems((prev) => new Set(prev).add(item.mediaFileId));
      await deleteFile(item.mediaFileId);
      enqueueSnackbar("Đã xoá hình ảnh thành công", { variant: "success" });

      // Remove item from selectedItems if it exists
      if (selectedItems.some((selected) => selected.mediaFileId === item.mediaFileId)) {
        const updatedSelectedItems = selectedItems.filter(
          (selected) => selected.mediaFileId !== item.mediaFileId
        );
        onSelect(updatedSelectedItems[0]);
      }

      onRefresh();
    } catch (error) {
      console.error("Error deleting file:", error);
      enqueueSnackbar("Có lỗi xảy ra khi xoá hình ảnh", { variant: "error" });
    } finally {
      setProcessingItems((prev) => {
        const next = new Set(prev);
        next.delete(item.mediaFileId);
        return next;
      });
    }
  };

  // Handle delete multiple items
  const handleDeleteSelected = async () => {
    if (!selectedItems.length) return;

    const results: { success: string[]; failed: string[] } = {
      success: [],
      failed: [],
    };

    setIsDeleting(true);
    let currentSelectedItems = [...selectedItems];

    try {
      for (const item of selectedItems) {
        // Skip if item is already being processed or has been deleted
        if (
          processingItems.has(item.mediaFileId) ||
          !currentSelectedItems.some((i) => i.mediaFileId === item.mediaFileId)
        ) {
          continue;
        }

        try {
          setProcessingItems((prev) => new Set(prev).add(item.mediaFileId));
          await deleteFile(item.mediaFileId);
          results.success.push(item.mediaFileId);

          // Update currentSelectedItems immediately after successful deletion
          currentSelectedItems = currentSelectedItems.filter(
            (i) => i.mediaFileId !== item.mediaFileId
          );
          onSelect(currentSelectedItems[0]);
        } catch (error) {
          console.error(`Error deleting file ${item.mediaFileId}:`, error);
          results.failed.push(item.mediaFileId);
        } finally {
          setProcessingItems((prev) => {
            const next = new Set(prev);
            next.delete(item.mediaFileId);
            return next;
          });
        }
      }

      // Show appropriate messages
      if (results.success.length > 0) {
        enqueueSnackbar(
          `Đã xoá thành công ${results.success.length} ${
            results.success.length > 1 ? "hình ảnh" : "hình ảnh"
          }`,
          { variant: "success" }
        );
        onRefresh();
      }

      if (results.failed.length > 0) {
        enqueueSnackbar(
          `Không thể xoá ${results.failed.length} ${
            results.failed.length > 1 ? "hình ảnh" : "hình ảnh"
          }`,
          { variant: "error" }
        );
      }
    } catch (error) {
      console.error("Delete operation failed:", error);
      enqueueSnackbar("Có lỗi xảy ra trong quá trình xoá hình ảnh", { variant: "error" });
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle copy link
  const handleCopyLink = useCallback(
    (link: string) => {
      navigator.clipboard.writeText(link);
      enqueueSnackbar("Đã sao chép link", { variant: "success" });
    },
    [enqueueSnackbar]
  );

  return (
    <Box sx={{ flex: 1, display: "flex", flexDirection: "column", overflow: "hidden" }}>
      {/* Header */}
      <Box
        sx={{
          p: 2,
          borderBottom: 1,
          borderColor: "divider",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Typography variant="h6">{selectedGroup?.groupName || "Chọn nhóm"}</Typography>

        <Stack direction="row" spacing={1}>
          {/* {selectedItems.length > 0 && (
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleDeleteSelected}
              size="small"
              disabled={isDeleting}
            >
              {isDeleting ? 'Đang xóa...' : `Xoá (${selectedItems.length})`}
            </Button>
          )} */}
          <Button
            variant="contained"
            startIcon={<UploadIcon />}
            onClick={() => setOpenUploadDialog(true)}
            disabled={!selectedGroup}
            size="small"
          >
            Tải ảnh lên
          </Button>
        </Stack>
      </Box>
      <Box>
        <Box sx={{ p: 2, display: "flex", alignItems: "center", gap: 2 }}>
          <Typography variant="body2" sx={{ fontWeight: 600 }}>
            Loại dữ liệu:
          </Typography>
          <Select
            value={selectedRefType}
            onChange={(e) => {
              setSelectedRefType(e.target.value);
            }}
            sx={{
              minWidth: 180,
              height: 45,
              borderRadius: 1,
              border: "1px solid #ccc",
              padding: "8px",
              backgroundColor: "background.paper",
              ".MuiSelect-select": {
                display: "flex",
                alignItems: "center",
                height: "45px",
                paddingTop: 0,
                paddingBottom: 0,
              },
            }}
          >
            {refTypeOptions.map((opt) => (
              <MenuItem key={opt.value} value={opt.value}>
                {getRefTypeLabelVi(opt.label)}
              </MenuItem>
            ))}
          </Select>
        </Box>
      </Box>

      {/* Grid View */}
      <Box sx={{ flex: 1, overflow: "auto", p: 2 }}>
        <Grid container spacing={2}>
          {items.map((item) => (
            <Grid item xs={6} sm={4} md={3} lg={2} key={item.mediaFileId}>
              <MediaCard
                item={item}
                isSelected={selectedItems.some(
                  (selected) => selected.mediaFileId === item.mediaFileId
                )}
                onSelect={() => onSelect(item)}
                onCopyLink={handleCopyLink}
                onDelete={handleDeleteSingle}
              />
            </Grid>
          ))}
        </Grid>
        {items.length === 0 && (
          <Box sx={{ textAlign: "center", color: "text.secondary", py: 6 }}>Không có dữ liệu</Box>
        )}
        <TablePagination
          labelRowsPerPage="Số hàng mỗi trang"
          component="div"
          count={totalCount}
          page={page}
          onPageChange={onPageChange}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={onRowsPerPageChange}
          rowsPerPageOptions={rowPerPageOptionsDefault}
        />
      </Box>

      {/* Upload Dialog */}
      <DialogUploadMedia
        open={openUploadDialog}
        onClose={() => setOpenUploadDialog(false)}
        groupId={selectedGroup?.groupFileId || ""}
        onSuccess={() => {
          setOpenUploadDialog(false);
          onRefresh();
        }}
        type={type}
      />
    </Box>
  );
};

export default MediaContent;
