import React from 'react';
import SettingLayout from '@/src/components/settings/settings-page/SettingLayout';
import ImageManagement from '@/src/components/settings/settings-page/Media';
import { Typography, IconButton } from '@mui/material';
import Grid from '@mui/system/Grid';

export default function SettingMedia() {
  return (
    <SettingLayout>
      <Grid>
        <Typography variant="h5" sx={{ ml: 1 }}>
          T<PERSON>i li<PERSON>u
        </Typography>
      </Grid>
      <Grid>
        <ImageManagement />
      </Grid>
    </SettingLayout>
  );
}
