import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Typography,
  Button,
  Radio,
  RadioGroup,
  FormControlLabel,
  Select,
  Stack,
  MenuItem,
  InputAdornment,
  Box,
} from "@mui/material";
import {
  TYPE_OPTION_DATE_DISPLAY,
  mapNumberToTypeValidUntil,
  mapTypeValidUntilToNumber,
  mapPercentValue,
  mapPercentToNumber,
} from "src/api/types/membership.types";
import { useSnackbar } from "notistack";

const DiscountShoppingDialog = ({ open, onClose, onSave, initialData }) => {
  const [limitPercent, setLimitPercent] = useState(initialData?.maxPercent || 100);
  const [usageLimit, setUsageLimit] = useState(initialData?.isScore?.isValue || true);
  const [limitValue, setLimitValue] = useState(initialData?.isScore?.value?.toString() || "0");
  const [limitPeriod, setLimitPeriod] = useState(
    mapNumberToTypeValidUntil(initialData?.isScore?.optionDate || 1)
  );
  const [maxSpent, setMaxSpent] = useState(initialData?.rule || 1000);
  const { enqueueSnackbar } = useSnackbar();

  useEffect(() => {
    if (initialData) {
      setLimitPercent(initialData.maxPercent || 100);
      setUsageLimit(initialData.isScore?.isValue || false);
      setLimitValue(initialData.isScore?.value?.toString() || "0");
      setLimitPeriod(mapNumberToTypeValidUntil(initialData.isScore?.optionDate || 1));
      setMaxSpent(initialData.rule?.toString() || "0");
    }
  }, [initialData]);

  const handleSave = () => {
    if (maxSpent < 0) {
      enqueueSnackbar("Số tiền quy đổi phải lớn hơn 0! Vui lòng nhập lại.", { variant: "error" });
      return;
    }

    if (!usageLimit && limitValue < 0) {
      enqueueSnackbar("Số lần đổi phải lớn hơn 0! Vui lòng nhập lại.", { variant: "error" });
      return;
    }

    const updatedData = {
      ...initialData,
      rule: maxSpent,
      maxPercent: limitPercent,
      isScore: {
        isValue: usageLimit,
        value: Number(limitValue),
        optionDate: mapTypeValidUntilToNumber(limitPeriod),
      },
    };
    onSave(updatedData);
  };

  const handleLimitPercentChange = (event) => {
    let value = event.target.value;
    if (value < 0) value = 0;
    if (value > 100) value = 100;
    setLimitPercent(value);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Typography fontWeight="700" fontSize="20px">
          Trừ điểm{" "}
          <Typography component="span" sx={{ color: "#888" }}>
            (Mua sắm)
          </Typography>
        </Typography>
      </DialogTitle>

      <DialogContent>
        <Typography fontWeight="bold" mb={1}>
          Quy tắc trừ điểm
        </Typography>
        <Stack
          direction={"row"}
          alignItems="center"
          gap={2}
          sx={{ "@media (max-width: 600px)": { flexDirection: "column" } }}
        >
          <TextField
            variant="outlined"
            size="small"
            defaultValue={1}
            sx={{
              width: "45%",
              padding: "0 !important",
              backgroundColor: "#F5F5F5",
              borderRadius: "8px",
              pointerEvents: "none",
              "& .MuiInputBase-input": {
                textAlign: "center",
                padding: "10px 15px",
              },
              "& .MuiOutlinedInput-root": {
                padding: "0 !important",
              },
              "@media (max-width: 600px)": {
                width: "100%",
              },
            }}
            InputProps={{
              endAdornment: (
                <InputAdornment
                  position="end"
                  sx={{ borderLeft: "1px solid #ccc", padding: "20px 15px" }}
                >
                  Điểm
                </InputAdornment>
              ),
            }}
          />
          <Typography>=</Typography>
          <TextField
            variant="outlined"
            size="small"
            value={maxSpent}
            onChange={(e) => {
              const value = e.target.value.replace(/[^0-9]/g, "");
              setMaxSpent(Number(value));
            }}
            sx={{
              width: "45%",
              padding: "0 !important",
              "& .MuiInputBase-input": {
                textAlign: "center",
                padding: "10px 15px",
              },
              "& .MuiOutlinedInput-root": {
                padding: "0 !important",
              },
              "@media (max-width: 600px)": {
                width: "100%",
              },
            }}
            InputProps={{
              endAdornment: (
                <InputAdornment
                  position="end"
                  sx={{
                    borderLeft: "1px solid #ccc",
                    padding: "20px 15px",
                    backgroundColor: "#F5F5F5",
                    borderRadius: "0 8px 8px 0",
                  }}
                >
                  VNĐ
                </InputAdornment>
              ),
            }}
          />
        </Stack>
        <Typography color="gray" fontSize="14px" mt={1}>
          Khách hàng sẽ đổi 1 điểm thành 1,000 đ khi thanh toán
        </Typography>

        <Stack
          direction={"row"}
          gap={4}
          sx={{
            mt: 4,
            "@media (max-width: 600px)": {
              flexDirection: "column",
              gap: 3,
            },
          }}
        >
          <Stack sx={{ flex: 1 }}>
            <Typography fontWeight="bold" mb={2}>
              Giới hạn tỷ lệ sử dụng điểm
            </Typography>
            <Stack direction="row" alignItems="center" flexWrap="wrap" gap={1}>
              <Typography fontSize="14px" color="text.secondary" sx={{ whiteSpace: "nowrap" }}>
                Cho phép sử dụng tối đa
              </Typography>
              <TextField
                size="small"
                value={limitPercent}
                type="number"
                variant="outlined"
                onChange={(e) => handleLimitPercentChange(e)}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          gap: 1, // Khoảng cách giữa thanh dọc và %
                          height: "100%", // Đảm bảo chiều cao bằng với input
                        }}
                      >
                        <Box
                          sx={{
                            width: "1px",
                            height: "2rem", // Thanh chia cao bằng input
                            backgroundColor: "#ccc", // Màu xám nhẹ
                            marginY: "auto", // Căn giữa thanh dọc
                          }}
                        />
                        %
                      </Box>
                    </InputAdornment>
                  ),
                }}
                sx={{
                  width: "120px",
                  "& .MuiOutlinedInput-input": {
                    textAlign: "center",
                  },
                  "& .MuiOutlinedInput-root": {
                    paddingRight: "8px", // Đảm bảo input không bị dính sát vào "%"
                  },
                }}
              />

              {/* <Select
                value={limitPercent}
                onChange={(e) => setLimitPercent(e.target.value)}
                size="small"
                sx={{
                  width: "100px",
                  "& .MuiSelect-select": {
                    padding: "6px 12px",
                  },
                }}
              >
                <MenuItem value="100%">100%</MenuItem>
                <MenuItem value="50%">50%</MenuItem>
                <MenuItem value="25%">25%</MenuItem>
              </Select> */}
              <Typography
                fontSize="14px"
                color="text.secondary"
                sx={{ whiteSpace: "nowrap", flexShrink: 0 }}
              >
                giá trị đơn hàng bằng điểm thưởng
              </Typography>
            </Stack>
          </Stack>

          <Stack sx={{ flex: 1 }}>
            <Typography fontWeight="bold" mb={2}>
              Hạn mức sử dụng điểm thưởng
            </Typography>
            <RadioGroup
              value={usageLimit}
              onChange={(e) => setUsageLimit(e.target.value === "true")}
              sx={{ gap: 1 }}
            >
              <FormControlLabel
                sx={{
                  margin: 0,
                  ".MuiFormControlLabel-label": {
                    fontSize: "14px",
                    color: "text.secondary",
                  },
                }}
                value="true"
                control={<Radio size="small" />}
                label="Không giới hạn"
              />
              <FormControlLabel
                sx={{
                  margin: 0,
                  alignItems: "flex-start",
                  ".MuiFormControlLabel-label": {
                    fontSize: "14px",
                    color: "text.secondary",
                  },
                }}
                value="false"
                control={<Radio size="small" sx={{ mt: 0.5 }} />}
                label={
                  <Stack
                    direction="row"
                    alignItems="center"
                    flexWrap="wrap"
                    gap={1}
                    sx={{ mt: 0.5 }}
                  >
                    <Typography fontSize="14px" color="text.secondary">
                      Mỗi tài khoản chỉ được dùng tối đa
                    </Typography>
                    <TextField
                      size="small"
                      value={limitValue}
                      type="number"
                      onChange={(e) => setLimitValue(e.target.value)}
                      disabled={usageLimit === true}
                      sx={{
                        width: "100px",
                        "& .MuiOutlinedInput-input": {
                          padding: "6px 12px",
                          textAlign: "center",
                        },
                      }}
                    />
                    <Typography fontSize="14px" color="text.secondary">
                      điểm thưởng mỗi
                    </Typography>
                    <Select
                      value={mapTypeValidUntilToNumber(limitPeriod)}
                      onChange={(e) =>
                        setLimitPeriod(mapNumberToTypeValidUntil(Number(e.target.value)))
                      }
                      size="small"
                      disabled={usageLimit === true}
                      sx={{
                        width: "100px",
                        "& .MuiSelect-select": {
                          padding: "6px 12px",
                        },
                      }}
                    >
                      {Object.entries(TYPE_OPTION_DATE_DISPLAY).map(([key, value]) => (
                        <MenuItem key={key} value={Number(key)}>
                          {value}
                        </MenuItem>
                      ))}
                    </Select>
                  </Stack>
                }
              />
            </RadioGroup>
          </Stack>
        </Stack>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} variant="outlined">
          Hủy bỏ
        </Button>
        <Button onClick={handleSave} variant="contained" color="primary">
          Xác nhận
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DiscountShoppingDialog;
