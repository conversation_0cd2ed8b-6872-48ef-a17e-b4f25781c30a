import React, { ReactElement, useEffect, useState, useRef } from "react";
import { Box, Grid2, Typography, Button, CircularProgress } from "@mui/material";
import { useRouter } from "next/router";
import { paths } from "src/paths";
import StoreLayout from "@/src/layouts/store";
import { useTranslation } from "react-i18next";
import { tokens } from "src/locales/tokens";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import { StorageService } from "nextjs-api-lib";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { useAppDispatch, useAppSelector } from "@/src/redux/hooks";
import { deleteShop, getShop } from "@/src/redux/slices/shopSlice";
import { StoreCard } from "@/src/components/StoreCard";
import { EmptyShop } from "@/src/components/EmptyShop";

interface StoresPageProps {
  searchQuery?: string;
}

const StoresPage: React.FC<StoresPageProps> & {
  getLayout?: (page: ReactElement) => ReactElement;
} = ({ searchQuery = null }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const snackbar = useSnackbar();
  const { shops, loading, error } = useAppSelector((state) => state.shop);
  const [skip, setSkip] = useState(0);
  const limit = 20;
  const { permissions, loading: permissionsLoading, isAgency } = useAllPermissions();
  const fetchedRef = useRef({});

  useEffect(() => {
    // Create a unique key for this specific fetch
    const fetchKey = `${skip}-${searchQuery}`;

    // Only fetch if we haven't already fetched this exact combination
    if (!fetchedRef.current[fetchKey]) {
      // Mark as fetched before actual fetch to prevent race conditions
      fetchedRef.current[fetchKey] = true;

      dispatch(
        getShop({
          skip,
          limit,
          search: searchQuery,
        })
      );
    }
  }, [searchQuery, skip, dispatch]);

  const handleDetailsClick = async (storeId: string) => {
    StorageService.set("currentStoreId", storeId);

    // Wait for permissions to load
    if (permissions && !isAgency) {
      // Get all URLs from permissions
      const urls = Object.keys(permissions);

      // Find the first non-empty URL
      const validUrl = urls.find((url) => url !== "");

      // If there are any valid URLs, redirect to the first one
      if (validUrl) {
        router.push(validUrl);
      } else {
        // Fallback to dashboard if no valid permissions are found
        router.push(paths.dashboard.index);
      }
    } else {
      // Fallback to dashboard if permissions are loading or not available
      router.push(paths.dashboard.index);
    }
  };

  const handleCreateStoreClick = () => {
    router.push(paths.store.create);
  };

  const handleDeleteStore = async (shopId: string) => {
    try {
      await dispatch(deleteShop(shopId));
      snackbar.success(t(tokens.store.deleteSuccess));
    } catch (err) {
      console.error("Failed to delete store:", err);
      snackbar.error(t(tokens.store.deleteError));
    }
  };

  return (
    <Box
      sx={{
        alignItems: "center",
        justifyContent: "center",
        flex: 1,
        minHeight: "100vh",
        overflow: "hidden",
        p: 3,
      }}
    >
      {loading && skip === 0 ? (
        <Box
          sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100vh" }}
        >
          <CircularProgress />
        </Box>
      ) : error ? (
        <Typography color="error">{error}</Typography>
      ) : shops.length === 0 ? (
        <EmptyShop onCreateStoreClick={handleCreateStoreClick} />
      ) : (
        <>
          <Grid2 container spacing={3} justifyContent="flex-start" direction="row">
            {shops.map((store) => (
              <Grid2 size={{ xs: 12, sm: 6, md: 4 }} key={store.shopId}>
                <StoreCard
                  store={store}
                  onDetailsClick={() => handleDetailsClick(store.shopId)}
                  onDelete={handleDeleteStore}
                />
              </Grid2>
            ))}
          </Grid2>
          {shops.length >= limit && (
            <Box sx={{ display: "flex", justifyContent: "center", mt: 3 }}>
              <Button
                variant="contained"
                onClick={() => setSkip((prevSkip) => prevSkip + limit)}
                disabled={loading}
              >
                {loading ? (
                  <CircularProgress size={24} color="inherit" sx={{ mr: 2 }} />
                ) : (
                  t(tokens.common.loadMore)
                )}
              </Button>
            </Box>
          )}
        </>
      )}
    </Box>
  );
};

StoresPage.getLayout = (page: ReactElement): ReactElement => {
  return <StoreLayout>{page}</StoreLayout>;
};

export default StoresPage;
