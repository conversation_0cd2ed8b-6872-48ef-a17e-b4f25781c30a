export enum ZaloElementTypeEnum {
  HEADER = "Header",
  BANNER = "Banner",
  CONTENT = "Text",
  TABLE = "Table",
}

export interface IContentTable {
  style: string;
  key?: string;
  value?: string;
}

export interface IZaloElement {
  file?: File;
  type: ZaloElementTypeEnum;
  mediaType?: string;
  align?: string;
  url?: string;
  imageUrl?: string;
  content?: string;
  contentTable?: IContentTable[];
}

export interface IZaloButton {
  file?: File;
  title: string;
  imageIcon: string;
  type: string;
  payload: {
    url: string;
  };
}

interface IZaloPayload {
  templateType?: string;
  language?: string;
  elements?: IZaloElement[];
  buttons?: IZaloButton[];
}

interface IZaloAttachment {
  type: string;
  payload: IZaloPayload;
}

export interface IZaloMessage {
  text: string;
  attachment: IZaloAttachment;
}

export interface IZaloRecipient {
  userId: string;
}

export interface IZaloTransaction {
  recipient: IZaloRecipient;
  message: IZaloMessage;
}

export interface ISendType {
  id: string;
  createdDate: string;
  modifiedDate: string;
  createdBy: string;
  modifiedBy: string;
  isDeleted: boolean;
  deletedAt: string;
  name: string;
  jobType: string;
  cronExpression: string;
  description: string;
}

export interface IZaloTemplate {
  partnerId?: string;
  shopId: string;
  refId?: string; // id trigger
  refType?: string;
  templateId?: string;
  name?: string;
  templateCategory: string;
  type: string;
  messageType: string;
  // sendType: ISendType;
  status: string;
  transaction: IZaloTransaction;
  // TriggerEvent: string;
}
