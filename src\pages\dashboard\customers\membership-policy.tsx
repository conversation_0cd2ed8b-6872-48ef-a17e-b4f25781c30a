import React, { useState } from "react";
import { <PERSON>, Typography, Button, useTheme } from "@mui/material";
import DashboardLayout from "../../../layouts/dashboard";
import Header from "../../../components/policy/Header";
import TierSelection from "../../../components/policy/TierSelection";
import InputFields from "../../../components/policy/InputFields";
import ExampleSection from "../../../components/policy/ExampleSection";
import TitleDialog from "@/src/components/dialog/TitleDialog";
import { useRouter } from "next/router";
import { paths } from "@/src/paths";
import Grid from "@mui/system/Grid";

export default function MembershipPolicy() {
  const [selectedTier, setSelectedTier] = useState("single");
  const [showDialog, setShowDialog] = useState(false);
  const router = useRouter();
  const theme = useTheme();

  const handleFormSubmit = () => {
    console.log("Thi<PERSON><PERSON> lập thành công");
    setShowDialog(false);
    router.push(paths.customers.setMembershipPolicy);
  };

  const handleDialogClose = () => {
    setShowDialog(false);
  };

  const handleOpenDialog = () => {
    setShowDialog(true);
  };

  const handleBack = () => {
    setShowDialog(false);
  };

  return (
    <DashboardLayout>
      <Header />
      <Grid
        container
        sx={{
          padding: { xs: 2, md: 3 },
          maxWidth: "1200px",
          margin: "0 auto",
          backgroundColor: theme.palette.background.paper,
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 2,
          boxShadow: theme.shadows[1],
          marginBottom: 4,
        }}
      >
        <Box sx={{ marginBottom: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: "bold", marginBottom: 2 }}>
            Cài đặt chương trình tích điểm
          </Typography>
          <label htmlFor="">
            {selectedTier === "single" ? "1. Điểm thưởng" : "1. Điểm tích lũy"}
          </label>
        </Box>
        <TierSelection selectedTier={selectedTier} setSelectedTier={setSelectedTier} />
        <Box sx={{ display: "flex", flexDirection: { xs: "column", md: "row" }, marginBottom: 4 }}>
          <Box
            sx={{
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: 2,
              padding: 3,
              backgroundColor: theme.palette.background.default,
              flex: 2,
              marginRight: { md: 2 },
              marginBottom: { xs: 2, md: 0 },
            }}
          >
            <InputFields
              selectedTier={selectedTier}
              onSubmit={handleOpenDialog}
              setShowNotification={setShowDialog}
            />
          </Box>
          <ExampleSection selectedTier={selectedTier} />
        </Box>
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", md: "row" },
            alignItems: "center",
            marginTop: 10,
            justifyContent: "space-between",
          }}
        >
          <Typography
            variant="body2"
            sx={{ color: theme.palette.text.secondary, marginRight: 2, width: "80%" }}
          >
            <strong>Lưu ý:</strong> Sau khi thiết lập chương trình tích điểm, bạn không thể thay đổi
            chương trình vì nó ảnh hưởng đến quyền lợi của khách hàng. Trong trường hợp đặc biệt,
            vui lòng liên hệ CSKH để được hỗ trợ.
          </Typography>
          <Button
            variant="contained"
            sx={{
              backgroundColor: "#2654FE",
              color: theme.palette.primary.contrastText,
            }}
            onClick={() =>
              document
                .getElementById("membership-form")
                .dispatchEvent(new Event("submit", { cancelable: true, bubbles: true }))
            }
          >
            Thiết lập
          </Button>
        </Box>
      </Grid>
      <TitleDialog
        open={showDialog}
        handleClose={handleDialogClose}
        handleSubmit={handleFormSubmit}
        closeBtnTitle="Hủy"
        submitBtnTitle="Tiếp tục"
      >
        <Typography
          variant="h5"
          sx={{
            color: theme.palette.error.main,
            fontWeight: "bold",
            marginBottom: 2,
            textAlign: "center",
          }}
        >
          Thông báo
        </Typography>
        <Typography variant="body1" sx={{ marginBottom: 2 }}>
          Chương trình tích điểm chỉ được thiết lập một lần duy nhất. Bạn có chắc muốn tiếp tục?
        </Typography>
      </TitleDialog>
    </DashboardLayout>
  );
}
