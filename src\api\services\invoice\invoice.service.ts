import { apiClient } from "../../config/api-client";
import { API_PATHS } from "../../constants/api-paths";
import type { ApiError, ErrorConfig } from "../../types/error.types";
import type { ExtendedRequestConfig } from "../../types/api.types";
import { logger } from "@/src/utils/logger";
import { InvoiceConfigData, InvoiceProvider } from "../../types/invoice.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }

  throw error;
}

export interface InvoiceQueryParams {
  ShopId: string;
  OrderNo: string;
  Status?: string;
  InvoiceNo?: string;
  Paging?: {
    NameType?: string;
    SortType?: string;
    PageSize?: number;
    PageIndex?: number;
    Search?: string;
    Name?: string;
    Sort?: string;
  };
}

export const invoiceService = {
  getInvoiceConfig: async (
    shopId: string,
    provider: InvoiceProvider,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get(
        `${API_PATHS.INVOICE.GET_INVOICE}?shopId=${shopId}&provider=${provider}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  activeInvoiceConfig: async (id: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put(`${API_PATHS.INVOICE.GET_INVOICE}/${id}/active`, config);

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  createInvoiceConfig: async (data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post(API_PATHS.INVOICE.GET_INVOICE, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateInvoiceConfig: async (data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put(
        `${API_PATHS.INVOICE.GET_INVOICE}/${data.invoiceConfigId}`,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  // invoice
  getListHistoryInvoice: async (data: InvoiceQueryParams, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const buildInvoiceUrl = (baseUrl: string, params: InvoiceQueryParams): string => {
        const query = new URLSearchParams();

        if (params.ShopId) query.append("ShopId", params.ShopId);
        if (params.OrderNo) query.append("OrderNo", params.OrderNo);
        if (params.Status) query.append("Status", params.Status);
        if (params.InvoiceNo) query.append("InvoiceNo", params.InvoiceNo);

        if (params.Paging) {
          if (params.Paging.NameType) query.append("Paging.NameType", "Created");
          if (params.Paging.SortType) query.append("Paging.SortType", "asc");
          if (params.Paging.PageSize !== undefined)
            query.append("Paging.PageSize", params.Paging.PageSize.toString());
          if (params.Paging.PageIndex !== undefined)
            query.append("Paging.PageIndex", params.Paging.PageIndex.toString());
          if (params.Paging.Search) query.append("Paging.Search", params.Paging.Search);
          if (params.Paging.Name) query.append("Paging.Name", "Created");
          if (params.Paging.Sort) query.append("Paging.Sort", "asc");
        }

        return `${baseUrl}?${query.toString()}`;
      };
      const url = buildInvoiceUrl(API_PATHS.INVOICE.API_INVOICE, data);

      const response = await apiClient.get(url, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createManualInvoice: async (orderId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      if (orderId) {
        const response = await apiClient.put(
          `${API_PATHS.INVOICE.API_INVOICE}/manual?orderId=${orderId}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  downloadFilePdfInvoice: async (invoiceNo: string, errorConfig?: ErrorConfig) => {
    const config: any = {
      errorHandling: errorConfig,
      // responseType: "blob",
    };

    try {
      if (invoiceNo) {
        const response = await apiClient.get(
          `${API_PATHS.INVOICE.API_INVOICE}/download/${invoiceNo}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
