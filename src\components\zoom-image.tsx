import React from "react";
import { Dialog, DialogContent } from "@mui/material";

interface ZoomImageDialogProps {
  open: boolean;
  onClose: () => void;
  src: string;
  alt?: string;
  maxHeight?: string | number;
}

const ZoomImageDialog: React.FC<ZoomImageDialogProps> = ({
  open,
  onClose,
  src,
  alt = "Zoomed image",
  maxHeight = "80vh",
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{
        sx: {
          backgroundColor: "background.paper",
          borderRadius: 0.5,
          p: "4px",
          maxWidth: "90vw",
          maxHeight: "90vh",
        },
      }}
    >
      <DialogContent sx={{ p: 0 }}>
        <img
          src={src}
          alt={alt}
          style={{
            maxWidth: "100%",
            maxHeight,
            objectFit: "contain",
            borderRadius: "4px",
          }}
        />
      </DialogContent>
    </Dialog>
  );
};

export default ZoomImageDialog;
