import { Box, Button, IconButton, Paper, Typography } from "@mui/material";
import React, { useState } from "react";
import TitleDialog from "../../dialog/TitleDialog";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import SearchCustomerOrder from "./SearchCustomerOrder";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import {
  Delete,
  Edit,
  PermContactCalendar,
  Person,
  PersonAdd,
  PersonOutline,
} from "@mui/icons-material";

export default function CustomerOrderBox({
  handleSelectCustomer,
  selectedCustomer,
  handleClickRemoveUser,
}) {
  const [openDialog, setOpenDialog] = useState(false);

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const onClickAddItem = () => {
    setOpenDialog(true);
  };

  const handleSubmit = async (item) => {
    handleSelectCustomer(item);
    // addItemToCart(items);

    handleCloseDialog();
  };

  const hadUser = selectedCustomer && selectedCustomer.userId;
  return (
    <Paper
      elevation={1}
      sx={{
        width: "100%",
        bgcolor: "background.paper",
        boxShadow: "0px 1px 2px rgb(255 255 255 / 8%)",
        overflow: "hidden",
      }}
    >
      <Box sx={{}}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            <PermContactCalendar sx={{ color: "rgb(201, 91, 91)", mr: 1, fontSize: 22 }} />
            Khách hàng
          </Typography>
          {hadUser && (
            <Box>
              <IconButton
                onClick={onClickAddItem}
                size="small"
                sx={{
                  mr: 0.5,
                  "&:hover": {
                    color: "primary.main",
                    bgcolor: "primary.lighter",
                  },
                }}
              >
                <Edit fontSize="small" />
              </IconButton>
              <IconButton
                onClick={handleClickRemoveUser}
                size="small"
                sx={{
                  "&:hover": {
                    color: "error.main",
                    bgcolor: "error.lighter",
                  },
                }}
              >
                <Delete fontSize="small" />
              </IconButton>
            </Box>
          )}
        </Box>

        {!hadUser ? (
          <Button
            startIcon={<PersonAdd />}
            onClick={onClickAddItem}
            variant="outlined"
            fullWidth
            sx={{
              py: 2,
              borderStyle: "dashed",
              color: "primary.main",
              "&:hover": {
                borderStyle: "dashed",
                bgcolor: "primary.lighter",
              },
            }}
          >
            Chọn khách hàng
          </Button>
        ) : (
          <Box sx={{ py: 1 }}>
            <Box
              sx={{
                transition: "all 0.2s",
              }}
            >
              <Typography variant="subtitle1" sx={{ fontWeight: 500, mb: 1 }}>
                {selectedCustomer.fullname}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                {selectedCustomer.phoneNumber}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {selectedCustomer.email}
              </Typography>
            </Box>
          </Box>
        )}
      </Box>

      <TitleDialog
        title="Chọn khách hàng"
        open={openDialog}
        handleClose={handleCloseDialog}
        submitBtnTitle="Xác nhận"
        showActionDialog={false}
        maxWidth="xl"
      >
        <SearchCustomerOrder
          handleSubmit={handleSubmit}
          handleClose={handleCloseDialog}
          selectedCustomer={selectedCustomer}
        />
      </TitleDialog>
    </Paper>
  );
}
