import React from "react";
import {
  Dialog,
  FormControl,
  MenuItem,
  Select,
  TextField,
  Typography,
  Button,
  Grid,
} from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import { useFormik } from "formik";
import * as Yup from "yup";
import TruncatedText from "../truncated-text/truncated-text";
import { MembershipLevel } from "@/src/api/types/membership-level.types";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useUser } from "@/src/api/hooks/user/use-user";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useTranslation } from "react-i18next";
import { tokens } from "@/src/locales/tokens";
type UserInfoProps = {
  userInfo?: {
    userId?: string;
    fullname?: string;
    email?: string;
    phoneNumber?: string;
    birthdate?: string;
    gender?: string;
    membershipLevelId?: string;
    membershipLevel?: {
      levelId: string;
    };
  };
};

const validationSchema = Yup.object({
  fullname: Yup.string().required("Họ tên là bắt buộc"),
  phoneNumber: Yup.string()
    .required("Số điện thoại là bắt buộc")
    .matches(/^(\+84|0)[3-9]\d{8}$/, "Số điện thoại không đúng định dạng"),
});

const FormUserInfo = ({
  open,
  setOpen,
  userInfo,
  membershipLevels,
  fetchUser,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  userInfo: UserInfoProps["userInfo"];
  membershipLevels: MembershipLevel[];
  fetchUser: () => void;
}) => {
  const storeId = useStoreId();
  const { updateUser } = useUser();
  const snackbar = useSnackbar();
  const { t } = useTranslation();

  const formik = useFormik({
    initialValues: {
      fullname: userInfo?.fullname || "",
      email: userInfo?.email || "",
      phoneNumber: userInfo?.phoneNumber || "",
      birthdate: userInfo?.birthdate ? dayjs(userInfo.birthdate) : null,
      gender: userInfo?.gender || "",
      membershipLevelId: userInfo?.membershipLevelId || userInfo?.membershipLevel?.levelId || "",
    },
    enableReinitialize: true,
    validationSchema,
    onSubmit: async (values) => {
      const userData = {
        shopId: storeId,
        userId: userInfo.userId,
        birthdate: values.birthdate
          ? dayjs(values.birthdate).format("YYYY-MM-DDTHH:mm:ss.SSS")
          : null,
        email: values.email,
        fullname: values.fullname,
        gender: values.gender,
        phoneNumber: values.phoneNumber,
        membershipLevelId: values.membershipLevelId || null,
      };
      try {
        const response = await updateUser(userData);
        if (response && response.data) {
          snackbar.success(t(tokens.settings.updateSuccess));
          fetchUser();
          setOpen(false);
        }
      } catch (err) {
        snackbar.error(t(tokens.settings.updateFailed));
        console.error(err);
      }
      setOpen(false);
    },
  });

  if (userInfo?.membershipLevelId && membershipLevels?.length === 0) return null;

  return (
    <Dialog open={open} onClose={() => setOpen(false)} fullWidth>
      <form onSubmit={formik.handleSubmit} style={{ padding: "24px", minWidth: 400 }}>
        <Grid xs={12} sx={{ mb: 3 }}>
          <Typography sx={{ display: "flex" }} variant="subtitle1" gutterBottom>
            Họ tên <Typography sx={{ color: "red", marginLeft: 0.5 }}>*</Typography>
          </Typography>
          <TextField
            fullWidth
            variant="outlined"
            name="fullname"
            value={formik.values.fullname}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.fullname && Boolean(formik.errors.fullname)}
            helperText={formik.touched.fullname && formik.errors.fullname}
            InputProps={{
              sx: {
                height: "45px",
                "& input::-ms-reveal, & input::-ms-clear": {
                  display: "none",
                },
              },
            }}
          />
        </Grid>

        <Grid sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Email
          </Typography>
          <TextField
            fullWidth
            variant="outlined"
            name="email"
            value={formik.values.email}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.email && Boolean(formik.errors.email)}
            helperText={formik.touched.email && formik.errors.email}
            InputProps={{
              sx: {
                height: "45px",
                "& input::-ms-reveal, & input::-ms-clear": {
                  display: "none",
                },
              },
            }}
          />
        </Grid>

        <Grid sx={{ mb: 3 }}>
          <Typography sx={{ display: "flex" }} variant="subtitle1" gutterBottom>
            Số điện thoại <Typography sx={{ color: "red", marginLeft: 0.5 }}>*</Typography>
          </Typography>
          <TextField
            fullWidth
            variant="outlined"
            name="phoneNumber"
            value={formik.values.phoneNumber}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.phoneNumber && Boolean(formik.errors.phoneNumber)}
            helperText={formik.touched.phoneNumber && formik.errors.phoneNumber}
            InputProps={{
              sx: {
                height: "45px",
                "& input::-ms-reveal, & input::-ms-clear": {
                  display: "none",
                },
              },
            }}
          />
        </Grid>

        <Grid sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Ngày sinh
          </Typography>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              value={formik.values.birthdate}
              onChange={(date) => formik.setFieldValue("birthdate", date)}
              format="DD/MM/YYYY"
              maxDate={dayjs()}
              slotProps={{
                textField: {
                  fullWidth: true,
                  variant: "outlined",
                  error: formik.touched.birthdate && Boolean(formik.errors.birthdate),
                  sx: {
                    "& .MuiOutlinedInput-root": {
                      height: "45px",
                    },
                  },
                  onBlur: () => formik.setFieldTouched("birthdate", true),
                },
              }}
            />
          </LocalizationProvider>
        </Grid>

        <Grid sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Giới tính
          </Typography>
          <FormControl fullWidth variant="outlined" sx={{ height: 45 }}>
            <Select
              name="gender"
              value={formik.values.gender}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.gender && Boolean(formik.errors.gender)}
              displayEmpty
              sx={{ height: "45px" }}
            >
              <MenuItem value="" disabled>
                Chọn giới tính
              </MenuItem>
              <MenuItem value="Male">Nam</MenuItem>
              <MenuItem value="Female">Nữ</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        <Grid sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Hạng thành viên
          </Typography>
          <FormControl fullWidth variant="outlined" sx={{ height: 45 }}>
            <Select
              name="membershipLevelId"
              value={formik.values.membershipLevelId}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.membershipLevelId && Boolean(formik.errors.membershipLevelId)}
              displayEmpty
              sx={{ height: "45px" }}
            >
              <MenuItem value="" disabled>
                Chọn hạng thành viên
              </MenuItem>
              {Array.isArray(membershipLevels) &&
                membershipLevels.length > 0 &&
                membershipLevels.map((level) => (
                  <MenuItem key={level.levelId} value={level.levelId} sx={{ width: 600 }}>
                    <TruncatedText text={level.levelName} />
                  </MenuItem>
                ))}
            </Select>
          </FormControl>
        </Grid>

        <Button type="submit" variant="contained" color="primary" fullWidth>
          Cập nhật
        </Button>
      </form>
    </Dialog>
  );
};

export default FormUserInfo;
