import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { SvgIcon } from "@mui/material";
import ArticleIcon from "@mui/icons-material/Article";
import PaletteIcon from "@mui/icons-material/Palette";
import InfoIcon from "@mui/icons-material/Info";
import CategoryIcon from "@mui/icons-material/Category";
import InventoryIcon from "@mui/icons-material/Inventory";
import GroupIcon from "@mui/icons-material/Group";
import LocalOfferIcon from "@mui/icons-material/LocalOffer";
import BusinessIcon from "@mui/icons-material/Business";
import ReceiptIcon from "@mui/icons-material/Receipt";
import PeopleIcon from "@mui/icons-material/People";
import CampaignIcon from "@mui/icons-material/Campaign";
import BarChartIcon from "@mui/icons-material/BarChart";
import ExtensionIcon from "@mui/icons-material/Extension";
import GamepadIcon from "@mui/icons-material/Gamepad";
import ShareIcon from "@mui/icons-material/Share";
import SettingsIcon from "@mui/icons-material/Settings";
import AddShoppingCartIcon from "@mui/icons-material/AddShoppingCart";
import ListAltIcon from "@mui/icons-material/ListAlt";
import PersonIcon from "@mui/icons-material/Person";
import ClassIcon from "@mui/icons-material/Class";
import StarsIcon from "@mui/icons-material/Stars";
import CardMembershipIcon from "@mui/icons-material/CardMembership";
import ConfirmationNumberIcon from "@mui/icons-material/ConfirmationNumber";
import GroupWorkIcon from "@mui/icons-material/GroupWork";
import EmailIcon from "@mui/icons-material/Email";
import AdsClickIcon from "@mui/icons-material/AdsClick";
import AssessmentIcon from "@mui/icons-material/Assessment";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import PhoneAndroidIcon from "@mui/icons-material/PhoneAndroid";
import ShoppingBagIcon from "@mui/icons-material/ShoppingBag";
import DashboardIcon from "@mui/icons-material/Dashboard";
import PointOfSaleIcon from "@mui/icons-material/PointOfSale";

import Shop from "../../../public/assets/iconSidebar/shop.svg";
import Hanghoa from "../../../public/assets/iconSidebar/hanghoa.svg";
import Order from "../../../public/assets/iconSidebar/order.svg";
import Customer from "../../../public/assets/iconSidebar/customer.svg";
import Marketing from "../../../public/assets/iconSidebar/marketing.svg";
import Report from "../../../public/assets/iconSidebar/report.svg";
import AppIcon from "../../../public/assets/iconSidebar/app.svg";
import Channel from "../../../public/assets/iconSidebar/channel.svg";
import Setup from "../../../public/assets/iconSidebar/setup.svg";
import { paths } from "src/paths";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";

export const useSections = () => {
  const { t } = useTranslation();
  const { permissions, isAgency, loading } = useAllPermissions();

  // Memoize the isGranted function to prevent recreating it on each render
  const isGranted = useMemo(() => {
    return (url, permission) => {
      if (loading) return false;
      const matchingUrl = Object.keys(permissions).find((key) => key === url);
      if (!matchingUrl) return false;
      return permissions[matchingUrl]?.includes(permission) || false;
    };
  }, [permissions, loading]);

  // Return a memoized array of sections that only updates when dependencies change
  return useMemo(() => {
    // Create section items with visibility checks
    const createItems = (items) => {
      return items.filter((item) => item.visible);
    };

    const homeSection = {
      items: createItems([
        {
          title: t("Trang chủ"),
          path: paths.dashboard.index,
          icon: (
            <SvgIcon fontSize="small">
              <DashboardIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.dashboard.index, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Pos"),
          path: paths.dashboard.listLocation,
          icon: (
            <SvgIcon fontSize="small">
              <PointOfSaleIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.dashboard.listLocation, PERMISSION_TYPE_ENUM.View),
        },
      ]),
    };

    const storeSection = {
      subheader: (
        <div fontSize="small" style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <Shop />
          {t("Cửa hàng")}
        </div>
      ),
      items: createItems([
        {
          title: t("Quản lý nội dung"),
          path: paths.dashboard.store.contentManagement,
          icon: (
            <SvgIcon fontSize="small">
              <ArticleIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.dashboard.store.contentManagement, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Quản lý giao diện"),
          path: paths.dashboard.store.themeSettings,
          icon: (
            <SvgIcon fontSize="small">
              <PaletteIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.dashboard.store.themeSettings, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Thông tin cửa hàng"),
          path: paths.dashboard.store.generalSettings,
          icon: (
            <SvgIcon fontSize="small">
              <InfoIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.dashboard.store.generalSettings, PERMISSION_TYPE_ENUM.View),
        },
      ]),
    };

    const productSection = {
      subheader: (
        <div fontSize="small" style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <Hanghoa />
          {t("Hàng hóa")}
        </div>
      ),
      items: createItems([
        {
          title: t("Danh mục"),
          path: paths.dashboard.product.categoryManagement,
          icon: (
            <SvgIcon fontSize="small">
              <CategoryIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.dashboard.product.categoryManagement, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Sản phẩm"),
          path: paths.dashboard.product.productManagement,
          icon: (
            <SvgIcon fontSize="small">
              <InventoryIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.dashboard.product.productManagement, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Nhóm tuỳ chọn"),
          path: paths.itemGroups.list,
          icon: (
            <SvgIcon fontSize="small">
              <GroupIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.itemGroups.list, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Bảng giá"),
          path: paths.priceList.list,
          icon: (
            <SvgIcon fontSize="small">
              <LocalOfferIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.priceList.list, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Nhà cung cấp"),
          path: paths.dashboard.product.supplier,
          icon: (
            <SvgIcon fontSize="small">
              <BusinessIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.dashboard.product.supplier, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Thương hiệu"),
          path: paths.dashboard.product.brand,
          visible: isGranted(paths.dashboard.product.brand, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Đánh giá"),
          path: paths.dashboard.product.evaluate,
          visible: isGranted(paths.dashboard.product.evaluate, PERMISSION_TYPE_ENUM.View),
        },
      ]),
    };

    const orderSection = {
      subheader: (
        <div fontSize="small" style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <Order />
          {t("Đơn hàng")}
        </div>
      ),
      items: createItems([
        {
          title: t("Tạo đơn hàng"),
          path: paths.orders.draft.listdraftorder,
          icon: (
            <SvgIcon fontSize="small">
              <AddShoppingCartIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.orders.draft.listdraftorder, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Danh sách đơn hàng"),
          path: paths.orders.list,
          icon: (
            <SvgIcon fontSize="small">
              <ListAltIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.orders.list, PERMISSION_TYPE_ENUM.View),
        },
      ]),
    };

    const customerSection = {
      subheader: (
        <div fontSize="small" style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <Customer />
          {t("Khách hàng")}
        </div>
      ),
      items: createItems([
        {
          title: t("Danh sách khách hàng"),
          path: paths.customers.list,
          icon: (
            <SvgIcon fontSize="small">
              <PersonIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.customers.list, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Phân loại khách hàng"),
          path: paths.customers.customerClassification,
          icon: (
            <SvgIcon fontSize="small">
              <ClassIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.customers.customerClassification, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Điểm thành viên"),
          path: paths.customers.membershipPoints,
          icon: (
            <SvgIcon fontSize="small">
              <StarsIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.customers.membershipPoints, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Thành viên trả phí"),
          path: paths.customers.paidMembership,
          icon: (
            <SvgIcon fontSize="small">
              <CardMembershipIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.customers.paidMembership, PERMISSION_TYPE_ENUM.View),
        },
      ]),
    };

    const marketingSection = {
      subheader: (
        <div fontSize="small" style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <Marketing />
          {t("Marketing")}
        </div>
      ),
      items: createItems([
        {
          title: t("Voucher"),
          path: paths.marketing.vouchers.list,
          icon: (
            <SvgIcon fontSize="small">
              <ConfirmationNumberIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.marketing.vouchers.list, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Mua theo nhóm"),
          path: paths.marketing.affiliate.BuyGroup,
          icon: (
            <SvgIcon fontSize="small">
              <GroupWorkIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.marketing.affiliate.BuyGroup, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Email marketing"),
          path: paths.marketing.affiliate.Email,
          icon: (
            <SvgIcon fontSize="small">
              <EmailIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.marketing.affiliate.Email, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Promoting ads"),
          path: paths.marketing.affiliate.PromotingsAds,
          icon: (
            <SvgIcon fontSize="small">
              <AdsClickIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.marketing.affiliate.PromotingsAds, PERMISSION_TYPE_ENUM.View),
        },
      ]),
    };

    const reportSection = {
      subheader: (
        <div fontSize="small" style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <Report />
          {t("Báo cáo")}
        </div>
      ),
      items: createItems([
        {
          title: t("Báo cáo tồn kho"),
          path: paths.report.Inventory,
          icon: (
            <SvgIcon fontSize="small">
              <InventoryIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.report.Inventory, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Báo cáo bán hàng"),
          path: paths.report.Sell,
          icon: (
            <SvgIcon fontSize="small">
              <TrendingUpIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.report.Sell, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Báo cáo người dùng"),
          path: paths.report.User,
          icon: (
            <SvgIcon fontSize="small">
              <AccountCircleIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.report.User, PERMISSION_TYPE_ENUM.View),
        },
      ]),
    };

    const appSection = {
      subheader: (
        <div fontSize="small" style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <AppIcon />
          {t("Ứng dụng")}
        </div>
      ),
      items: createItems([
        {
          title: t("Marketing Automation"),
          path: paths.marketing_automation.home,
          icon: (
            <SvgIcon fontSize="small">
              <CampaignIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.marketing_automation.home, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Gamification"),
          path: paths.app.gamification,
          icon: (
            <SvgIcon fontSize="small">
              <GamepadIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.app.gamification, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Tiếp thị liên kết"),
          path: paths.marketing.affiliate.index,
          icon: (
            <SvgIcon fontSize="small">
              <ShareIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.marketing.affiliate.index, PERMISSION_TYPE_ENUM.View),
        },
      ]),
    };

    const channelSection = {
      subheader: (
        <div fontSize="small" style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <Channel />
          {t("Kênh bán")}
        </div>
      ),
      items: createItems([
        {
          title: t("Zalo Mini App"),
          path: paths.reports.statistics,
          icon: (
            <SvgIcon fontSize="small">
              <PhoneAndroidIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.reports.statistics, PERMISSION_TYPE_ENUM.View),
        },
        {
          title: t("Tiktok Shop"),
          path: paths.sale.tiktok,
          icon: (
            <SvgIcon fontSize="small">
              <ShoppingBagIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.sale.tiktok, PERMISSION_TYPE_ENUM.View),
        },
      ]),
    };

    const settingsSection = {
      subheader: (
        <div fontSize="small" style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <Setup />
          {t("Cài đặt")}
        </div>
      ),
      items: createItems([
        {
          title: t("Cài đặt"),
          path: paths.settings.payment.index,
          icon: (
            <SvgIcon fontSize="small">
              <SettingsIcon />
            </SvgIcon>
          ),
          visible: isGranted(paths.settings.payment.index, PERMISSION_TYPE_ENUM.View),
        },
      ]),
    };

    // Return only sections with items
    return [
      homeSection,
      storeSection,
      productSection,
      orderSection,
      customerSection,
      marketingSection,
      reportSection,
      appSection,
      channelSection,
      settingsSection,
    ].filter((section) => section.items.length > 0);
  }, [t, isGranted]); // Only recalculate when translations or permissions change
};
