import { formatMoney } from "@/src/utils/format-money";
import {
  getOrderStatusPayLabel,
  getTransportStatusLabel,
  OrderProductPayStatusWithBg,
} from "@/src/utils/order/order-helper";
import { Box, Button, Divider, Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";
import React from "react";

export default function OrderDetailPaymentSummary({ order, handleClickPaid }) {
  const totalQuantity = order?.listItems.reduce((total, item) => total + item.quantity, 0);
  return (
    <Box>
      <Box marginBottom={2}>
        <OrderProductPayStatusWithBg status={order?.statusPay} />
      </Box>
      <Box>
        <Grid container spacing={2} marginBottom={1}>
          <Grid size={4}>
            <Box display="flex" gap={1} alignItems="center">
              <Typography variant="body2" color="text.secondary">
                Thành tiền chưa thuế:
              </Typography>
            </Box>
          </Grid>
          <Grid size={2}>
            <Box display="flex" alignItems="center" height="100%">
              <Typography variant="body2">{totalQuantity} sản phẩm</Typography>
            </Box>
          </Grid>
          <Grid size={6}>
            <Box display="flex" alignItems="center" height="100%" justifyContent="end">
              <Typography fontWeight="bold" variant="body2">
                {formatMoney(order?.price || 0)}đ
              </Typography>
            </Box>
          </Grid>
        </Grid>
        <Grid container spacing={2} marginBottom={1}>
          <Grid size={4}>
            <Box display="flex" gap={1} alignItems="center">
              <Typography variant="body2" color="text.secondary">
                Tiền thuế GTGT:
              </Typography>
            </Box>
          </Grid>
          <Grid size={2}>
            <Box display="flex" alignItems="center" height="100%">
              <Typography></Typography>
            </Box>
          </Grid>
          <Grid size={6}>
            <Box display="flex" alignItems="center" height="100%" justifyContent="end">
              <Typography fontWeight="bold" variant="body2">
                {formatMoney(order?.totalTaxAmount || 0)}đ
              </Typography>
            </Box>
          </Grid>
        </Grid>
        <Grid container spacing={2} marginBottom={1}>
          <Grid size={4}>
            <Box display="flex" gap={1} alignItems="center">
              <Typography variant="body2" color="text.secondary">
                Thanh toán điểm:
              </Typography>
            </Box>
          </Grid>
          <Grid size={2}>
            <Box display="flex" alignItems="center" height="100%">
              <Typography variant="body2">
                {order?.exchangePoints > 0 ? `-${order?.exchangePoints}` : 0}
              </Typography>
            </Box>
          </Grid>
          <Grid size={6}>
            <Box display="flex" alignItems="center" height="100%" justifyContent="end">
              <Typography fontWeight="bold" variant="body2">
                {order?.pointPrice > 0 ? `-${formatMoney(order?.pointPrice)}` : 0}đ
              </Typography>
            </Box>
          </Grid>
        </Grid>
        {/* {order?.statusDelivery !== "InShop" && ( */}
        <>
          <Grid container spacing={2} marginBottom={1}>
            <Grid size={4}>
              <Box display="flex" gap={1} alignItems="center">
                <Typography variant="body2" color="text.secondary">
                  Phí vận chuyển:
                </Typography>
              </Box>
            </Grid>
            <Grid size={2}>
              <Box display="flex" alignItems="center" height="100%">
                <Typography></Typography>
              </Box>
            </Grid>
            <Grid size={6}>
              <Box display="flex" alignItems="center" height="100%" justifyContent="end">
                <Typography fontWeight="bold" variant="body2">
                  {formatMoney(order?.transportPrice || 0)}đ
                </Typography>
              </Box>
            </Grid>
          </Grid>
          <Grid container spacing={2} marginBottom={1}>
            <Grid size={4}>
              <Box display="flex" gap={1} alignItems="center">
                <Typography variant="body2" color="text.secondary">
                  Giảm giá vận chuyển:
                </Typography>
              </Box>
            </Grid>
            <Grid size={2}>
              <Box display="flex" alignItems="center" height="100%">
                <Typography></Typography>
              </Box>
            </Grid>
            <Grid size={6}>
              <Box display="flex" alignItems="center" height="100%" justifyContent="end">
                <Typography fontWeight="bold" variant="body2">
                  {order?.voucherTransportPrice > 0
                    ? `-${formatMoney(order?.voucherTransportPrice)}`
                    : 0}
                  đ
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </>
        {/* )} */}
        <Grid container spacing={2} marginBottom={1}>
          <Grid size={4}>
            <Box display="flex" gap={1} alignItems="center">
              <Typography variant="body2" color="text.secondary">
                Giảm giá sản phẩm:
              </Typography>
            </Box>
          </Grid>
          <Grid size={2}>
            <Box display="flex" alignItems="center" height="100%">
              <Typography></Typography>
            </Box>
          </Grid>
          <Grid size={6}>
            <Box display="flex" alignItems="center" height="100%" justifyContent="end">
              <Typography fontWeight="bold" variant="body2">
                {order?.voucherPromotionPrice > 0
                  ? `-${formatMoney(order?.voucherPromotionPrice)}`
                  : 0}
                đ
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {/* <Grid container spacing={2}>
          <Grid size={4}>
            <Box display="flex" gap={1} alignItems="center">
              <Typography variant="body2" color="text.secondary">
                Tổng cộng:
              </Typography>
            </Box>
          </Grid>
          <Grid size={2}></Grid>
          <Grid size={6}>
            <Box display="flex" alignItems="center" height="100%" justifyContent="end">
              <Typography fontWeight="bold" variant="body2">
                {formatMoney(
                  order?.price +
                    totalTaxPrice +
                    order?.transportPrice -
                    order?.voucherTransportPrice -
                    order?.voucherPromotionPrice || 0
                )}
                đ
              </Typography>
            </Box>
          </Grid>
        </Grid> */}
      </Box>
      <Divider sx={{ marginBottom: 2, marginTop: 2 }} />
      <Box>
        <Grid container spacing={2}>
          <Grid size={4}>
            <Box display="flex" gap={1} alignItems="center">
              <Typography sx={{ fontWeight: 600, fontSize: 17 }}>
                Tổng cộng tiền thanh toán:
              </Typography>
            </Box>
          </Grid>
          {/* <Grid size={2}>
            <Box display="flex" gap={1} alignItems="center">
              <Typography variant="body2">{order?.typePay}</Typography>
            </Box>
          </Grid> */}
          <Grid size={8}>
            <Box display="flex" alignItems="center" height="100%" justifyContent="end">
              <Typography fontWeight="bold" variant="body2">
                {formatMoney(order?.totalAfterTax || 0)}đ
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Box>
      <Divider sx={{ marginBottom: 2, marginTop: 2 }} />
      {order?.statusPay === "NotPaid" && (
        <Box display="flex" justifyContent="end">
          <Button
            variant="contained"
            onClick={handleClickPaid}
            disabled={order?.statusOrder === "Failed"}
          >
            Đã thanh toán
          </Button>
        </Box>
      )}
    </Box>
  );
}
