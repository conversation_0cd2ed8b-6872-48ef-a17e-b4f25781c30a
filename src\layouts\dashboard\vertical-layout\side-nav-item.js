import { useCallback, useState, useEffect, useRef } from "react";
import PropTypes from "prop-types";
// import ChevronDownIcon from "@untitled-ui/icons-react/build/esm/ChevronDown"; // Temporarily unused
// import ChevronRightIcon from "@untitled-ui/icons-react/build/esm/ChevronRight"; // Temporarily unused
import Box from "@mui/material/Box";
import ButtonBase from "@mui/material/ButtonBase";
import Collapse from "@mui/material/Collapse";
import SvgIcon from "@mui/material/SvgIcon";
import { RouterLink } from "src/components/router-link";
// import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions"; // Temporarily unused
// import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant"; // Temporarily unused
// import { paths } from "@/src/paths"; // Temporarily unused
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
  Tooltip,
} from "@mui/material";
import { useRouter } from "next/router";
import { useFunction } from "@/src/api/hooks/function/use-function";
import FiberManualRecordIcon from "@mui/icons-material/FiberManualRecord";
import MenuIcon from "@mui/icons-material/Menu";

export const SideNavItem = (props) => {
  const {
    active,
    children,
    depth = 0,
    disabled,
    external,
    icon,
    label,
    open: openProp,
    path,
    title,
    isCollapsed = false,
  } = props;
  const [open, setOpen] = useState(!!openProp);
  const itemRef = useRef(null);
  const [openDialog, setOpenDialog] = useState(false);
  const router = useRouter();
  const { checkPermission } = useFunction();

  const handleToggle = useCallback(() => {
    setOpen((prevOpen) => !prevOpen);
  }, []);

  useEffect(() => {
    if (active && itemRef.current) {
      itemRef.current.scrollIntoView({
        behavior: "auto",
        block: "center",
      });
    }
  }, [active]);

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  let startIcon;

  if (depth === 0) {
    // Nếu có icon thì dùng, không thì dùng icon mặc định
    startIcon = icon || (
      <SvgIcon
        sx={{
          fontSize: isCollapsed ? 20 : 16,
          width: isCollapsed ? 20 : 16,
          height: isCollapsed ? 20 : 16,
        }}
      >
        <FiberManualRecordIcon
          sx={{
            fontSize: 8,
            color: active ? "#2654FE" : "#666",
          }}
        />
      </SvgIcon>
    );
  } else {
    startIcon = (
      <Box
        sx={{
          alignItems: "center",
          display: "center",
          height: 20,
          justifyContent: "center",
          width: 20,
        }}
      >
        <Box
          sx={{
            backgroundColor: "var(--nav-item-icon-color)",
            borderRadius: "50%",
            height: 4,
            opacity: 0,
            width: 4,
            ...(active && {
              backgroundColor: "var(--nav-item-icon-active-color)",
              height: 6,
              opacity: 1,
              width: 6,
            }),
          }}
        />
      </Box>
    );
  }

  const offset = depth === 0 ? 0 : (depth - 1) * 16;

  // Branch
  if (children) {
    return (
      <li ref={itemRef}>
        {" "}
        {/* Gắn ref vào đây */}
        <ButtonBase
          disabled={disabled}
          onClick={handleToggle}
          sx={{
            alignItems: "center",
            borderRadius: 1,
            display: "flex",
            justifyContent: "flex-start",
            pl: `${16 + offset}px`,
            pr: "16px",
            py: "6px",
            textAlign: "left",
            width: "100%",
            ...(active && {
              ...(depth === 0 && {
                backgroundColor: "var(--nav-item-active-bg)",
              }),
            }),
            "&:hover": {
              backgroundColor: "var(--nav-item-hover-bg)",
            },
          }}
        >
          <Box
            component="span"
            sx={{
              flexGrow: 1,
              fontSize: depth > 0 ? 13 : 14,
              fontWeight: depth > 0 ? 500 : 600,
              lineHeight: "24px",
              whiteSpace: "nowrap",
              ...(active && {
                color: "#000000",
                fontWeight: "400",
              }),
              ...(disabled && {
                color: "#000000",
                fontWeight: "400",
              }),
            }}
          >
            {title}
          </Box>
        </ButtonBase>
        <Collapse in={open} sx={{ mt: 0.5 }}>
          {children}
        </Collapse>
      </li>
    );
  }

  // Leaf
  const linkProps = path
    ? external
      ? {
          component: "a",
          href: path,
          target: "_blank",
        }
      : {
          component: RouterLink,
          href: path,
        }
    : {};

  const buttonContent = (
    <ButtonBase
      disabled={disabled}
      sx={{
        alignItems: "center",
        borderRadius: 1,
        display: "flex",
        justifyContent: isCollapsed ? "center" : "flex-start",
        pl: isCollapsed ? 0 : depth === 0 ? "16px" : `${23 + offset}px`,
        pr: isCollapsed ? 0 : "16px",
        py: "6px",
        textAlign: "left",
        width: "100%",
        marginBottom: "5px",
        height: "40px",
        ...(active && {
          ...(depth === 0 && {
            background: "#2654FE",
            color: "#fff !important",
            "& *": {
              color: "#fff !important",
            },
            "& svg": {
              filter:
                "invert(100%) sepia(0%) saturate(7485%) hue-rotate(140deg) brightness(100%) contrast(103%)",
            },
          }),
        }),
        "&:hover": {
          backgroundColor: "#2654FE",
          borderRadius: "8px",
          "& *": {
            color: "#fff",
          },
          "& svg": {
            filter:
              "invert(100%) sepia(0%) saturate(7485%) hue-rotate(140deg) brightness(100%) contrast(103%)",
            color: "#fff !important",
          },
          ...(isCollapsed && {
            transform: "scale(1.02)",
            transition: "all 0.2s ease",
          }),
        },
        ...(isCollapsed && {
          width: "44px",
          height: "44px",
          minHeight: "44px",
          borderRadius: "8px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          margin: "0 auto 4px auto",
          // Styling đặc biệt cho menu cha (Dashboard, POS)
          ...(depth === 0 && {
            width: "48px",
            height: "48px",
            minHeight: "48px",
            marginBottom: "6px",
          }),
        }),
      }}
      onClick={async () => {
        const res = await checkPermission({
          url: path,
          permission: "View",
        });
        if (res?.status === 200 && res.data.data) {
          if (
            res.data.data.hasPermission == false &&
            res.data.data.isAgency == true &&
            path !== "/dashboard"
          ) {
            setOpenDialog(true);
          } else {
            router.push(path);
          }
        }
      }}
      // {...(isGranted(path, PERMISSION_TYPE_ENUM.View) ? linkProps : {})}
    >
      {startIcon && (
        <Box
          component="span"
          sx={{
            alignItems: "center",
            color: "var(--nav-item-icon-color)",
            display: "inline-flex",
            justifyContent: "center",
            mr: isCollapsed ? 0 : "8px",
            width: isCollapsed ? "auto" : "auto",
            ...(active && {
              color: "var(--nav-item-icon-active-color)",
            }),
            // Styling chung cho tất cả SVG icons
            "& svg": {
              width: isCollapsed ? "20px" : "16px",
              height: isCollapsed ? "20px" : "16px",
              "& path": {
                fill: "currentColor",
              },
              "& g": {
                fill: "currentColor",
              },
            },

            ...(isCollapsed && {
              "& svg": {
                fontSize: "20px !important",
                color: active ? "#2654FE" : "#666",
                display: "block",
                // Đặc biệt cho custom SVG icons
                "& path": {
                  fill: "currentColor",
                },
                "& g": {
                  fill: "currentColor",
                },
              },
              "& div": {
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              },
            }),
          }}
        >
          {startIcon}
        </Box>
      )}
      {!isCollapsed && (
        <Box
          component="span"
          sx={{
            color: "#000000",
            fontWeight: "400",
            flexGrow: 1,
            fontFamily: (theme) => theme.typography.fontFamily,
            fontSize: depth > 0 ? 13 : 14,
            lineHeight: "24px",
            whiteSpace: "nowrap",
            ...(active && {
              color: "#000000",
              fontWeight: "700",
            }),
            ...(disabled && {
              color: "#000000",
              fontWeight: "700",
            }),
          }}
        >
          {title}
        </Box>
      )}
      {label && !isCollapsed && (
        <Box component="span" sx={{ ml: 2, color: "red" }}>
          {label}
        </Box>
      )}
    </ButtonBase>
  );

  return (
    <li ref={itemRef} className="item-sidebar">
      {isCollapsed ? (
        <Tooltip title={title} placement="right" arrow>
          {buttonContent}
        </Tooltip>
      ) : (
        buttonContent
      )}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        PaperProps={{
          sx: {
            borderRadius: "24px",
            p: 3,
            maxWidth: "400px",
            width: "100%",
            boxShadow: "0px 8px 24px rgba(0, 0, 0, 0.15)",
          },
        }}
      >
        <DialogContent sx={{ p: 0, mt: 2 }}>
          <Box sx={{ textAlign: "center", mb: 3 }}>
            <Typography sx={{ fontWeight: "600", fontSize: "1.1rem", mb: 1 }}>Nâng cấp</Typography>
            <Typography sx={{ fontSize: "0.9rem", color: "text.secondary" }}>
              Mua gói dịch vụ để tiếp tục thực hiện chức năng
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 0, justifyContent: "center", gap: 2 }}>
          {/* Temporarily hidden package purchase button */}
          {/* <Button
            onClick={() => {
              setOpenDialog(false);
              router.push("/dashboard/package");
            }}
            variant="contained"
            sx={{
              borderRadius: "50px",
              textTransform: "none",
              fontSize: "0.95rem",
              px: 3,
              py: 1,
              backgroundColor: "#6366F1",
              "&:hover": {
                backgroundColor: "#5254cc",
              },
            }}
          >
            Mua gói dịch vụ
          </Button> */}
          <Button
            onClick={() => {
              setOpenDialog(false);
            }}
            variant="outlined"
            sx={{
              borderRadius: "50px",
              textTransform: "none",
              fontSize: "0.95rem",
              px: 3,
              py: 1,
              borderColor: "#6366F1",
              color: "#6366F1",
              "&:hover": {
                borderColor: "#5254cc",
                color: "#5254cc",
              },
            }}
          >
            Đóng
          </Button>
        </DialogActions>
      </Dialog>
    </li>
  );
};

SideNavItem.propTypes = {
  active: PropTypes.bool,
  children: PropTypes.node,
  depth: PropTypes.number,
  disabled: PropTypes.bool,
  external: PropTypes.bool,
  icon: PropTypes.node,
  label: PropTypes.node,
  open: PropTypes.bool,
  path: PropTypes.string,
  title: PropTypes.string.isRequired,
};
