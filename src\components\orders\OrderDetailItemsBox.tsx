import { useItemOption } from "@/src/api/hooks/item-option/use-item-option";
import { useValidImage } from "@/src/hooks/useValidImage";
import { useAppSelector } from "@/src/redux/hooks";
import { formatMoney } from "@/src/utils/format-money";
import {
  getTransportStatusLabel,
  OrderProductTransportStatusWithBg,
} from "@/src/utils/order/order-helper";
import {
  Backdrop,
  Box,
  Button,
  Dialog,
  DialogContent,
  Divider,
  Fade,
  Modal,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import React, { useEffect, useState } from "react";

export default function OrderDetailItemsBox({
  order,
  handleClickUpdateStatusOrder,
  handleClickCancelOrder,
}) {
  return (
    <Box>
      <Box marginBottom={2}>
        <OrderProductTransportStatusWithBg status={order?.statusTransport} />
      </Box>

      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ minWidth: "200px" }}>Tên sản phẩm</TableCell>
              <TableCell sx={{ width: "20%" }} align="left">
                Đơn giá
              </TableCell>
              <TableCell sx={{ minWidth: "100px" }} align="left">
                Số lượng
              </TableCell>
              <TableCell sx={{ minWidth: "150px" }} align="left">
                Thành tiền chưa thuế
              </TableCell>
              <TableCell sx={{ minWidth: "150px" }} align="left">
                Chiết khấu mã giảm giá
              </TableCell>
              <TableCell sx={{ minWidth: "150px" }} align="left">
                Chiết khấu điểm thưởng
              </TableCell>
              <TableCell sx={{ minWidth: "150px" }} align="left">
                Thuế suất GTGT
              </TableCell>
              <TableCell sx={{ minWidth: "150px" }} align="left">
                Tiền thuế GTGT
              </TableCell>
              <TableCell sx={{ minWidth: "250px" }} align="left">
                Thành tiền đã có thuế GTGT
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {order?.listItems.map((item, index) => (
              <OrderDetailItem key={item.itemsId} item={item} />
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      {order?.statusOrder !== "Failed" && order?.statusOrder !== "Success" && (
        <Box display="flex" justifyContent="space-between" sx={{ mt: 2 }}>
          <Button variant="outlined" color="error" onClick={handleClickCancelOrder}>
            Hủy đơn
          </Button>
          <Button
            variant="contained"
            onClick={() => handleClickUpdateStatusOrder("StatusDelivery")}
          >
            {order?.statusTransport === "Created" || order?.statusTransport === "WaitingForDelivery"
              ? "Đang vận chuyển"
              : "Hoàn thành"}
          </Button>
        </Box>
      )}
    </Box>
  );
}

const OrderDetailItem = ({ item }) => {
  const [openZoom, setOpenZoom] = useState(false);
  const currentShop = useAppSelector((state) => state.shop.currentShop);

  const handleOpenZoom = () => setOpenZoom(true);
  const handleCloseZoom = () => setOpenZoom(false);
  const isVariant = item.isVariant;

  const imgItemSrc = useValidImage(item.images?.[0]?.link, currentShop?.shopLogo?.link);
  const imgVariantSrc = useValidImage(item.variantImage?.link, imgItemSrc);
  const itemTotalPrice = item.quantity * item.price;
  const { listItemOptionByIds } = useItemOption();
  const [itemOptions, setItemOptions] = useState([]);

  const fetchItemOptions = async (itemOptionIds) => {
    const response = await listItemOptionByIds(itemOptionIds);
    if (response?.data) setItemOptions(response.data.data);
  };

  useEffect(() => {
    if (item.extraOptions && item.extraOptions.length > 0) {
      fetchItemOptions(item.extraOptions);
    }
  }, [item]);

  return (
    <>
      <TableRow>
        <TableCell>
          <Box display="flex" gap={1}>
            <Box
              // sx={{
              //   width: 50,
              //   height: 50,
              //   overflow: "hidden",
              //   borderRadius: 1,
              //   boxShadow: 3,
              //   flexShrink: 0,
              // }}
              sx={{
                width: 50,
                height: 50,
                overflow: "hidden",
                borderRadius: 1,
                boxShadow: 3,
                flexShrink: 0,
                cursor: "pointer",
                "&:hover": {
                  opacity: 0.8,
                },
              }}
              onClick={handleOpenZoom}
            >
              <img
                src={imgVariantSrc}
                alt={item.itemsName}
                style={{
                  width: "100%",
                  height: "100%",
                  objectFit: "cover",
                }}
              />
            </Box>
            <Box display="flex" flexDirection="column">
              <Typography>{item.itemsName}</Typography>
              {isVariant && (
                <Typography variant="subtitle2" color="text.secondary">
                  {[item.variantValueOne, item.variantValueTwo, item.variantValueThree]
                    .filter((value) => value != null && value !== "")
                    .join("/")}
                </Typography>
              )}
              {itemOptions?.length > 0 && (
                <Typography variant="subtitle2" color="text.secondary">
                  {itemOptions
                    ?.map(
                      (itemGroup) =>
                        `${itemGroup.name}: ${itemGroup.itemOptions
                          .map((itemOption) => itemOption.name)
                          .join(", ")}`
                    )
                    .join("; ")}
                </Typography>
              )}
              {item?.note && (
                <Typography variant="subtitle2" color="text.secondary">
                  Ghi chú: {item?.note}
                </Typography>
              )}
            </Box>
          </Box>
        </TableCell>
        <TableCell align="left">
          <Typography>{formatMoney(item.price)}đ</Typography>
        </TableCell>
        <TableCell align="left">
          <Typography>x{item.quantity}</Typography>
        </TableCell>
        <TableCell align="left">
          <Typography>{formatMoney(item.totalBeforeTax)}đ</Typography>
        </TableCell>
        <TableCell align="left">
          <Typography>{formatMoney(item.voucherDiscount)}đ</Typography>
        </TableCell>
        <TableCell align="left">
          <Typography>{formatMoney(item.pointDiscount)}đ</Typography>
        </TableCell>
        <TableCell align="left">
          <Typography>{item.taxRate ? item.taxRate : 0}%</Typography>
        </TableCell>
        <TableCell align="left">
          <Typography>{item.taxAmount ? formatMoney(item.taxAmount) : 0}đ</Typography>
        </TableCell>
        <TableCell align="left">
          <Typography>{formatMoney(item.totalAfterTax)}</Typography>
        </TableCell>
      </TableRow>

      <Dialog
        open={openZoom}
        onClose={handleCloseZoom}
        maxWidth={false}
        PaperProps={{
          sx: {
            backgroundColor: "background.paper",
            borderRadius: 1,
            p: 1,
            maxWidth: "90vw",
            maxHeight: "90vh",
          },
        }}
      >
        <DialogContent sx={{ p: 0 }}>
          <img
            src={imgVariantSrc}
            alt={item.itemsName}
            style={{
              maxWidth: "100%",
              maxHeight: "80vh",
              objectFit: "contain",
            }}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};
