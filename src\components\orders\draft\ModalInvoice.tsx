import React, { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Tabs,
  Tab,
  Box,
  TextField,
  Button,
  FormControlLabel,
  Checkbox,
  Typography,
  InputAdornment,
} from "@mui/material";
import {
  ArrowBack as ArrowBackIcon,
  Receipt as ReceiptIcon,
  Search as SearchIcon,
} from "@mui/icons-material";
import { useTaxInvoice } from "@/src/api/hooks/tax-invoice/use-tax-invoice";
import useSnackbar from "@/src/hooks/use-snackbar";

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 2 }}>{children}</Box>}
    </div>
  );
}

export default function ModalInvoice({
  open,
  setOpen,
  businessForm,
  setBusinessForm,
  individualForm,
  setIndividualForm,
  tabValue,
  setTabValue,
}) {
  const { getInforBusinessConfigTaxInvoice, getInforIndividualConfigTaxInvoice } = useTaxInvoice();
  const snackbar = useSnackbar();
  const [errors, setErrors] = useState({
    business: {
      taxCode: false,
      companyName: false,
      address: false,
      email: false,
    },
    individual: {
      name: false,
      taxCode: false,
      address: false,
      email: false,
    },
  });

  const validateForm = () => {
    if (tabValue === 0) {
      // Business validation
      const businessErrors = {
        taxCode: !businessForm.taxCode,
        companyName: !businessForm.companyName,
        address: !businessForm.address,
        email: !businessForm.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(businessForm.email),
      };

      setErrors((prev) => ({
        ...prev,
        business: businessErrors,
      }));

      return !Object.values(businessErrors).some((error) => error);
    } else {
      // Individual validation
      const individualErrors = {
        name: !individualForm.name,
        taxCode: !individualForm.taxCode,
        address: !individualForm.address,
        email: !individualForm.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(individualForm.email),
      };

      setErrors((prev) => ({
        ...prev,
        individual: individualErrors,
      }));

      return !Object.values(individualErrors).some((error) => error);
    }
  };

  // Form states for business

  const handleTabChange = (event, newValue) => {
    if (newValue === 0) {
      setIndividualForm({
        address: "",
        name: "",
        taxCode: "",
        email: "",
        taxPayerType: "",
      });
    } else {
      setBusinessForm({
        taxCode: "",
        companyName: "",
        address: "",
        email: "",
        taxPayerType: "",
      });
    }
    setTabValue(newValue);
  };

  const handleBusinessFormChange = (field) => (event) => {
    setBusinessForm((prev) => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  const handleIndividualFormChange = (field) => (event) => {
    setIndividualForm((prev) => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  const handleSubmit = () => {
    if (!validateForm()) {
      snackbar.error("Vui lòng điền đầy đủ thông tin");
      return;
    }
    const formData = tabValue === 0 ? businessForm : individualForm;
    // if()
    setOpen(false);
  };

  const handleLookup = () => {
    getInforBusinessOrIndividual(tabValue);
  };

  const getInforBusinessOrIndividual = async (tabValue: number) => {
    if (tabValue === 0) {
      const res = await getInforBusinessConfigTaxInvoice(businessForm.taxCode);
      if (res && res?.data?.data !== null) {
        setBusinessForm((prev) => ({
          ...prev,
          address: res?.data?.data?.address,
          companyName: res?.data?.data?.name,
          taxPayerType: res?.data?.data?.taxPayerType,
        }));
      } else {
        snackbar.info("Không tìm thấy doanh nghiệp này");
      }
    } else {
      const res = await getInforIndividualConfigTaxInvoice(
        individualForm.taxCode,
        individualForm.name
      );
      if (res && res?.data?.data !== null) {
        setIndividualForm((prev) => ({
          ...prev,
          address: res?.data?.data.address,
          taxPayerType: res?.data?.data?.taxPayerType,
        }));
      } else {
        snackbar.info("Không tìm thấy thông tin cá nhân này");
      }
    }
  };

  return (
    <div className="p-8">
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            maxHeight: "90vh",
          },
        }}
      >
        <DialogTitle sx={{ p: 0 }}>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              p: 2,
              borderBottom: 1,
              borderColor: "divider",
              backgroundColor: "#fafafa",
            }}
          >
            <IconButton onClick={() => setOpen(false)} sx={{ mr: 1 }}>
              <ArrowBackIcon />
            </IconButton>
            <ReceiptIcon sx={{ mr: 1, color: "#2e7d32" }} />
            <Typography variant="h6" sx={{ fontWeight: 600, fontSize: 14 }}>
              Xuất hóa đơn GTGT
            </Typography>
          </Box>
        </DialogTitle>

        <DialogContent sx={{ p: 0 }}>
          <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              sx={{
                "& .MuiTab-root": {
                  textTransform: "none",
                  fontSize: "16px",
                  fontWeight: 500,
                },
                "& .Mui-selected": {
                  color: "primary.main !important",
                },
                "& .MuiTabs-indicator": {
                  backgroundColor: "primary.main",
                },
              }}
            >
              <Tab label="Doanh nghiệp" />
              <Tab label="Cá nhân" />
            </Tabs>
          </Box>

          {/* Business Tab */}
          <TabPanel value={tabValue} index={0}>
            <Box sx={{ p: 2 }}>
              <Box sx={{ mb: 3 }}>
                <TextField
                  fullWidth
                  placeholder="Mã số thuế *"
                  value={businessForm.taxCode}
                  onChange={handleBusinessFormChange("taxCode")}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <Button
                          size="small"
                          onClick={handleLookup}
                          sx={{
                            minWidth: "auto",
                            px: 2,
                            color: "#666",
                            border: "1px solid #ddd",
                            borderRadius: 1,
                          }}
                        >
                          Tra cứu
                        </Button>
                      </InputAdornment>
                    ),
                  }}
                  sx={{ mb: 2 }}
                />
                <Typography variant="body2" sx={{ color: "#666", mb: 2 }}>
                  Vui lòng nhập <strong>Mã số thuế</strong> để Tra cứu thông tin
                </Typography>
              </Box>

              <TextField
                fullWidth
                placeholder="Tên công ty *"
                value={businessForm.companyName}
                onChange={handleBusinessFormChange("companyName")}
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                placeholder="Địa chỉ *"
                value={businessForm.address}
                onChange={handleBusinessFormChange("address")}
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                placeholder="Email nhận hóa đơn *"
                value={businessForm.email}
                onChange={handleBusinessFormChange("email")}
                sx={{ mb: 3 }}
              />
            </Box>
          </TabPanel>

          {/* Individual Tab */}
          <TabPanel value={tabValue} index={1}>
            <Box sx={{ p: 2 }}>
              <TextField
                fullWidth
                placeholder="Họ tên *"
                value={individualForm.name}
                onChange={handleIndividualFormChange("name")}
                sx={{ mb: 2 }}
              />

              <Box sx={{ mb: 3 }}>
                <TextField
                  fullWidth
                  placeholder="Mã số thuế *"
                  value={individualForm.taxCode}
                  onChange={handleIndividualFormChange("taxCode")}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <Button
                          size="small"
                          onClick={handleLookup}
                          sx={{
                            minWidth: "auto",
                            px: 2,
                            color: "#666",
                            border: "1px solid #ddd",
                            borderRadius: 1,
                          }}
                        >
                          Tra cứu
                        </Button>
                      </InputAdornment>
                    ),
                  }}
                  sx={{ mb: 2 }}
                />
                <Typography variant="body2" sx={{ color: "#666", mb: 2 }}>
                  Vui lòng nhập <strong>Họ tên</strong> và <strong>Mã số thuế</strong> để Tra cứu
                  thông tin
                </Typography>
              </Box>

              <TextField
                fullWidth
                placeholder="Họ tên *"
                value={individualForm.name}
                onChange={handleIndividualFormChange("name")}
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                placeholder="Địa chỉ *"
                value={individualForm.address}
                onChange={handleIndividualFormChange("address")}
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                placeholder="Email nhận hóa đơn *"
                value={individualForm.email}
                onChange={handleIndividualFormChange("email")}
                sx={{ mb: 3 }}
              />
            </Box>
          </TabPanel>

          {/* Footer */}
          <Box sx={{ p: 2, paddingTop: 0, backgroundColor: "#fafafa" }}>
            <Button
              fullWidth
              variant="contained"
              onClick={handleSubmit}
              sx={{
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#primary.main",
                },
                py: 1.5,
                fontSize: "16px",
                textTransform: "none",
                borderRadius: 2,
              }}
            >
              Xác nhận
            </Button>
          </Box>
        </DialogContent>
      </Dialog>
    </div>
  );
}
