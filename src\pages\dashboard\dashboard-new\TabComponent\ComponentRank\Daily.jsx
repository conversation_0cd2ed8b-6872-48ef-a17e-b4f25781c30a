import React from 'react';
import { Box, Stack, Typography, Avatar, Grid, Card } from '@mui/material';

const rankingData = [
  { rank: 1, name: '<PERSON><PERSON><PERSON>', image: '/path-to-image-1.jpg', score: '100' },
  { rank: 2, name: '<PERSON><PERSON><PERSON>', image: '/path-to-image-2.jpg', score: '99' },
  { rank: 3, name: 'Tuấ<PERSON> Ngô3', image: '/path-to-image-3.jpg', score: '98' },
  { rank: 4, name: '<PERSON><PERSON><PERSON>', image: '', score: '70' },
  { rank: 5, name: '<PERSON><PERSON><PERSON>', image: '', score: '60' },
  { rank: 6, name: '<PERSON><PERSON><PERSON>', image: '', score: '50' },
  { rank: 7, name: '<PERSON><PERSON><PERSON>', image: '', score: '40' },
  { rank: 8, name: '<PERSON><PERSON><PERSON>', image: '', score: '20' },
  { rank: 9, name: '<PERSON><PERSON><PERSON>', image: '', score: '11' },
];

const Daily = () => {
  if (!Array.isArray(rankingData)) {
    return (
      <Box>
        <Typography color="error">Lỗi: rankingData không phải là mảng.</Typography>
      </Box>
    );
  }

  const topAffiliate = rankingData[0];

  const secondAffiliate = rankingData.slice(1, 2);
  const thirdAffiliate = rankingData.slice(2, 3);

  return (
    <Box>
      <Stack>
        {topAffiliate && (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              marginBottom: '-25px',
              '@media(max-width: 480px)': {
                marginBottom: '20px',
              },
            }}
          >
            <Stack alignItems="center">
              <Box
                sx={{
                  position: 'relative',
                  mb: 1,
                }}
              >
                <img src="/logo/logo-dashboard/Group 48472.png" alt="Logo" />

                <Avatar
                  sx={{
                    width: 58,
                    height: 58,
                    borderRadius: '50%',
                    position: 'absolute',
                    top: '28px',
                    left: '27%',
                  }}
                  alt={topAffiliate.name}
                  src={topAffiliate.image}
                />
              </Box>
              <Box position={'relative'}>
                <img
                  src="/logo/logo-dashboard/Group 48473.png"
                  alt="Logo"
                  style={{ minWidth: '120px' }}
                />

                <Typography
                  position={'absolute'}
                  top={'3px'}
                  left={'25%'}
                  fontSize={'14px'}
                  fontWeight={'400'}
                  color="#fff"
                >
                  {topAffiliate.name}
                </Typography>
              </Box>
            </Stack>
          </Box>
        )}
      </Stack>
      <Stack flexDirection={'row'} justifyContent={'space-between'}>
        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 3, mb: 3 }}>
          {secondAffiliate.map((affiliate) => (
            <Stack key={affiliate.rank} alignItems="center">
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
                <Stack alignItems="center">
                  <Box
                    sx={{
                      position: 'relative',
                      mb: 1,
                    }}
                  >
                    <img src="/logo/logo-dashboard/Union.png" alt="Logo" />

                    <Avatar
                      sx={{
                        width: 58,
                        height: 58,
                        borderRadius: '50%',
                        position: 'absolute',
                        top: '28px',
                        left: '27%',
                      }}
                      alt={affiliate.name}
                      src={affiliate.image}
                    />
                  </Box>
                  <Box position={'relative'}>
                    <img
                      src="/logo/logo-dashboard/Group 48473.png"
                      alt="Logo"
                      style={{ minWidth: '120px' }}
                    />

                    <Typography
                      position={'absolute'}
                      top={'3px'}
                      left={'25%'}
                      fontSize={'14px'}
                      fontWeight={'400'}
                      color="#fff"
                    >
                      {affiliate.name}
                    </Typography>
                  </Box>
                </Stack>
              </Box>
            </Stack>
          ))}
        </Box>

        <Box
          sx={{ display: 'flex', justifyContent: 'center', alignContent: 'center', gap: 3, mb: 3 }}
        >
          {thirdAffiliate.map((affiliate) => (
            <Stack key={affiliate.rank} alignItems="center" justifyContent={'center'}>
              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                <Stack alignItems="center">
                  <Box
                    sx={{
                      position: 'relative',
                      mb: 1,
                    }}
                  >
                    <img src="/logo/logo-dashboard/frame.png" alt="Logo" />

                    <Avatar
                      sx={{
                        width: 58,
                        height: 58,
                        borderRadius: '50%',
                        position: 'absolute',
                        top: '4px',
                        left: '8%',
                      }}
                      alt={affiliate.name}
                      src={affiliate.image}
                    />
                  </Box>
                  <Box position={'relative'}>
                    <img src="/logo/Group 48473.png" alt="Logo" style={{ minWidth: '120px' }} />

                    <Typography
                      position={'absolute'}
                      top={'3px'}
                      left={'25%'}
                      fontSize={'14px'}
                      fontWeight={'400'}
                      color="#fff"
                    >
                      {affiliate.name}
                    </Typography>
                  </Box>
                </Stack>
              </Box>
            </Stack>
          ))}
        </Box>
      </Stack>

      <Grid container spacing={2} sx={{}}>
        {rankingData.slice(3).map((affiliate) => (
          <Grid item xs={12} sm={6} key={affiliate.rank}>
            <Card sx={{ display: 'flex', alignItems: 'center', gap: '38px', padding: '25px' }}>
              <Typography color="#000000" fontSize={'20px'} fontWeight={'400'}>
                {affiliate.rank}
              </Typography>
              <Typography color="#000000" fontSize={'20px'} fontWeight={'400'}>
                {affiliate.name}
              </Typography>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default Daily;
