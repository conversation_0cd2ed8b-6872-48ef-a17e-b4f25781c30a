import React from "react";
import {
  Text<PERSON>ield,
  FormControl,
  FormLabel,
  Typography,
  Box,
  useTheme,
  alpha,
  InputAdornment,
  Tooltip,
  IconButton,
} from "@mui/material";
import { Controller } from "react-hook-form";
import InfoIcon from "@mui/icons-material/Info";

interface FormFieldProps {
  name: string;
  control: any;
  label: string;
  placeholder?: string;
  required?: boolean;
  type?: string;
  multiline?: boolean;
  rows?: number;
  disabled?: boolean;
  helperText?: string;
  tooltip?: string;
  startAdornment?: React.ReactNode;
  endAdornment?: React.ReactNode;
  error?: any;
  sx?: any;
  fullWidth?: boolean;
  variant?: "outlined" | "filled" | "standard";
  size?: "small" | "medium";
}

const FormField: React.FC<FormFieldProps> = ({
  name,
  control,
  label,
  placeholder,
  required = false,
  type = "text",
  multiline = false,
  rows = 4,
  disabled = false,
  helperText,
  tooltip,
  startAdornment,
  endAdornment,
  error,
  sx = {},
  fullWidth = true,
  variant = "outlined",
  size = "medium",
}) => {
  const theme = useTheme();

  return (
    <FormControl fullWidth={fullWidth} sx={sx}>
      <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
        <Typography
          component="label"
          sx={{
            fontSize: "0.875rem",
            fontWeight: 500,
            color: theme.palette.text.primary,
            display: "flex",
            alignItems: "center",
          }}
        >
          {label}
          {required && (
            <Typography
              component="span"
              sx={{
                color: theme.palette.error.main,
                ml: 0.5,
                fontSize: "0.875rem",
              }}
            >
              *
            </Typography>
          )}
        </Typography>
        {tooltip && (
          <Tooltip title={tooltip} placement="top">
            <IconButton size="small" sx={{ ml: 0.5, p: 0.25 }}>
              <InfoIcon sx={{ fontSize: 16, color: theme.palette.text.secondary }} />
            </IconButton>
          </Tooltip>
        )}
      </Box>

      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            fullWidth={fullWidth}
            variant={variant}
            size={size}
            type={type}
            multiline={multiline}
            rows={multiline ? rows : undefined}
            placeholder={placeholder}
            disabled={disabled}
            error={!!error}
            helperText={error?.message || helperText}
            InputProps={{
              startAdornment: startAdornment,
              endAdornment: endAdornment,
            }}
            InputLabelProps={{
              shrink: true,
            }}
            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: 2,
                backgroundColor: disabled
                  ? alpha(theme.palette.action.disabled, 0.05)
                  : theme.palette.background.paper,
                "&:hover .MuiOutlinedInput-notchedOutline": {
                  borderColor: alpha(theme.palette.primary.main, 0.4),
                },
                "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 1,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: alpha(theme.palette.divider, 0.3),
                  transition: "border-color 0.2s ease-in-out",
                },
              },
              "& .MuiFormHelperText-root": {
                fontSize: "0.75rem",
                mt: 0.75,
              },
            }}
          />
        )}
      />
    </FormControl>
  );
};

export default FormField;
