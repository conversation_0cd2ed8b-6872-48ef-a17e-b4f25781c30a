import React, { useEffect, useState } from "react";
import {
  Box,
  Typography,
  IconButton,
  Paper,
  Link as MuiLink,
  Stack,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  Switch,
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ToggleOnIcon from "@mui/icons-material/ToggleOn";
import ToggleOffIcon from "@mui/icons-material/ToggleOff";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import SettingLayout from "@/src/components/settings/settings-page/SettingLayout";
import Link from "next/link";
import { paths } from "@/src/paths";
import Grid from "@mui/system/Grid";
import { branchService } from "@/src/api/services/branch/branch.service";
import { GetServiceRequest } from "@/src/api/types/service.types";
import { ApiClient, TokenService } from "nextjs-api-lib";
import { tokens } from "@/src/locales/tokens";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/router";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useShop } from "@/src/api/hooks/shop/use-shop";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { usePathname } from "next/navigation";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
export interface Branch {
  address: string;
  branchId: string;
  branchName: string;
  created: string;
  districtId: string;
  districtName: string;
  houseNumber: string;
  image: null;
  location: null;
  phoneNumber: string;
  provinceId: string;
  provinceName: string;
  updated: string;
  wardId: string;
  wardName: string;
}

export default function SalesPoints() {
  const [salesPointEnabled, setSalesPointEnabled] = useState(true);
  const [listBranch, setListBranch] = useState<Branch[]>([]);
  const { t } = useTranslation();
  const [openDialog, setOpenDialog] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const [selectedBranch, setSelectedBranch] = useState(null);
  const storeId = useStoreId();
  const { detailShop, updateShopDelivery } = useShop();
  const [enableInShop, setEnableInShop] = useState<boolean>(false);
  const [enableExpressDelivery, setEnableExpressDelivery] = useState<boolean>(false);
  const snackbar = useSnackbar();

  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  const handleToggle = () => {
    setSalesPointEnabled(!salesPointEnabled);
  };
  const fetchBranch = async () => {
    try {
      const params = {
        skip: 0,
        limit: 99,
      };
      const res = await branchService.getBranchs(storeId, params);
      setListBranch(res?.data?.data);
    } catch (error) {
      console.error("Error fetching branches:", error);
    }
  };

  const fetchShopDetail = async () => {
    try {
      const detail = await detailShop(storeId);
      const shopDetail = detail?.data as any;
      if (shopDetail) {
        setEnableInShop(shopDetail.enableInShop);
        setEnableExpressDelivery(shopDetail.enableExpressDelivery);
      }
    } catch (error) {
    } finally {
    }
  };

  useEffect(() => {
    if (storeId) {
      fetchBranch();
      fetchShopDetail();
    }
  }, [storeId]);

  const handleDeleteBranch = async (branch) => {
    setSelectedBranch(branch);
    setOpenDialog(true);
  };
  const confirmDelete = async () => {
    if (selectedBranch) {
      const res = await branchService.deleteBranch(selectedBranch.branchId);
      setOpenDialog(false);
      fetchBranch();
    }
  };

  const handleEditBranch = (branch: any) => {
    router.push({
      pathname: paths.settings.addLocation,
      query: { id: branch?.branchId },
    });
  };

  const handleToggleInshop = async (e) => {
    const res = await updateShopDelivery({
      shopId: storeId,
      enableInShop: e.target.checked,
    });
    if (res.data) {
      fetchShopDetail();
      snackbar.success("Cập nhật thành công");
    }
  };

  const handleToggleExpressDelivery = async (e) => {
    const res = await updateShopDelivery({
      shopId: storeId,
      enableExpressDelivery: e.target.checked,
    });
    if (res.data) {
      fetchShopDetail();
      snackbar.success("Cập nhật thành công");
    }
  };

  return (
    <SettingLayout>
      <Typography variant="h5" sx={{ ml: 1 }}>
        Điểm bán
      </Typography>
      <Grid container spacing={2} sx={{ mt: 1 }}>
        <Grid size={{ xs: 12, md: 4 }} sx={{ p: 2, mt: 3 }}></Grid>
        <Grid size={{ xs: 12, md: 8 }} sx={{ flexGrow: 1, p: 3, pb: 0, pt: 0 }}>
          <Stack sx={{ backgroundColor: "white", boxShadow: 3, p: 2, borderRadius: "8px" }}>
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Typography variant="h6">Nhận tại cửa hàng</Typography>
              <Switch
                disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                checked={enableInShop}
                onChange={handleToggleInshop}
                inputProps={{ "aria-label": "controlled" }}
              />
            </Stack>

            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
              sx={{ mt: 1 }}
            >
              <Typography variant="h6">Nhận hàng tại nhà</Typography>
              <Switch
                disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                checked={enableExpressDelivery}
                onChange={handleToggleExpressDelivery}
                inputProps={{ "aria-label": "controlled" }}
              />
            </Stack>
          </Stack>
        </Grid>
      </Grid>

      <Grid container spacing={2} sx={{ mt: 1 }}>
        <Grid size={{ xs: 12, md: 4 }} sx={{ p: 2, mt: 3 }}>
          <Typography variant="body2" color="text.secondary" sx={{ px: { xs: 2, md: 1 } }}>
            Quản lý nơi lưu trữ hàng tồn kho, giao hàng và bán sản phẩm.
          </Typography>
        </Grid>
        <Grid size={{ xs: 12, md: 8 }} sx={{ flexGrow: 1, p: 3 }}>
          <Stack spacing={0}>
            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
              sx={{ backgroundColor: "white", boxShadow: 3, p: 2, borderRadius: "8px 8px 0 0" }}
            >
              <Typography variant="h6">Danh sách điểm bán</Typography>
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                sx={{ backgroundColor: "white" }}
              >
                {isGranted(pathname, PERMISSION_TYPE_ENUM.Add) ? (
                  <Link href={paths.settings.location.add} passHref>
                    <MuiLink
                      sx={{
                        color: "#2654FE",
                        textDecoration: "none",
                        fontSize: "0.875rem",
                        "&:hover": {
                          textDecoration: "underline",
                        },
                      }}
                    >
                      Thêm điểm bán
                    </MuiLink>
                  </Link>
                ) : (
                  <Tooltip title="Bạn không có quyền thêm">
                    <span>
                      <MuiLink
                        sx={{
                          color: "gray",
                          textDecoration: "none",
                          fontSize: "0.875rem",
                          cursor: "not-allowed",
                          pointerEvents: "none",
                          opacity: 0.5,
                        }}
                      >
                        Thêm điểm bán
                      </MuiLink>
                    </span>
                  </Tooltip>
                )}
              </Stack>
            </Stack>

            <Paper
              sx={{
                p: 2,
                bgcolor: "#F4F6F8",
                borderColor: "divider",
                boxShadow: 3,
              }}
            >
              {listBranch &&
                listBranch.length > 0 &&
                listBranch.map((branch, index) => (
                  <Stack spacing={1} id={branch.branchId} key={index}>
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                      <Stack direction="row" spacing={1} alignItems="center">
                        <Typography variant="subtitle1">{branch.branchName}</Typography>
                        {/* <Box
                          sx={{
                            bgcolor: 'primary.lighter',
                            color: 'primary.main',
                            px: 1,
                            py: 0.5,
                            borderRadius: 1,
                            typography: 'caption',
                            // display: 'none'
                          }}
                        >
                          Mặc định
                        </Box> */}
                      </Stack>
                      {/* <IconButton onClick={handleToggle} sx={{ p: 0 }}>
                        {salesPointEnabled ? (
                          <ToggleOnIcon sx={{ fontSize: 40, color: 'blue' }} />
                        ) : (
                          <ToggleOffIcon sx={{ fontSize: 40, color: 'grey' }} />
                        )}
                      </IconButton> */}
                      <Box sx={{ display: "flex", justifyContent: "center", gap: 1 }}>
                        {isGranted(pathname, PERMISSION_TYPE_ENUM.Edit) ? (
                          <Link
                            href={paths.settings.location.index + "/" + branch.branchId}
                            passHref
                          >
                            <Tooltip title={t(tokens.contentManagement.category.tooltips.edit)}>
                              <IconButton
                                onClick={() => handleEditBranch(branch)}
                                size="small"
                                sx={{ color: "primary.main" }}
                              >
                                <EditIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Link>
                        ) : (
                          <Tooltip title="Bạn không có quyền chỉnh sửa">
                            <span>
                              <IconButton
                                size="small"
                                sx={{ color: "gray", opacity: 0.5 }}
                                disabled
                              >
                                <EditIcon fontSize="small" />
                              </IconButton>
                            </span>
                          </Tooltip>
                        )}

                        {isGranted(pathname, PERMISSION_TYPE_ENUM.Delete) ? (
                          <Tooltip title={t(tokens.contentManagement.category.tooltips.delete)}>
                            <IconButton
                              onClick={() => handleDeleteBranch(branch)}
                              size="small"
                              sx={{ color: "error.main" }}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        ) : (
                          <Tooltip title="Bạn không có quyền xóa">
                            <span>
                              <IconButton
                                size="small"
                                sx={{ color: "gray", opacity: 0.5 }}
                                disabled
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </span>
                          </Tooltip>
                        )}
                      </Box>
                      <Dialog
                        open={openDialog}
                        onClose={() => setOpenDialog(false)}
                        maxWidth="sm"
                        fullWidth
                      >
                        <DialogTitle>
                          {t(tokens.contentManagement.category.delete.confirmTitle)}
                        </DialogTitle>
                        <DialogContent>
                          <DialogContentText>
                            {t(tokens.contentManagement.category.delete.confirmMessage, {
                              name: selectedBranch?.branchName,
                            })}
                          </DialogContentText>
                        </DialogContent>
                        <DialogActions>
                          <Button onClick={() => setOpenDialog(false)} variant="outlined">
                            {t(tokens.common.cancel)}
                          </Button>
                          <Button
                            onClick={confirmDelete}
                            color="error"
                            variant="contained"
                            autoFocus
                          >
                            {t(tokens.common.delete)}
                          </Button>
                        </DialogActions>
                      </Dialog>
                    </Stack>
                    <Typography variant="body2" color="text.secondary">
                      {branch.address}
                    </Typography>
                  </Stack>
                ))}
            </Paper>
          </Stack>
        </Grid>
      </Grid>
    </SettingLayout>
  );
}
