import { apiClient } from "../../config/api-client";
import { API_PATHS } from "../../constants/api-paths";
import type { ApiError, ErrorConfig } from "../../types/error.types";
import type { ExtendedRequestConfig } from "../../types/api.types";
import { logger } from "@/src/utils/logger";
import { UpdateUserInfoBody } from "../../types/user.types";
import { Interface } from "readline";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }

  throw error;
}

export interface FunctionPartnerDto {
  packageId: string;
  paymentMethod: string;
  invoiceNumber?: string;
}

export interface IBodyCheckPermission {
  url: string;
  permission: string;
}

export const functionService = {
  purchasePackage: async <T = any>(data: FunctionPartnerDto, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<T>(
        API_PATHS.PARTNER_FUNCTION.PURCHASE_PACKAGE,
        data,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  upgradePackage: async <T = any>(data: FunctionPartnerDto, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<T>(
        API_PATHS.PARTNER_FUNCTION.UPGRADE_PACKAGE,
        data,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getActivePackageFunctions: async <T = any>(errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<T>(
        `${API_PATHS.PARTNER_FUNCTION.ACTIVE_PACKAGE_FUNCTIONS}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getAvailablePackages: async <T = any>(errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.get<T>(
        `${API_PATHS.PARTNER_FUNCTION.AVAILABLE_PACKAGES}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  checkPermission: async <T = any>(data: IBodyCheckPermission, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.post<T>(
        `${API_PATHS.PARTNER_FUNCTION.CHECK_PERMISSION}`,
        data,
        config
      );
      return response;
    } catch (error: any) {
      // handleApiError(error);
    }
  },

  getPackageHistory: async <T = any>(errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.get<T>(
        `${API_PATHS.PARTNER_FUNCTION.PACKAGE_HISTORY}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getAllPermissions: async <T = any>(errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.get<T>(
        `${API_PATHS.PARTNER_FUNCTION.ALL_PERMISSIONS}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
