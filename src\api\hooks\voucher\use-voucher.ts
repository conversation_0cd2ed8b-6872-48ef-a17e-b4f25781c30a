import { useState } from "react";
import { useStoreId } from "@/src/hooks/use-store-id";
import { GetProductCategoryRequest, CategoryType } from "@/src/api/types/product-category.types";
import { productCategoryService } from "@/src/api/services/dashboard/product/category.service";
import { ErrorHandlerService } from "@/src/api/services/error-handler.service";
import { StorageService } from "nextjs-api-lib";
import { voucherService } from "@/src/api/services/voucher/voucher.service";

export const useVoucher = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const partnerId = StorageService.get("partnerId") as string | null;
  const getProductCategory = async (
    skip: number,
    limit: number,
    categoryType: CategoryType = "Product",
    search: string = "",
    shopId: string
  ) => {
    try {
      setLoading(true);
      setError(null);
      const params = {
        shopId,
        partnerId: partnerId || null,
        categoryType,
        search,
        skip,
        limit,
      };
      const response = await voucherService.getProductCategory(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const createVoucher = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await voucherService.createVoucher(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getVoucher = async (id: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await voucherService.getVoucher(id);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const listVoucher = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await voucherService.listVoucher(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const listVoucherDetail = async (data: { voucherId: string; skip: number; limit: number }) => {
    try {
      setLoading(true);
      setError(null);
      const response = await voucherService.listVoucherDetail(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const exportPdf = async (voucherId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await voucherService.exportPdf(voucherId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const deleteVoucher = async (voucherIds: string[]) => {
    try {
      setLoading(true);
      setError(null);
      const response = await voucherService.deleteVoucher(voucherIds);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const changeActiveVoucher = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await voucherService.changeActiveVoucher(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updateVoucher = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await voucherService.updateVoucher(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const listItemsByItemsIds = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await voucherService.listItemsByItemsIds(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  return {
    getProductCategory,
    createVoucher,
    getVoucher,
    listVoucher,
    deleteVoucher,
    changeActiveVoucher,
    updateVoucher,
    listItemsByItemsIds,
    listVoucherDetail,
    exportPdf,
    loading,
    error,
  };
};
