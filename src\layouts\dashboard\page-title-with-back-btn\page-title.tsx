import { IconButton, Typography } from "@mui/material";
import { Box } from "@mui/system";
import { useRouter } from "next/router";
import React from "react";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { paths } from "@/src/paths";

interface PageTitleWithBackBtnProps {
  title: string;
  backPath?: string;
  sx?: object;
}

export default React.memo(function PageTitleWithBackBtn({
  title,
  backPath,
  sx,
}: PageTitleWithBackBtnProps) {
  const router = useRouter();
  const handleCancel = () => {
    if (backPath) {
      router.push(backPath);
    } else {
      router.push(paths.customers.list);
    }
  };

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        ...sx,
        paddingBottom: "20px",
        marginBottom: "40px",
        borderBottom: "1px solid #bdbdbd",
      }}
    >
      <IconButton onClick={handleCancel} sx={{ mr: 1, p: 0 }}>
        <ArrowBackIcon />
      </IconButton>
      <Typography fontSize={"20px !important"} fontWeight={"500"} marginBottom={0}>
        {title}
      </Typography>
    </Box>
  );
});
