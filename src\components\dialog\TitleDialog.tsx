import * as React from "react";
import Button, { ButtonPropsColorOverrides } from "@mui/material/Button";
import { styled } from "@mui/material/styles";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import Typography from "@mui/material/Typography";
import { Breakpoint } from "@mui/system";
import { OverridableStringUnion } from "@mui/types";
const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialogContent-root": {
    padding: theme.spacing(2),
  },
  "& .MuiDialogActions-root": {
    padding: theme.spacing(1),
  },
}));

export default function TitleDialog({
  open,
  handleSubmit,
  handleClose,
  children,
  title = "",
  closeBtnTitle = "Hủy",
  submitBtnTitle = "Lưu",
  showActionDialog = true,
  maxWidth = "sm",
  color,
}: {
  open: boolean;
  handleSubmit?: () => void;
  handleClose?: () => void;
  children: React.ReactNode;
  title?: string;
  closeBtnTitle?: string;
  submitBtnTitle?: string;
  showActionDialog?: boolean;
  maxWidth?: Breakpoint; // 'xs' |'sm' |'md' | 'lg' | 'xl'
  color?: OverridableStringUnion<
    "inherit" | "primary" | "secondary" | "success" | "error" | "info" | "warning",
    ButtonPropsColorOverrides
  >;
}) {
  return (
    <React.Fragment>
      <BootstrapDialog
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={open}
        fullWidth={true}
        maxWidth={maxWidth}
      >
        <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
          <Typography sx={{ fontSize: 18, fontWeight: 600 }}>{title}</Typography>
        </DialogTitle>
        <IconButton
          aria-label="close"
          onClick={handleClose}
          sx={(theme) => ({
            position: "absolute",
            right: 8,
            top: 8,
            color: theme.palette.grey[500],
          })}
        >
          <CloseIcon />
        </IconButton>
        <DialogContent dividers>{children}</DialogContent>
        {showActionDialog ? (
          <DialogActions sx={{ margin: "8px" }}>
            <Button variant="outlined" onClick={handleClose}>
              {closeBtnTitle}
            </Button>
            <Button color={color} variant="contained" onClick={handleSubmit}>
              {submitBtnTitle}
            </Button>
          </DialogActions>
        ) : null}
      </BootstrapDialog>
    </React.Fragment>
  );
}
