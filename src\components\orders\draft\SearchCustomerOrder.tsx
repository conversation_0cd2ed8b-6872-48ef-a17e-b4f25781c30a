import { useCart } from "@/src/api/hooks/cart/use-cart";
import { useStoreId } from "@/src/hooks/use-store-id";
import { paths } from "@/src/paths";
import {
  Box,
  Button,
  Checkbox,
  Divider,
  InputAdornment,
  Radio,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
} from "@mui/material";
import _ from "lodash";
import React, { useCallback, useEffect, useState } from "react";
import TitleDialog from "../../dialog/TitleDialog";
import CreateCustomerOrder from "./CreateCustomerOrder";
import { useUser } from "@/src/api/hooks/user/use-user";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useDebounce } from "@/src/hooks/use-debounce";
import TruncatedText from "../../truncated-text/truncated-text";
import { Search } from "@mui/icons-material";

export default function SearchCustomerOrder({ handleSubmit, handleClose, selectedCustomer }) {
  const snackbar = useSnackbar();
  const [searchText, setSearchText] = useState("");
  const { createUser, createUserAddress, loading, error } = useUser();
  const [openDialog, setOpenDialog] = useState(false);

  const debouncedSearchValue = useDebounce(searchText, 500);

  const [localSelectedCustomer, setLocalSelectedCustomer] = useState(selectedCustomer); // Local state for selectedCustomer
  const [items, setItems] = useState([]);

  const handleFetchItems = (items) => {
    setItems(items);
  };

  const handleSelectOne = (event, item) => {
    setLocalSelectedCustomer(item);
  };

  const storeId = useStoreId();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const { searchUsers, loading: productLoading } = useCart();

  const fetchItemList = async (currentPage, pageSize, searchQuery, shopId) => {
    const skip = currentPage * pageSize;
    const limit = pageSize;
    const response = await searchUsers(skip, limit, shopId, searchQuery);

    if (response?.data) {
      handleFetchItems(response.data.data || []);
      setTotalCount(response.data.total || 0);
    }
  };

  // Wrap fetchUserList with debounce
  const debouncedFetchItemList = useCallback(
    _.debounce((currentPage, pageSize, searchQuery, shopId) => {
      fetchItemList(currentPage, pageSize, searchQuery, shopId);
    }, 400), // Delay 1s
    []
  );

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const onClickCreateCustomer = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleCreateCustomerForm = async (data) => {
    const userData = {
      fullName: data.name,
      email: data.email,
      phoneNumber: data.phone,
      shopId: storeId,
    };

    const response = await createUser(userData);
    if (response && response.data) {
      const { userId } = response.data;

      const userAddressData = {
        fullName: userData.fullName,
        address: data.shippingAddressAddress,
        phoneNumber: userData.phoneNumber,
        wardId: data.shippingAddressWard,
        districtId: data.shippingAddressDistrict,
        provinceId: data.shippingAddressProvince,
        userId,
      };

      const responseUserAddress = await createUserAddress(userAddressData);
      handleCloseDialog();
      debouncedFetchItemList(page, rowsPerPage, debouncedSearchValue, storeId);
      snackbar.success("Tạo tài khoản khách hàng thành công");
    }
  };

  useEffect(() => {
    if (!storeId) return;
    debouncedFetchItemList(page, rowsPerPage, debouncedSearchValue, storeId);
    return () => {
      debouncedFetchItemList.cancel(); // Cancel debounce if component unmounts or searchText changes
    };
  }, [storeId, page, rowsPerPage, debouncedSearchValue]);

  // Biến đổi dữ liệu từ `selectedCustomer`
  useEffect(() => {
    setLocalSelectedCustomer(selectedCustomer); // Chỉ sao chép vào state, không thay đổi prop
  }, [selectedCustomer]);

  return (
    <Box>
      <TextField
        fullWidth
        placeholder="Tìm kiếm số điện thoại, tên, email"
        variant="outlined"
        size="small"
        onChange={(e) => setSearchText(e.target.value)}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Search />
            </InputAdornment>
          ),
        }}
      />

      <Box>
        <Box sx={{ marginTop: 2 }}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell></TableCell>
                  <TableCell>Tên</TableCell>
                  <TableCell>Số điện thoại</TableCell>
                  <TableCell>Email</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {items.map((item, index) => (
                  <TableRow key={item.userId}>
                    <TableCell padding="checkbox">
                      <Radio
                        checked={
                          localSelectedCustomer && localSelectedCustomer.userId === item.userId
                        }
                        onChange={() => handleSelectOne(null, item)}
                        value={item.userId}
                        name="radio-buttons"
                      />
                    </TableCell>
                    <TableCell>
                      <TruncatedText typographyProps={{ width: 200 }} text={item.fullname} />
                    </TableCell>
                    <TableCell sx={{ fontSize: 15 }}>{item.phoneNumber}</TableCell>
                    <TableCell sx={{ fontSize: 15 }}>{item.email}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            component="div"
            count={totalCount}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={[5, 10, 20]}
            labelRowsPerPage="Số dòng mỗi trang" // Thay đổi văn bản ở phần này
          />
        </Box>
        <Divider />
        <Box
          display="flex"
          flexDirection={{ xs: "column", sm: "row" }}
          justifyContent={{ xs: "flex-start", md: "space-between" }}
          marginTop={2}
        >
          <Box>
            <Button variant="outlined" onClick={onClickCreateCustomer}>
              Tạo khách hàng
            </Button>
          </Box>
          <Box sx={{ display: "flex", justifyContent: "end" }} marginTop={{ xs: 2, md: 0 }}>
            <Button sx={{ marginRight: 1 }} variant="outlined" onClick={handleClose}>
              Hủy
            </Button>
            <Button variant="contained" onClick={() => handleSubmit(localSelectedCustomer)}>
              Xác nhận
            </Button>
          </Box>
        </Box>
      </Box>

      <TitleDialog
        title="Thêm khách hàng"
        open={openDialog}
        handleClose={handleCloseDialog}
        submitBtnTitle="Xác nhận"
        showActionDialog={false}
        maxWidth="md"
      >
        <CreateCustomerOrder
          handleSubmitForm={handleCreateCustomerForm}
          handleClose={handleCloseDialog}
        />
      </TitleDialog>
    </Box>
  );
}
