import { VariantType } from '../types/product/form';
import { Specification } from '../types/product/variant';

export const convertVariantsToSpecifications = (variants: VariantType[]): Specification[] => {
  const specMap = new Map<string, Set<string>>();

  variants.forEach((variant) => {
    // Xử lý variantNameOne và variantValueOne
    if (variant.variantNameOne && variant.variantValueOne) {
      if (!specMap.has(variant.variantNameOne)) {
        specMap.set(variant.variantNameOne, new Set());
      }
      specMap.get(variant.variantNameOne)?.add(variant.variantValueOne);
    }

    // Xử lý variantNameTwo và variantValueTwo
    if (variant.variantNameTwo && variant.variantValueTwo) {
      if (!specMap.has(variant.variantNameTwo)) {
        specMap.set(variant.variantNameTwo, new Set());
      }
      specMap.get(variant.variantNameTwo)?.add(variant.variantValueTwo);
    }

    // Xử lý variantNameThree và variantValueThree
    if (variant.variantNameThree && variant.variantValueThree) {
      if (!specMap.has(variant.variantNameThree)) {
        specMap.set(variant.variantNameThree, new Set());
      }
      specMap.get(variant.variantNameThree)?.add(variant.variantValueThree);
    }
  });

  // Chuyển đổi Map thành mảng Specification
  return Array.from(specMap).map(([name, values]) => ({
    name,
    values: Array.from(values)
  }));
}; 