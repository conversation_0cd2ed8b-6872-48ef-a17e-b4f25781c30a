// Request Types
export interface GetGroupFileRequest {
  ShopId: string;
  Skip?: number;
  Limit?: number;
  Search?: string;
}
export interface CreateGroupFileRequest {
  groupFileId: string;
  partnerId: string;
  shopId: string;
  groupName: string;
  numberFile?: number;
  status: string;
  created?: string;
  updated?: string;
}

export enum RefType {
  Shop = "Shop",
  TriggerEvent = "TriggerEvent",
  Campaign = "Campaign",
  User = "User",
  CategoryProduct = "CategoryProduct",
  CategoryService = "CategoryService",
  Product = "Product",
  Service = "Service",
  ZaloUID = "ZaloUID",
  Voucher = "Voucher",
  ArticleCategory = "ArticleCategory",
  Article = "Article",
  MembershipPoint = "MembershipPoint",
  PopupAds = "PopupAds",
}

export interface GetFileGroupRequest {
  ShopId: string;
  RefType?: RefType;
  GroupFileId?: string;
  Skip?: number;
  Limit?: number;
  Search?: string;
}

export interface CreateFileGroupRequest {
  ShopId: string;
  GroupFileId: string;
  RefType?: RefType;
  RefId?: string;
  FileUpload: File;
}

export interface CreateGroupRequest {
  groupName: string;
  desc?: string;
}

export interface UpdateGroupRequest {
  groupFileId: string;
  groupName: string;
  desc?: string;
}

// Response Types
export interface MediaGroup {
  groupFileId?: string;
  groupName: string;
  desc?: string;
  numberFile: number;
  created?: string;
  updated?: string;
}

export interface MediaFile {
  mediaFileId: string;
  groupFileId: string;
  type: "IMAGE" | "VIDEO";
  link: string;
  created: string;
  updated: string;
}

export interface BaseResponse<T> {
  data: {
    data: T[];
    total: number;
    page: number;
    limit: number;
  };
  status: number;
  message: string;
}

export interface MediaResponse extends BaseResponse<MediaFile> {}
export interface MediaGroupResponse extends BaseResponse<MediaGroup> {}
