import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import SettingLayout from "@/src/components/settings/settings-page/SettingLayout";
import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  IconButton,
  Paper,
  TextField,
  Typography,
  Button,
  Collapse,
  Divider,
  FormHelperText,
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import Grid from "@mui/system/Grid";
import AuthorizationScreen from "./components/authorization-screen";
import { useSections } from "@/src/layouts/dashboard/config";
import { useFunction } from "@/src/api/hooks/function/use-function";
import { useRole } from "@/src/api/hooks/role/use-role";
import {
  FunctionObject,
  RoleBodyCreateApi,
  RoleBodyUpdateApi,
} from "@/src/api/services/role/role.service";
import { paths } from "@/src/paths";
import AuthorizationScreenUpdate from "./components/authorization-screen-update";
import { RoleDto } from "@/src/components/settings/settings-page/Auth";
import { PackageDto, FunctionDto } from "./add-role";

interface RoleFormData {
  displayName?: string;
  roleName?: string;
  roleDescription?: string;
}

const validationSchema = Yup.object({
  // displayName: Yup.string()
  //   .required("Tên hiển thị là bắt buộc")
  //   .min(2, "Tên hiển thị phải có ít nhất 2 ký tự"),
  roleName: Yup.string()
    .required("Tên vai trò là bắt buộc")
    .min(2, "Tên vai trò phải có ít nhất 2 ký tự")
    .max(255, "Tên vai trò không được vượt quá 255 ký tự"),
  roleDescription: Yup.string().max(1024, "Mô tả không được vượt quá 1024 ký tự"),

  // roleDescription: Yup.string(),
});

export interface selectedPermissionsDto {
  moduleId: {
    functionId: string[];
  };
}

export default function AddRole() {
  const router = useRouter();
  const [activePackage, setActivePackage] = useState<PackageDto>();
  const { roleId } = router.query;
  // const isViewMode = mode === "view";
  const [selectedPermissions, setSelectedPermissions] = useState<FunctionObject[]>();
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset,
  } = useForm<RoleFormData>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      // displayName: "",
      roleName: "",
      roleDescription: "",
    },
  });

  const [orderOpen, setOrderOpen] = useState(false);
  const [editOrderOpen, setEditOrderOpen] = useState(false);
  const sections = useSections();
  const { getActivePackageFunctions } = useFunction();
  const { createRole, getRoleById, updateRole } = useRole();

  const fetchRoleById = async () => {
    const res = await getRoleById(roleId as string);
    if (res && res.status === 200) {
      const roleData: RoleDto = res.data.data;
      reset({
        roleName: roleData.role.roleName,
        roleDescription: roleData.role.roleDescription,
      });
      setSelectedPermissions(roleData.functions);
    }
  };
  useEffect(() => {
    if (roleId) {
      fetchRoleById();
    }
  }, [roleId]);

  const fetchActivePackageFunctions = async () => {
    const res = await getActivePackageFunctions();
    if (res && res.status === 200) {
      setActivePackage(res.data.data);
    }
  };
  useEffect(() => {
    fetchActivePackageFunctions();
  }, []);

  // Watch permissions để update selectAll state
  // const permissions = watch("permissions");
  const [selectAll, setSelectAll] = useState(false);

  // useEffect(() => {
  //   const allChecked = Object.values(permissions).every((value) => value === true);
  //   setSelectAll(allChecked);
  // }, [permissions]);

  const handleBackClick = () => {
    router.push(paths.settings.managementRole);
  };

  // const handleSelectAllChange = (event: React.ChangeEvent<HTMLInputElement>) => {
  //   const checked = event.target.checked;
  //   setSelectAll(checked);
  //   setOrderOpen(checked);
  //   setEditOrderOpen(checked);

  //   // Update all permissions
  //   Object.keys(permissions).forEach((key) => {
  //     setValue(`permissions.${key}`, checked);
  //   });
  // };

  function convertPermissionsFormat(inputData) {
    try {
      const parentId = Object.keys(inputData)[0];

      if (!parentId) {
        return { functions: [] };
      }

      const childObjects = inputData[parentId];

      const functions = Object.entries(childObjects).map(([functionId, permissions]) => {
        return {
          functionId,
          permissions: Array.isArray(permissions) ? permissions : [],
        };
      });

      return { functions };
    } catch (error) {
      throw error;
    }
  }

  const onSubmit = async (data: RoleFormData) => {
    try {
      const objectData: RoleBodyUpdateApi = {
        roleId: roleId as string,
        body: {
          roleName: data.roleName,
          roleDescription: data.roleDescription,
          roleIcon: "",
          functions: selectedPermissions,
        },
      };
      const res = await updateRole(objectData);
      if (res && res.status === 200) {
        router.push(paths.settings.managementRole);
      }
    } catch (error) {}
  };

  const handleOrderToggle = () => {
    setOrderOpen(!orderOpen);
  };

  const handleEditOrderToggle = () => {
    setEditOrderOpen(!editOrderOpen);
  };

  // const handlePermissionChange = (event: React.ChangeEvent<HTMLInputElement>) => {
  //   const { name, checked } = event.target;
  //   setValue(`permissions.${name}`, checked);

  //   // Nếu uncheck một permission, đảm bảo selectAll cũng được uncheck
  //   if (!checked) {
  //     setSelectAll(false);
  //   }
  // };

  const handleSelectAllPermissions = () => {
    if (!activePackage?.functions?.length) return;

    const allPermissions: FunctionObject[] = [];

    // Process all functions and their children recursively
    const processFunctions = (functions: FunctionDto[]) => {
      functions.forEach((func) => {
        // Add current function with all available permissions
        allPermissions.push({
          functionId: func.functionId,
          permissions: func.permissions || [],
        });

        // Process children recursively
        if (func.children?.length) {
          processFunctions(func.children);
        }
      });
    };

    processFunctions(activePackage.functions);
    setSelectedPermissions(allPermissions);
  };

  return (
    <SettingLayout>
      <Grid container alignItems="center" spacing={2} sx={{ mb: 1 }}>
        <Grid>
          <IconButton onClick={handleBackClick}>
            <ArrowBackIcon />
          </IconButton>
        </Grid>
        <Grid>
          <Typography variant="h5">{"Sửa vai trò"}</Typography>
        </Grid>
      </Grid>
      <Grid container spacing={3}>
        <Grid size={{ xs: 12, md: 12 }}>
          <Paper sx={{ p: 3, boxShadow: 3 }}>
            <Box sx={{ display: "flex" }}>
              {/* <FormControl fullWidth sx={{ mb: 3, marginRight: 2 }}>
                <Typography component="label" variant="body2" sx={{ mb: 1, display: "flex" }}>
                  <Typography
                    sx={{ marginRight: 0.5, color: "red", fontWeight: 600, fontSize: 18 }}
                  >
                    *
                  </Typography>{" "}
                  Tên hiển thị
                </Typography>
                <TextField
                  size="small"
                  placeholder="Vui lòng nhập tên hiển thị"
                  fullWidth
                  disabled={isViewMode}
                  {...register("displayName")}
                  error={!!errors.displayName}
                  helperText={errors.displayName?.message}
                />
              </FormControl> */}
              <FormControl fullWidth sx={{ mb: 3 }}>
                <Typography
                  variant="body2"
                  sx={{ mb: 1, display: "flex", fontSize: 16, fontWeight: 500 }}
                >
                  <Typography
                    sx={{ marginRight: 0.5, color: "red", fontWeight: 600, fontSize: 18 }}
                  >
                    *
                  </Typography>{" "}
                  Tên vai trò
                </Typography>
                <TextField
                  size="small"
                  placeholder="Vui lòng nhập tên vai trò"
                  fullWidth
                  // disabled={isViewMode}
                  {...register("roleName")}
                  error={!!errors.roleName}
                  helperText={errors.roleName?.message}
                />
              </FormControl>
            </Box>
            <FormControl fullWidth>
              <Typography variant="body2" sx={{ mb: 1, fontSize: 16, fontWeight: 500 }}>
                Mô tả
              </Typography>
              <TextField
                multiline
                rows={4}
                placeholder="Vui lòng nhập Mô tả"
                fullWidth
                // disabled={isViewMode}
                {...register("roleDescription")}
                error={!!errors.roleDescription}
                helperText={errors.roleDescription?.message}
              />
            </FormControl>
          </Paper>
        </Grid>
        <Grid size={{ xs: 12, md: 12 }}>
          <Paper sx={{ p: 3, boxShadow: 3 }}>
            <Box
              sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2 }}
            >
              <Typography variant="subtitle1">Quyền hạn</Typography>
              <Button variant="outlined" color="primary" onClick={handleSelectAllPermissions}>
                Chọn tất cả quyền
              </Button>
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Vui lòng chọn các quyền mà vai trò này có thể chỉnh sửa.
            </Typography>
            <AuthorizationScreenUpdate
              sections={sections}
              activePackage={activePackage}
              selectedPermissions={selectedPermissions}
              setSelectedPermissions={setSelectedPermissions}
            />
          </Paper>
        </Grid>
      </Grid>
      {
        <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 3 }}>
          <Button variant="outlined" sx={{ mr: 2 }} onClick={handleBackClick}>
            Hủy bỏ
          </Button>
          <Button variant="contained" color="primary" onClick={handleSubmit(onSubmit)}>
            Lưu
          </Button>
        </Box>
      }
    </SettingLayout>
  );
}
