import React from 'react';
import SettingLayout from '@/src/components/settings/settings-page/SettingLayout';
import { Box, Typography, IconButton } from '@mui/material';
import CombinedShipping from '../../../components/settings/settings-page/CombinedShipping';
import Grid from '@mui/system/Grid';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { useRouter } from 'next/router';

export default function SettingShipping() {
  const router = useRouter();

  return (
    <SettingLayout>
      <Grid>
        <Typography variant="h5" gutterBottom sx={{ ml: 1 }}>
          Vận chuyển và giao hàng
        </Typography>
      </Grid>
      <Grid sx={{ ml: 1 }}>
        <CombinedShipping />
      </Grid>
    </SettingLayout>
  );
}
