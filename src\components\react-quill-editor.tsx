import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import dynamic from "next/dynamic";
import { useMedia } from "../api/hooks/media/use-media";
import { CreateFileGroupRequest, RefType } from "../api/types/media.types";
import { Box, Typography } from "@mui/material";

const ReactQuill = dynamic(() => import("react-quill"), {
  ssr: false,
  loading: () => <p>Loading...</p>,
});
const ATTRIBUTES = ["style", "width", "height", "alt", "class", "id", "cursor"];

let ImageBlot = null;

if (typeof window !== "undefined") {
  const Quill = require("quill");
  const BlockEmbed = Quill.import("blots/block/embed");

  class ImageBlotClass extends BlockEmbed {
    static blotName = "image";
    static tagName = "img";

    static create(value) {
      let node = super.create();

      if (typeof value === "string") {
        node.setAttribute("src", value);
      } else if (value && typeof value === "object") {
        node.setAttribute("src", value.url);

        ATTRIBUTES.forEach((attr) => {
          if (value[attr]) {
            node.setAttribute(attr, value[attr]);
          }
        });
      }
      return node;
    }

    static formats(domNode) {
      let formats = {};
      ATTRIBUTES.forEach((attr) => {
        if (domNode.hasAttribute(attr)) {
          formats[attr] = domNode.getAttribute(attr);
        }
      });
      return formats;
    }

    format(name, value) {
      if (ATTRIBUTES.indexOf(name) > -1) {
        if (value) {
          this.domNode.setAttribute(name, value);
        } else {
          this.domNode.removeAttribute(name);
        }
      } else {
        super.format(name, value);
      }
    }

    static value(domNode) {
      let value = { url: domNode.getAttribute("src") };
      ATTRIBUTES.forEach((attr) => {
        if (domNode.hasAttribute(attr)) {
          value[attr] = domNode.getAttribute(attr);
        }
      });
      return value;
    }
  }

  ImageBlot = ImageBlotClass;

  Quill.register(ImageBlot, true);
}

if (typeof window !== "undefined") {
  require("react-quill/dist/quill.snow.css");
}

let ImageResize = null;
if (typeof window !== "undefined") {
  ImageResize = require("quill-image-resize-module-react").default;
}

interface TextEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  shopId: string;
  defaultGroupId: string;
  error?: string | false | undefined;
}

export const stripHtmlAndSpaces = (html) => {
  if (!html) return "";
  let textOnly = html.replace(/<\/?[^>]+(>|$)/g, "");
  return textOnly.replace(/\s+/g, "");
};

const ReactQuillEditor = ({
  value = "",
  onChange = () => {},
  shopId,
  defaultGroupId,
  error,
}: TextEditorProps) => {
  const [currentValue, setCurrentValue] = useState("");
  const { uploadFile } = useMedia();
  const storeIdRef = useRef(shopId);
  useEffect(() => {
    storeIdRef.current = shopId;
  }, [shopId]);

  useEffect(() => {
    if (value) setCurrentValue(value);
  }, [value]);

  useEffect(() => {
    if (typeof window !== "undefined" && ImageResize) {
      const Quill = require("react-quill").Quill;
      Quill.register("modules/imageResize", ImageResize);
      Quill.register(ImageBlot, true);
    }
  }, []);

  const uploadFileMedia = async (file: File, type: string): Promise<string> => {
    const curStoreId = storeIdRef.current;

    if (curStoreId) {
      const data: CreateFileGroupRequest = {
        FileUpload: file,
        GroupFileId: defaultGroupId,
        ShopId: curStoreId,
        RefType: RefType.Shop,
      };

      const response = await uploadFile(data);
      const url = response.data.link;
      return url;
    }
  };

  const imageHandler = useCallback(() => {
    const input = document.createElement("input");
    input.setAttribute("type", "file");
    input.setAttribute("accept", "image/*");
    input.click();

    input.onchange = async () => {
      if (input.files && input.files[0]) {
        const file = input.files[0];
        const url = await uploadFileMedia(file, "image");

        const quillEditor = document.querySelector(".ql-editor");
        if (quillEditor) {
          const quill = (quillEditor as any).__quill || (quillEditor.parentElement as any).__quill;
          if (quill) {
            const range = quill.getSelection();
            const index = range ? range.index : quill.getLength();

            quill.insertEmbed(index, "image", {
              url,
              width: "150",
              style: "cursor: nesw-resize;",
            });
            quill.setSelection(index + 1);
          }
        }
      }
    };
  }, []);

  const handleChange = (value: string) => {
    onChange(value);
    setCurrentValue(value);
  };

  const FORMAT_TEXT_EDITOR = [
    "font",
    "size",
    "bold",
    "italic",
    "underline",
    "strike",
    "color",
    "background",
    "script",
    "header",
    "blockquote",
    "code",
    "list",
    "align",
    "link",
    "image",
    "video",
    "formula",
  ];

  const MODULES_TEXT_EDITOR = useMemo(
    () => ({
      toolbar: {
        container: [
          [{ font: [] }],
          [{ size: ["small", false, "large", "huge"] }],
          ["bold", "italic", "underline", "strike"],
          [{ color: [] }, { background: [] }],
          [{ script: "sub" }, { script: "super" }],
          [{ header: 1 }, { header: 2 }],
          ["blockquote", "code"],
          [{ list: "ordered" }, { list: "bullet" }],
          [{ align: "" }, { align: "center" }, { align: "right" }],
          ["link", "image", "video"],
          ["formula"],
        ],
        handlers: {
          image: imageHandler,
        },
      },
      imageResize: {
        parchment:
          typeof window !== "undefined" ? require("react-quill").Quill.import("parchment") : null,
        modules: ["Resize", "DisplaySize", "Toolbar"],
      },
    }),
    [imageHandler]
  );

  const textLength = useMemo(() => {
    return stripHtmlAndSpaces(currentValue).length;
  }, [currentValue]);

  return (
    <div className="mt-2 text-editor">
      <Box
        sx={{
          "& .ql-editor": {
            minHeight: "350px",
            maxHeight: "350px",
            overflowY: "auto",
          },
          "& .image-resize-module": {
            position: "absolute",
            border: "1px dashed #444",
          },
          "& .image-resize-module__handle": {
            position: "absolute",
            width: "12px",
            height: "12px",
            backgroundColor: "#fff",
            border: "1px solid #777",
            borderRadius: "2px",
          },
          "& .image-resize-module__handle--nw": {
            top: "-6px",
            left: "-6px",
            cursor: "nw-resize",
          },
          "& .image-resize-module__handle--ne": {
            top: "-6px",
            right: "-6px",
            cursor: "ne-resize",
          },
          "& .image-resize-module__handle--sw": {
            bottom: "-6px",
            left: "-6px",
            cursor: "sw-resize",
          },
          "& .image-resize-module__handle--se": {
            bottom: "-6px",
            right: "-6px",
            cursor: "se-resize",
          },
          "& .image-resize-module__toolbar": {
            position: "absolute",
            top: "-40px",
            left: "50%",
            transform: "translateX(-50%)",
            backgroundColor: "rgba(0,0,0,0.8)",
            borderRadius: "4px",
            padding: "4px 8px",
            display: "flex",
            gap: "4px",
          },
        }}
      >
        <ReactQuill
          theme="snow"
          modules={MODULES_TEXT_EDITOR}
          formats={FORMAT_TEXT_EDITOR}
          value={currentValue}
          onChange={handleChange}
        />
      </Box>
      <Box>
        {error && (
          <Typography
            sx={{
              pt: 1,
              color: textLength > 4000 ? "red" : "gray",
              fontSize: 12,
              textAlign: "left",
              justifyContent: "flex-start",
            }}
          >
            {error}
          </Typography>
        )}
        <Box
          sx={{
            pt: 1,
            color: textLength > 4000 ? "red" : "gray",
            fontSize: 12,
            textAlign: "right",
            justifyContent: "flex-end",
          }}
        >
          {textLength} / 4000 ký tự
        </Box>
      </Box>
    </div>
  );
};

export default ReactQuillEditor;
