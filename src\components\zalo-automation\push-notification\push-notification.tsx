import { useState, useEffect } from "react";
import {
  Box,
  Paper,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Switch,
  Button,
  Link,
  Typography,
  FormControl,
  Select,
  MenuItem,
  IconButton,
  Dialog,
  DialogContent,
  DialogTitle,
  SelectChangeEvent,
  DialogContentText,
  DialogActions,
  Grid,
} from "@mui/material";
import {
  Add,
  ArrowBackIos,
  CheckCircle,
  CheckCircleOutline,
  ChevronLeft,
  ChevronRight,
  Delete,
  Edit,
  ForkLeft,
} from "@mui/icons-material";
import { useZaloTemplate } from "@/src/api/hooks/zalo-template/zalo-template";
import { logger } from "@/src/utils/logger";
import {
  ACTIVE_STATUS,
  INACTIVE_STATUS,
  LIMIT,
  PUSH_NOTIFICATION_TABS,
  SKIP,
} from "@/src/constants/constant";
import { formatMoney } from "@/src/utils/format-money";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useTriggerEvent } from "@/src/api/hooks/trigger-event/use-trigger-event";
import {
  ParamGetListTriggerEvent,
  TriggerEventDto,
} from "@/src/api/services/trigger-event/trigger-event.service";
import TableOrderSuccess from "./table-order-success";

export interface ZaloTemplateRequest {
  skip: number;
  limit: number;
  search?: string;
  type: string;
  shopId: string;
}

export default function PushNotification() {
  const { getListTriggerEventByType } = useTriggerEvent();
  const snackbar = useSnackbar();
  const storeId = useStoreId();
  const { getZaloTemplate, deleteZaloTemplate, changeTemplateStatus } = useZaloTemplate();
  const [zaloTemplates, setZaloTemplates] = useState<any[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<any | null>(null);
  const [total, setTotal] = useState<number>();
  const [tabValue, setTabValue] = useState(0);
  const [modelSearch, setModelSearch] = useState<ZaloTemplateRequest>({
    skip: SKIP,
    limit: LIMIT,
    search: "",
    type: PUSH_NOTIFICATION_TABS[tabValue].text,
    shopId: storeId || "",
  });
  const [isPopupOpen, setIsPopupOpen] = useState(false); // State to control popup visibility
  const [isSwitchLoading, setIsSwitchLoading] = useState(false);
  const [deleteTemplateId, setDeleteTemplateId] = useState(null);
  const [openDialogDelete, setOpenDialogDelete] = useState(false);
  const [listTriggerEvent, setListTriggerEvent] = useState<TriggerEventDto[]>([]);
  const [selectedTriggerEvent, setSelectedTriggerEvent] = useState<TriggerEventDto>();
  const [isEditTriggerEvent, setIsEditTriggerEvent] = useState<boolean>(false);
  const fetchTriggerEvent = async () => {
    const param: ParamGetListTriggerEvent = {
      type: PUSH_NOTIFICATION_TABS[tabValue].text,
      shopId: storeId,
    };
    const res = await getListTriggerEventByType(param);
    if (res && res?.status === 200) {
      setListTriggerEvent(res?.data?.data);
    }
  };

  useEffect(() => {
    fetchTriggerEvent();
  }, [tabValue, storeId]);
  useEffect(() => {
    setModelSearch((prev) => ({
      ...prev,
      shopId: storeId || "",
    }));
  }, [storeId]);

  useEffect(() => {
    if (!modelSearch.shopId || modelSearch.shopId == "") {
      return;
    }
    fetchZaloTemplate();
  }, [modelSearch]);

  const fetchZaloTemplate = async () => {
    try {
      const response = await getZaloTemplate(modelSearch);
      setZaloTemplates(response.data.data);
      setTotal(response.data.total);
    } catch (error) {
      logger.error("Error fetching shops:", error);
    }
  };

  useEffect(() => {
    const newType = PUSH_NOTIFICATION_TABS[tabValue].text;
    setModelSearch((prev) => ({
      ...prev,
      type: newType,
      skip: 0,
    }));
  }, [tabValue]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleChangePage = (direction: "next" | "prev") => {
    setModelSearch((prev) => {
      const newSkip =
        direction === "next" ? prev.skip + prev.limit : Math.max(0, prev.skip - prev.limit);
      return { ...prev, skip: newSkip };
    });
  };

  const handleChangeRowsPerPage = (event: SelectChangeEvent<number>) => {
    const newLimit = Number(event.target.value);
    setModelSearch((prev) => ({
      ...prev,
      limit: newLimit,
      skip: 0,
    }));
  };
  const handleChangeStatusTemplate = async (event, id) => {
    setIsSwitchLoading(true);
    const newStatus = event.target.checked ? ACTIVE_STATUS : INACTIVE_STATUS;
    await changeTemplateStatus(id, newStatus)
      .then((response) => {
        snackbar.success("Cập nhật trạng thái thành công");
        const updatedId = response.data.data;
        const updatedTemplates = zaloTemplates.map((template) =>
          template.id === updatedId ? { ...template, status: newStatus } : template
        );
        setZaloTemplates(updatedTemplates);
      })
      .catch((err) => {
        console.error("Cập nhật trạng thái thất bại");
      })
      .finally(() => {
        setIsSwitchLoading(false);
      });
  };

  const handleOpenPopup = (template?: any) => {
    setSelectedTemplate(template || null);
    setIsPopupOpen(true);
  };

  const handleClosePopup = () => {
    setSelectedTemplate(null);
    setIsPopupOpen(false);
  };

  const handleDeleteClick = (id) => {
    setDeleteTemplateId(id);
    setOpenDialogDelete(true);
  };

  const handleCancelDelete = () => {
    setDeleteTemplateId(null);
    setOpenDialogDelete(false);
  };

  const handleConfirmDelete = async () => {
    if (deleteTemplateId !== null) {
      try {
        await deleteZaloTemplate(deleteTemplateId);
        await fetchZaloTemplate();
        snackbar.success("Xóa mẫu tin Zalo thành công");
      } catch (error) {
        snackbar.error("Có lỗi xảy ra khi xóa mẫu tin Zalo");
      }
    }
    setDeleteTemplateId(null);
    setOpenDialogDelete(false);
  };

  // Styles
  const tabStyles = {
    "& .MuiTab-root": {
      textTransform: "none",
      color: "black",
      alignItems: "flex-start",
    },
  };

  const tableCellHeaderStyles = { fontWeight: "bold", color: "black" };

  const selectStyles = {
    "& .MuiOutlinedInput-notchedOutline": {
      border: "none",
    },
    boxShadow: "none",
  };

  const paginationBoxStyles = {
    display: "flex",
    justifyContent: "flex-end",
    alignItems: "center",
    mt: 2,
  };

  const currentPageStart = modelSearch.skip + 1;
  const currentPageEnd = Math.min(modelSearch.skip + modelSearch.limit, total);
  const isPrevDisabled = modelSearch.skip === 0;
  const isNextDisabled = currentPageEnd >= total;

  const rowsPerPageControl = (
    <>
      <Typography variant="body2" sx={{ mr: 2 }}>
        Số dòng mỗi trang
      </Typography>
      <FormControl size="small" sx={{ minWidth: 70 }}>
        <Select value={modelSearch.limit} onChange={handleChangeRowsPerPage} sx={selectStyles}>
          <MenuItem value={10}>10</MenuItem>
          <MenuItem value={25}>25</MenuItem>
          <MenuItem value={50}>50</MenuItem>
        </Select>
      </FormControl>
    </>
  );

  const paginationControls = (
    <>
      <Typography variant="body2" sx={{ mx: 2 }}>
        {`${currentPageStart}-${currentPageEnd} of ${total}`}
      </Typography>
      <Button size="small" disabled={isPrevDisabled} onClick={() => handleChangePage("prev")}>
        <ChevronLeft />
      </Button>
      <Button size="small" disabled={isNextDisabled} onClick={() => handleChangePage("next")}>
        <ChevronRight />
      </Button>
    </>
  );

  const handleClickTabPushNoti = (e) => {};
  return isEditTriggerEvent ? (
    <>
      <TableOrderSuccess
        isEditTriggerEvent={isEditTriggerEvent}
        setIsEditTriggerEvent={setIsEditTriggerEvent}
        setSelectedTriggerEvent={setSelectedTriggerEvent}
        selectedTriggerEvent={selectedTriggerEvent}
        fetchTriggerEvent={fetchTriggerEvent}
        fetchZaloTemplate={fetchZaloTemplate}
      />
    </>
  ) : (
    <>
      <Box sx={{ width: "100%", p: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="notification tabs"
          textColor="inherit"
          TabIndicatorProps={{
            style: { backgroundColor: "blue" },
          }}
          sx={tabStyles}
        >
          {PUSH_NOTIFICATION_TABS.map((tab) => (
            <Tab
              key={tab.value}
              label={tab.label}
              value={tab.value}
              onClick={(e) => {
                console.log(e);
              }}
            />
          ))}
        </Tabs>

        <Grid container spacing={2} sx={{ marginTop: 1 }}>
          {Array.isArray(listTriggerEvent) &&
            listTriggerEvent.length > 0 &&
            listTriggerEvent.map((item, index) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                <Box
                  sx={{
                    bgcolor: "#f9f9f9",
                    borderRadius: "8px",
                    padding: "16px",
                    // height: "280px",
                    boxShadow: "0 1px 3px rgba(0,0,0,0.05)",
                    display: "flex",
                    flexDirection: "column",
                    // justifyContent: "space-between",
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "start",
                      height: "40px",
                      mb: 2.5,
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        color: "#333",
                        fontSize: "15px",
                      }}
                    >
                      {item.eventName}
                    </Typography>
                    <Box
                      sx={{
                        cursor: "pointer",
                        "&:hover": {
                          backgroundColor: "#f0f0f0",
                        },
                        borderRadius: 99,
                        p: 0.5,
                        display: "inline-flex",
                        alignItems: "center",
                      }}
                      onClick={() => {
                        setSelectedTriggerEvent(item);
                        setIsEditTriggerEvent(true);
                      }}
                    >
                      <Edit
                        sx={{
                          color: "#2196f3",
                          fontSize: "20px",
                          cursor: "pointer",
                          marginLeft: 0.5,
                        }}
                      />
                    </Box>
                  </Box>

                  {Array.isArray(item.templates) &&
                    item.templates.length > 0 &&
                    item.templates.map((temp) => (
                      <>
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                            mb: 1,
                          }}
                        >
                          <Typography
                            sx={{
                              fontSize: "14px",
                              color: "#666",
                            }}
                          >
                            {temp.channelName}{" "}
                          </Typography>
                          <CheckCircle
                            sx={{
                              color: temp.isActive === true ? "#4caf50" : "grey",
                              fontSize: "20px",
                            }}
                          />
                        </Box>
                      </>
                    ))}
                </Box>
              </Grid>
            ))}
        </Grid>
      </Box>
    </>
  );
}
