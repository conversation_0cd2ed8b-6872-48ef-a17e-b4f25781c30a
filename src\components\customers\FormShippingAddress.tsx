import { AppDispatch, RootState } from "@/src/store";
import { FormControl, InputLabel, MenuItem, Select, TextField, Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";
import React, { useEffect, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";

import { districts } from "@/src/utils/address/districts";
import { wards } from "@/src/utils/address/wards";

export type ShippingAddressFormData = {
  shippingAddressFullname: string;
  shippingAddressPhone: string;
  shippingAddressAddress: string;
  shippingAddressProvince: string;
  shippingAddressDistrict: string;
  shippingAddressWard: string;
  shippingAddressId?: string;
};

type ShippingAddressProps = {
  shippingAddress?: {
    fullName?: string;
    phoneNumber?: string;
    address?: string;
    provinceId?: string;
    districtId?: string;
    wardId?: string;
    shippingAddressId?: string;
  };
};

const defaultValues: ShippingAddressFormData = {
  shippingAddressFullname: "",
  shippingAddressPhone: "",
  shippingAddressAddress: "",
  shippingAddressProvince: "",
  shippingAddressDistrict: "",
  shippingAddressWard: "",
};

export default function FormShippingAddress({ shippingAddress }: ShippingAddressProps) {
  const { provinces } = useSelector((state: RootState) => state.address);
  const dispatch = useDispatch<AppDispatch>();
  const [listDistrict, setListDistrict] = useState([]);
  const [listWard, setListWard] = useState([]);
  // const [provinceCode, setProvinceCode] = useState('');
  // const [districtCode, setDistrictCode] = useState(''); // Giá trị đã chọn
  const {
    control,
    setValue,
    getValues,
    formState: { errors },
    watch,
  } = useFormContext();

  const selectedProvince = watch("shippingAddressProvince");
  const selectedDistrict = watch("shippingAddressDistrict");

  useEffect(() => {
    if (shippingAddress) {
      setValue("shippingAddressFullname", shippingAddress.fullName || "");
      setValue("shippingAddressPhone", shippingAddress.phoneNumber || "");
      setValue("shippingAddressAddress", shippingAddress.address || "");
      setValue("shippingAddressProvince", shippingAddress.provinceId || "");
      // Populate districts based on the province and then set the district value
      const provinceDistricts = districts.filter(
        (district) => district.provinceID === parseInt(shippingAddress.provinceId)
      );

      setListDistrict(provinceDistricts);

      setValue("shippingAddressDistrict", shippingAddress.districtId || "");

      // Populate wards based on the district and then set the ward value
      const districtWards = wards.filter(
        (ward) => ward.districtID === shippingAddress.districtId?.toString()
      );
      setListWard(districtWards);
      setValue("shippingAddressWard", shippingAddress.wardId || "");
      setValue("shippingAddressId", shippingAddress.shippingAddressId || "");
    }
  }, [shippingAddress, setValue]);

  const handleDistrictChange = (e) => {
    const district = e.target.value;
    setValue("shippingAddressDistrict", district);
  };

  useEffect(() => {
    if (selectedProvince) {
      const data = districts.filter(
        (district) => district.provinceID === parseInt(selectedProvince)
      );
      setListDistrict(data);

      // Reset shippingAddressDistrict and shippingAddressWard only if there's no existing value
      if (!shippingAddress || shippingAddress.provinceId !== selectedProvince) {
        setValue("shippingAddressDistrict", "");
        setValue("shippingAddressWard", "");
        setListWard([]);
      }
    }
  }, [selectedProvince, shippingAddress, setValue]);

  useEffect(() => {
    // if (!selectedDistrict) {
    //   setListWard([]); // Clear ward list if no district is selected
    //   setValue('shippingAddressWard', ''); // Reset ward selection
    //   return;
    // }
    if (selectedDistrict) {
      const dataWards = wards.filter((ward) => ward.districtID === selectedDistrict.toString());
      setListWard(dataWards);
      // Reset ward only if there's no existing value or it doesn't match the selected district
      if (!shippingAddress || shippingAddress.districtId !== selectedDistrict) {
        setValue("shippingAddressWard", "");
      }
    }
  }, [selectedDistrict, shippingAddress, setValue]);
  return (
    <>
      <Grid size={{ xs: 12 }} sx={{ mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom sx={{ display: "flex" }}>
          Họ tên <Typography sx={{ color: "red", marginLeft: 0.5 }}>*</Typography>
        </Typography>
        <Controller
          name="shippingAddressFullname"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              variant="outlined"
              error={!!errors.shippingAddressFullname}
              helperText={errors.shippingAddressFullname?.message as string}
              InputProps={{
                sx: {
                  "& input::-ms-reveal, & input::-ms-clear": {
                    display: "none",
                  },
                  height: "45px",
                },
              }}
            />
          )}
        />
      </Grid>
      <Grid sx={{ mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom sx={{ display: "flex" }}>
          Số điện thoại <Typography sx={{ color: "red", marginLeft: 0.5 }}>*</Typography>
        </Typography>
        <Controller
          name="shippingAddressPhone"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              variant="outlined"
              error={!!errors.shippingAddressPhone}
              helperText={errors.shippingAddressPhone?.message as string}
              InputProps={{
                sx: {
                  "& input::-ms-reveal, & input::-ms-clear": {
                    display: "none",
                  },
                  height: "45px",
                },
              }}
            />
          )}
        />
      </Grid>
      <Grid sx={{ mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom sx={{ display: "flex" }}>
          Địa chỉ chi tiết <Typography sx={{ color: "red", marginLeft: 0.5 }}>*</Typography>
        </Typography>
        <Controller
          name="shippingAddressAddress"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              variant="outlined"
              error={!!errors.shippingAddressAddress}
              helperText={errors.shippingAddressAddress?.message as string}
              InputProps={{
                sx: {
                  "& input::-ms-reveal, & input::-ms-clear": {
                    display: "none",
                  },
                  height: "45px",
                },
              }}
            />
          )}
        />
      </Grid>
      <FormControl fullWidth sx={{ mb: 2 }} error={!!errors.shippingAddressProvince}>
        <Typography variant="subtitle1" gutterBottom sx={{ display: "flex" }}>
          Tỉnh/Thành phố <Typography sx={{ color: "red", marginLeft: 0.5 }}>*</Typography>
        </Typography>
        <Controller
          name="shippingAddressProvince"
          control={control}
          render={({ field }) => (
            <Select
              {...field}
              value={field.value || ""}
              label="Tỉnh/Thành phố"
              sx={{ height: "45px" }}
            >
              {provinces?.map((p, index) => {
                return (
                  <MenuItem key={`province-${index}`} value={p.provinceID}>
                    {p.provinceName}
                  </MenuItem>
                );
              })}
            </Select>
          )}
        />
        {errors.shippingAddressProvince && (
          <Typography variant="caption" color="error" sx={{ mt: 1, ml: 2 }}>
            {errors.shippingAddressProvince?.message as string}
          </Typography>
        )}
      </FormControl>

      <FormControl fullWidth sx={{ mb: 2 }} error={!!errors.shippingAddressDistrict}>
        <Typography variant="subtitle1" gutterBottom sx={{ display: "flex" }}>
          Quận/Huyện <Typography sx={{ color: "red", marginLeft: 0.5 }}>*</Typography>
        </Typography>
        <Controller
          name="shippingAddressDistrict"
          control={control}
          render={({ field }) => (
            <Select
              {...field}
              value={field.value || ""}
              label="Quận/Huyện"
              onChange={handleDistrictChange}
              sx={{ height: "45px" }}
            >
              {listDistrict?.map((d, index) => {
                return (
                  <MenuItem key={`district-${index}`} value={d.districtID}>
                    {d.districtName}
                  </MenuItem>
                );
              })}
            </Select>
          )}
        />
        {errors.shippingAddressDistrict && (
          <Typography variant="caption" color="error" sx={{ mt: 1, ml: 2 }}>
            {errors.shippingAddressDistrict?.message as string}
          </Typography>
        )}
      </FormControl>

      <FormControl fullWidth error={!!errors.shippingAddressWard}>
        <Typography variant="subtitle1" gutterBottom sx={{ display: "flex" }}>
          Phường/Xã <Typography sx={{ color: "red", marginLeft: 0.5 }}>*</Typography>
        </Typography>
        <Controller
          name="shippingAddressWard"
          control={control}
          render={({ field }) => (
            <Select {...field} value={field.value || ""} label="Phường" sx={{ height: "45px" }}>
              {listWard?.map((w, index) => {
                return (
                  <MenuItem key={`ward-${index}`} value={w.wardID}>
                    {w.wardName}
                  </MenuItem>
                );
              })}
            </Select>
          )}
        />
        {errors.shippingAddressWard && (
          <Typography variant="caption" color="error" sx={{ mt: 1, ml: 2 }}>
            {errors.shippingAddressWard?.message as string}
          </Typography>
        )}
      </FormControl>
    </>
  );
}
