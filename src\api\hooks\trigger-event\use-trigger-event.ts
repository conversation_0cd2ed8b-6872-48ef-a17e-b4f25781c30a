import { useState } from "react";
import { ErrorHandlerService } from "../../services/error-handler.service";
import { userService } from "../../services/user/user.service";
import {
  BodyExportExcelListUser,
  FilterRequestDto,
  ParamImportExcelUserGroup,
  SearchUserOfUserGroupDto,
  UserGroupAdvancedSearchDto,
  UserGroupQueryParams,
  userGroupService,
} from "../../services/user-group/user-group.service";
import {
  ParamGetListTemplate,
  ParamGetListTriggerEvent,
  ParamUpdateStatusTemplate,
  userTriggerEventService,
} from "../../services/trigger-event/trigger-event.service";

export interface TriggerParameterDto {
  name: string;
  value: string;
  dataType: string;
  orderNumber: number;
}

export const useTriggerEvent = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getListTriggerEvent = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await userTriggerEventService.getListTriggerEvent();
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getListTriggerEventByType = async (params: ParamGetListTriggerEvent) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userTriggerEventService.getListTriggerEventByType(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getListParamByTriggerEventId = async (triggerEventId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userTriggerEventService.getListParamByTriggerEventId(triggerEventId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getListTemplateByTriggerEventId = async (params: ParamGetListTemplate) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userTriggerEventService.getListTemplateByTriggerEventId(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const activeStatusTemplate = async (params: ParamUpdateStatusTemplate) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userTriggerEventService.activeStatusTemplate(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  return {
    getListTriggerEvent,
    getListTriggerEventByType,
    getListTemplateByTriggerEventId,
    getListParamByTriggerEventId,
    activeStatusTemplate,
    loading,
    error,
  };
};
