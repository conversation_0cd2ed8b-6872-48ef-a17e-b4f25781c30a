import { FC, useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useTranslation } from "react-i18next";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import TitleTypography from "@/src/components/title-typography/title-typography";
import { tokens } from "@/src/locales/tokens";
import StoreLayout from "src/layouts/store/store-layout";
import { paths } from "src/paths";
import { useSearchParams } from "src/hooks/use-search-params";
import Box from "@mui/material/Box";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import CardHeader from "@mui/material/CardHeader";
import Divider from "@mui/material/Divider";
import MenuItem from "@mui/material/MenuItem";
import Stack from "@mui/material/Stack";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import { useRouter } from "next/navigation";
import CircularProgress from "@mui/material/CircularProgress";
import { StorageService } from "nextjs-api-lib";
import { useAppDispatch, useAppSelector } from "@/src/redux/hooks";
import { updateProfile } from "@/src/redux/slices/profileSlice";

const languageOptions = [
  { value: "en", label: "English" },
  { value: "vi", label: "Tiếng Việt" },
];

const Settings: FC = () => {
  const { t } = useTranslation();
  const snackbar = useSnackbar();
  const searchParams = useSearchParams();
  const returnTo = searchParams.get("returnTo");
  const router = useRouter();

  const dispatch = useAppDispatch();
  const { profile, loading } = useAppSelector((state) => state.profile);

  const [partnerId, setPartnerId] = useState("");

  useEffect(() => {
    const storedPartnerId = StorageService.get("partnerId") || "";
    if (storedPartnerId) {
      setPartnerId(storedPartnerId as string);
    }
  }, []);

  const formik = useFormik({
    initialValues: {
      firstname: "",
      lastname: "",
      phoneNumber: "",
      email: "",
      language: "vi",
    },
    validationSchema: Yup.object({
      firstname: Yup.string().required(t(tokens.settings.firstNameRequired)),
      lastname: Yup.string().required(t(tokens.settings.lastNameRequired)),
      language: Yup.string().required().oneOf(["en", "vi"]),
    }),
    onSubmit: async (values) => {
      try {
        const updatedProfile = {
          ...values,
          partnerId: partnerId,
          language: values.language.toUpperCase(),
          fullname: `${values.firstname} ${values.lastname}`.trim(),
          updated: new Date().toISOString(),
        };

        await dispatch(updateProfile(updatedProfile));
        snackbar.success(t(tokens.settings.updateSuccess));

        if (returnTo) {
          router.push(returnTo);
        }
      } catch (error) {
        //snackbar.error(t(tokens.settings.updateError));
      }
    },
  });

  useEffect(() => {
    formik.setValues({
      firstname: profile?.firstname || "",
      lastname: profile?.lastname || "",
      phoneNumber: profile?.phoneNumber || "",
      email: profile?.email || "",
      language: profile?.language?.toLowerCase() || "vi",
    });
  }, [profile]);

  const handleSaveChanges = () => {
    formik.handleSubmit();
  };

  if (loading && !formik.values.firstname) {
    return (
      <StoreLayout>
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            minHeight: "100vh",
          }}
        >
          <CircularProgress />
        </Box>
      </StoreLayout>
    );
  }

  return (
    <StoreLayout>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          minHeight: "100vh",
          overflow: "hidden",
          p: 3,
        }}
      >
        <Card
          elevation={16}
          sx={{
            minWidth: 800,
            flex: 1,
            width: "100%",
            mx: "auto",
            my: 3,
          }}
        >
          <CardHeader
            title={<TitleTypography>{t(tokens.settings.title)}</TitleTypography>}
            sx={{
              textAlign: "center",
              pb: 3,
              "& .MuiCardHeader-title": {
                fontSize: "h4.fontSize",
              },
            }}
          />
          <CardContent>
            <form onSubmit={formik.handleSubmit}>
              <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>
                {t(tokens.settings.personalInfo)}
              </Typography>
              <Stack spacing={3}>
                <Stack direction="row" spacing={2}>
                  <TextField
                    label={t(tokens.settings.firstName)}
                    fullWidth
                    name="firstname"
                    value={formik.values.firstname}
                    onChange={formik.handleChange}
                    error={!!(formik.touched.firstname && formik.errors.firstname)}
                    helperText={formik.touched.firstname && t(formik.errors.firstname as string)}
                  />
                  <TextField
                    label={t(tokens.settings.lastName)}
                    fullWidth
                    name="lastname"
                    value={formik.values.lastname}
                    onChange={formik.handleChange}
                    error={!!(formik.touched.lastname && formik.errors.lastname)}
                    helperText={formik.touched.lastname && t(formik.errors.lastname as string)}
                  />
                </Stack>
                <Stack direction="row" spacing={2}>
                  <TextField
                    label={t(tokens.settings.phone)}
                    fullWidth
                    name="phoneNumber"
                    value={formik.values.phoneNumber}
                    disabled={true}
                    InputProps={{
                      readOnly: true,
                    }}
                  />
                  <TextField
                    label={t(tokens.settings.email)}
                    fullWidth
                    name="email"
                    value={formik.values.email}
                    disabled={true}
                    InputProps={{
                      readOnly: true,
                    }}
                  />
                </Stack>
              </Stack>
              <Divider sx={{ my: 4 }} />
              <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>
                {t(tokens.settings.languageSection)}
              </Typography>
              <TextField
                select
                label={t(tokens.settings.language)}
                fullWidth
                name="language"
                value={formik.values.language}
                onChange={formik.handleChange}
                error={!!(formik.touched.language && formik.errors.language)}
                helperText={formik.touched.language && t(formik.errors.language as string)}
              >
                {languageOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </TextField>
              <Box sx={{ mt: 4, textAlign: "center" }}>
                <Button variant="contained" color="primary" onClick={handleSaveChanges}>
                  {t(tokens.settings.saveButton)}
                </Button>
              </Box>
            </form>
          </CardContent>
        </Card>
      </Box>
    </StoreLayout>
  );
};

export default Settings;
