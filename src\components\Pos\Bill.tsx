import React, { useRef } from "react";
import { formatCurrency } from "@/src/utils/format-number";
import DetachableLabel from "./tem";
import OptionName from "./invoiceData";

interface BillProps {
  cart: any;
  changeAmount: number;
  showLabels?: boolean;
  order: any;
}

const Bill: React.FC<BillProps> = ({ cart = {}, changeAmount, showLabels = true, order = {} }) => {
  const contentRef = useRef<HTMLDivElement>(null);

  let data = cart;
  if (order) {
    data = order;
  }
  const branch = JSON.parse(localStorage.getItem("selectedBranch"));
  const companyInfo = [
    {
      label: "Địa chỉ:",
      value: [branch?.address, branch?.wardName, branch?.districtName, branch?.provinceName]
        .filter(Boolean)
        .join(", "),
    },
    {
      label: "Hotline:",
      value: branch?.phoneNumber,
    },
    {
      label: "Ứng dụng:",
      value: "zalo mini app",
    },
  ];
  const listItems = data.listItems || [];
  const billDetails = [
    {
      label: "Tổng số lượng:",
      value: `${listItems.length} sản phẩm`,
      price: `${formatCurrency(data.originPrice || 0)} đ`,
      valueStyle: {},
    },
    {
      label: "Khuyến mãi:",
      price: `${formatCurrency(data.voucherPromotionPrice || 0)} đ`,
      color: "primary.main",
      valueStyle: {},
    },
    {
      label: "Thanh toán điểm:",
      value: `${-data.exchangePoints || 0} điểm`,
      price: data.pointPrice > 0 ? `-${formatCurrency(data.pointPrice)} đ` : "0 đ",
      valueStyle: {},
    },
    {
      label: "Tổng tiền:",
      value: "",
      price: `${formatCurrency(data.price || 0)} đ`,
      valueStyle: {},
    },
    {
      label: "Tiền thừa:",
      value: "",
      price: `${formatCurrency(changeAmount)} đ`,
      priceWeight: "bold",
      valueStyle: {},
    },

  ];

  // const handlePrintLabels = () => {
  //   const labelContents = listItems.flatMap((item: any, index: number) => {
  //     return Array.from({ length: item.quantity || 1 }).map((_, quantityIndex) => {
  //       const labelContent = document.getElementById(`print-label-content-${index}-${quantityIndex}`)?.innerHTML;
  //       if (!labelContent) {
  //         console.warn(`Không tìm thấy nội dung in cho ID: print-label-content-${index}-${quantityIndex}`);
  //       }
  //       return labelContent ? `<div class="page">${labelContent}</div>` : null;
  //     });
  //   }).filter(Boolean);

  //   if (labelContents.length === 0) {
  //     console.error("Không tìm thấy nội dung in");
  //     return;
  //   }

  //   if (window.electron && typeof window.electron.invoke === 'function') {
  //     window.electron.invoke('print-label', labelContents)
  //       .catch(error => console.error("Lỗi khi in tem qua Electron:", error));
  //   } else {
  //     console.warn("Electron API không khả dụng, chuyển sang in truyền thống.");
  //     const printWindow = window.open('', '', 'width=600,height=400');
  //     if (!printWindow) {
  //       console.error("Không thể mở cửa sổ in. Có thể bị chặn bởi trình duyệt.");
  //       return;
  //     }
  //     printWindow.document.write(`
  //       <html>
  //         <head>
  //           <title>In Tem</title>
  //           <style>
  //             @page {
  //               margin: 25mm 20mm 20mm 20mm;
  //             }
  //             body {
  //               font-family: Arial, sans-serif;
  //               font-size: 9px;
  //               margin: 0;
  //               padding: 10px;
  //             }
  //             .page {
  //               page-break-after: always;
  //               margin: 0;
  //               padding: 10px;
  //             }
  //           </style>
  //         </head>
  //         <body>
  //           ${labelContents.join('')}
  //         </body>
  //       </html>
  //     `);
  //     printWindow.document.close();
  //     printWindow.focus();
  //     printWindow.print();
  //     printWindow.close();
  //   }
  // };

  let displayPaymentPhoto = null;

  if (order?.paymentInfo || cart?.paymentSelected) {
    const paymentInfo = order?.paymentInfo || cart?.paymentSelected;

    if (paymentInfo) {
      const { bankAccountNumber, customerBankName, bankShortCode, paymentPhoto } = paymentInfo;

      if (bankAccountNumber && customerBankName && bankShortCode) {
        displayPaymentPhoto = `https://apiqr.web2m.com/api/generate/${bankShortCode}/${bankAccountNumber}/${encodeURIComponent(
          customerBankName
        )}?amount=${data.price}&memo=${encodeURIComponent(
          `Thanh toan ${cart?.cartNo ? cart.cartNo : order?.orderNo}`
        )}&is_mask=0&bg=`;
      } else if (paymentPhoto) {
        displayPaymentPhoto = paymentPhoto;
      }
    }
  }

  return (
    <div style={{ maxWidth: 300, margin: "auto" }}>
      <div
        ref={contentRef}
        id="print-invoice"
        style={{
          padding: 4,
          border: "1px solid #ccc",
          borderRadius: 4,
          height: 400,
          overflowY: "auto",
          fontFamily: "Roboto, sans-serif",
          scrollbarWidth: "thin",
          scrollbarColor: "#ccc transparent",
        }}
      >
        <style>
          {`
          /* Thanh cuộn mỏng hơn cho Chrome, Edge và Safari */
          #print-invoice::-webkit-scrollbar {
            width: 2px; 
          }
          #print-invoice::-webkit-scrollbar-thumb {
            background-color: #ccc; /* Màu thanh cuộn */
            border-radius: 3px; /* Bo góc thanh cuộn */
          }
          #print-invoice::-webkit-scrollbar-track {
            background: transparent; /* Nền thanh cuộn */
          }
        `}
        </style>
        <div style={{ textAlign: "center", marginBottom: 4 }}>
          <div style={{ padding: 4, marginBottom: 4 }}>
            <img
              src={data.shopLogo || branch?.shopLogo}
              alt="Shop Logo"
              style={{ display: "block", margin: "0 auto", width: "auto", maxHeight: 60 }}
            />
          </div>
          <p style={{ fontSize: "0.8rem", fontWeight: "bold", margin: 0 }}>
            {data.shopName || branch?.shopName || ""}
          </p>
        </div>
        {companyInfo.map((info, index) => (
          <div
            key={index}
            style={{
              display: "flex",
              fontSize: "0.8rem",
              marginBottom: 6,
              gap: 4,
            }}
          >
            <p style={{ margin: 0, whiteSpace: "nowrap" }}>{info.label}</p>
            <p style={{ margin: 0 }}>{info.value}</p>
          </div>
        ))}
        <div style={{ textAlign: "center", margin: "4px 0" }}>
          <p style={{ fontSize: "0.8rem", margin: 0 }}>HÓA ĐƠN THANH TOÁN</p>
          <p style={{ fontSize: "0.85rem", margin: 0 }}>{data.orderNo || ""}</p>
        </div>
        <div style={{ marginTop: 4 }}>
          {listItems.map((item, index) => (
            <React.Fragment key={index}>
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  padding: "8px 0",
                }}
              >
                <div style={{ flex: 1 }}>
                  <p style={{ fontSize: "0.8rem", margin: "0 0 5px 0", color: "black" }}>
                    <strong>{item.itemsName}</strong>
                  </p>
                  <p
                    style={{
                      fontSize: "0.8rem",
                      margin: "0 0 5px 0",
                      color: "#333333",
                      fontWeight: "normal",
                    }}
                  >
                    <strong>
                      {[item.variantValueOne, item.variantValueTwo, item.variantValueThree]
                        .filter(Boolean)
                        .join(" - ")}
                    </strong>
                  </p>
                  <p
                    style={{
                      fontSize: "0.8rem",
                      margin: "0 0 5px 0",
                      color: "#333333",
                      fontWeight: "normal",
                    }}
                  >
                    <strong>{item.note}</strong>
                  </p>
                  <p
                    style={{
                      fontSize: "0.8rem",
                      margin: "0 0 5px 0",
                      color: "#333333",
                      fontWeight: "normal",
                    }}
                  >
                    <strong>
                      <OptionName optionIds={item.extraOptions} />
                    </strong>
                  </p>
                </div>
                <p
                  style={{
                    fontSize: "0.8rem",
                    margin: "0 8px",
                    textAlign: "center",
                    flex: "0 0 50px",
                  }}
                >
                  x{item.quantity}
                </p>
                <div style={{ flex: 1, textAlign: "right" }}>
                  <p
                    style={{
                      fontSize: "0.8rem",
                      fontWeight: "bold",
                      color: "black",
                      margin: 0,
                    }}
                  >
                    {`${formatCurrency(item.price)} đ`}
                  </p>
                  {item.priceReal > item.price && (
                    <p
                      style={{
                        color: "#999",
                        textDecoration: "line-through",
                        fontSize: "0.7rem",
                        margin: 0,
                      }}
                    >
                      {formatCurrency(item.priceReal)} đ
                    </p>
                  )}
                </div>
              </div>
              {index < listItems.length - 1 && (
                <hr
                  style={{
                    border: "none",
                    borderTop: "1px solid #ddd",
                    margin: "8px 0",
                  }}
                />
              )}
            </React.Fragment>
          ))}
        </div>
        <div style={{ marginTop: 8, paddingTop: 4 }}>
          {billDetails.map((detail, index) => (
            <div
              key={index}
              style={{
                display: "flex",
                justifyContent: "space-between",
                fontWeight: "bold",
                marginBottom: 6,
              }}
            >
              <p style={{ fontSize: "0.8rem", fontWeight: "bold", margin: 0 }}>{detail.label}</p>
              <p
                style={
                  {
                    fontSize: "0.8rem",
                    flex: 1,
                    textAlign: "left",
                    paddingLeft: 4,
                    color: detail.color || "inherit",
                    ...(detail.valueStyle || {}),
                    margin: 0,
                  } as React.CSSProperties
                }
              >
                <strong>{detail.value}</strong>
              </p>
              <p
                style={{
                  fontSize: "0.8rem",
                  textAlign: "right",
                  fontWeight: detail.priceWeight || "bold",
                  margin: 0,
                }}
              >
                <strong>{detail.price}</strong>
              </p>
            </div>
          ))}
        </div>
        {displayPaymentPhoto && (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              marginTop: 16,
              flexDirection: "column",
            }}
          >
            <p
              style={{
                textAlign: "center",
                marginTop: 4,
                fontSize: "0.7rem",
                fontWeight: "bold",
                margin: 0,
              }}
            >
              Quét mã QR để thanh toán
            </p>
            <img
              src={displayPaymentPhoto}
              alt="QR Code"
              style={{
                maxWidth: "100%",
                maxHeight: 200,
                objectFit: "contain",
              }}
            />
          </div>
        )}
        <p
          style={{
            textAlign: "center",
            marginTop: 4,
            fontSize: "0.8rem",
            fontWeight: "bold",
            margin: 0,
          }}
        >
          Cảm ơn quý khách!
        </p>
      </div>
      {showLabels &&
        listItems.map((item, index) => (
          <div className="detachable-label" key={index}>
            <DetachableLabel item={item} cart={data} index={index} order={order} />
          </div>
        ))}
      {/* <div style={{ textAlign: 'center', marginTop: 16 }}>
        <button
          onClick={handlePrintLabels}
          style={{
            padding: '8px 16px',
            backgroundColor: '#FF0F0F',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '0.8rem',
          }}
        >
          In Tem
        </button>
      </div> */}
    </div>
  );
};

export default Bill;
