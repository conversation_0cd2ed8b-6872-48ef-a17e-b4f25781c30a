import { FileType } from "@/src/constants/file-types";

export interface VariantImage {
  type: FileType;
  link: string;
  mediaFileId: string;
}

export interface VariantSpecification {
  name: string;
  value: string;
}

export interface ValidationErrors {
  priceCapital?: string;
  price?: string;
  priceReal?: string;
  quantity?: string;
  quantityPurchase?: string;
}

export interface ProductVariant {
  itemsId: string;
  variantImage: VariantImage;
  specifications?: VariantSpecification[];
  variantNameOne: string;
  variantValueOne: string;
  variantNameTwo: string;
  variantValueTwo: string;
  variantNameThree: string;
  variantValueThree: string;
  priceCapital: number;
  priceReal: number;
  price: number;
  quantity: number;
  quantityPurchase: number;
  errors?: ValidationErrors;
}

export interface Specification {
  /** Name of the specification (e.g., "Size", "Color", "Material") */
  name: string;

  /** Possible values for this specification */
  values: string[];
}

export interface VariantFormData {
  specifications: Specification[];
  variants: ProductVariant[];
}

export const DEFAULT_VARIANT: ProductVariant = {
  itemsId: "",
  variantImage: {
    type: FileType.IMAGE,
    link: "",
    mediaFileId: "",
  },
  variantNameOne: "",
  variantValueOne: "",
  variantNameTwo: "",
  variantValueTwo: "",
  variantNameThree: "",
  variantValueThree: "",
  priceCapital: 0,
  priceReal: 0,
  price: 0,
  quantity: 0,
  quantityPurchase: 0,
  errors: {},
};

export const EXAMPLE_VARIANT: ProductVariant = {
  itemsId: "12d20f9c-e77e-4a4f-97cc-8d6fa860e47c",
  variantImage: {
    type: FileType.IMAGE,
    link: "https://dev-file.evotech.vn/ecommerce-dev/sample/items/quan_1_xanh.webp",
    mediaFileId: "",
  },
  variantNameOne: "Kích thước",
  variantValueOne: "XL",
  variantNameTwo: "Màu sắc",
  variantValueTwo: "Xanh",
  variantNameThree: "Chất liệu",
  variantValueThree: "Kaki",
  priceCapital: 550000,
  priceReal: 600000,
  price: 590000,
  quantity: 1000,
  quantityPurchase: 10,
};
