import { useState, useEffect, useMemo } from "react";
import PropTypes from "prop-types";
import { Box } from "@mui/material";
import { useSettings } from "src/hooks/use-settings";
import { useSections } from "src/layouts/dashboard/config";
import { useMobileNav } from "src/layouts/dashboard/vertical-layout/use-mobile-nav";
import { TopNav as VerticalTopNav } from "src/layouts/dashboard/vertical-layout/top-nav";
import { SideNav } from "src/layouts/dashboard/vertical-layout/side-nav";
import { TopNav as HorizontalTopNav } from "src/layouts/dashboard/horizontal-layout/top-nav";
import { MobileNav } from "src/layouts/dashboard/mobile-nav";
import useMediaQuery from "@mui/material/useMediaQuery";
import { styled } from "@mui/material/styles";
import { useAppDispatch } from "@/src/redux/hooks";
import { detailShop } from "@/src/redux/slices/shopSlice";
import { useStoreId } from "@/src/hooks/use-store-id";
import { SidebarProvider, useSidebar } from "src/contexts/sidebar-context";
import { SidebarToggleButton } from "src/components/sidebar-toggle-button";

export const SIDE_NAV_WIDTH = 280;
export const SIDE_NAV_COLLAPSED_WIDTH = 80;

const VerticalLayoutRoot = styled("div")(({ theme, isCollapsed }) => ({
  display: "flex",
  flex: "1 1 auto",
  maxWidth: "100%",
  [theme.breakpoints.up("lg")]: {
    paddingLeft: isCollapsed ? SIDE_NAV_COLLAPSED_WIDTH : SIDE_NAV_WIDTH,
    transition: "padding-left 0.3s ease",
  },
}));

const VerticalLayoutContainer = styled("div")({
  display: "flex",
  flex: "1 1 auto",
  flexDirection: "column",
  width: "100%",
});

const HorizontalLayoutRoot = styled("div")({
  display: "flex",
  flex: "1 1 auto",
  maxWidth: "100%",
});

const HorizontalLayoutContainer = styled("div")({
  display: "flex",
  flex: "1 1 auto",
  flexDirection: "column",
  width: "100%",
});

const MainLayoutContent = ({ children }) => {
  const settings = useSettings();
  const sections = useSections();
  const mobileNav = useMobileNav();
  const lgUp = useMediaQuery((theme) => theme.breakpoints.up("lg"));
  const dispatch = useAppDispatch();
  const storeId = useStoreId();
  const { isCollapsed } = useSidebar();

  // Determine the layout type based on settings
  const isHorizontal = settings.layout === "horizontal";

  // Layout-specific components
  const LayoutRoot = isHorizontal ? HorizontalLayoutRoot : VerticalLayoutRoot;
  const LayoutContainer = isHorizontal ? HorizontalLayoutContainer : VerticalLayoutContainer;

  useEffect(() => {
    if (storeId) {
      dispatch(detailShop(storeId));
    }
  }, [dispatch, storeId]);

  return (
    <Box sx={{ display: "flex", flexDirection: "column", minHeight: "100vh" }}>
      {/* Render the appropriate TopNav based on layout */}
      {isHorizontal ? (
        <HorizontalTopNav
          color={settings.navColor}
          onMobileNav={mobileNav.handleOpen}
          sections={sections}
        />
      ) : (
        <VerticalTopNav onMobileNavOpen={mobileNav.handleOpen} />
      )}

      {/* Render SideNav for vertical layout on desktop */}
      {!isHorizontal && lgUp && <SideNav color={settings.navColor} sections={sections} />}

      {/* Mobile navigation for both layouts */}
      {!lgUp && (
        <MobileNav
          color={settings.navColor}
          onClose={mobileNav.handleClose}
          open={mobileNav.open}
          sections={sections}
        />
      )}

      {/* Sidebar toggle button */}
      <SidebarToggleButton />

      {/* Main content area */}
      <LayoutRoot isCollapsed={isCollapsed}>
        <LayoutContainer>{children}</LayoutContainer>
      </LayoutRoot>
    </Box>
  );
};

export const MainLayout = ({ children }) => {
  return (
    <SidebarProvider>
      <MainLayoutContent>{children}</MainLayoutContent>
    </SidebarProvider>
  );
};

MainLayout.propTypes = {
  children: PropTypes.node.isRequired,
};
