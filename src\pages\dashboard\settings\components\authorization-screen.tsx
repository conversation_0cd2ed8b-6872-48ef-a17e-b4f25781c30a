import React, { useState, useEffect } from "react";
import { Box, Typography, Checkbox, Divider, Paper, styled } from "@mui/material";
import { PermissionType } from "@/src/constants/constant";

// <PERSON><PERSON><PERSON> nghĩa các kiểu dữ liệu
export interface FunctionDto {
  functionId: string;
  functionCode: string;
  functionName: string;
  url: string;
  icon: string;
  orderNumber: number;
  roleDescription: string;
  permissions: PermissionType[];
  children: FunctionDto[];
}

interface PermissionModuleProps {
  permissionModules: FunctionDto[];
  onPermissionChange?: (moduleId: string, permission: PermissionType, checked: boolean) => void;
  selectedPermissions?: Record<string, PermissionType[]>;
}

// Styled component cho container chính
const PermissionContainer = styled(Paper)(({ theme }) => ({
  padding: 0,
  borderRadius: "4px",
  border: "1px solid #e0e0e0",
  // maxWidth: "700px",
  overflow: "hidden",
}));

// Styled component cho tiêu đề của mục cha
const ParentTitle = styled(Typography)(({ theme }) => ({
  fontWeight: "bold",
  fontSize: "14px",
  padding: "10px 16px",
  backgroundColor: "#f5f5f5",
  textTransform: "uppercase",
}));

// Styled component cho tiêu đề của mục con
const ChildTitle = styled(Typography)(({ theme }) => ({
  fontSize: "14px",
  padding: "10px 16px",
  paddingLeft: "16px",
  color: "#000",
  display: "flex",
  alignItems: "center",
  "&:hover": {
    backgroundColor: "#f9f9f9",
  },
}));

// Styled component cho hàng hiển thị quyền
const PermissionRow = styled(Box)(({ theme }) => ({
  display: "flex",
  padding: "10px 16px",
  alignItems: "center",
  borderBottom: "1px solid #f0f0f0",
  "&:last-child": {
    borderBottom: "none",
  },
}));

// Styled component cho vùng chứa các checkbox quyền
const PermissionCheckboxes = styled(Box)(({ theme }) => ({
  display: "flex",
  marginLeft: "auto",
  gap: "20px",
}));

// Styled component cho container của mỗi checkbox
const CheckboxContainer = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  minWidth: "80px",
}));

// Tiêu đề của component
const TitleBox = styled(Box)(({ theme }) => ({
  padding: "16px 16px 8px 16px",
  borderBottom: "1px solid #e0e0e0",
}));

const PermissionModule: React.FC<PermissionModuleProps> = ({
  permissionModules,
  onPermissionChange,
  selectedPermissions = {},
}) => {
  // Kiểm tra nếu một quyền đã được chọn
  const isPermissionSelected = (moduleId: string, permission: PermissionType) => {
    return selectedPermissions[moduleId]?.includes(permission) || false;
  };

  // Kiểm tra nếu tất cả quyền đã được chọn
  const isAllPermissionsSelected = (moduleId: string, permissions: PermissionType[]) => {
    if (!permissions || permissions.length === 0) return false;
    return permissions.every((permission) => isPermissionSelected(moduleId, permission));
  };

  // Xử lý khi chọn tất cả quyền
  const handleSelectAllPermissions = (
    moduleId: string,
    permissions: PermissionType[],
    checked: boolean,
    children?: FunctionDto[]
  ) => {
    console.log({ moduleId });
    console.log({ permissions });
    console.log({ children });
    if (onPermissionChange) {
      // Xử lý permissions của module cha
      permissions.forEach((permission) => {
        onPermissionChange(moduleId, permission, checked);
      });

      // Xử lý permissions của tất cả module con
      if (children && children.length > 0) {
        children.forEach((child) => {
          child.permissions.forEach((permission) => {
            onPermissionChange(child.functionId, permission, checked);
          });
        });
      }
    }
  };

  // Xử lý khi checkbox quyền thay đổi
  const handlePermissionChange = (
    moduleId: string,
    permission: PermissionType,
    checked: boolean
  ) => {
    if (onPermissionChange) {
      onPermissionChange(moduleId, permission, checked);
    }
  };

  // Chuyển đổi PermissionType sang tiếng Việt
  const translatePermission = (permission: PermissionType): string => {
    const translations: Record<PermissionType, string> = {
      View: "Xem",
      Add: "Thêm",
      Edit: "Chỉnh sửa",
      Delete: "Xóa",
      Import: "Import",
      Export: "Export",
    };
    return translations[permission] || permission;
  };

  // Kiểm tra nếu một module có quyền Export
  const hasExportPermission = (permissions: PermissionType[]) => {
    return permissions.includes("Export");
  };

  // Hiển thị checkboxes cho mỗi permission
  const renderPermissionCheckboxes = (
    moduleId: string,
    permissions: PermissionType[],
    indent: number = 0
  ) => {
    return (
      <PermissionCheckboxes>
        {permissions.map((permission) => (
          <CheckboxContainer key={`${moduleId}-${permission}`}>
            <Checkbox
              checked={isPermissionSelected(moduleId, permission)}
              onChange={(e) => handlePermissionChange(moduleId, permission, e.target.checked)}
              size="small"
            />
            <Typography variant="body2">{translatePermission(permission)}</Typography>
          </CheckboxContainer>
        ))}
      </PermissionCheckboxes>
    );
  };

  // Hiển thị module cha
  const renderParentModule = (module: FunctionDto) => {
    return (
      <React.Fragment key={module.functionId}>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <ParentTitle>{module.functionName}</ParentTitle>
          <PermissionRow>
            <Box ml="auto">
              <Checkbox
                checked={
                  isAllPermissionsSelected(module.functionId, module.permissions) &&
                  module.children?.every((child) =>
                    isAllPermissionsSelected(child.functionId, child.permissions)
                  )
                }
                onChange={(e) =>
                  handleSelectAllPermissions(
                    module.functionId,
                    module.permissions,
                    e.target.checked,
                    module.children
                  )
                }
                size="small"
              />
            </Box>
            <Typography variant="body2" sx={{ fontWeight: 500, ml: 2 }}>
              Tất cả quyền
            </Typography>
          </PermissionRow>
        </Box>
        {module.children && module.children.map((child) => renderChildModule(child))}
      </React.Fragment>
    );
  };

  // Hiển thị module con
  const renderChildModule = (module: FunctionDto) => {
    return (
      <PermissionRow key={module.functionId}>
        <Typography variant="body2" sx={{ ml: 2 }}>
          {module.functionName}
        </Typography>
        {renderPermissionCheckboxes(module.functionId, module.permissions)}
      </PermissionRow>
    );
  };

  return (
    <PermissionContainer elevation={0}>
      <TitleBox>
        <Typography variant="subtitle1" fontWeight="medium">
          Quyền hạn
        </Typography>
      </TitleBox>
      {Array.isArray(permissionModules) &&
        permissionModules.map((module) => renderParentModule(module))}
    </PermissionContainer>
  );
};

// Component sử dụng để demo
export default function PermissionManagement({
  activePackage,
  selectedPermissions,
  setSelectedPermissions,
}) {
  // Demo data
  const pkg = activePackage;
  const permissionModules = pkg?.functions;
  // State để lưu trữ các quyền đã chọn

  console.log({ selectedPermissions });
  // Hàm xử lý khi quyền thay đổi
  const handlePermissionChange = (
    moduleId: string,
    permission: PermissionType,
    checked: boolean
  ) => {
    setSelectedPermissions((prev) => {
      const modulePermissions = prev[moduleId] || [];

      if (checked && !modulePermissions.includes(permission)) {
        return {
          ...prev,
          [moduleId]: [...modulePermissions, permission],
        };
      } else if (!checked && modulePermissions.includes(permission)) {
        return {
          ...prev,
          [moduleId]: modulePermissions.filter((p) => p !== permission),
        };
      }

      return prev;
    });
  };

  return (
    <Box sx={{ p: 3 }}>
      <PermissionModule
        permissionModules={permissionModules}
        onPermissionChange={handlePermissionChange}
        selectedPermissions={selectedPermissions}
      />
    </Box>
  );
}
