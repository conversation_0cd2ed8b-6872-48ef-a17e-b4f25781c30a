import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  TextField,
  InputAdornment,
  Button,
  Box,
} from "@mui/material";
import Checkbox from "@mui/material/Checkbox";
import FormControlLabel from "@mui/material/FormControlLabel";
import { useSnackbar } from "notistack";

const EditReferralRuleDialog = ({ open, onClose, onSave, initialData }) => {
  const [referralPoints, setReferralPoints] = useState(initialData?.rule || "10");
  const [maxTurns, setMaxTurns] = useState(initialData?.maxTurns || "10");
  const [requirePurchase, setRequirePurchase] = useState(
    initialData?.isPurchase?.isRequire || false
  );
  const { enqueueSnackbar } = useSnackbar();
  const [minSpending, setMinSpending] = useState(initialData?.isPurchase?.minSpent || "0");
  const [error, setError] = useState("");

  const handleSave = () => {
    if (requirePurchase && !minSpending) {
      setError("Vui lòng nhập số tiền tối thiểu.");
      return;
    }

    if (Number(referralPoints) < 0 || Number(maxTurns) < 0) {
      enqueueSnackbar("Điểm phải lớn hơn 0! Vui lòng nhập lại.", { variant: "error" });
      return;
    }

    setError("");
    onSave({
      ...initialData,
      rule: referralPoints,
      maxTurns: maxTurns,
      isPurchase: {
        isRequire: requirePurchase,
        minSpent: minSpending,
      },
    });
  };

  useEffect(() => {
    if (initialData?.rule !== undefined) {
      setReferralPoints(initialData.rule);
      setMaxTurns(initialData.maxTurns);
      setRequirePurchase(initialData.isPurchase?.isRequire || false);
      setMinSpending(initialData.isPurchase?.minSpent || "");
    }
  }, [initialData]);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ borderBottom: "1px solid #ccc" }}>
        <Typography fontWeight="700" fontSize="20px">
          Kiếm điểm{" "}
          <Typography component="span" sx={{ color: "gray", fontSize: "14px" }}>
            {" "}
            ( Chia sẻ cửa hàng )
          </Typography>
        </Typography>
      </DialogTitle>

      <DialogContent sx={{ marginTop: "20px" }}>
        <Box
          sx={{
            backgroundColor: "#F5F5F5",
            padding: "12px",
            borderRadius: "8px",
            marginBottom: "16px",
          }}
        >
          <Typography variant="body2" color="textSecondary">
            Người dùng chia sẻ cửa hàng với bạn bè và khi bạn bè kích hoạt tài khoản thành công,
            người dùng có thể nhận được điểm.
          </Typography>
        </Box>
        <Typography fontWeight="bold" marginBottom={2}>
          Yêu cầu mua hàng
        </Typography>

        <Box sx={{ mb: 3 }}>
          <Box
            sx={{
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              gap: 2,
            }}
          >
            <FormControlLabel
              control={
                <Checkbox
                  checked={requirePurchase}
                  onChange={(e) => setRequirePurchase(e.target.checked)}
                />
              }
              label="Tổng số tiền chi tiêu tối thiểu của người được giới thiệu"
              sx={{ flexShrink: 0 }}
            />
            {requirePurchase && (
              <TextField
                value={minSpending}
                onChange={(e) => {
                  const value = e.target.value.replace(/[^0-9]/g, "");
                  setMinSpending(value ? value : "0");
                }}
                variant="outlined"
                size="small"
                placeholder="Nhập số tiền tối thiểu"
                sx={{
                  minWidth: "200px",
                  flex: "1 1 auto",
                  maxWidth: "300px",
                  "& .MuiOutlinedInput-root": {
                    padding: "0 !important",
                  },
                  "& .MuiInputBase-input": {
                    textAlign: "right",
                    paddingRight: "8px",
                  },
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <Typography
                        sx={{
                          color: "#757575",
                          fontSize: "14px",
                          padding: "10px 15px",
                          background: "#eeeeee",
                          borderRadius: "0 8px 8px 0",
                          borderLeft: "1px solid #ccc",
                          minWidth: "45px",
                          textAlign: "center",
                        }}
                      >
                        đ
                      </Typography>
                    </InputAdornment>
                  ),
                }}
              />
            )}
          </Box>
          {error && (
            <Typography fontWeight="400" color="error" variant="body2" mt={1}>
              {error}
            </Typography>
          )}
        </Box>

        <Typography fontWeight="bold" marginBottom={2}>
          Quy tắc
        </Typography>

        <Box>
          <Typography fontWeight="400" mb={1}>
            Chia sẻ cửa hàng để nhận điểm
          </Typography>
          <TextField
            variant="outlined"
            value={referralPoints}
            type="text"
            onChange={(e) => {
              const value = e.target.value;
              if (/^\d*$/.test(value)) {
                setReferralPoints(value);
              }
            }}
            onKeyDown={(e) => {
              if (["-", "e", "E", "+", "."].includes(e.key)) {
                e.preventDefault();
              }
            }}
            size="small"
            defaultValue={10}
            fullWidth
            inputMode="decimal"
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <Typography
                    sx={{
                      color: "#757575",
                      fontSize: "14px",
                      padding: "10px 15px 10px 15px",
                      background: "#eeeeee",
                      borderRadius: "0 8px 8px 0",
                      borderLeft: "1px solid #ccc",
                    }}
                  >
                    điểm
                  </Typography>
                </InputAdornment>
              ),
            }}
            sx={{
              "& .MuiInputBase-input": {
                textAlign: "left",
              },
              "& .MuiOutlinedInput-root": {
                padding: "0 !important",
                margin: "0 !important",
              },
            }}
          />
          <Typography fontWeight="400" mt={2} mb={1}>
            Số lượt chia sẻ tối đa được nhận thưởng
          </Typography>
          <TextField
            variant="outlined"
            value={maxTurns}
            type="text"
            onChange={(e) => {
              const value = e.target.value;
              if (/^\d*$/.test(value)) {
                setMaxTurns(value);
              }
            }}
            onKeyDown={(e) => {
              if (["-", "e", "E", "+", "."].includes(e.key)) {
                e.preventDefault();
              }
            }}
            size="small"
            defaultValue={10}
            fullWidth
            InputProps={{
              inputMode: "numeric",
              endAdornment: (
                <InputAdornment position="end">
                  <Typography
                    sx={{
                      color: "#757575",
                      fontSize: "14px",
                      padding: "10px 15px 10px 15px",
                      background: "#eeeeee",
                      borderRadius: "0 8px 8px 0",
                      borderLeft: "1px solid #ccc",
                    }}
                  >
                    Lượt
                  </Typography>
                </InputAdornment>
              ),
            }}
            sx={{
              "& .MuiInputBase-input": {
                textAlign: "left",
              },
              "& .MuiOutlinedInput-root": {
                padding: "0 !important",
              },
            }}
          />
        </Box>
      </DialogContent>

      <DialogActions sx={{ marginRight: 2, marginBottom: 1.5 }}>
        <Button onClick={onClose} variant="outlined">
          Hủy bỏ
        </Button>
        <Button onClick={handleSave} variant="contained" color="primary">
          Xác nhận
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EditReferralRuleDialog;
