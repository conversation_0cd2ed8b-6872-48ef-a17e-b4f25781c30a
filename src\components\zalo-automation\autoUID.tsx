// import { useState, useEffect } from "react";
// import {
//   Box,
//   Paper,
//   Tabs,
//   Tab,
//   Table,
//   TableBody,
//   TableCell,
//   TableContainer,
//   TableHead,
//   TableRow,
//   Switch,
//   Button,
//   Link,
//   Typography,
//   FormControl,
//   Select,
//   MenuItem,
//   IconButton,
//   Dialog,
//   DialogContent,
//   DialogTitle,
//   SelectChangeEvent,
//   DialogContentText,
//   DialogActions,
// } from "@mui/material";
// import { Add, ChevronLeft, ChevronRight, Delete } from "@mui/icons-material";
// import TransactionNotificationForm from "./editUID"; // Import the popup component
// import { useZaloTemplate } from "@/src/api/hooks/zalo-template/zalo-template";
// import { logger } from "@/src/utils/logger";
// import { ACTIVE_STATUS, INACTIVE_STATUS, LIMIT, SKIP, ZALO_TABS } from "@/src/constants/constant";
// import { formatMoney } from "@/src/utils/format-money";
// import useSnackbar from "@/src/hooks/use-snackbar";
// import { useStoreId } from "@/src/hooks/use-store-id";

// export interface ZaloTemplateRequest {
//   skip: number;
//   limit: number;
//   search?: string;
//   type: string;
//   shopId: string;
// }

// export default function TransactionNotification() {
//   const snackbar = useSnackbar();
//   const storeId = useStoreId();
//   const { getZaloTemplate, deleteZaloTemplate, changeTemplateStatus } = useZaloTemplate();
//   const [zaloTemplates, setZaloTemplates] = useState<any[]>([]);
//   const [selectedTemplate, setSelectedTemplate] = useState<any | null>(null);
//   const [total, setTotal] = useState<number>();
//   const [tabValue, setTabValue] = useState(0);
//   const [modelSearch, setModelSearch] = useState<ZaloTemplateRequest>({
//     skip: SKIP,
//     limit: LIMIT,
//     search: "",
//     type: ZALO_TABS[tabValue].text,
//     shopId: storeId || "",
//   });
//   const [isPopupOpen, setIsPopupOpen] = useState(false); // State to control popup visibility
//   const [isSwitchLoading, setIsSwitchLoading] = useState(false);
//   const [deleteTemplateId, setDeleteTemplateId] = useState(null);
//   const [openDialogDelete, setOpenDialogDelete] = useState(false);

//   useEffect(() => {
//     setModelSearch((prev) => ({
//       ...prev,
//       shopId: storeId || "",
//     }));
//   }, [storeId]);

//   useEffect(() => {
//     if (!modelSearch.shopId || modelSearch.shopId == "") {
//       return;
//     }
//     fetchZaloTemplate();
//   }, [modelSearch]);

//   const fetchZaloTemplate = async () => {
//     try {
//       const response = await getZaloTemplate(modelSearch);
//       setZaloTemplates(response.data.data);
//       setTotal(response.data.total);
//     } catch (error) {
//       logger.error("Error fetching shops:", error);
//     }
//   };

//   useEffect(() => {
//     const newType = ZALO_TABS[tabValue].text;
//     setModelSearch((prev) => ({
//       ...prev,
//       type: newType,
//       skip: 0,
//     }));
//   }, [tabValue]);

//   const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
//     setTabValue(newValue);
//   };

//   const handleChangePage = (direction: "next" | "prev") => {
//     setModelSearch((prev) => {
//       const newSkip =
//         direction === "next" ? prev.skip + prev.limit : Math.max(0, prev.skip - prev.limit);
//       return { ...prev, skip: newSkip };
//     });
//   };

//   const handleChangeRowsPerPage = (event: SelectChangeEvent<number>) => {
//     const newLimit = Number(event.target.value);
//     setModelSearch((prev) => ({
//       ...prev,
//       limit: newLimit,
//       skip: 0,
//     }));
//   };
//   const handleChangeStatusTemplate = async (event, id) => {
//     setIsSwitchLoading(true);
//     const newStatus = event.target.checked ? ACTIVE_STATUS : INACTIVE_STATUS;
//     await changeTemplateStatus(id, newStatus)
//       .then((response) => {
//         snackbar.success("Cập nhật trạng thái thành công");
//         const updatedId = response.data.data;
//         const updatedTemplates = zaloTemplates.map((template) =>
//           template.id === updatedId ? { ...template, status: newStatus } : template
//         );
//         setZaloTemplates(updatedTemplates);
//       })
//       .catch((err) => {
//         console.error("Cập nhật trạng thái thất bại");
//       })
//       .finally(() => {
//         setIsSwitchLoading(false);
//       });
//   };

//   const handleOpenPopup = (template?: any) => {
//     setSelectedTemplate(template || null);
//     setIsPopupOpen(true);
//   };

//   const handleClosePopup = () => {
//     setSelectedTemplate(null);
//     setIsPopupOpen(false);
//   };

//   const handleDeleteClick = (id) => {
//     setDeleteTemplateId(id);
//     setOpenDialogDelete(true);
//   };

//   const handleCancelDelete = () => {
//     setDeleteTemplateId(null);
//     setOpenDialogDelete(false);
//   };

//   const handleConfirmDelete = async () => {
//     if (deleteTemplateId !== null) {
//       try {
//         await deleteZaloTemplate(deleteTemplateId);
//         await fetchZaloTemplate();
//         snackbar.success("Xóa mẫu tin Zalo thành công");
//       } catch (error) {
//         snackbar.error("Có lỗi xảy ra khi xóa mẫu tin Zalo");
//       }
//     }
//     setDeleteTemplateId(null);
//     setOpenDialogDelete(false);
//   };

//   // Styles
//   const tabStyles = {
//     "& .MuiTab-root": {
//       textTransform: "none",
//       color: "black",
//       alignItems: "flex-start",
//     },
//   };

//   const tableCellHeaderStyles = { fontWeight: "bold", color: "black" };

//   const selectStyles = {
//     "& .MuiOutlinedInput-notchedOutline": {
//       border: "none",
//     },
//     boxShadow: "none",
//   };

//   const paginationBoxStyles = {
//     display: "flex",
//     justifyContent: "flex-end",
//     alignItems: "center",
//     mt: 2,
//   };

//   const currentPageStart = modelSearch.skip + 1;
//   const currentPageEnd = Math.min(modelSearch.skip + modelSearch.limit, total);
//   const isPrevDisabled = modelSearch.skip === 0;
//   const isNextDisabled = currentPageEnd >= total;

//   const rowsPerPageControl = (
//     <>
//       <Typography variant="body2" sx={{ mr: 2 }}>
//         Số dòng mỗi trang
//       </Typography>
//       <FormControl size="small" sx={{ minWidth: 70 }}>
//         <Select value={modelSearch.limit} onChange={handleChangeRowsPerPage} sx={selectStyles}>
//           <MenuItem value={10}>10</MenuItem>
//           <MenuItem value={25}>25</MenuItem>
//           <MenuItem value={50}>50</MenuItem>
//         </Select>
//       </FormControl>
//     </>
//   );

//   const paginationControls = (
//     <>
//       <Typography variant="body2" sx={{ mx: 2 }}>
//         {`${currentPageStart}-${currentPageEnd} of ${total}`}
//       </Typography>
//       <Button size="small" disabled={isPrevDisabled} onClick={() => handleChangePage("prev")}>
//         <ChevronLeft />
//       </Button>
//       <Button size="small" disabled={isNextDisabled} onClick={() => handleChangePage("next")}>
//         <ChevronRight />
//       </Button>
//     </>
//   );
//   return (
//     <Box sx={{ width: "100%", p: 3 }}>
//       <Tabs
//         value={tabValue}
//         onChange={handleTabChange}
//         aria-label="notification tabs"
//         textColor="inherit"
//         TabIndicatorProps={{
//           style: { backgroundColor: "blue" },
//         }}
//         sx={tabStyles}
//       >
//         {ZALO_TABS.map((tab) => (
//           <Tab key={tab.value} label={tab.label} value={tab.value} />
//         ))}
//       </Tabs>

//       <TableContainer component={Paper} sx={{ mt: 3 }}>
//         <Table>
//           <TableHead>
//             <TableRow>
//               <TableCell sx={tableCellHeaderStyles}>STT</TableCell>
//               <TableCell sx={tableCellHeaderStyles}>Tên mẫu tin</TableCell>
//               <TableCell sx={tableCellHeaderStyles}>Trạng thái</TableCell>
//               <TableCell sx={tableCellHeaderStyles}>Giới hạn gửi tin</TableCell>
//               <TableCell sx={tableCellHeaderStyles}>Chi phí/tin</TableCell>
//               <TableCell sx={tableCellHeaderStyles}>Sự kiện kích hoạt</TableCell>
//               <TableCell sx={tableCellHeaderStyles}>Quản lý</TableCell>
//             </TableRow>
//           </TableHead>
//           <TableBody>
//             {zaloTemplates && zaloTemplates.length > 0 ? (
//               zaloTemplates.map((template, index) => (
//                 <TableRow key={template.id || template.name}>
//                   <TableCell>{modelSearch.limit * modelSearch.skip + index + 1}</TableCell>
//                   <TableCell>{template.name}</TableCell>
//                   <TableCell>
//                     <Switch
//                       onChange={(event) => handleChangeStatusTemplate(event, template.id)}
//                       checked={template.status === ACTIVE_STATUS}
//                       size="small"
//                       disabled={isSwitchLoading}
//                     />
//                   </TableCell>
//                   <TableCell>Theo chính sách Zalo</TableCell>
//                   <TableCell>{formatMoney(template?.cost)}đ</TableCell>
//                   <TableCell>{template?.eventName || ""}</TableCell>
//                   <TableCell align="right">
//                     <Box
//                       sx={{
//                         display: "flex",
//                         justifyContent: "space-between",
//                         alignItems: "center",
//                       }}
//                     >
//                       <Link href="#" underline="hover" onClick={() => handleOpenPopup(template)}>
//                         Sửa mẫu tin
//                       </Link>
//                       <IconButton
//                         color="error"
//                         size="small"
//                         sx={{ ml: 2 }}
//                         onClick={() => handleDeleteClick(template.id)}
//                       >
//                         <Delete />
//                       </IconButton>
//                       <Dialog
//                         open={openDialogDelete}
//                         onClose={handleCancelDelete}
//                         maxWidth="sm"
//                         fullWidth
//                       >
//                         <DialogTitle>Xác nhận xoá</DialogTitle>
//                         <DialogContent>
//                           <DialogContentText>
//                             Bạn có chắc chắn muốn xóa mẫu tin này không?
//                           </DialogContentText>
//                         </DialogContent>
//                         <DialogActions>
//                           <Button
//                             onClick={handleCancelDelete}
//                             variant="outlined"
//                             sx={{ minWidth: 100 }}
//                           >
//                             Hủy
//                           </Button>
//                           <Button
//                             onClick={handleConfirmDelete}
//                             color="error"
//                             variant="contained"
//                             autoFocus
//                             sx={{ minWidth: 100 }}
//                           >
//                             Xóa
//                           </Button>
//                         </DialogActions>
//                       </Dialog>
//                     </Box>
//                   </TableCell>
//                 </TableRow>
//               ))
//             ) : (
//               <TableRow>
//                 <TableCell colSpan={6} align="center">
//                   Không có mẫu tin nào
//                 </TableCell>
//               </TableRow>
//             )}
//             <TableRow>
//               <TableCell colSpan={6} align="left" sx={{ textAlign: "left" }}>
//                 <Link
//                   href="#"
//                   underline="hover"
//                   onClick={() => handleOpenPopup(null)}
//                   sx={{ color: "primary.main", fontWeight: "bold" }}
//                 >
//                   Thêm mẫu tin
//                 </Link>
//               </TableCell>
//             </TableRow>
//           </TableBody>
//         </Table>
//       </TableContainer>
//       <Box sx={paginationBoxStyles}>
//         {rowsPerPageControl}
//         {paginationControls}
//       </Box>
//       <Dialog open={isPopupOpen} onClose={handleClosePopup} fullWidth maxWidth="lg">
//         <DialogContent>
//           <TransactionNotificationForm
//             type={tabValue}
//             onClose={handleClosePopup}
//             fetchZaloTemplate={fetchZaloTemplate}
//             selectedTemplate={selectedTemplate}
//           />
//         </DialogContent>
//       </Dialog>
//     </Box>
//   );
// }
