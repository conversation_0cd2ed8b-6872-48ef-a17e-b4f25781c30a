import { Box, Button, TextField, Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";
import React, { useState } from "react";
import ListItemOrder from "./ListItemOrder";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import TitleDialog from "../../dialog/TitleDialog";
import SearchItemOrder from "./SearchItemOrder";
export default function OrderItemsBox({
  cartItems,
  updateQuantity,
  removeItem,
  addItemToCart,
  taxRateShop,
}) {
  const [openDialog, setOpenDialog] = useState(false);

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const onClickAddItem = () => {
    setOpenDialog(true);
  };

  const handleSubmit = async (items) => {
    addItemToCart(items);

    handleCloseDialog();
  };

  return (
    <Box sx={{ width: "100%" }}>
      <Button
        startIcon={<AddCircleIcon />}
        onClick={onClickAddItem}
        variant="outlined"
        fullWidth
        sx={{
          borderStyle: "dashed",
          paddingTop: 2,
          paddingBottom: 2,
          color: "#2654FE",
          borderColor: "#2654FE",
          "&:hover": {
            borderStyle: "dashed",
            bgcolor: "primary.lighter",
          },
        }}
      >
        Thêm sản phẩm
      </Button>

      <ListItemOrder
        cartItems={cartItems}
        updateQuantity={updateQuantity}
        removeItem={removeItem}
        taxRateShop={taxRateShop}
      />

      <TitleDialog
        title="Chọn sản phẩm"
        open={openDialog}
        handleClose={handleCloseDialog}
        submitBtnTitle="Xác nhận"
        showActionDialog={false}
        maxWidth="xl"
      >
        <SearchItemOrder handleSubmit={handleSubmit} handleClose={handleCloseDialog} />
      </TitleDialog>
    </Box>
  );
}
