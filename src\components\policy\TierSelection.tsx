import React from "react";
import { Typography, Box, useTheme } from "@mui/material";
import DoneIcon from "@mui/icons-material/Done";
import Grid from "@mui/system/Grid";

const TierSelection = ({ selectedTier, setSelectedTier }) => {
  const theme = useTheme();

  return (
    <Grid
      container
      sx={{
        display: "flex",
        marginBottom: 4,
        alignItems: "center",
        justifyContent: "space-between",
        "@media(max-width: 440px)": {
          flexDirection: "column",
        },
      }}
      spacing={2}
    >
      <Grid
        onClick={() => setSelectedTier("single")}
        sx={{
          border: `2px solid ${
            selectedTier === "single" ? theme.palette.primary.main : theme.palette.grey[400]
          }`,
          borderRadius: 2,
          padding: 2,
          cursor: "pointer",
          width: "48%",
          flexDirection: "column",
          "@media(max-width: 440px)": {
            width: "100%",
          },
        }}
      >
        <Typography
          variant="subtitle1"
          sx={{
            fontWeight: "bold",
            marginBottom: 1,
            overflow: "hidden",
            whiteSpace: "nowrap",
            textOverflow: "ellipsis",
          }}
        >
          Tích điểm đơn cấp
        </Typography>
        <Typography
          variant="body2"
          sx={{
            marginBottom: 1,
            wordBreak: "break-word",
            overflow: "hidden",
          }}
        >
          Người tiêu dùng mua hàng, họ được tích điểm và số điểm này có thể dùng để quy đổi voucher
          hoặc trừ vào các lần mua hàng tiếp theo.
        </Typography>
        <Typography
          variant="body2"
          sx={{
            color: theme.palette.error.main,
            display: "flex",
            alignItems: "center",
            wordBreak: "break-word",
            marginTop: "auto",
          }}
        >
          * Tỉ lệ tích điểm 1 hạng duy nhất
          <Box
            component="span"
            sx={{ marginLeft: 1, visibility: selectedTier === "single" ? "visible" : "hidden" }}
          >
            <DoneIcon sx={{ fontSize: 20, color: theme.palette.primary.main }} />
          </Box>
        </Typography>
      </Grid>
      <Grid
        onClick={() => setSelectedTier("multi")}
        sx={{
          border: `2px solid ${
            selectedTier === "multi" ? theme.palette.primary.main : theme.palette.grey[400]
          }`,
          borderRadius: 2,
          padding: 2,
          width: "48%",
          cursor: "pointer",
          flexDirection: "column",
          "@media(max-width: 440px)": {
            width: "100%",
          },
        }}
      >
        <Typography
          variant="subtitle1"
          sx={{
            fontWeight: "bold",
            marginBottom: 1,
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
        >
          Tích điểm nâng hạng
        </Typography>
        <Typography
          variant="body2"
          sx={{
            marginBottom: 1,
            wordBreak: "break-word",
            overflow: "hidden",
          }}
        >
          Người tiêu dùng mua hàng, họ được tích điểm và số điểm này có thể dùng để quy đổi voucher
          hoặc trừ vào các lần mua hàng tiếp theo.
        </Typography>
        <Typography
          variant="body2"
          sx={{
            color: theme.palette.error.main,
            display: "flex",
            alignItems: "center",
            wordBreak: "break-word",
            marginTop: "auto",
          }}
        >
          * Tỉ lệ tích điểm phân 3 hạng
          <Box
            component="span"
            sx={{ marginLeft: 1, visibility: selectedTier === "multi" ? "visible" : "hidden" }}
          >
            <DoneIcon sx={{ fontSize: 20, color: theme.palette.primary.main }} />
          </Box>
        </Typography>
      </Grid>
    </Grid>
  );
};

export default TierSelection;
