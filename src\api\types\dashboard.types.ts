export interface ParamDashboard {
  shopId: string;
  paramMonth: string;
  paramDate?: string;
  startDate?: string;
  endDate?: string;
  branchId?: string;
  typeSortRank?: string;
}
export interface IInfoCount {
  iconName: string;
  title: string;
  valueToday: string;
  valueYesterday: string;
  valuePercent: string;
}

export interface IDataOrderShop {
  iconName: string;
  title: string;
  value: string;
}
export interface IDataOrderShopOffline {
  title: string;
  value: string;
}
export interface Top10ItemPopulars {
  key: string;
  image: string;
  name: string;
  totalQuantity: string;
}
interface RankUser {
  fullName: string;
  avatarUrl: string;
}
export interface IDashboarPartner {
  today: string;
  shopId: string;
  infoCount: IInfoCount[];
  top10ItemPopulars: Top10ItemPopulars[];
  totalUserByDayChart: number[];
  totalUserOrderByDayChart: number[];
  listDayChart: string[];
  dataShopOnline: IDataOrderShop[];
  dataShopOffline: IDataOrderShopOffline[];
  dataChartDetailRevenue: {
    listDayOfMonth: string[];
    listRevenueOfDay: number[];
  };
  dataChartConvertCustomer: {
    day: string[];
    user: string[];
    customer: string[];
  };
  rankUsers: RankUser[];
}
