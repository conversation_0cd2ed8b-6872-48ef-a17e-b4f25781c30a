// Request Types
export interface GetServiceRequest {
  partnerId: string | null;
  shopId: string;
  itemsType: string;
  categoryId?: string;
  subCategoryId?: string;
  search?: string;
  skip: number;
  limit: number;
}

export interface GetServiceRequest2 {
  skip: number;
  limit: number;
}

export interface GetServiceRequestBody {
  partnerId: string | null;
  shopId: string;
  itemsType: string;
  categoryId?: string;
  subCategoryId?: string;
  search?: string;
}

export interface Service {
  serviceId: string;
  serviceName: string;
  serviceDesc: string;
  serviceImage: string;
  categoryId: string;
  price: number;
  publish: 'Publish' | 'UnPublish';
  active: 'Actived' | 'InActived';
  created: string;
  updated: string;
}

export interface ServiceResponse {
  data: Service[];
  total: number;
}
